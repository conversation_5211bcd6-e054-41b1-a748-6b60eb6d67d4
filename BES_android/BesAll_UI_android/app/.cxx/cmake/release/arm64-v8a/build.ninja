# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.10

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: Project
# Configuration: Release
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include rules.ninja


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/release/arm64-v8a && /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/bin/cmake -H/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app -B/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/release/arm64-v8a
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1
build rebuild_cache: phony CMakeFiles/rebuild_cache.util

#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/release/arm64-v8a && /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1
build edit_cache: phony CMakeFiles/edit_cache.util
# =============================================================================
# Object build statements for SHARED_LIBRARY target ImageMagic


#############################################
# Order-only phony target for ImageMagic

build cmake_object_order_depends_target_ImageMagic: phony
build CMakeFiles/ImageMagic.dir/src/main/jni/imageMagic/ImageMagic.c.o: C_COMPILER__ImageMagic ../../../../src/main/jni/imageMagic/ImageMagic.c || cmake_object_order_depends_target_ImageMagic
  DEFINES = -DImageMagic_EXPORTS
  DEP_FILE = CMakeFiles/ImageMagic.dir/src/main/jni/imageMagic/ImageMagic.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include
  OBJECT_DIR = CMakeFiles/ImageMagic.dir
  OBJECT_FILE_DIR = CMakeFiles/ImageMagic.dir/src/main/jni/imageMagic

# =============================================================================
# Link build statements for SHARED_LIBRARY target ImageMagic


#############################################
# Link the shared library ../../../../build/intermediates/cmake/release/obj/arm64-v8a/libImageMagic.so

build ../../../../build/intermediates/cmake/release/obj/arm64-v8a/libImageMagic.so: C_SHARED_LIBRARY_LINKER__ImageMagic CMakeFiles/ImageMagic.dir/src/main/jni/imageMagic/ImageMagic.c.o | ../../../../jniLibs/arm64-v8a/libimagequant.so ../../../../jniLibs/arm64-v8a/libpng.so ../../../../jniLibs/arm64-v8a/libquantcmd.so ../../../../jniLibs/arm64-v8a/liblcms2.so /Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/24/liblog.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -O2 -DNDEBUG
  LINK_FLAGS = -Wl,--exclude-libs,libgcc.a -Wl,--exclude-libs,libgcc_real.a -Wl,--exclude-libs,libatomic.a -static-libstdc++ -Wl,--build-id -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = ../../../../jniLibs/arm64-v8a/libimagequant.so ../../../../jniLibs/arm64-v8a/libpng.so ../../../../jniLibs/arm64-v8a/libquantcmd.so ../../../../jniLibs/arm64-v8a/liblcms2.so -llog -latomic -lm
  OBJECT_DIR = CMakeFiles/ImageMagic.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libImageMagic.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = ../../../../build/intermediates/cmake/release/obj/arm64-v8a/libImageMagic.so
  TARGET_PDB = ImageMagic.so.dbg
# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for rebuild_cache

build src/main/jni/opus/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/release/arm64-v8a/src/main/jni/opus && /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/bin/cmake -H/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app -B/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/release/arm64-v8a
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1
build src/main/jni/opus/rebuild_cache: phony src/main/jni/opus/CMakeFiles/rebuild_cache.util

#############################################
# Utility command for edit_cache

build src/main/jni/opus/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/release/arm64-v8a/src/main/jni/opus && /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1
build src/main/jni/opus/edit_cache: phony src/main/jni/opus/CMakeFiles/edit_cache.util
# =============================================================================
# Object build statements for SHARED_LIBRARY target besOpus


#############################################
# Order-only phony target for besOpus

build cmake_object_order_depends_target_besOpus: phony
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/bands.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/celt/bands.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/bands.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/celt.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/celt/celt.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/celt.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/celt_encoder.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/celt/celt_encoder.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/celt_encoder.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/celt_decoder.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/celt/celt_decoder.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/celt_decoder.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/cwrs.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/celt/cwrs.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/cwrs.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/entcode.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/celt/entcode.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/entcode.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/entdec.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/celt/entdec.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/entdec.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/entenc.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/celt/entenc.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/entenc.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/kiss_fft.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/celt/kiss_fft.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/kiss_fft.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/laplace.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/celt/laplace.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/laplace.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/mathops.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/celt/mathops.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/mathops.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/mdct.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/celt/mdct.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/mdct.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/modes.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/celt/modes.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/modes.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/pitch.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/celt/pitch.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/pitch.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/celt_lpc.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/celt/celt_lpc.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/celt_lpc.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/quant_bands.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/celt/quant_bands.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/quant_bands.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/rate.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/celt/rate.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/rate.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/vq.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/celt/vq.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/vq.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/CNG.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/CNG.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/CNG.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/code_signs.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/code_signs.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/code_signs.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/init_decoder.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/init_decoder.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/init_decoder.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_core.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/decode_core.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_core.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_frame.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/decode_frame.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_frame.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_parameters.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/decode_parameters.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_parameters.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_indices.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/decode_indices.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_indices.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_pulses.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/decode_pulses.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_pulses.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decoder_set_fs.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/decoder_set_fs.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decoder_set_fs.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/dec_API.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/dec_API.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/dec_API.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/enc_API.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/enc_API.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/enc_API.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/encode_indices.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/encode_indices.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/encode_indices.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/encode_pulses.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/encode_pulses.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/encode_pulses.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/gain_quant.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/gain_quant.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/gain_quant.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/interpolate.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/interpolate.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/interpolate.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/LP_variable_cutoff.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/LP_variable_cutoff.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/LP_variable_cutoff.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_decode.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/NLSF_decode.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_decode.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NSQ.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/NSQ.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NSQ.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NSQ_del_dec.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/NSQ_del_dec.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NSQ_del_dec.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/PLC.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/PLC.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/PLC.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/shell_coder.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/shell_coder.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/shell_coder.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_gain.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/tables_gain.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_gain.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_LTP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/tables_LTP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_LTP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_NLSF_CB_NB_MB.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/tables_NLSF_CB_NB_MB.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_NLSF_CB_NB_MB.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_NLSF_CB_WB.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/tables_NLSF_CB_WB.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_NLSF_CB_WB.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_other.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/tables_other.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_other.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_pitch_lag.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/tables_pitch_lag.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_pitch_lag.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_pulses_per_block.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/tables_pulses_per_block.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_pulses_per_block.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/VAD.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/VAD.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/VAD.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/control_audio_bandwidth.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/control_audio_bandwidth.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/control_audio_bandwidth.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/quant_LTP_gains.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/quant_LTP_gains.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/quant_LTP_gains.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/VQ_WMat_EC.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/VQ_WMat_EC.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/VQ_WMat_EC.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/HP_variable_cutoff.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/HP_variable_cutoff.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/HP_variable_cutoff.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_encode.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/NLSF_encode.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_encode.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_VQ.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/NLSF_VQ.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_VQ.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_unpack.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/NLSF_unpack.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_unpack.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_del_dec_quant.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/NLSF_del_dec_quant.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_del_dec_quant.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/process_NLSFs.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/process_NLSFs.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/process_NLSFs.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_LR_to_MS.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/stereo_LR_to_MS.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_LR_to_MS.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_MS_to_LR.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/stereo_MS_to_LR.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_MS_to_LR.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/check_control_input.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/check_control_input.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/check_control_input.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/control_SNR.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/control_SNR.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/control_SNR.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/init_encoder.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/init_encoder.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/init_encoder.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/control_codec.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/control_codec.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/control_codec.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/A2NLSF.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/A2NLSF.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/A2NLSF.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/ana_filt_bank_1.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/ana_filt_bank_1.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/ana_filt_bank_1.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/biquad_alt.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/biquad_alt.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/biquad_alt.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/bwexpander_32.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/bwexpander_32.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/bwexpander_32.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/bwexpander.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/bwexpander.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/bwexpander.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/debug.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/debug.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/debug.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_pitch.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/decode_pitch.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_pitch.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/inner_prod_aligned.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/inner_prod_aligned.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/inner_prod_aligned.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/lin2log.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/lin2log.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/lin2log.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/log2lin.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/log2lin.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/log2lin.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/LPC_analysis_filter.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/LPC_analysis_filter.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/LPC_analysis_filter.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/LPC_inv_pred_gain.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/LPC_inv_pred_gain.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/LPC_inv_pred_gain.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/table_LSF_cos.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/table_LSF_cos.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/table_LSF_cos.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF2A.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/NLSF2A.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF2A.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_stabilize.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/NLSF_stabilize.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_stabilize.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_VQ_weights_laroia.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/NLSF_VQ_weights_laroia.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_VQ_weights_laroia.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/pitch_est_tables.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/pitch_est_tables.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/pitch_est_tables.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/resampler.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_down2_3.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/resampler_down2_3.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_down2_3.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_down2.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/resampler_down2.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_down2.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_private_AR2.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/resampler_private_AR2.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_private_AR2.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_private_down_FIR.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/resampler_private_down_FIR.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_private_down_FIR.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_private_IIR_FIR.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/resampler_private_IIR_FIR.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_private_IIR_FIR.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_private_up2_HQ.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/resampler_private_up2_HQ.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_private_up2_HQ.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_rom.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/resampler_rom.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_rom.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/sigm_Q15.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/sigm_Q15.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/sigm_Q15.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/sort.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/sort.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/sort.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/sum_sqr_shift.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/sum_sqr_shift.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/sum_sqr_shift.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_decode_pred.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/stereo_decode_pred.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_decode_pred.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_encode_pred.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/stereo_encode_pred.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_encode_pred.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_find_predictor.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/stereo_find_predictor.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_find_predictor.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_quant_pred.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/stereo_quant_pred.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_quant_pred.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/LPC_fit.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/LPC_fit.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/LPC_fit.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/apply_sine_window_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/apply_sine_window_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/apply_sine_window_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/corrMatrix_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/corrMatrix_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/corrMatrix_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/encode_frame_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/encode_frame_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/encode_frame_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/find_LPC_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/find_LPC_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/find_LPC_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/find_LTP_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/find_LTP_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/find_LTP_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/find_pitch_lags_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/find_pitch_lags_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/find_pitch_lags_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/find_pred_coefs_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/find_pred_coefs_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/find_pred_coefs_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/LPC_analysis_filter_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/LPC_analysis_filter_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/LPC_analysis_filter_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/LTP_analysis_filter_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/LTP_analysis_filter_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/LTP_analysis_filter_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/LTP_scale_ctrl_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/LTP_scale_ctrl_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/LTP_scale_ctrl_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/noise_shape_analysis_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/noise_shape_analysis_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/noise_shape_analysis_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/process_gains_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/process_gains_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/process_gains_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/regularize_correlations_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/regularize_correlations_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/regularize_correlations_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/residual_energy_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/residual_energy_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/residual_energy_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/warped_autocorrelation_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/warped_autocorrelation_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/warped_autocorrelation_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/wrappers_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/wrappers_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/wrappers_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/autocorrelation_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/autocorrelation_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/autocorrelation_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/burg_modified_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/burg_modified_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/burg_modified_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/bwexpander_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/bwexpander_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/bwexpander_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/energy_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/energy_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/energy_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/inner_product_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/inner_product_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/inner_product_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/k2a_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/k2a_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/k2a_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/LPC_inv_pred_gain_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/LPC_inv_pred_gain_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/LPC_inv_pred_gain_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/pitch_analysis_core_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/pitch_analysis_core_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/pitch_analysis_core_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/scale_copy_vector_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/scale_copy_vector_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/scale_copy_vector_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/scale_vector_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/scale_vector_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/scale_vector_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/schur_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/schur_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/schur_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/sort_FLP.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/silk/float/sort_FLP.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/sort_FLP.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/src/opus.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus_decoder.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/src/opus_decoder.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus_decoder.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus_encoder.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/src/opus_encoder.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus_encoder.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus_multistream.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/src/opus_multistream.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus_multistream.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus_multistream_encoder.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/src/opus_multistream_encoder.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus_multistream_encoder.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus_multistream_decoder.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/src/opus_multistream_decoder.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus_multistream_decoder.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/repacketizer.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/src/repacketizer.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/repacketizer.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/mlp.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/src/mlp.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/mlp.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/mlp_data.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/src/mlp_data.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/mlp_data.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src
build src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/analysis.c.o: C_COMPILER__besOpus ../../../../src/main/jni/opus/opus-1.2.1/src/analysis.c || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/analysis.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src
build src/main/jni/opus/CMakeFiles/besOpus.dir/com_bes_opus_OpusDecoder.cpp.o: CXX_COMPILER__besOpus ../../../../src/main/jni/opus/com_bes_opus_OpusDecoder.cpp || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/com_bes_opus_OpusDecoder.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
build src/main/jni/opus/CMakeFiles/besOpus.dir/HuyaDecoder.cpp.o: CXX_COMPILER__besOpus ../../../../src/main/jni/opus/HuyaDecoder.cpp || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/HuyaDecoder.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
build src/main/jni/opus/CMakeFiles/besOpus.dir/HuyaEncoder.cpp.o: CXX_COMPILER__besOpus ../../../../src/main/jni/opus/HuyaEncoder.cpp || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/HuyaEncoder.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
build src/main/jni/opus/CMakeFiles/besOpus.dir/com_bes_opus_OpusEncoder.cpp.o: CXX_COMPILER__besOpus ../../../../src/main/jni/opus/com_bes_opus_OpusEncoder.cpp || cmake_object_order_depends_target_besOpus
  DEFINES = -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION="\"\\\"1.2.1\\\"\"" -DVAR_ARRAYS -DbesOpus_EXPORTS
  DEP_FILE = src/main/jni/opus/CMakeFiles/besOpus.dir/com_bes_opus_OpusEncoder.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -DNDEBUG  -fPIC
  INCLUDES = -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  OBJECT_FILE_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir

# =============================================================================
# Link build statements for SHARED_LIBRARY target besOpus


#############################################
# Link the shared library ../../../../build/intermediates/cmake/release/obj/arm64-v8a/libbesOpus.so

build ../../../../build/intermediates/cmake/release/obj/arm64-v8a/libbesOpus.so: CXX_SHARED_LIBRARY_LINKER__besOpus src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/bands.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/celt.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/celt_encoder.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/celt_decoder.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/cwrs.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/entcode.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/entdec.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/entenc.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/kiss_fft.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/laplace.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/mathops.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/mdct.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/modes.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/pitch.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/celt_lpc.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/quant_bands.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/rate.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/vq.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/CNG.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/code_signs.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/init_decoder.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_core.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_frame.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_parameters.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_indices.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_pulses.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decoder_set_fs.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/dec_API.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/enc_API.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/encode_indices.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/encode_pulses.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/gain_quant.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/interpolate.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/LP_variable_cutoff.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_decode.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NSQ.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NSQ_del_dec.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/PLC.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/shell_coder.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_gain.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_LTP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_NLSF_CB_NB_MB.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_NLSF_CB_WB.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_other.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_pitch_lag.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_pulses_per_block.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/VAD.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/control_audio_bandwidth.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/quant_LTP_gains.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/VQ_WMat_EC.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/HP_variable_cutoff.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_encode.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_VQ.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_unpack.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_del_dec_quant.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/process_NLSFs.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_LR_to_MS.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_MS_to_LR.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/check_control_input.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/control_SNR.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/init_encoder.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/control_codec.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/A2NLSF.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/ana_filt_bank_1.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/biquad_alt.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/bwexpander_32.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/bwexpander.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/debug.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_pitch.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/inner_prod_aligned.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/lin2log.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/log2lin.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/LPC_analysis_filter.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/LPC_inv_pred_gain.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/table_LSF_cos.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF2A.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_stabilize.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_VQ_weights_laroia.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/pitch_est_tables.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_down2_3.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_down2.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_private_AR2.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_private_down_FIR.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_private_IIR_FIR.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_private_up2_HQ.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_rom.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/sigm_Q15.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/sort.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/sum_sqr_shift.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_decode_pred.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_encode_pred.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_find_predictor.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_quant_pred.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/LPC_fit.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/apply_sine_window_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/corrMatrix_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/encode_frame_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/find_LPC_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/find_LTP_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/find_pitch_lags_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/find_pred_coefs_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/LPC_analysis_filter_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/LTP_analysis_filter_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/LTP_scale_ctrl_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/noise_shape_analysis_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/process_gains_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/regularize_correlations_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/residual_energy_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/warped_autocorrelation_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/wrappers_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/autocorrelation_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/burg_modified_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/bwexpander_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/energy_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/inner_product_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/k2a_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/LPC_inv_pred_gain_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/pitch_analysis_core_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/scale_copy_vector_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/scale_vector_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/schur_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/sort_FLP.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus_decoder.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus_encoder.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus_multistream.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus_multistream_encoder.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus_multistream_decoder.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/repacketizer.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/mlp.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/mlp_data.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/analysis.c.o src/main/jni/opus/CMakeFiles/besOpus.dir/com_bes_opus_OpusDecoder.cpp.o src/main/jni/opus/CMakeFiles/besOpus.dir/HuyaDecoder.cpp.o src/main/jni/opus/CMakeFiles/besOpus.dir/HuyaEncoder.cpp.o src/main/jni/opus/CMakeFiles/besOpus.dir/com_bes_opus_OpusEncoder.cpp.o | /Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/24/liblog.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -DNDEBUG
  LINK_FLAGS = -Wl,--exclude-libs,libgcc.a -Wl,--exclude-libs,libgcc_real.a -Wl,--exclude-libs,libatomic.a -static-libstdc++ -Wl,--build-id -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = -llog -latomic -lm
  OBJECT_DIR = src/main/jni/opus/CMakeFiles/besOpus.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libbesOpus.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = ../../../../build/intermediates/cmake/release/obj/arm64-v8a/libbesOpus.so
  TARGET_PDB = besOpus.so.dbg
# =============================================================================
# Target aliases.

build ImageMagic: phony ../../../../build/intermediates/cmake/release/obj/arm64-v8a/libImageMagic.so
build besOpus: phony ../../../../build/intermediates/cmake/release/obj/arm64-v8a/libbesOpus.so
build libImageMagic.so: phony ../../../../build/intermediates/cmake/release/obj/arm64-v8a/libImageMagic.so
build libbesOpus.so: phony ../../../../build/intermediates/cmake/release/obj/arm64-v8a/libbesOpus.so
# =============================================================================
# Folder targets.

# =============================================================================
# =============================================================================

#############################################
# Folder: /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/release/arm64-v8a/src/main/jni/opus

build  src/main/jni/opus/all: phony besOpus
# =============================================================================
# Built-in targets


#############################################
# The main all target.

build all: phony ../../../../build/intermediates/cmake/release/obj/arm64-v8a/libImageMagic.so ../../../../build/intermediates/cmake/release/obj/arm64-v8a/libbesOpus.so

#############################################
# Make the all target the default.

default all

#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | ../../../../CMakeLists.txt ../../../../src/main/jni/opus/CMakeLists.txt /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeCCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeCCompilerABI.c /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeCInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeCXXCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeCXXCompilerABI.cpp /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeCXXInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeCommonLanguageInclude.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeDetermineCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeDetermineCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeDetermineCompileFeatures.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeDetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeDetermineCompilerABI.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeDetermineSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeFindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeGenericSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeLanguageInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeParseImplicitLinkInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeSystem.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeSystemSpecificInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeSystemSpecificInitialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeTestCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeTestCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeTestCompilerCommon.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/CMakeCommonCompilerMacros.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/Clang-C-FeatureTests.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/Clang-CXX-FeatureTests.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/Clang-CXX-TestableFeatures.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/Clang-FindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/GNU.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Internal/FeatureTesting.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android-Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android-Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android-Determine-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android-Determine-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android-Determine.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android/Determine-Compiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Linux.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/UnixPaths.cmake /Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/build/cmake/android.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.10.2/CMakeCCompiler.cmake CMakeFiles/3.10.2/CMakeCXXCompiler.cmake CMakeFiles/3.10.2/CMakeSystem.cmake CMakeFiles/feature_tests.c CMakeFiles/feature_tests.cxx
  pool = console

#############################################
# A missing CMake input file is not an error.

build ../../../../CMakeLists.txt ../../../../src/main/jni/opus/CMakeLists.txt /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeCCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeCCompilerABI.c /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeCInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeCXXCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeCXXCompilerABI.cpp /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeCXXInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeCommonLanguageInclude.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeDetermineCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeDetermineCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeDetermineCompileFeatures.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeDetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeDetermineCompilerABI.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeDetermineSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeFindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeGenericSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeLanguageInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeParseImplicitLinkInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeSystem.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeSystemSpecificInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeSystemSpecificInitialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeTestCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeTestCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/CMakeTestCompilerCommon.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/CMakeCommonCompilerMacros.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/Clang-C-FeatureTests.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/Clang-CXX-FeatureTests.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/Clang-CXX-TestableFeatures.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/Clang-FindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Compiler/GNU.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Internal/FeatureTesting.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android-Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android-Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android-Determine-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android-Determine-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android-Determine.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Android/Determine-Compiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/Linux.cmake /Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/share/cmake-3.10/Modules/Platform/UnixPaths.cmake /Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/build/cmake/android.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.10.2/CMakeCCompiler.cmake CMakeFiles/3.10.2/CMakeCXXCompiler.cmake CMakeFiles/3.10.2/CMakeSystem.cmake CMakeFiles/feature_tests.c CMakeFiles/feature_tests.cxx: phony

#############################################
# Clean all the built files.

build clean: CLEAN

#############################################
# Print all primary targets available.

build help: HELP
