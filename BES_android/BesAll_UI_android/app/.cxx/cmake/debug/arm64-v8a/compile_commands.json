[{"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DImageMagic_EXPORTS -I../../../../src/main/jni/imageMagic/include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -O0 -fno-limit-debug-info  -fPIC   -o CMakeFiles/ImageMagic.dir/src/main/jni/imageMagic/ImageMagic.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/imageMagic/ImageMagic.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/imageMagic/ImageMagic.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/bands.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/bands.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/bands.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/celt.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/celt.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/celt.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/celt_encoder.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/celt_encoder.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/celt_encoder.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/celt_decoder.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/celt_decoder.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/celt_decoder.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/cwrs.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/cwrs.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/cwrs.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/entcode.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/entcode.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/entcode.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/entdec.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/entdec.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/entdec.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/entenc.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/entenc.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/entenc.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/kiss_fft.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/kiss_fft.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/kiss_fft.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/laplace.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/laplace.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/laplace.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/mathops.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/mathops.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/mathops.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/mdct.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/mdct.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/mdct.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/modes.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/modes.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/modes.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/pitch.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/pitch.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/pitch.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/celt_lpc.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/celt_lpc.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/celt_lpc.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/quant_bands.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/quant_bands.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/quant_bands.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/rate.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/rate.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/rate.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/celt/vq.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/vq.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/vq.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/CNG.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/CNG.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/CNG.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/code_signs.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/code_signs.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/code_signs.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/init_decoder.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/init_decoder.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/init_decoder.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_core.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/decode_core.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/decode_core.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_frame.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/decode_frame.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/decode_frame.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_parameters.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/decode_parameters.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/decode_parameters.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_indices.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/decode_indices.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/decode_indices.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_pulses.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/decode_pulses.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/decode_pulses.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decoder_set_fs.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/decoder_set_fs.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/decoder_set_fs.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/dec_API.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/dec_API.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/dec_API.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/enc_API.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/enc_API.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/enc_API.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/encode_indices.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/encode_indices.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/encode_indices.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/encode_pulses.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/encode_pulses.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/encode_pulses.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/gain_quant.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/gain_quant.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/gain_quant.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/interpolate.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/interpolate.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/interpolate.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/LP_variable_cutoff.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/LP_variable_cutoff.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/LP_variable_cutoff.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_decode.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF_decode.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF_decode.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NSQ.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NSQ.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NSQ.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NSQ_del_dec.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NSQ_del_dec.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NSQ_del_dec.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/PLC.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/PLC.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/PLC.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/shell_coder.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/shell_coder.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/shell_coder.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_gain.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/tables_gain.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/tables_gain.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_LTP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/tables_LTP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/tables_LTP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_NLSF_CB_NB_MB.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/tables_NLSF_CB_NB_MB.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/tables_NLSF_CB_NB_MB.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_NLSF_CB_WB.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/tables_NLSF_CB_WB.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/tables_NLSF_CB_WB.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_other.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/tables_other.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/tables_other.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_pitch_lag.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/tables_pitch_lag.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/tables_pitch_lag.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/tables_pulses_per_block.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/tables_pulses_per_block.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/tables_pulses_per_block.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/VAD.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/VAD.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/VAD.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/control_audio_bandwidth.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/control_audio_bandwidth.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/control_audio_bandwidth.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/quant_LTP_gains.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/quant_LTP_gains.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/quant_LTP_gains.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/VQ_WMat_EC.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/VQ_WMat_EC.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/VQ_WMat_EC.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/HP_variable_cutoff.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/HP_variable_cutoff.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/HP_variable_cutoff.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_encode.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF_encode.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF_encode.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_VQ.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF_VQ.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF_VQ.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_unpack.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF_unpack.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF_unpack.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_del_dec_quant.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF_del_dec_quant.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF_del_dec_quant.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/process_NLSFs.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/process_NLSFs.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/process_NLSFs.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_LR_to_MS.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/stereo_LR_to_MS.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/stereo_LR_to_MS.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_MS_to_LR.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/stereo_MS_to_LR.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/stereo_MS_to_LR.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/check_control_input.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/check_control_input.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/check_control_input.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/control_SNR.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/control_SNR.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/control_SNR.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/init_encoder.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/init_encoder.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/init_encoder.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/control_codec.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/control_codec.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/control_codec.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/A2NLSF.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/A2NLSF.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/A2NLSF.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/ana_filt_bank_1.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/ana_filt_bank_1.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/ana_filt_bank_1.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/biquad_alt.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/biquad_alt.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/biquad_alt.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/bwexpander_32.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/bwexpander_32.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/bwexpander_32.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/bwexpander.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/bwexpander.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/bwexpander.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/debug.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/debug.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/debug.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/decode_pitch.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/decode_pitch.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/decode_pitch.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/inner_prod_aligned.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/inner_prod_aligned.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/inner_prod_aligned.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/lin2log.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/lin2log.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/lin2log.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/log2lin.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/log2lin.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/log2lin.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/LPC_analysis_filter.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/LPC_analysis_filter.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/LPC_analysis_filter.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/LPC_inv_pred_gain.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/LPC_inv_pred_gain.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/LPC_inv_pred_gain.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/table_LSF_cos.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/table_LSF_cos.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/table_LSF_cos.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF2A.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF2A.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF2A.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_stabilize.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF_stabilize.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF_stabilize.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/NLSF_VQ_weights_laroia.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF_VQ_weights_laroia.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF_VQ_weights_laroia.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/pitch_est_tables.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/pitch_est_tables.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/pitch_est_tables.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_down2_3.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler_down2_3.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler_down2_3.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_down2.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler_down2.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler_down2.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_private_AR2.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler_private_AR2.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler_private_AR2.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_private_down_FIR.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler_private_down_FIR.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler_private_down_FIR.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_private_IIR_FIR.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler_private_IIR_FIR.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler_private_IIR_FIR.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_private_up2_HQ.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler_private_up2_HQ.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler_private_up2_HQ.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/resampler_rom.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler_rom.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler_rom.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/sigm_Q15.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/sigm_Q15.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/sigm_Q15.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/sort.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/sort.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/sort.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/sum_sqr_shift.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/sum_sqr_shift.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/sum_sqr_shift.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_decode_pred.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/stereo_decode_pred.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/stereo_decode_pred.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_encode_pred.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/stereo_encode_pred.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/stereo_encode_pred.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_find_predictor.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/stereo_find_predictor.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/stereo_find_predictor.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/stereo_quant_pred.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/stereo_quant_pred.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/stereo_quant_pred.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/LPC_fit.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/LPC_fit.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/LPC_fit.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/apply_sine_window_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/apply_sine_window_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/apply_sine_window_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/corrMatrix_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/corrMatrix_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/corrMatrix_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/encode_frame_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/encode_frame_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/encode_frame_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/find_LPC_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/find_LPC_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/find_LPC_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/find_LTP_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/find_LTP_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/find_LTP_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/find_pitch_lags_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/find_pitch_lags_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/find_pitch_lags_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/find_pred_coefs_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/find_pred_coefs_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/find_pred_coefs_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/LPC_analysis_filter_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/LPC_analysis_filter_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/LPC_analysis_filter_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/LTP_analysis_filter_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/LTP_analysis_filter_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/LTP_analysis_filter_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/LTP_scale_ctrl_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/LTP_scale_ctrl_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/LTP_scale_ctrl_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/noise_shape_analysis_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/noise_shape_analysis_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/noise_shape_analysis_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/process_gains_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/process_gains_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/process_gains_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/regularize_correlations_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/regularize_correlations_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/regularize_correlations_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/residual_energy_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/residual_energy_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/residual_energy_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/warped_autocorrelation_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/warped_autocorrelation_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/warped_autocorrelation_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/wrappers_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/wrappers_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/wrappers_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/autocorrelation_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/autocorrelation_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/autocorrelation_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/burg_modified_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/burg_modified_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/burg_modified_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/bwexpander_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/bwexpander_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/bwexpander_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/energy_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/energy_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/energy_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/inner_product_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/inner_product_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/inner_product_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/k2a_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/k2a_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/k2a_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/LPC_inv_pred_gain_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/LPC_inv_pred_gain_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/LPC_inv_pred_gain_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/pitch_analysis_core_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/pitch_analysis_core_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/pitch_analysis_core_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/scale_copy_vector_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/scale_copy_vector_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/scale_copy_vector_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/scale_vector_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/scale_vector_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/scale_vector_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/schur_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/schur_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/schur_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/silk/float/sort_FLP.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/sort_FLP.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/sort_FLP.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/opus.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/opus.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus_decoder.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/opus_decoder.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/opus_decoder.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus_encoder.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/opus_encoder.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/opus_encoder.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus_multistream.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/opus_multistream.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/opus_multistream.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus_multistream_encoder.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/opus_multistream_encoder.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/opus_multistream_encoder.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/opus_multistream_decoder.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/opus_multistream_decoder.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/opus_multistream_decoder.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/repacketizer.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/repacketizer.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/repacketizer.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/mlp.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/mlp.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/mlp.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/mlp_data.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/mlp_data.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/mlp_data.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/opus-1.2.1/src/analysis.c.o   -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/analysis.c", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/analysis.c"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot  -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/com_bes_opus_OpusDecoder.cpp.o -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/com_bes_opus_OpusDecoder.cpp", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/com_bes_opus_OpusDecoder.cpp"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot  -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/HuyaDecoder.cpp.o -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/HuyaDecoder.cpp", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/HuyaDecoder.cpp"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot  -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/HuyaEncoder.cpp.o -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/HuyaEncoder.cpp", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/HuyaEncoder.cpp"}, {"directory": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot  -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC   -o src/main/jni/opus/CMakeFiles/besOpus.dir/com_bes_opus_OpusEncoder.cpp.o -c /Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/com_bes_opus_OpusEncoder.cpp", "file": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/com_bes_opus_OpusEncoder.cpp"}]