{"stringTable": {"0": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a", "1": "--target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DImageMagic_EXPORTS -I../../../../src/main/jni/imageMagic/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -O0 -fno-limit-debug-info -fPIC", "2": "--target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -Wall -W -Wstrict-prototypes -Wextra -Wcast-align -Wnested-externs -Wshadow -O0 -fno-limit-debug-info -fPIC", "3": "--target=aarch64-none-linux-android24 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DHAVE_LRINTF -DOPUS_BUILD -DOPUS_VERSION=\"\\\"\\\\\\\"1.2.1\\\\\\\"\\\"\" -DVAR_ARRAYS -DbesOpus_EXPORTS -I../../../../src/main/jni/imageMagic/include -I../../../../src/main/jni/opus/opus-1.2.1/include -I../../../../src/main/jni/opus/opus-1.2.1/silk -I../../../../src/main/jni/opus/opus-1.2.1/silk/float -I../../../../src/main/jni/opus/opus-1.2.1/silk/fixed -I../../../../src/main/jni/opus/opus-1.2.1/celt -I../../../../src/main/jni/opus/opus-1.2.1/src -I../../../../src/main/jni/opus/opus-1.2.1/../include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -O0 -fno-limit-debug-info -fPIC"}, "buildFiles": ["/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/CMakeLists.txt", "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/build/cmake/android.toolchain.cmake", "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/build/cmake/platforms.cmake", "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/CMakeLists.txt"], "cleanCommands": ["/Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/bin/ninja -C \"/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a\" clean"], "buildTargetsCommand": "/Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/bin/ninja  -C \"/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a\" {LIST_OF_TARGETS_TO_BUILD}", "libraries": {"ImageMagic-Debug-arm64-v8a": {"buildCommand": "/Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/bin/ninja -C \"/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a\" ImageMagic", "buildType": "debug", "toolchain": "4153100576", "abi": "arm64-v8a", "artifactName": "ImageMagic", "files": [{"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/imageMagic/ImageMagic.c", "flagsOrdinal": 1, "workingDirectoryOrdinal": 0}], "headers": [], "output": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/build/intermediates/cmake/debug/obj/arm64-v8a/libImageMagic.so", "runtimeFiles": ["/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/jniLibs/arm64-v8a/libimagequant.so", "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/jniLibs/arm64-v8a/libpng.so", "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/jniLibs/arm64-v8a/libquantcmd.so", "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/jniLibs/arm64-v8a/liblcms2.so"]}, "besOpus-Debug-arm64-v8a": {"buildCommand": "/Users/<USER>/Library/Android/sdk/cmake/3.10.2.4988404/bin/ninja -C \"/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/.cxx/cmake/debug/arm64-v8a\" besOpus", "buildType": "debug", "toolchain": "4153100576", "abi": "arm64-v8a", "artifactName": "besOpus", "files": [{"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/com_bes_opus_OpusDecoder.cpp", "flagsOrdinal": 3, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/HuyaDecoder.cpp", "flagsOrdinal": 3, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/HuyaEncoder.cpp", "flagsOrdinal": 3, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/com_bes_opus_OpusEncoder.cpp", "flagsOrdinal": 3, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/bands.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/celt.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/celt_encoder.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/celt_decoder.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/cwrs.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/entcode.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/entdec.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/entenc.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/kiss_fft.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/laplace.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/mathops.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/mdct.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/modes.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/pitch.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/celt_lpc.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/quant_bands.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/rate.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/celt/vq.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/CNG.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/code_signs.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/init_decoder.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/decode_core.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/decode_frame.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/decode_parameters.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/decode_indices.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/decode_pulses.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/decoder_set_fs.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/dec_API.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/enc_API.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/encode_indices.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/encode_pulses.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/gain_quant.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/interpolate.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/LP_variable_cutoff.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF_decode.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NSQ.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NSQ_del_dec.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/PLC.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/shell_coder.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/tables_gain.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/tables_LTP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/tables_NLSF_CB_NB_MB.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/tables_NLSF_CB_WB.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/tables_other.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/tables_pitch_lag.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/tables_pulses_per_block.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/VAD.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/control_audio_bandwidth.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/quant_LTP_gains.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/VQ_WMat_EC.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/HP_variable_cutoff.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF_encode.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF_VQ.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF_unpack.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF_del_dec_quant.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/process_NLSFs.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/stereo_LR_to_MS.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/stereo_MS_to_LR.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/check_control_input.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/control_SNR.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/init_encoder.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/control_codec.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/A2NLSF.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/ana_filt_bank_1.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/biquad_alt.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/bwexpander_32.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/bwexpander.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/debug.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/decode_pitch.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/inner_prod_aligned.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/lin2log.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/log2lin.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/LPC_analysis_filter.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/LPC_inv_pred_gain.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/table_LSF_cos.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF2A.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF_stabilize.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/NLSF_VQ_weights_laroia.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/pitch_est_tables.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler_down2_3.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler_down2.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler_private_AR2.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler_private_down_FIR.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler_private_IIR_FIR.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler_private_up2_HQ.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/resampler_rom.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/sigm_Q15.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/sort.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/sum_sqr_shift.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/stereo_decode_pred.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/stereo_encode_pred.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/stereo_find_predictor.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/stereo_quant_pred.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/LPC_fit.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/apply_sine_window_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/corrMatrix_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/encode_frame_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/find_LPC_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/find_LTP_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/find_pitch_lags_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/find_pred_coefs_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/LPC_analysis_filter_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/LTP_analysis_filter_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/LTP_scale_ctrl_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/noise_shape_analysis_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/process_gains_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/regularize_correlations_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/residual_energy_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/warped_autocorrelation_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/wrappers_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/autocorrelation_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/burg_modified_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/bwexpander_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/energy_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/inner_product_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/k2a_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/LPC_inv_pred_gain_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/pitch_analysis_core_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/scale_copy_vector_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/scale_vector_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/schur_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/silk/float/sort_FLP.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/opus.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/opus_decoder.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/opus_encoder.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/opus_multistream.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/opus_multistream_encoder.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/opus_multistream_decoder.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/repacketizer.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/mlp.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/mlp_data.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}, {"src": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/src/main/jni/opus/opus-1.2.1/src/analysis.c", "flagsOrdinal": 2, "workingDirectoryOrdinal": 0}], "headers": [], "output": "/Users/<USER>/Desktop/BES_git/android_bes/BesAll_UI_android/app/build/intermediates/cmake/debug/obj/arm64-v8a/libbesOpus.so", "runtimeFiles": []}}, "toolchains": {"4153100576": {"cCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang", "cppCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++"}}, "cFileExtensions": ["c"], "cppFileExtensions": ["cpp"]}