<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\celt\arch.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\celt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\entdec.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\entenc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\float_cast.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\os_support.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\stack_alloc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\opus.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\opus_defines.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\opus_types.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\opus_multistream.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\win32\config.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\analysis.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\mlp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\opus_private.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\tansig_table.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\x86\x86cpu.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\_kiss_fft_guts.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\bands.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\celt_lpc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\x86\celt_lpc_sse.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\cwrs.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\ecintrin.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\entcode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\fixed_c5x.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\fixed_c6x.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\fixed_debug.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\fixed_generic.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\kiss_fft.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\laplace.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\mathops.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\mdct.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\mfrngcod.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\modes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\pitch.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\x86\pitch_sse.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\quant_bands.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\rate.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\static_modes_fixed.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\static_modes_float.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\vq.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\silk\typedef.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\silk\API.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\silk\control.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\silk\debug.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\silk\define.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\silk\errors.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\silk\Inlines.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\silk\MacroCount.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\silk\MacroDebug.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\silk\macros.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\silk\main.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\silk\x86\main_sse.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\silk\pitch_est_defines.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\silk\PLC.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\silk\resampler_private.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\silk\resampler_rom.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\silk\resampler_structs.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\silk\structs.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\silk\tables.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\silk\tuning_parameters.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\silk\float\main_FLP.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\silk\float\SigProc_FLP.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\silk\float\structs_FLP.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\celt\x86\vq_sse.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\src\analysis.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\bands.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\celt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\celt_decoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\celt_encoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\celt_lpc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\x86\celt_lpc_sse.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\cwrs.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\entcode.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\entdec.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\entenc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\kiss_fft.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\laplace.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\mathops.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\mdct.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\mlp.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\mlp_data.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\modes.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\opus.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\opus_compare.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\opus_decoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\opus_encoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\opus_multistream.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\opus_multistream_decoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\opus_multistream_encoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\pitch.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\x86\pitch_sse.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\x86\pitch_sse2.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\x86\pitch_sse4_1.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\quant_bands.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\rate.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\repacketizer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\vq.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\x86\x86_celt_map.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\x86\x86cpu.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\A2NLSF.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\ana_filt_bank_1.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\biquad_alt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\bwexpander.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\bwexpander_32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\check_control_input.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\CNG.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\code_signs.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\control_audio_bandwidth.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\control_codec.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\control_SNR.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\debug.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\dec_API.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\decode_core.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\decode_frame.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\decode_indices.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\decode_parameters.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\decode_pitch.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\decode_pulses.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\decoder_set_fs.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\enc_API.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\encode_indices.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\encode_pulses.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\gain_quant.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\HP_variable_cutoff.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\init_decoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\init_encoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\inner_prod_aligned.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\interpolate.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\lin2log.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\log2lin.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\LP_variable_cutoff.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\LPC_analysis_filter.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\LPC_inv_pred_gain.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\NLSF_decode.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\NLSF_del_dec_quant.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\NLSF_encode.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\NLSF_stabilize.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\NLSF_unpack.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\NLSF_VQ.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\NLSF_VQ_weights_laroia.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\NLSF2A.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\NSQ.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\NSQ_del_dec.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\x86\NSQ_del_dec_sse.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\x86\NSQ_sse.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\pitch_est_tables.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\PLC.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\process_NLSFs.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\quant_LTP_gains.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\resampler.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\resampler_down2.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\resampler_down2_3.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\resampler_private_AR2.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\resampler_private_down_FIR.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\resampler_private_IIR_FIR.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\resampler_private_up2_HQ.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\resampler_rom.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\shell_coder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\sigm_Q15.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\sort.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\stereo_decode_pred.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\stereo_encode_pred.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\stereo_find_predictor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\stereo_LR_to_MS.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\stereo_MS_to_LR.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\stereo_quant_pred.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\sum_sqr_shift.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\table_LSF_cos.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\tables_gain.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\tables_LTP.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\tables_NLSF_CB_NB_MB.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\tables_NLSF_CB_WB.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\tables_other.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\tables_pitch_lag.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\tables_pulses_per_block.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\VAD.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\x86\VAD_sse.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\VQ_WMat_EC.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\x86\VQ_WMat_EC_sse.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\x86\x86_silk_map.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\silk\LPC_fit.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\celt\x86\vq_sse2.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
</Project>