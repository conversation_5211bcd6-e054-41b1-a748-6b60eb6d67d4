<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="DebugDLL_fixed|Win32">
      <Configuration>DebugDLL_fixed</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DebugDLL_fixed|x64">
      <Configuration>DebugDLL_fixed</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DebugDLL|Win32">
      <Configuration>DebugDLL</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DebugDLL|x64">
      <Configuration>DebugDLL</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseDLL_fixed|Win32">
      <Configuration>ReleaseDLL_fixed</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseDLL_fixed|x64">
      <Configuration>ReleaseDLL_fixed</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseDLL|Win32">
      <Configuration>ReleaseDLL</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseDLL|x64">
      <Configuration>ReleaseDLL</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <Keyword>Win32Proj</Keyword>
    <ProjectName>opus</ProjectName>
    <ProjectGuid>{219EC965-228A-1824-174D-96449D05F88A}</ProjectGuid>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DebugDLL|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DebugDLL_fixed|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DebugDLL|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DebugDLL_fixed|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseDLL|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseDLL_fixed|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseDLL|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseDLL_fixed|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="common.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='DebugDLL|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="common.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='DebugDLL_fixed|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="common.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="common.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='DebugDLL|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="common.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='DebugDLL_fixed|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="common.props" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="common.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseDLL|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="common.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseDLL_fixed|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="common.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="common.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseDLL|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="common.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseDLL_fixed|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="common.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <ItemDefinitionGroup>
    <ClCompile>
      <AdditionalIncludeDirectories>..\..\silk\fixed;..\..\silk\float;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(ConfigurationType)'=='DynamicLibrary'">DLL_EXPORT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)'=='DebugDLL_fixed' or '$(Configuration)'=='ReleaseDLL_fixed'">FIXED_POINT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalOptions Condition="'$(Platform)'=='Win32'">/arch:IA32 %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Lib>
      <AdditionalOptions>/ignore:4221 %(AdditionalOptions)</AdditionalOptions>
    </Lib>
    <PreBuildEvent>
      <Command>"$(ProjectDir)..\..\win32\genversion.bat" "$(ProjectDir)..\..\win32\version.h" PACKAGE_VERSION</Command>
      <Message>Generating version.h</Message>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\..\celt\arch.h" />
    <ClInclude Include="..\..\celt\bands.h" />
    <ClInclude Include="..\..\celt\celt.h" />
    <ClInclude Include="..\..\celt\celt_lpc.h" />
    <ClInclude Include="..\..\celt\cwrs.h" />
    <ClInclude Include="..\..\celt\ecintrin.h" />
    <ClInclude Include="..\..\celt\entcode.h" />
    <ClInclude Include="..\..\celt\entdec.h" />
    <ClInclude Include="..\..\celt\entenc.h" />
    <ClInclude Include="..\..\celt\fixed_c5x.h" />
    <ClInclude Include="..\..\celt\fixed_c6x.h" />
    <ClInclude Include="..\..\celt\fixed_debug.h" />
    <ClInclude Include="..\..\celt\fixed_generic.h" />
    <ClInclude Include="..\..\celt\float_cast.h" />
    <ClInclude Include="..\..\celt\kiss_fft.h" />
    <ClInclude Include="..\..\celt\laplace.h" />
    <ClInclude Include="..\..\celt\mathops.h" />
    <ClInclude Include="..\..\celt\mdct.h" />
    <ClInclude Include="..\..\celt\mfrngcod.h" />
    <ClInclude Include="..\..\celt\modes.h" />
    <ClInclude Include="..\..\celt\os_support.h" />
    <ClInclude Include="..\..\celt\pitch.h" />
    <ClInclude Include="..\..\celt\quant_bands.h" />
    <ClInclude Include="..\..\celt\rate.h" />
    <ClInclude Include="..\..\celt\stack_alloc.h" />
    <ClInclude Include="..\..\celt\static_modes_fixed.h" />
    <ClInclude Include="..\..\celt\static_modes_float.h" />
    <ClInclude Include="..\..\celt\vq.h" />
    <ClInclude Include="..\..\celt\x86\celt_lpc_sse.h" />
    <ClInclude Include="..\..\celt\x86\pitch_sse.h" />
    <ClInclude Include="..\..\celt\x86\vq_sse.h" />
    <ClInclude Include="..\..\celt\x86\x86cpu.h" />
    <ClInclude Include="..\..\celt\_kiss_fft_guts.h" />
    <ClInclude Include="..\..\include\opus.h" />
    <ClInclude Include="..\..\include\opus_defines.h" />
    <ClInclude Include="..\..\include\opus_types.h" />
    <ClInclude Include="..\..\include\opus_multistream.h" />
    <ClInclude Include="..\..\silk\API.h" />
    <ClInclude Include="..\..\silk\control.h" />
    <ClInclude Include="..\..\silk\debug.h" />
    <ClInclude Include="..\..\silk\define.h" />
    <ClInclude Include="..\..\silk\errors.h" />
    <ClInclude Include="..\..\silk\float\main_FLP.h" />
    <ClInclude Include="..\..\silk\float\SigProc_FLP.h" />
    <ClInclude Include="..\..\silk\float\structs_FLP.h" />
    <ClInclude Include="..\..\silk\Inlines.h" />
    <ClInclude Include="..\..\silk\MacroCount.h" />
    <ClInclude Include="..\..\silk\MacroDebug.h" />
    <ClInclude Include="..\..\silk\macros.h" />
    <ClInclude Include="..\..\silk\main.h" />
    <ClInclude Include="..\..\silk\pitch_est_defines.h" />
    <ClInclude Include="..\..\silk\PLC.h" />
    <ClInclude Include="..\..\silk\resampler_private.h" />
    <ClInclude Include="..\..\silk\resampler_rom.h" />
    <ClInclude Include="..\..\silk\resampler_structs.h" />
    <ClInclude Include="..\..\silk\structs.h" />
    <ClInclude Include="..\..\silk\tables.h" />
    <ClInclude Include="..\..\silk\tuning_parameters.h" />
    <ClInclude Include="..\..\silk\typedef.h" />
    <ClInclude Include="..\..\silk\x86\main_sse.h" />
    <ClInclude Include="..\..\win32\config.h" />
    <ClInclude Include="..\..\src\analysis.h" />
    <ClInclude Include="..\..\src\mlp.h" />
    <ClInclude Include="..\..\src\opus_private.h" />
    <ClInclude Include="..\..\src\tansig_table.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\celt\bands.c" />
    <ClCompile Include="..\..\celt\celt.c" />
    <ClCompile Include="..\..\celt\celt_decoder.c" />
    <ClCompile Include="..\..\celt\celt_encoder.c" />
    <ClCompile Include="..\..\celt\celt_lpc.c" />
    <ClCompile Include="..\..\celt\cwrs.c" />
    <ClCompile Include="..\..\celt\entcode.c" />
    <ClCompile Include="..\..\celt\entdec.c" />
    <ClCompile Include="..\..\celt\entenc.c" />
    <ClCompile Include="..\..\celt\kiss_fft.c" />
    <ClCompile Include="..\..\celt\laplace.c" />
    <ClCompile Include="..\..\celt\mathops.c" />
    <ClCompile Include="..\..\celt\mdct.c" />
    <ClCompile Include="..\..\celt\modes.c" />
    <ClCompile Include="..\..\celt\pitch.c" />
    <ClCompile Include="..\..\celt\quant_bands.c" />
    <ClCompile Include="..\..\celt\rate.c" />
    <ClCompile Include="..\..\celt\vq.c" />
    <ClCompile Include="..\..\celt\x86\celt_lpc_sse.c" />
    <ClCompile Include="..\..\celt\x86\pitch_sse.c" />
    <ClCompile Include="..\..\celt\x86\pitch_sse2.c" />
    <ClCompile Include="..\..\celt\x86\pitch_sse4_1.c" />
    <ClCompile Include="..\..\celt\x86\vq_sse2.c" />
    <ClCompile Include="..\..\celt\x86\x86cpu.c" />
    <ClCompile Include="..\..\celt\x86\x86_celt_map.c" />
    <ClCompile Include="..\..\silk\A2NLSF.c" />
    <ClCompile Include="..\..\silk\ana_filt_bank_1.c" />
    <ClCompile Include="..\..\silk\biquad_alt.c" />
    <ClCompile Include="..\..\silk\bwexpander.c" />
    <ClCompile Include="..\..\silk\bwexpander_32.c" />
    <ClCompile Include="..\..\silk\check_control_input.c" />
    <ClCompile Include="..\..\silk\CNG.c" />
    <ClCompile Include="..\..\silk\code_signs.c" />
    <ClCompile Include="..\..\silk\control_audio_bandwidth.c" />
    <ClCompile Include="..\..\silk\control_codec.c" />
    <ClCompile Include="..\..\silk\control_SNR.c" />
    <ClCompile Include="..\..\silk\debug.c" />
    <ClCompile Include="..\..\silk\decoder_set_fs.c" />
    <ClCompile Include="..\..\silk\decode_core.c" />
    <ClCompile Include="..\..\silk\decode_frame.c" />
    <ClCompile Include="..\..\silk\decode_indices.c" />
    <ClCompile Include="..\..\silk\decode_parameters.c" />
    <ClCompile Include="..\..\silk\decode_pitch.c" />
    <ClCompile Include="..\..\silk\decode_pulses.c" />
    <ClCompile Include="..\..\silk\dec_API.c" />
    <ClCompile Include="..\..\silk\encode_indices.c" />
    <ClCompile Include="..\..\silk\encode_pulses.c" />
    <ClCompile Include="..\..\silk\enc_API.c" />
    <ClCompile Include="..\..\silk\gain_quant.c" />
    <ClCompile Include="..\..\silk\HP_variable_cutoff.c" />
    <ClCompile Include="..\..\silk\init_decoder.c" />
    <ClCompile Include="..\..\silk\init_encoder.c" />
    <ClCompile Include="..\..\silk\inner_prod_aligned.c" />
    <ClCompile Include="..\..\silk\interpolate.c" />
    <ClCompile Include="..\..\silk\lin2log.c" />
    <ClCompile Include="..\..\silk\log2lin.c" />
    <ClCompile Include="..\..\silk\LPC_analysis_filter.c" />
    <ClCompile Include="..\..\silk\LPC_fit.c" />
    <ClCompile Include="..\..\silk\LPC_inv_pred_gain.c" />
    <ClCompile Include="..\..\silk\LP_variable_cutoff.c" />
    <ClCompile Include="..\..\silk\NLSF2A.c" />
    <ClCompile Include="..\..\silk\NLSF_decode.c" />
    <ClCompile Include="..\..\silk\NLSF_del_dec_quant.c" />
    <ClCompile Include="..\..\silk\NLSF_encode.c" />
    <ClCompile Include="..\..\silk\NLSF_stabilize.c" />
    <ClCompile Include="..\..\silk\NLSF_unpack.c" />
    <ClCompile Include="..\..\silk\NLSF_VQ.c" />
    <ClCompile Include="..\..\silk\NLSF_VQ_weights_laroia.c" />
    <ClCompile Include="..\..\silk\NSQ.c" />
    <ClCompile Include="..\..\silk\NSQ_del_dec.c" />
    <ClCompile Include="..\..\silk\pitch_est_tables.c" />
    <ClCompile Include="..\..\silk\PLC.c" />
    <ClCompile Include="..\..\silk\process_NLSFs.c" />
    <ClCompile Include="..\..\silk\quant_LTP_gains.c" />
    <ClCompile Include="..\..\silk\resampler.c" />
    <ClCompile Include="..\..\silk\resampler_down2.c" />
    <ClCompile Include="..\..\silk\resampler_down2_3.c" />
    <ClCompile Include="..\..\silk\resampler_private_AR2.c" />
    <ClCompile Include="..\..\silk\resampler_private_down_FIR.c" />
    <ClCompile Include="..\..\silk\resampler_private_IIR_FIR.c" />
    <ClCompile Include="..\..\silk\resampler_private_up2_HQ.c" />
    <ClCompile Include="..\..\silk\resampler_rom.c" />
    <ClCompile Include="..\..\silk\shell_coder.c" />
    <ClCompile Include="..\..\silk\sigm_Q15.c" />
    <ClCompile Include="..\..\silk\sort.c" />
    <ClCompile Include="..\..\silk\stereo_decode_pred.c" />
    <ClCompile Include="..\..\silk\stereo_encode_pred.c" />
    <ClCompile Include="..\..\silk\stereo_find_predictor.c" />
    <ClCompile Include="..\..\silk\stereo_LR_to_MS.c" />
    <ClCompile Include="..\..\silk\stereo_MS_to_LR.c" />
    <ClCompile Include="..\..\silk\stereo_quant_pred.c" />
    <ClCompile Include="..\..\silk\sum_sqr_shift.c" />
    <ClCompile Include="..\..\silk\tables_gain.c" />
    <ClCompile Include="..\..\silk\tables_LTP.c" />
    <ClCompile Include="..\..\silk\tables_NLSF_CB_NB_MB.c" />
    <ClCompile Include="..\..\silk\tables_NLSF_CB_WB.c" />
    <ClCompile Include="..\..\silk\tables_other.c" />
    <ClCompile Include="..\..\silk\tables_pitch_lag.c" />
    <ClCompile Include="..\..\silk\tables_pulses_per_block.c" />
    <ClCompile Include="..\..\silk\table_LSF_cos.c" />
    <ClCompile Include="..\..\silk\VAD.c" />
    <ClCompile Include="..\..\silk\VQ_WMat_EC.c" />
    <ClCompile Include="..\..\silk\x86\NSQ_del_dec_sse.c" />
    <ClCompile Include="..\..\silk\x86\NSQ_sse.c" />
    <ClCompile Include="..\..\silk\x86\VAD_sse.c" />
    <ClCompile Include="..\..\silk\x86\VQ_WMat_EC_sse.c" />
    <ClCompile Include="..\..\silk\x86\x86_silk_map.c" />
    <ClCompile Include="..\..\src\analysis.c" />
    <ClCompile Include="..\..\src\mlp.c" />
    <ClCompile Include="..\..\src\mlp_data.c" />
    <ClCompile Include="..\..\src\opus.c" />
    <ClCompile Include="..\..\src\opus_compare.c">
      <DisableSpecificWarnings>4244;%(DisableSpecificWarnings)</DisableSpecificWarnings>
    </ClCompile>
    <ClCompile Include="..\..\src\opus_decoder.c" />
    <ClCompile Include="..\..\src\opus_encoder.c" />
    <ClCompile Include="..\..\src\opus_multistream.c" />
    <ClCompile Include="..\..\src\opus_multistream_decoder.c" />
    <ClCompile Include="..\..\src\opus_multistream_encoder.c" />
    <ClCompile Include="..\..\src\repacketizer.c" />
  </ItemGroup>
  <Choose>
    <When Condition="'$(Configuration)'=='DebugDLL_fixed' or '$(Configuration)'=='ReleaseDLL_fixed' or $(PreprocessorDefinitions.Contains('FIXED_POINT'))">
      <ItemGroup>
        <ClCompile Include="..\..\silk\fixed\*.c">
          <ExcludedFromBuild>false</ExcludedFromBuild>
        </ClCompile>
        <ClCompile Include="..\..\silk\fixed\x86\*.c">
          <ExcludedFromBuild>false</ExcludedFromBuild>
        </ClCompile>
        <ClCompile Include="..\..\silk\float\*.c">
          <ExcludedFromBuild>true</ExcludedFromBuild>
        </ClCompile>
      </ItemGroup>
    </When>
    <Otherwise>
      <ItemGroup>
        <ClCompile Include="..\..\silk\fixed\*.c">
          <ExcludedFromBuild>true</ExcludedFromBuild>
        </ClCompile>
        <ClCompile Include="..\..\silk\fixed\x86\*.c">
          <ExcludedFromBuild>true</ExcludedFromBuild>
        </ClCompile>
        <ClCompile Include="..\..\silk\float\*.c">
          <ExcludedFromBuild>false</ExcludedFromBuild>
        </ClCompile>
      </ItemGroup>
    </Otherwise>
  </Choose>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>