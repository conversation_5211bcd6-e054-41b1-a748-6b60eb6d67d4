package com.bes.sdk.control;

import androidx.annotation.Nullable;

import com.bes.sdk.device.HmDevice;
import com.bes.sdk.message.BaseMessage;
import com.bes.sdk.message.EQSettings;
import com.bes.sdk.message.EQStatusMessage;
import com.bes.sdk.message.EQPayload;
import com.bes.sdk.utils.MessageID;
import com.bes.sdk.utils.StatusCode;

import java.util.ArrayList;
import java.util.LinkedList;

/**
 * EQControl API.
 */
public interface EQControl extends BaseControl
{
    /**
     * Get all EQ settings.
     * @param listener
     * {@link DeviceListener#onRead(HmDevice device, StatusCode code, BaseMessage message)} callback will indicate the operation result.
     * <p>parameter code in deviceCallback, {@link StatusCode#STATUS_SUCCESS} of means success, others for fail with reasons.
     * <p>parameter message in deviceCallback is an object of {@link EQStatusMessage}, which
     * {@link EQStatusMessage#getMsgID()} is {@link MessageID#EQ_STATUS},
     * {@link EQStatusMessage#getMsgContent()} is current running EQ settings, type is {@link LinkedList<EQSettings>}
     * <p>
     * {@link EQSettings#getEqPayload()} is null for preset EQ settings, {@link EQPayload} for customized EQ.
     */
    void getAllEQSettings(HmDevice device, @Nullable DeviceListener listener);

    /**
     * Get EQ index that currently turned on.
     * @param listener
     * {@link DeviceListener#onRead(HmDevice device, StatusCode code, BaseMessage message)} callback will indicate the operation result.
     * <p>parameter code in deviceCallback, {@link StatusCode#STATUS_SUCCESS} of means success, others for fail with reasons.
     * <p>parameter message in deviceCallback is an object of {@link EQStatusMessage}, which
     * {@link EQStatusMessage#getMsgID()} is {@link MessageID#RUNNING_EQ_STATUS},
     * {@link EQStatusMessage#getMsgContent()} is current running EQ setting, type is {@link LinkedList<EQSettings>}
     * <p>
     * {@link EQSettings#getEqPayload()} is null for preset EQ settings, {@link EQPayload} for customized EQ.
     */
    void getRunningEQSetting(HmDevice device, @Nullable DeviceListener listener);

    /**
     * Set enable/disable EQ.
     *
     * @param categoryId preset ID scope in [1 ~ 100], customized EQ ID start with 101.
     * <li> -1 - turn off EQ (no any EQ enabled),
     * <li> 0 - default EQ,
     * <li> 1 - Preset EQ 1,
     * <li> 2 - Preset EQ 2,
     * <li> 3 - Preset EQ 3,
     * <li>...
     * <li>101 - Customized EQ 1 (DJ-Signature),
     * <li>102 - Customized EQ 2 (Personi-Fi),
     * <li>103 - Customized EQ 3 (My-EQ),
     * <li>...
     * <li>200 - Combined EQ. Means combined several EQ available on device together as output.
     *
     * @param payload might be null for preset EQ.
     *
     * @param saveOrNot true save current payload, otherwise only set current payload.
     *
     * @param listener
     * {@link DeviceListener#onChanged(HmDevice, StatusCode code, BaseMessage message)} callback will indicate the operation result.
     * <p>parameter code in deviceCallback, {@link StatusCode#STATUS_SUCCESS} of means success, others for fail with reasons.
     * <p>parameter message in deviceCallback is an object of {@link EQStatusMessage}, which
     * {@link EQStatusMessage#getMsgID()} is {@link MessageID#EQ_STATUS},
     * {@link EQStatusMessage#getMsgContent()} is new changed EQ settings, type is {@link LinkedList<EQSettings>}
     * <p>
     * {@link EQSettings#getEqPayload()} is null for preset EQ settings, {@link EQPayload} for customized EQ.
     */
    void setEQSetting(HmDevice device, int categoryId, EQPayload payload, boolean saveOrNot, @Nullable DeviceListener listener);

    /**
     * OPTIONAL
     * Set combined EQ settings.
     * Enable combined EQ, indexArray are the EQ indexes that should be combined.
     *
     * @param indexArray combined EQ index
     *
     * @param listener
     * {@link DeviceListener#onChanged(HmDevice device, StatusCode code, BaseMessage message)} callback will indicate the operation result.
     * <p>parameter code in deviceCallback, {@link StatusCode#STATUS_SUCCESS} of means success, others for fail with reasons.
     * <p>parameter message in deviceCallback is an object of {@link BaseMessage}, which
     * {@link BaseMessage#getMsgID()} is {@link MessageID#COMBINED_EQ_STATUS},
     * {@link BaseMessage#getMsgContent()} is new changed combined EQ, type is {@link ArrayList<Integer>}
     */
    void setCombinedEQ(HmDevice device, int[] indexArray, @Nullable DeviceListener listener);

    /**
     * OPTIONAL:
     * Get combined EQ settings,
     * If Combined EQ enabled, get the EQ indexes that combined.
     *
     * @param listener
     * {@link DeviceListener#onRead (HmDevice device, StatusCode code, BaseMessage message)} callback will indicate the operation result.
     * <p>parameter code in deviceCallback, {@link StatusCode#STATUS_SUCCESS} of means success, others for fail with reasons.
     * <p>parameter message in deviceCallback is an object of {@link BaseMessage}, which
     * {@link BaseMessage#getMsgID()} is {@link MessageID#COMBINED_EQ_STATUS},
     * {@link BaseMessage#getMsgContent()} is current combined EQ, type is {@link ArrayList<Integer>}
     */
    void getCombinedEQIndex(HmDevice device, @Nullable DeviceListener listener);
}
