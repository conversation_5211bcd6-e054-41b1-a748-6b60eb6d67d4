package com.bes.bessdk.utils.sha;


import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;


public class Sha256 {
    public static byte[] getSHA256(byte[] bytes){
        MessageDigest messageDigest;
        byte[] encodestr = new byte[]{};
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(bytes);
            encodestr = messageDigest.digest();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return encodestr;
    }



}
