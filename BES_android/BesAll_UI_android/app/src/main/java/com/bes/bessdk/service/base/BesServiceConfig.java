package com.bes.bessdk.service.base;

import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.DeviceProtocol;

import java.io.Serializable;
import java.util.UUID;

public class BesServiceConfig implements Serializable {
    private DeviceProtocol deviceProtocol;
    private UUID serviceUUID;
    private UUID characteristicsUUID;
    private UUID characteristicsTX;
    private UUID descriptorUUID;
    private HmDevice device;
    private Boolean totaConnect = false;
    private Boolean useTotaV2 = false;

    private Integer USER_FLAG = 0;
    private Integer curUser;
    private Integer curUpgateType = 1;
    private Boolean curAckType = false;//1:without ack 2:with ack
    private Integer imageSideSelection = 0;//0：stereo 1：left 2:right 3:both same 4:both diff
    private Boolean isWithoutResponse = false;

    private Boolean isUserOtaService = false;

    public void setDeviceProtocol(DeviceProtocol deviceProtocol) {
        this.deviceProtocol = deviceProtocol;
    }

    public void setServiceUUID(UUID serviceUUID) {
        this.serviceUUID = serviceUUID;
    }

    public void setCharacteristicsUUID(UUID characteristicsUUID) {
        this.characteristicsUUID = characteristicsUUID;
    }

    public void setCharacteristicsTX(UUID characteristicsTX) {
        this.characteristicsTX = characteristicsTX;
    }

    public void setDescriptorUUID(UUID descriptorUUID) {
        this.descriptorUUID = descriptorUUID;
    }

    public void setDevice(HmDevice device) {
        this.device = device;
    }

    public void setTotaConnect(Boolean totaConnect) {
        this.totaConnect = totaConnect;
    }

    public void setUseTotaV2(Boolean useTotaV2) {
        this.useTotaV2 = useTotaV2;
    }

    public void setUSER_FLAG(Integer user_flag) {
        this.USER_FLAG = user_flag;
    }
    public void setCurUser(Integer curUser) {
        this.curUser = curUser;
    }
    public void setCurUpgateType(Integer curUpgateType) {
        this.curUpgateType = curUpgateType;
    }
    public void setCurAckType(Boolean curAckType) {
        this.curAckType = curAckType;
    }
    public void setImageSideSelection(Integer imageSideSelection) {
        this.imageSideSelection = imageSideSelection;
    }
    public void setIsWithoutResponse(Boolean withoutResponse) {
        this.isWithoutResponse = withoutResponse;
    }

    public void setIsUserOtaService(Boolean userOtaService) {
        isUserOtaService = userOtaService;
    }

    public DeviceProtocol getDeviceProtocol() {
        return deviceProtocol;
    }

    public UUID getServiceUUID() {
        return serviceUUID;
    }

    public UUID getCharacteristicsUUID() {
        return characteristicsUUID;
    }

    public UUID getCharacteristicsTX() {
        return characteristicsTX;
    }

    public UUID getDescriptorUUID() {
        return descriptorUUID;
    }

    public HmDevice getDevice() {
        return device;
    }

    public Boolean getTotaConnect() {
        return totaConnect;
    }

    public Boolean getUseTotaV2() {
        return useTotaV2;
    }

    public Integer getUSER_FLAG() {
        return USER_FLAG;
    }
    public Integer getCurUser() {
        return curUser;
    }
    public Integer getCurUpgateType() {
        return curUpgateType;
    }
    public Boolean getCurAckType() {
        return curAckType;
    }
    public Integer getImageSideSelection() {
        return imageSideSelection;
    }
    public Boolean getIsWithoutResponse() {
        return isWithoutResponse;
    }

    public Boolean getIsUserOtaService() {
        return isUserOtaService;
    }
}
