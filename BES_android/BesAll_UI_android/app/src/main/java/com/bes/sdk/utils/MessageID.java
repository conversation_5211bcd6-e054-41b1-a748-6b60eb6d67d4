package com.bes.sdk.utils;

/**
 * Define the enum of messages that used to control device.
 *
 */
public enum MessageID {
    UNKNOWN(-1, "UnknownCommand"),

    /**
     * Device control request messages.
     */
    DEVICE_LIST_INFO    (1001,      "DEVICE_LIST_INFO"),
    DEVICE_NAME         (1002,      "DEVICE_NAME"),
    ANC_STATUS          (1003,      "ANC_STATUS"),
    AUTO_PAUSE          (1004,      "AUTO_PAUSE"),
    AUTO_POWER_OFF      (1005,      "AUTO_POWER_OFF"),
    MULTI_AI_STATUS     (1006,      "MULTI_AI_STATUS"),
    A2DP_STATUS         (1007,      "A2DP_STATUS"),
    FIND_ME_STATUS      (1008,      "FIND_ME_STATUS"),
    TWS_STATUS          (1009,      "TWS_STATUS"),
    SEALING_STATUS      (1010,      "SEALING_STATUS"),
    GESTURE_STATUS      (1011,      "GESTURE_STATUS"),
    <PERSON>Y<PERSON>_CRC_STATUS     (1012,      "SYNC_CRC_STATUS"),
    DEVICE_INFO         (1013,      "DEVICE_INFO"),
    BATTERY_STATUS      (1014,      "BATTERY_STATUS"),
    IN_EAR_STATUS       (1015,      "IN_EAR_STATUS"),
    SMART_SWITCH_STATUS (1016,      "SMART_SWITCH_STATUS"),

    /**
     * OTA control request messages. Start from 2001.
     */
    OTA_STATUS          (2001,      "OTA_STATUS"),

    /**
     * EQ control request messages. Start from 3001.
     */
    EQ_STATUS           (3001,      "EQ_STATUS"),
    RUNNING_EQ_STATUS   (3002,      "RUNNING_EQ_STATUS"),
    COMBINED_EQ_STATUS  (3003,      "COMBINED_EQ_STATUS");

    private int mValue;
    private String mName;

    MessageID(int value, String name) {
        mValue = value;
        mName = name;
    }

    public int getValue() { return mValue; }

    public String getCmdName() { return mName; }
}