package com.bes.sdk.message;

import java.io.Serializable;
import java.util.Objects;

/**
 * Smart Switch Info
 */
public class SmartSwitchInfo implements Serializable {

    /**
     * Invalid value -1.
     */
    public static final int INVALID = -1;

    private int aac;

    private int sbc;

    private int latency = -1;

    private int latency2 = -1;

    public SmartSwitchInfo() {}

    public SmartSwitchInfo(int aac, int sbc, int latency, int latency2) {
        this.aac = aac;
        this.sbc = sbc;
        this.latency = latency;
        this.latency2 = latency2;
    }

    /**
     * Get AAC value
     */
    public int getAac() {
        return aac;
    }

    public void setAac(int aac) {
        this.aac = aac;
    }

    /**
     * Get SBC value
     */
    public int getSbc() {
        return sbc;
    }

    public void setSbc(int sbc) {
        this.sbc = sbc;
    }

    /**
     * Get latency
     * @return
     */
    public int getLatency() {
        return latency;
    }

    public void setLatency(int latency) {
        this.latency = latency;
    }

    /**
     * Get the 2nd latency, some device doesn't support 2nd latency, then return {@link #INVALID}.
     * @return
     */
    public int getLatency2() {
        return latency2;
    }

    public void setLatency2(int latency2) {
        this.latency2 = latency2;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SmartSwitchInfo that = (SmartSwitchInfo) o;
        return aac == that.aac &&
                sbc == that.sbc &&
                latency == that.latency &&
                latency2 == that.latency2;
    }

    @Override
    public int hashCode() {
        return Objects.hash(aac, sbc, latency, latency2);
    }

    @Override
    public String toString() {
        return "SmartSwitchInfo{" +
                "aac=" + aac +
                ", sbc=" + sbc +
                ", latency=" + latency +
                ", latency2=" + latency2 +
                '}';
    }
}
