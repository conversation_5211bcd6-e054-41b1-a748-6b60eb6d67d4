package com.bes.sdk.connect;

import android.bluetooth.BluetoothDevice;

import com.bes.sdk.message.BaseMessage;
import com.bes.sdk.utils.DeviceProtocol;
import com.bes.sdk.device.HmDevice;

import java.util.List;

/**
 * Device connector. Used to maintain a connection with device.
 * It can be either SPP connection, BLE connection, or even Wi-Fi connection.
 */
public interface DeviceConnector
{
    /**
     * Get all supported protocols.
     *
     * @return
     */
    List<DeviceProtocol> getSupportedProtocols();

    /**
     * Check if specific device protocol is supported or not. One device connector implementation can support multiple protocols.
     */
    boolean isProtocolSupported(DeviceProtocol deviceProtocol);

    /**
     * Connect to the device.
     *
     * @return
     */
    void connect(HmDevice device);

    /**
     * Connect to the device and register listener..
     *
     * @return
     */
    void connect(HmDevice device, ConnectionListener connectionListener);

    /**
     * Disconnect from the device.
     *
     * @return
     */
    void disconnect(HmDevice device);

    /**
     * Register a listener.
     *
     * @param connectionListener
     */
    void registerConnectionListener(ConnectionListener connectionListener);

    /**
     * Unregister a listener.
     *
     * @param connectionListener
     */
    void unregisterConnectionListener(ConnectionListener connectionListener);

    public interface ConnectionListener
    {
        /**
         * Callback when device connection status changed.
         * @param device
         * @param status
         * @param protocol
         */
        void onStatusChanged(HmDevice device, int status, DeviceProtocol protocol);

        /**
         * Callback when message received from device.
         * @param deviceMessage
         */
        void onDataReceived(BaseMessage deviceMessage);

        void notifyWrite(int state);
    }
}
