package com.bes.sdk.utils;

import android.text.TextUtils;

/**
 * Enum of device roles.
 */
public enum DeviceRole
{
    // General roles.
    UNKNOWN         (-1,    "Unknown"),
    NORMAL          (0,     "Normal"),
    MASTER          (1,     "Master"),
    SLAVE           (2,     "Slave"),

    // Roles for TWS.
    TWS_MASTER      (101, "TWS_MASTER"),
    TWS_SLAVE       (102, "TWS_SLAVE");

    private int mValue;
    private String mName;

    DeviceRole(int value, String name)
    {
        mValue = value;
        mName = name;
    }

    public static DeviceRole getRole(int role)
    {
        switch (role) {
            case 0:
                return NORMAL;
            case 1:
                return MASTER;
            case 2:
                return SLAVE;
            case 101:
                return TWS_MASTER;
            case 102:
                return TWS_SLAVE;
        }
        return UNKNOWN;
    }

    public static DeviceRole getRole(String role) {
        if (TextUtils.isEmpty(role)){
            return UNKNOWN;
        }
        switch (role) {
            case "00":
            case "0":
                return NORMAL;
            case "01":
            case "1":
                return MASTER;
            case "02":
            case "2":
            case "10":
                return SLAVE;
        }
        return NORMAL;
    }

    public int getValue() {
        return mValue;
    }

    public String getName() {
        return mName;
    }
}
