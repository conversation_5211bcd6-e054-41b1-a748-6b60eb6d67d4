package com.bes.bessdk.utils.sha;

public class besFunc {


    private byte[] key = new byte[16];


    private byte generate_random_algorithm (int id, byte a, byte b) {
        int op = id % 6;
        switch (op) {
            case 0:
                return (byte) (a + b);
            case 1:
                return (byte) (a * b);
            case 2:
                return (byte) (a & b);
            case 3:
                return (byte) (a | b);
            case 4:
                return (byte) (a ^ b);
            case 5:
                return (byte) (a*a + b*b);
            default:
                return 0x00;
        }
    }

    public byte[] func1 (byte[] a, byte[] b) {
        if (a.length > b.length) {
            byte[] byteB = new byte[a.length];
            System.arraycopy(b, 0, byteB, 0, b.length);
            b = byteB;
        } else if(a.length < b.length) {
            byte[] byteA = new byte[b.length];
            System.arraycopy(a, 0, byteA, 0, a.length);
            a = byteA;
        }

        byte[] rand_key = new byte[a.length];
        for (int i = 0; i < a.length; i ++) {
            rand_key[i] = generate_random_algorithm(i, a[i], b[i]);
        }
        return rand_key;
    }

    public byte[] func (byte[] hash_key) {
        for (int i = 0; i < 16; i ++) {
            key[i] = hash_key[2 * i];
        }
        return key;
    }


}


