package com.bes.sdk.message;

import java.io.Serializable;

/**
 * Anc status definition
 */
public class EQSettings implements Serializable
{
    private int categoryId;

    private int status;

    /**
     * gain: for specified filter, double value.
     */
    private EQPayload eqPayload;

    /**
     * EQ category number. Preset ID scope in [1 ~ 100], customized EQ ID start with 101.
     * <li> -1 - turn off EQ (no any EQ enabled),
     * <li> 0 - default EQ,
     * <li> 1 - Preset EQ 1,
     * <li> 2 - Preset EQ 2,
     * <li> 3 - Preset EQ 3,
     * <li>...
     * <li>101 - Customized EQ 1 (DJ-Signature),
     * <li>102 - Customized EQ 2 (Personi-Fi),
     * <li>103 - Customized EQ 3 (My-EQ),
     * <li>...
     * <li>200 - Combined EQ. Means combined several EQ available on device together as output.
     */
    public int getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    /**
     * status:
     * <li>0 for OFF;
     * <li>1 for ON;
     */
    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public EQPayload getEqPayload() {
        return eqPayload;
    }

    public void setEqPayload(EQPayload eqPayload) {
        this.eqPayload = eqPayload;
    }

    @Override
    public String toString() {
        return "\nEQSettings{" +
                "\ncategoryId=" + categoryId +
                "\nstatus=" + status +
                "\neqPayload=" + eqPayload +
                '}';
    }
}
