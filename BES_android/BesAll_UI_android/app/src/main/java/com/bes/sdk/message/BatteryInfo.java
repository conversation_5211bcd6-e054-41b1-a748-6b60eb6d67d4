package com.bes.sdk.message;


import java.io.Serializable;

/**
 * Battery info
 */
public class BatteryInfo implements Serializable
{
    public static final int INVALID_LEVEL = -1;

    private boolean masterCharging;

    private int masterLevel;

    private boolean slaveCharging;

    private int slaveLevel;

    private boolean boxCharging;

    private int boxLevel;

    private int masterVoltage;

    private int slaveVoltage;

    public boolean isMasterCharging() {
        return masterCharging;
    }

    public void setMasterCharging(boolean masterCharging) {
        this.masterCharging = masterCharging;
    }

    /**
     * Get master battery level.
     * @return master battery level, {@link #INVALID_LEVEL} means invalid value.
     */
    public int getMasterLevel() {
        return masterLevel;
    }

    public void setMasterLevel(int masterLevel) {
        this.masterLevel = masterLevel;
    }

    public boolean isSlaveCharging() {
        return slaveCharging;
    }

    public void setSlaveCharging(boolean slaveCharging) {
        this.slaveCharging = slaveCharging;
    }

    /**
     * Get slave level
     * @return slave battery level, {@link #INVALID_LEVEL} means invalid value.
     */
    public int getSlaveLevel() {
        return slaveLevel;
    }

    public void setSlaveLevel(int slaveLevel) {
        this.slaveLevel = slaveLevel;
    }

    public boolean isBoxCharging() {
        return boxCharging;
    }

    public void setBoxCharging(boolean boxCharging) {
        this.boxCharging = boxCharging;
    }

    /**
     * Box battery level
     * @return box battery level, {@link #INVALID_LEVEL} means invalid value.
     */
    public int getBoxLevel() {
        return boxLevel;
    }

    public void setBoxLevel(int boxLevel) {
        this.boxLevel = boxLevel;
    }

    public int getMasterVoltage() {
        return masterVoltage;
    }

    public void setMasterVoltage(int masterVoltage) {
        this.masterVoltage = masterVoltage;
    }

    public int getSlaveVoltage() {
        return slaveVoltage;
    }

    public void setSlaveVoltage(int slaveVoltage) {
        this.slaveVoltage = slaveVoltage;
    }

    @Override
    public String toString() {
        return "BatteryInfo{" +
                "\nmasterCharging=" + masterCharging +
                "\nmasterLevel=" + masterLevel +
                "\nslaveCharging=" + slaveCharging +
                "\nslaveLevel=" + slaveLevel +
                "\nboxCharging=" + boxCharging +
                "\nboxLevel=" + boxLevel +
                "\nmasterVoltage=" + masterVoltage +
                "\nslaveVoltage=" + slaveVoltage +
                '}';
    }
}
