package com.bes.sdk.message;

import java.io.Serializable;

/**
 * Anc status definition
 */
public class GestureInfo implements Serializable
{
    // Gestures IDs
    public static final int SINGLE_TAP_LEFT = 0x01;
    public static final int SINGLE_TAP_RIGHT = 0x02;
    public static final int DOUBLE_TAP_LEFT = 0x03;
    public static final int DOUBLE_TAP_RIGHT = 0x04;
    public static final int LONG_PRESS_LEFT = 0x05;
    public static final int LONG_PRESS_RIGHT = 0x06;
    public static final int TRIPLE_TAP_LEFT = 0x07;
    public static final int TRIPLE_TAP_RIGHT = 0x08;

    public static final int SWIPE_FORWARD_LEFT = 0x11;
    public static final int SWIPE_FORWARD_RIGHT = 0x12;
    public static final int SWIPE_BACKWARD_LEFT = 0x13;
    public static final int SWIPE_BACKWARD_RIGHT = 0x14;
    public static final int SWIPE_UP_LEFT = 0x15;
    public static final int SWIPE_UP_RIGHT = 0x16;
    public static final int SWIPE_DOWN_LEFT = 0x17;
    public static final int SWIPE_DOWN_RIGHT = 0x18;

    public static final int SINGLE_TAP_HOLD_LEFT = 0x31;
    public static final int SINGLE_TAP_HOLD_RIGHT = 0x32;
    public static final int DOUBLE_TAP_HOLD_LEFT = 0x33;
    public static final int DOUBLE_TAP_HOLD_RIGHT = 0x34;
    public static final int RELEASE_TAP_HOLD_LEFT = 0x35;
    public static final int RELEASE_TAP_HOLD_RIGHT = 0x36;

    public static final int LEFT_ALL = 0xFD;
    public static final int RIGHT_ALL = 0xFE;
    public static final int ALL = 0xFF;

    // Action IDs
    // No any action, always used to reset gesture's action.
    public static final int ACTION_NONE = 0x0;
    public static final int VOLUME_UP = 0x01;
    public static final int VOLUME_DOWN =0x02;
    public static final int ANC = 0x03;
    public static final int AMBIENT_AWARE = 0x04;
    public static final int TALK_THROUGH = 0x05;
    public static final int NEXT_TRACK = 0x06;
    public static final int PREVIOUS_TRACK = 0x07;
    public static final int PLAY_PAUSE = 0x08;
    // switch function between ANC / AMBIENT AWARE / OFF
    public static final int ANC_AMBIENT_AWARE_OFF = 0x09;

    public static final int CANCEL_DEFAULT_ASSISTANT = 0xa0;
    public static final int TALK_TO_DEFAULT_ASSISTANT = 0xa1;
    public static final int CANCEL_GOOGLE_ASSISTANT = 0xa2;
    public static final int GOOGLE_ASSISTANT_NOTIFICATION_CALL_OUT = 0xa3;
    public static final int TALK_TO_GOOGLE_ASSISTANT = 0xa4;
    public static final int CANCEL_AMAZON_ALEXA = 0xa5;
    public static final int TALK_TO_AMAZON_ALEXA = 0xa6;
    public static final int CANCEL_TENCENT_XIAOWEI = 0xa7;
    public static final int TALK_TO_TENCENT_XIAOWEI = 0xa8;

    public GestureInfo() {
    }

    public GestureInfo(int gestureId, int actionId) {
        this.gestureId = gestureId;
        this.actionId = actionId;
    }

    private int gestureId;

    private int actionId;

    /**
     * Get gestureId
     * <li>0x01 - Single click on left {@link #SINGLE_TAP_LEFT};
     * <li>0x02 - Single click on right {@link #SINGLE_TAP_RIGHT};
     * <li>0x03 - Double click on left {@link #DOUBLE_TAP_LEFT};
     * <li>0x04 - Double click on right {@link #DOUBLE_TAP_RIGHT};
     * <li>0x05 - Long press on left {@link #LONG_PRESS_LEFT};
     * <li>0x06 - Long press on right {@link #LONG_PRESS_RIGHT};
     * <li>0x07 - Triple click on left {@link #TRIPLE_TAP_LEFT};
     * <li>0x08 - Triple click on right {@link #TRIPLE_TAP_RIGHT};
     *
     * <li>0x11 - Swipe forward on left {@link #SWIPE_FORWARD_LEFT};
     * <li>0x12 - Swipe forward on right {@link #SWIPE_FORWARD_RIGHT};
     * <li>0x13 - Swipe backward on left {@link #SWIPE_BACKWARD_LEFT};
     * <li>0x14 - Swipe backward on right {@link #SWIPE_BACKWARD_RIGHT};
     * <li>0x15 - Swipe up on left {@link #SWIPE_UP_LEFT};
     * <li>0x16 - Swipe up on right {@link #SWIPE_UP_RIGHT};
     * <li>0x17 - Swipe down on left {@link #SWIPE_DOWN_LEFT};
     * <li>0x18 - Swipe down on right {@link #SWIPE_DOWN_RIGHT};
     *
     * <li>0x31 - Single click and hold on left {@link #SINGLE_TAP_HOLD_LEFT};
     * <li>0x32 - Single click and hold on right {@link #SINGLE_TAP_HOLD_RIGHT};
     * <li>0x33 - Double click and hold on left {@link #DOUBLE_TAP_HOLD_LEFT};
     * <li>0x34 - Double click and hold on right {@link #DOUBLE_TAP_HOLD_RIGHT};
     *
     * <li>0xFD - {@link #LEFT_ALL} always used to reset actions on left
     * <li>0xFE - {@link #RIGHT_ALL} always used to reset actions on right
     * <li>0xFF - {@link #LEFT_ALL} always used to reset actions both on left and right
     * <li> ...
     * */
    public int getGestureId() {
        return gestureId;
    }

    public void setGestureId(int gestureId) {
        this.gestureId = gestureId;
    }

    /**
     * actionId
     * <li>0x00 - no any action {@link #ACTION_NONE};
     * <li>0x01 - Volume Up {@link #VOLUME_UP};
     * <li>0x02 - Volume Down {@link #VOLUME_DOWN};
     * <li>0x03 - ANC {@link #ANC};
     * <li>0x04 - Ambient Aware {@link #AMBIENT_AWARE};
     * <li>0x05 - Talk Through {@link #TALK_THROUGH};
     * <li>0x06 - Next Track {@link #NEXT_TRACK};
     * <li>0x07 - Previous Track {@link #PREVIOUS_TRACK};
     * <li>0x08 - Play/Pause {@link #PLAY_PAUSE};
     * <li>0x09 - ANC(default)/Ambient Aware On/Off {@link #ANC_AMBIENT_AWARE_OFF};
     *
     * <li>0xa0 - Cancel Default Assistant {@link #CANCEL_DEFAULT_ASSISTANT}
     * <li>0xa1 - Talk to Default Assistant {@link #TALK_THROUGH}
     * <li>0xa2 - Cancel Google Assistant {@link #CANCEL_GOOGLE_ASSISTANT}
     * <li>0xa3 - Google Assistant notification call-out {@link #GOOGLE_ASSISTANT_NOTIFICATION_CALL_OUT}
     * <li>0xa4 - Talk to Google Assistant {@link #TALK_TO_GOOGLE_ASSISTANT}
     * <li>0xa5 - Cancel Amazon Assistant {@link #CANCEL_AMAZON_ALEXA}
     * <li>0xa6 - Talk to Amazon Alexa {@link #TALK_TO_AMAZON_ALEXA}
     * <li>0xa7 - Cancel Tencent XiaoWei {@link #CANCEL_TENCENT_XIAOWEI}
     * <li>0xa8 - Talk to Tencent XiaoWei {@link #TALK_TO_TENCENT_XIAOWEI}
     * <li>...
     * */
    public int getActionId() {
        return actionId;
    }

    public void setActionId(int actionId) {
        this.actionId = actionId;
    }

    @Override
    public String toString() {
        return "GestureInfo{" +
                "gestureId=" + gestureId +
                ", actionId=" + actionId +
                '}';
    }
}
