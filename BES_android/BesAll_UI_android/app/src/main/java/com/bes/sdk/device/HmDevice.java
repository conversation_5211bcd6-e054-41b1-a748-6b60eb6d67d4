package com.bes.sdk.device;

import com.bes.sdk.message.BatteryInfo;
import com.bes.sdk.utils.AudioChannel;
import com.bes.sdk.utils.DeviceProtocol;
import com.bes.sdk.utils.DeviceRole;

import java.io.Serializable;
import java.util.Objects;

public class HmDevice implements Serializable
{
    private String devicePid;

    private String deviceVid;

    private String deviceMid;

    private String firmwareVer;

    private String deviceMAC;

    private String bleAddress;

    private int rssi;

    /**
     * Default device name.
     */
    private String deviceName;

    private BatteryInfo batteryInfo;

    private DeviceRole role;

    private boolean isConnectable;

    private long scannedTimestamp;

    private String crc;

    private DeviceProtocol preferredProtocol;

    private String deviceUid;

    private AudioChannel channel;

    /**
     * @see #getDeviceVid()
     * @param deviceVid
     */
    public void setDeviceVid(String deviceVid) {
        this.deviceVid = deviceVid;
    }

    /**
     * @see #getDevicePid()
     * @param devicePid
     */
    public void setDevicePid(String devicePid) {
        this.devicePid = devicePid;
    }

    /**
     * Set device model ID(color ID)
     * @param deviceMid, value can be:
     * <li>0 : Black</li>
     * <li>1 : White</li>
     * <li>2 : Blue</li>
     * <li>3 : Red</li>
     * <li>4 : Green</li>
     * <li>5 : Purple</li>
     * <li>6 : GoldSilver</li>
     * <li>7 : Silver</li>
     * <li>8 : Gray</li>
     * <li>9 : Beige</li>
     * <li>a : Pink</li>
     */
    public void setDeviceMid(String deviceMid) {
        this.deviceMid = deviceMid;
    }

    /**
     * @see #getDeviceMAC()
     * @param deviceMAC
     */
    public void setDeviceMAC(String deviceMAC) {
        this.deviceMAC = deviceMAC;
    }

    public void setBleAddress(String bleAddress) {
        this.bleAddress = bleAddress;
    }


    public void setRssi(int rssi) {
        this.rssi = rssi;
    }

    /**
     * Default device name.
     * @return
     */
    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    /**
     * @see #getBatteryInfo()
     * @param batteryInfo
     */
    public void setBatteryInfo(BatteryInfo batteryInfo) {
        this.batteryInfo = batteryInfo;
    }

    /**
     * @see #getRole()
     * @param role
     */
    public void setRole(DeviceRole role) {
        this.role = role;
    }

    /**
     * @deprecated
     * @see #isConnectable()
     * @param connectable
     */
    public void setConnectable(boolean connectable) {
        isConnectable = connectable;
    }

    /**
     * @see #getScannedTimestamp()
     * @param scannedTimestamp
     */
    public void setScannedTimestamp(long scannedTimestamp) {
        this.scannedTimestamp = scannedTimestamp;
    }

    /**
     * @see #getCrc()
     * @param crc
     */
    public void setCrc(String crc) {
        this.crc = crc;
    }

    /**
     * @see #getPreferredProtocol()
     * @param preferredProtocol
     */
    public void setPreferredProtocol(DeviceProtocol preferredProtocol) {
        this.preferredProtocol = preferredProtocol;
    }

    /**
     * @see #getDeviceUid()
     *
     * @param deviceUid
     */
    public void setDeviceUid(String deviceUid) {
        this.deviceUid = deviceUid;
    }

    /**
     * Get device vendor ID.
     * Harman will provide to fw, sdk will get from fw
     *
     * @return
     */
    public String getDeviceVid() {
        return deviceVid;
    }

    /**
     * Get device product ID.
     * Harman will provide to fw, sdk will get from fw
     * @return
     */
    public String getDevicePid() {
        return devicePid;
    }

    /**
     * Get device model ID(color ID)
     * @return
     * <li>0 : Black</li>
     * <li>1 : White</li>
     * <li>2 : Blue</li>
     * <li>3 : Red</li>
     * <li>4 : Green</li>
     * <li>5 : Purple</li>
     * <li>6 : GoldSilver</li>
     * <li>7 : Silver</li>
     * <li>8 : Gray</li>
     * <li>9 : Beige</li>
     * <li>a : Pink</li>
     */
    public String getDeviceMid() {
        return deviceMid;
    }

    /**
     * Get MAC address of the device.
     *
     * @return
     */
    public String getDeviceMAC() {
        return deviceMAC;
    }

    /**
     * Get BLE address of the device
     * @return
     */
    public String getBleAddress() {
        return bleAddress;
    }

    public int getRssi() {
        return rssi;
    }

    /**
     * Get device battery info, which is got from broadcast.
     * CAUTION: It might be inaccurate. Sending dedicate command to
     * sync up real time battery info is more reliable.
     *
     * @return
     */
    public BatteryInfo getBatteryInfo() {
        return batteryInfo;
    }

    /**
     * Get current role of the device.
     *
     * @return
     */
    public DeviceRole getRole() {
        return role;
    }

    /**
     * Check if the device BLE is connectable or not.
     * @deprecated
     * @return
     */
    public boolean isConnectable() {
        return isConnectable;
    }

    /**
     * Get device scanned timestamp.
     *
     * @return
     */
    public long getScannedTimestamp() {
        return scannedTimestamp;
    }

    /**
     * Get CRC included from device broadcast.
     *
     * @return
     */
    public String getCrc() {
        return crc;
    }

    /**
     * Get preferred connection protocol.
     */
    public DeviceProtocol getPreferredProtocol() {
        return preferredProtocol;
    }

    /**
     * Convert to a string, can be used as UID of this HmDevice.
     * Example: VID+PID+MAC+PROTOCOL.
     *
     * Possible case: One device supports both SPP and BLE, we need to try SPP first then BLE.
     *
     * @return
     */
    public String getDeviceUid() {
        return deviceUid;
    }

    /**
     * Get device firmware version
     * @return
     */
    public String getFirmwareVer() {
        return firmwareVer;
    }

    /**
     * Set device firmware version
     * @param firmwareVer
     */
    public void setFirmwareVer(String firmwareVer) {
        this.firmwareVer = firmwareVer;
    }

    /**
     * Get current channel
     * @return
     */
    public AudioChannel getChannel() {
        return channel;
    }

    public void setChannel(AudioChannel channel) {
        this.channel = channel;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        HmDevice device = (HmDevice) o;
        return Objects.equals(deviceMAC, device.deviceMAC);
    }

    @Override
    public int hashCode() {
        return Objects.hash(deviceMAC);
    }


    public void setDevice(HmDevice hmDevice) {
        this.devicePid = hmDevice.devicePid;
        this.deviceVid = hmDevice.deviceVid;
        this.deviceMid = hmDevice.deviceMid;
        this.firmwareVer = hmDevice.firmwareVer;
        this.deviceMAC = hmDevice.deviceMAC;
        this.bleAddress = hmDevice.bleAddress;
        this.deviceName = hmDevice.deviceName;
        this.batteryInfo = hmDevice.batteryInfo;
        this.role = hmDevice.role;
        this.isConnectable = hmDevice.isConnectable;
        this.scannedTimestamp = hmDevice.scannedTimestamp;
        this.crc = hmDevice.crc;
        this.preferredProtocol = hmDevice.preferredProtocol;
        this.deviceUid = hmDevice.deviceUid;
        this.channel = hmDevice.channel;
    }

    @Override
    public String toString() {
        return "HmDevice{" +
                "\ndevicePid='" + devicePid + '\'' +
                "\ndeviceVid='" + deviceVid + '\'' +
                "\ndeviceMid='" + deviceMid + '\'' +
                "\nfirmwareVer='" + firmwareVer + '\'' +
                "\ndeviceMAC='" + deviceMAC + '\'' +
                "\ndeviceName='" + deviceName + '\'' +
                "\nbatteryInfo=" + batteryInfo +
                "\nrole=" + role +
                "\nisConnectable=" + isConnectable +
                "\nscannedTimestamp=" + scannedTimestamp +
                "\ncrc='" + crc + '\'' +
                "\npreferredProtocol=" + preferredProtocol +
                "\ndeviceUid='" + deviceUid + '\'' +
                "\nchannel=" + channel +
                '}';
    }
}
