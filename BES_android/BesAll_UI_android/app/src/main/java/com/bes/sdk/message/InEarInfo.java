package com.bes.sdk.message;

import java.io.Serializable;

/**
 * In ear info
 */
public class InEarInfo implements Serializable {

    public static final int INVALID = -1;
    public static final int NOT_READY = 0;
    public static final int READY = 1;

    private int leftInEar;

    private int rightInEar;

    /**
     * Get left device in ear status
     * @return {@link #NOT_READY} for not ready, {@link #READY} ready or {@link #INVALID} invalid.
     */
    public int getLeftInEar() {
        return leftInEar;
    }

    public void setLeftInEar(int leftInEar) {
        this.leftInEar = leftInEar;
    }

    /**
     * Get right device in ear status
     * @return {@link #NOT_READY} for not ready, {@link #READY} ready or {@link #INVALID} invalid.
     */
    public int getRightInEar() {
        return rightInEar;
    }

    public void setRightInEar(int rightInEar) {
        this.rightInEar = rightInEar;
    }

    @Override
    public String toString() {
        return "InEarInfo{" +
                "leftInEar=" + leftInEar +
                ", rightInEar=" + rightInEar +
                '}';
    }
}
