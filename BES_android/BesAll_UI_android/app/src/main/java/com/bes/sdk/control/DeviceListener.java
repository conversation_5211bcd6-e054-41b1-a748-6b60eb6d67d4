package com.bes.sdk.control;

import androidx.annotation.NonNull;

import com.bes.sdk.device.HmDevice;
import com.bes.sdk.message.BaseMessage;
import com.bes.sdk.utils.StatusCode;

public interface DeviceListener
{
    /**
     * General callback of {@link DeviceStatusControl} and {@link EQControl} get APIs.
     * @param device received message from this device, might be null for API {@link DeviceStatusControl#getDeviceInfo(DeviceListener)}
     * @param code {@link StatusCode#STATUS_SUCCESS} of means success, others for fail with reasons text in {@link BaseMessage#getMsgContent()}.
     * @param msg return value, data format refer to the related API in {@link DeviceStatusControl} and {@link EQControl}
     */
    void onRead(HmDevice device, StatusCode code, BaseMessage msg);

    /**
     * General callback of {@link DeviceStatusControl} and {@link EQControl} set/change/update APIs
     * @param device some device info/settings has been changed in this device.
     * @param code {@link StatusCode#STATUS_SUCCESS} of means success, others for fail with reasons text in {@link BaseMessage#getMsgContent()}.
     * @param msg return value, data format refer to the related API in {@link DeviceStatusControl} and {@link EQControl}
     */
    void onChanged(@NonNull HmDevice device, StatusCode code, BaseMessage msg);
}
