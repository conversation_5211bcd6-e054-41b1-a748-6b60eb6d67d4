package com.bes.d3dsdk;




public class D3DSdk {

    public D3DSdk() throws Exception {
        System.loadLibrary("d3d_sdk");
    }
    public native void d3dInit(int sampleRate, int bufferSize, int maxBufferSize, int numProcessingThreads);
    public native float[] d3dProcess(int bufferSize, float positionx, float positiony, float positionz, float[] rotMat, float[] inputData);
    public native void d3dShutdown();

}

/*
0.配置NDK
1.java类写接口
2.打开android studio终端 : javac D3DSdk.java 生成 D3DSdk.class
3.cd 到项目里的java目录。然后在java目录使用 javah -jni 包名.类名 命令生成.h头文件 : javah -jni com.bes.d3dsdk.D3DSdk  (失败则用 : javac D3DSdk.java -h .)
4.在src/main下 用 new->folder->JNI Folder 新建jni文件夹, jni文件夹下，新建 D3DBridge.c
5.把 ”javah -jni 包名.类名 命令生成.h头文件“ 生成的.文件放入jni文件夹下。并在 D3DBridge.c 实现它的方法
6.接着在jni文件夹下新建Android.mk和Application.mk文件。
  Android.mk:

  LOCAL_PATH := $(call my-dir)
  include $(CLEAR_VARS)
  LOCAL_MODULE := d3d_sdk
  LOCAL_SRC_FILES := D3DBridge.c
  include $(BUILD_SHARED_LIBRARY)

  Application.mk:

  APP_ABI := all

7.在Project 目录下,右键app，点击新建File文件,命名为CMakeLists.txt
 # For more information about using CMake with Android Studio, read the
# documentation: https://d.android.com/studio/projects/add-native-code.html

# Sets the minimum version of CMake required to build the native library.

cmake_minimum_required(VERSION 3.4.1)

include_directories(src/main/jni/)
file(GLOB CPP_FILES "src/main/cpp/*.c")

# Creates and names a library, sets it as either STATIC
# or SHARED, and provides the relative paths to its source code.
# You can define multiple libraries, and CMake builds them for you.
# Gradle automatically packages shared libraries with your APK.
add_library( sotest
        SHARED
        IMPORTED )

add_library( # Sets the name of the library.
             d3d_sdk #.so库名 可自定义

             # Sets the library as a shared library.
             SHARED

             # Provides a relative path to your source file(s).
             src/main/jni/D3DBridge.c ) #源文件所在目录

set_target_properties( d3d_sdk

        PROPERTIES IMPORTED_LOCATION
        /Users/<USER>/Desktop/NDKDemo/app/src/main/jniLibs/libd3d_render.so
        )

# Searches for a specified prebuilt library and stores the path as a
# variable. Because CMake includes system libraries in the search path by
# default, you only need to specify the name of the public NDK library
# you want to add. CMake verifies that the library exists before
# completing its build.
find_library( # Sets the name of the path variable.
              log-lib
              # Specifies the name of the NDK library that
              # you want CMake to locate.
              log )
# Specifies libraries CMake should link to your target library. You
# can link multiple libraries, such as libraries you define in this
# build script, prebuilt third-party libraries, or system libraries.
target_link_libraries( # Specifies the target library.
                       d3d_sdk #.so库名 可自定义
                       # Links the target library to the log library
                       # included in the NDK.
                       ${log-lib} )


8.右键app，点击Link C++ Project with Gradle，选择path：刚才的CMakeLists.txt，它会在 App build 生成相应的代码。
9.make projext 大功告成。



gcc -dynamiclib -I /Library/Java/JavaVirtualMachines/jdk-14.0.2.jdk/Contents/Home/include/ D3DBridge.c -o libd3d_sdk.so















*/


