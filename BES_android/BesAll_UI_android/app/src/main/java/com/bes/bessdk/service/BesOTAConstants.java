package com.bes.bessdk.service;

public class BesOTAConstants {

    //cmd msg
    public static final int                                 OTA_CMD_RETURN = 0x00000900;
    public static final int                   OTA_CMD_GET_PROTOCOL_VERSION = 0x00000901;
    public static final int                        OTA_CMD_SET_OAT_USER_OK = 0x00000902;
    public static final int                            OTA_CMD_GET_HW_INFO = 0x00000903;
    public static final int                OTA_CMD_SET_UPGRADE_TYPE_NORMAL = 0x00000904;
    public static final int                  OTA_CMD_SET_UPGRADE_TYPE_FAST = 0x00000905;
    public static final int                OTA_CMD_ROLESWITCH_GET_RANDOMID = 0x00000906;
    public static final int                         OTA_CMD_SELECT_SIDE_OK = 0x00000907;
    public static final int                    OTA_CMD_BREAKPOINT_CHECK_80 = 0x00000908;
    public static final int          OTA_CMD_BREAKPOINT_CHECK_80_NO_CHANGE = 0x00000909;
    public static final int                       OTA_CMD_BREAKPOINT_CHECK = 0x00000910;
    public static final int                      OTA_CMD_SEND_CONFIGURE_OK = 0x00000911;
    public static final int                     OTA_CMD_SEND_VERIFY_BTN_OK = 0x00000912;
    public static final int                             OTA_CMD_DISCONNECT = 0x00000913;
    public static final int                          OTA_CMD_SEND_OTA_DATA = 0x00000914;
    public static final int                      OTA_CMD_CRC_CHECK_PACKAGE = 0x00000915;
    public static final int                   OTA_CMD_CRC_CHECK_PACKAGE_OK = 0x00000916;
    public static final int                        OTA_CMD_WHOLE_CRC_CHECK = 0x00000917;
    public static final int                     OTA_CMD_WHOLE_CRC_CHECK_OK = 0x00000918;
    public static final int                     OTA_CMD_IMAGE_OVER_CONFIRM = 0x00000919;
    public static final int                         OTA_SEND_DATA_PROGRESS = 0x00000920;
    public static final int                    OTA_CMD_ROLESWITCH_COMPLETE = 0x00000921;
    public static final int                        OTA_CMD_ADJUST_INTERVAL = 0x00000922;
    public static final int                                OTA_CMD_SEND_82 = 0x00000923;
    public static final int                 OTA_CMD_DIFF_MODE_START_MASTER = 0x00000924;

    //error
    public static final int                            OTA_START_OTA_ERROR = 0x00000940;
    public static final int                      OTA_CMD_SELECT_SIDE_ERROR = 0x00000941;
    public static final int                   OTA_CMD_SEND_CONFIGURE_ERROR = 0x00000942;
    public static final int                OTA_CMD_CRC_CHECK_PACKAGE_ERROR = 0x00000943;
    public static final int                  OTA_CMD_WHOLE_CRC_CHECK_ERROR = 0x00000944;
    public static final int               OTA_CMD_IMAGE_OVER_CONFIRM_ERROR = 0x00000945;
    public static final int                     OTA_CMD_SET_OAT_USER_ERROR = 0x00000946;
    public static final int             OTA_START_DEVICE_LOW_BATTERY_ERROR = 0x00000947;
    public static final int                    OTA_DIFF_UPGRADE_PATH_ERROR = 0x00000948;

    //handle_msg
    public static final int                       MSG_GET_VERSION_TIME_OUT = 0x00000950;
    public static final int                      MSG_GET_RANDOMID_TIME_OUT = 0x00000951;
    public static final int                         MSG_OTA_OVER_RECONNECT = 0x00000952;
    public static final int              MSG_GET_PROTOCOL_VERSION_TIME_OUT = 0x00000953;
    public static final int                  MSG_GET_UPGRATE_TYPE_TIME_OUT = 0x00000954;
    public static final int                   MSG_GET_SELECT_SIDE_TIME_OUT = 0x00000955;
    public static final int                    MSG_SET_OTA_CONFIG_TIME_OUT = 0x00000956;
    public static final int             MSG_GET_CRC_CHECK_PACKAGE_TIME_OUT = 0x00000957;
    public static final int                          MSG_SET_USER_TIME_OUT = 0x00000958;
    public static final int                        MSG_VERIFY_BTH_TIME_OUT = 0x00000959;
    public static final int                 MSG_START_OTA_PACKAGE_TIME_OUT = 0x00000960;
    public static final int    MSG_IMAGE_OVERWRITING_CONFIRMATION_TIME_OUT = 0x00000961;


    //devicetype
    public static final int                             DEVICE_TYPE_STEREO = 0x00000930;
    public static final int                   DEVICE_TYPE_TWS_CONNECT_LEFT = 0x00000931;
    public static final int                  DEVICE_TYPE_TWS_CONNECT_RIGHT = 0x00000932;
    public static final int                               DEVICE_TYPE_DIFF = 0x00000933;


    public static final byte                             CONFIRM_BYTE_PASS = 0x01;
    public static final byte                             CONFIRM_BYTE_FAIL = 0x00;
    public static final byte                      CONFIRM_BYTE_LOW_BATTERY = 0x02;

    public static final int                                  USER_MULTIPLE = 6;
    public static final int                                       USER_BTH = 7;
    public static final int                                 USER_DIFF_FILE = 8;

    public static final int                                 USER_ZIP_FILEs = 9;
    public static final int                                  USER_OTA_BOOT = 10;


    //SPHelper
    public static final String                    BES_OTA_RANDOM_CODE_LEFT = "BES_OTA_RANDOM_CODE_LEFT";
    public static final String                         BES_OTA_CURRENT_MTU = "BES_OTA_CURRENT_MTU";
    public static final String              BES_OTA_IS_MULTIDEVICE_UPGRADE = "BES_OTA_IS_MULTIDEVICE_UPGRADE";

    public static final boolean                    DEFAULT_CLEAR_USER_DATA = false;
    public static final boolean                  DEFAULT_UPDATE_BT_ADDRESS = false;
    public static final boolean                     DEFAULT_UPDATE_BT_NAME = false;
    public static final boolean                 DEFAULT_UPDATE_BLE_ADDRESS = false;
    public static final boolean                    DEFAULT_UPDATE_BLE_NAME = false;

    public static final String               KEY_OTA_CONFIG_CLEAR_USER_DATA = "ota_config_clear_user_data";
    public static final String             KEY_OTA_CONFIG_UPDATE_BT_ADDRESS = "ota_config_update_bt_address";
    public static final String       KEY_OTA_CONFIG_UPDATE_BT_ADDRESS_VALUE = "ota_config_update_bt_address_value";
    public static final String                KEY_OTA_CONFIG_UPDATE_BT_NAME = "ota_config_update_bt_name";
    public static final String          KEY_OTA_CONFIG_UPDATE_BT_NAME_VALUE = "ota_config_update_bt_name_value";
    public static final String            KEY_OTA_CONFIG_UPDATE_BLE_ADDRESS = "ota_config_update_ble_address";
    public static final String      KEY_OTA_CONFIG_UPDATE_BLE_ADDRESS_VALUE = "ota_config_update_ble_address_value";
    public static final String               KEY_OTA_CONFIG_UPDATE_BLE_NAME = "ota_config_update_ble_name";
    public static final String         KEY_OTA_CONFIG_UPDATE_BLE_NAME_VALUE = "ota_config_update_ble_name_value";

    //TOTA
    public static final short                                   OP_TOTA_OTA = (short) 0x9000;

    //file access
    public static final boolean                BES_USE_INTERNAL_FILE_ACCESS = false;
    public static final String             BES_KEY_USE_INTERNAL_FILE_ACCESS = "BES_KEY_USE_INTERNAL_FILE_ACCESS";
    public static final String BES_KEY_USE_INTERNAL_FILE_ACCESS_CHOOSE_FILE = "BES_KEY_USE_INTERNAL_FILE_ACCESS_CHOOSE_FILE";
}
