package com.bes.sdk.message;


import java.io.Serializable;
import java.util.LinkedList;
import java.util.Objects;

public class EQPayload implements Serializable {

    private int index = -1;

    private float calibration;

    private int sampleRate;

    private float leftGain;

    private float rightGain;

    private float bandCount;

    private LinkedList<EQIDParam> iirParams;

    /**
     * Index in the category. For the preset EQ, index is always -1.
     * @return
     */
    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public float getCalibration() {
        return calibration;
    }

    public void setCalibration(float calibration) {
        this.calibration = calibration;
    }

    public int getSampleRate() {
        return sampleRate;
    }

    public void setSampleRate(int sampleRate) {
        this.sampleRate = sampleRate;
    }

    public float getLeftGain() {
        return leftGain;
    }

    public void setLeftGain(float leftGain) {
        this.leftGain = leftGain;
    }

    public float getRightGain() {
        return rightGain;
    }

    public void setRightGain(float rightGain) {
        this.rightGain = rightGain;
    }

    public float getBandCount() {
        return bandCount;
    }

    public void setBandCount(float bandCount) {
        this.bandCount = bandCount;
    }

    /**
     * Get list of EQID parameter
     * @return might be null.
     */
    public LinkedList<EQIDParam> getIirParams() {
        return iirParams;
    }

    public void setIirParams(LinkedList<EQIDParam> iirParams) {
        this.iirParams = iirParams;
    }


    /**
     * Compare without index.
     * @param o
     * @return
     */
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EQPayload eqPayload = (EQPayload) o;
        return Float.compare(eqPayload.calibration, calibration) == 0 &&
                sampleRate == eqPayload.sampleRate &&
                Float.compare(eqPayload.leftGain, leftGain) == 0 &&
                Float.compare(eqPayload.rightGain, rightGain) == 0 &&
                Objects.equals(iirParams, eqPayload.iirParams);
    }

    @Override
    public int hashCode() {
        return Objects.hash(calibration, sampleRate, leftGain, rightGain, iirParams);
    }

    public static class EQIDParam implements Serializable {

        int bandType;

        float gainValue;

        float frequency;

        float qValue;

        public int getBandType() {
            return bandType;
        }

        public void setBandType(int bandType) {
            this.bandType = bandType;
        }

        public float getGainValue() {
            return gainValue;
        }

        public void setGainValue(float gainValue) {
            this.gainValue = gainValue;
        }

        public float getFrequency() {
            return frequency;
        }

        public void setFrequency(float frequency) {
            this.frequency = frequency;
        }

        public float getQValue() {
            return qValue;
        }

        public void setQValue(float qValue) {
            this.qValue = qValue;
        }

        @Override
        public String toString() {
            return  "\ntype=" + bandType +
                    ",gain=" + gainValue +
                    ",freq=" + frequency +
                    ",qValue=" + qValue ;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            EQIDParam eqidParam = (EQIDParam) o;
            return bandType == eqidParam.bandType &&
                    Float.compare(eqidParam.gainValue, gainValue) == 0 &&
                    Float.compare(eqidParam.frequency, frequency) == 0 &&
                    Float.compare(eqidParam.qValue, qValue) == 0;
        }

        @Override
        public int hashCode() {
            return Objects.hash(bandType, gainValue, frequency, qValue);
        }
    }

    @Override
    public String toString() {
        String iir ="";
        if (iirParams != null){
            iir = iirParams.toString();
        }
        return  "\nindex=" + index +
                "\ncalibration=" + calibration +
                "\nsampleRate=" + sampleRate +
                "\nleftGain=" + leftGain +
                "\nrightGain=" + rightGain +
                "\nbandCount=" + bandCount
                + "\n" + iir;
    }
}
