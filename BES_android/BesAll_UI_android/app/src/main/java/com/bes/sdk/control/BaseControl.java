package com.bes.sdk.control;

/**
 * General device control API.
 */
public interface BaseControl
{
    /**
     * Register global listener to device status and EQ update/info receive. Changes triggered from device should be notified by callback.
     * Such as change/get about ANC, AA, Auto Pause, Auto Off, and so on.
     */
    void registerGlobalListener(DeviceListener listener);

    /**
     * Unregister global listener to device status change.
     */
    void unregisterGlobalListener(DeviceListener listener);
}
