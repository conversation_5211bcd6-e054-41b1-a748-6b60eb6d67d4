package com.bes.bessdk;

import java.util.UUID;

public class BesSdkConstants {

    public enum BesConnectState {
        BES_CONFIG_ERROR,  //current config parameter incomplete
        BES_NO_CONNECT,    //The current status is No connection
        BES_CONNECT_NOTOTA,//The current status Is connected, not TOTA connection, does not match current config
        BES_CONNECT_TOTA,  //The current status Is connected, TOTA connection, does not match current config
        BES_CONNECT        //The current status Is connected, match current config, you can use it directly
    }

    public static final int                                 DEFAULT_MTU = 512;


    public static final String                      BES_CONNECT_SERVICE = "CONNECT_SERVICE";
    public static final String               BES_CONNECT_CHARACTERISTIC = "BES_CONNECT_CHARACTERISTIC";
    public static final String                   BES_CONNECT_DESCRIPTOR = "BES_CONNECT_DESCRIPTOR";

    public static final UUID               BES_OTA_SERVICE_OTA_UUID_OLD = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB");
    public static final UUID                   BES_OTA_SERVICE_OTA_UUID = UUID.fromString("*************-6666-6666-************");
    public static final UUID            BES_OTA_CHARACTERISTIC_OTA_UUID = UUID.fromString("*************-7777-7777-************");
    public static final UUID                BES_OTA_DESCRIPTOR_OTA_UUID = UUID.fromString("00002902-0000-1000-8000-00805f9b34fb");

//    public static final UUID BES_OTA_SERVICE_OTA_UUID = UUID.fromString("65786365-6c70-6f69-6e74-2e636f6d0000");
//    public static final UUID BES_OTA_CHARACTERISTIC_OTA_UUID = UUID.fromString("65786365-6c70-6f69-6e74-2e636f6d0001");
//    public static final UUID TX_CHAR_UUID = UUID.fromString("65786365-6c70-6f69-6e74-2e636f6d0002");
//    public static final UUID BES_OTA_DESCRIPTOR_OTA_UUID = UUID.fromString("00002902-0000-1000-8000-00805f9b34fb");


    public static final UUID                  BES_TOTA_SERVICE_OTA_UUID = UUID.fromString("*************-8686-8686-************");
    public static final UUID            BES_TOTA_CHARACTERISTI_OTA_UUID = UUID.fromString("*************-9797-9797-************");
    public static final UUID               BES_TOTA_DESCRIPTOR_OTA_UUID = UUID.fromString("00002902-0000-1000-8000-00805f9b34fb");
 //WIFI UUID
    public static final UUID                      BES_WIFI_SERVICE_UUID = UUID.fromString("04000400-0000-1000-8000-009078563412");
    public static final UUID            BES_WIFI_CHARACTERISTIC_TX_UUID = UUID.fromString("05000500-0000-1000-8000-009178563412");
    public static final UUID            BES_WIFI_CHARACTERISTIC_RX_UUID = UUID.fromString("02000200-0000-1000-8000-009178563412");
    public static final UUID                   BES_WIFI_DESCRIPTOR_UUID = UUID.fromString("00002902-0000-1000-8000-00805f9b34fb");

    public static final UUID                      BES_GATT_SERVICE_UUID = UUID.fromString("00001801-0000-1000-8000-00805f9b34fb");
    public static final UUID               BES_GATT_DESCRIPTOR_OTA_UUID = UUID.fromString("00002902-0000-1000-8000-00805f9b34fb");

    public static final UUID                            BES_SPP_CONNECT = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB");
//public static final UUID                            BES_SPP_CONNECT = UUID.fromString("5a3aa4d1-41b9-4da3-9820-ed330885b29f");
public static final UUID              BES_WIFI_CHARACTERISTIC_TX_UUID_6 = UUID.fromString("06000600-0000-1000-8000-009278563412");

    public static final UUID                    THROUGHPUT_SERVICE_UUID = UUID.fromString("65786365-6c70-6f69-6e74-2e636f820000");
    public static final UUID          THROUGHPUT_CHARACTERISTIC_TX_UUID = UUID.fromString("65786365-6c70-6f69-6e74-2e636f820002");
    public static final UUID          THROUGHPUT_CHARACTERISTIC_RX_UUID = UUID.fromString("65786365-6c70-6f69-6e74-2e636f820001");
    public static final UUID                   BES_DATA_DESCRIPTOR_UUID = UUID.fromString("00002902-0000-1000-8000-00805f9b34fb");


    public static final int                         BES_CONNECT_SUCCESS = 0x0000029a;
    public static final int                           BES_CONNECT_ERROR = 0x000001bc;

    public static final int                          BES_NOTIFY_SUCCESS = 0x0000029b;
    public static final int                            BES_NOTIFY_ERROR = 0x000001bd;

    //tota
    public static final int                              BES_TOTA_START = 0x00000300;
    public static final int                            BES_TOTA_CONFIRM = 0x00000301;
    public static final int                            BES_TOTA_SUCCESS = 0x00000302;
    public static final int                              BES_TOTA_ERROR = 0x00000303;
    public final static String                  BES_TOTA_ENCRYPTION_KEY = "BES_TOTA_ENCRYPTION_KEY";
    public final static String                      BES_TOTA_USE_TOTAV2 = "BES_TOTA_USE_TOTAV2";
    public final static boolean               BES_TOTA_ENCRYPTION_VALUE = true;
    public final static boolean               BES_TOTA_USE_TOTAV2_VALUE = true;

    public static final int                               TOTA_LOG_INFO = 0x00000304;

    //handle_msg
    public static final int                                MSG_TIME_OUT = 0x00000404;
    public static final int                         MSG_PARAMETER_ERROR = 0x00000405;
    public static final int             BES_NOTI_MSG_TIMEOUT_TOTA_START = 0x00000401;

    //LOG
    public static final String                         BES_SAVE_LOG_OTA = "BESOTA.txt";
    public final static String                         BES_SAVE_LOG_KEY = "BES_SAVE_LOG";
    public final static boolean                      BES_SAVE_LOG_VALUE = true;
    public final static String                        BES_SAVE_LOG_NAME = "BES_SAVE_LOG_NAME";

    public final static String                    CUSTOMER_SERVICE_UUID = "CUSTOMER_SERVICE_UUID";
    public final static String          CUSTOMER_CHARACTERISTIC_TX_UUID = "CUSTOMER_CHARACTERISTIC_TX_UUID";
    public final static String          CUSTOMER_CHARACTERISTIC_RX_UUID = "CUSTOMER_CHARACTERISTIC_RX_UUID";
    public final static String                 CUSTOMER_DESCRIPTOR_UUID = "CUSTOMER_DESCRIPTOR_UUID";

    public final static String                     BES_default_INTERVAL = "BES_default_INTERVAL";
    public final static boolean              BES_default_INTERVAL_VALUE = true;
    public final static String                         BES_BLE_INTERVAL = "BES_BLE_INTERVAL";
    public final static String                         BES_SPP_INTERVAL = "BES_SPP_INTERVAL";

    public static final String                                 TOTA_LOG = "TOTA_LOG_INFO";

    //phy_2m
    public final static String                           BES_USE_PHY_2M = "BES_USE_PHY_2M";
    public final static boolean                     BES_USE_PHY_2MVALUE = true;

    public final static String                   BES_USE_NORMAL_CONNECT = "BES_USE_NORMAL_CONNECT";
    public final static boolean            BES_USE_NORMAL_CONNECT_VALUE = false;

    public final static String                    BES_BLE_OTA_AUTO_TEST = "BES_BLE_OTA_AUTO_TEST";
    public final static boolean             BES_BLE_OTA_AUTO_TEST_VALUE = false;
    public final static String           BES_BLE_OTA_AUTO_TEST_INTERVAL = "BES_BLE_OTA_AUTO_TEST_INTERVAL";
    public final static int        BES_BLE_OTA_AUTO_TEST_INTERVAL_VALUE = 7;//min
    public final static String      BES_BLE_OTA_AUTO_TEST_BIN_NAME_MARK = "_autotest";

}
