package com.bes.sdk.message;

import com.bes.sdk.utils.MessageID;

/**
 * Message to describe A2DP connection info list.
 */
public class A2DPConnectionMessage extends BaseMessage
{
    private A2DPConnection a2dpConnection;

    public A2DPConnectionMessage(A2DPConnection a2dpConnection) {
        this.a2dpConnection = a2dpConnection;
    }

    @Override
    public MessageID getMsgID() {
        return MessageID.A2DP_STATUS;
    }

    @Override
    public A2DPConnection getMsgContent() {
        return a2dpConnection;
    }
}
