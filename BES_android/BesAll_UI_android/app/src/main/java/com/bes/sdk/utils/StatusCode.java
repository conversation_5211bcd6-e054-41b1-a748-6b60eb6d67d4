package com.bes.sdk.utils;

public enum StatusCode
{
    STATUS_UNKNOWN          (-1,    "Status Unknown"),
    STATUS_SUCCESS          (0,     "Status Success"),
    STATUS_TIMEOUT          (1,     "Status Timeout"),
    STATUS_CANCEL           (2,     "Status Canceled"),
    STATUS_FAIL             (3,     "Status Failed");

    private int mValue;
    private String mDescription;

    StatusCode(int value, String description) {
        mValue = value;
        mDescription = description;
    }

    public int getValue() {
        return mValue;
    }

    public String getDescription() {
        return mDescription;
    }
}
