package com.bes.bessdk.connect;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCallback;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattDescriptor;
import android.bluetooth.BluetoothGattService;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.scan.BtHeleper;
import com.bes.bessdk.service.base.BesBaseConnectListener;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.connect.DeviceConnector;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.message.BaseMessage;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.common.utils.LogUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
import java.util.UUID;

import static android.bluetooth.BluetoothDevice.DEVICE_TYPE_CLASSIC;
import static android.bluetooth.BluetoothDevice.DEVICE_TYPE_LE;
import static android.bluetooth.BluetoothGattCharacteristic.PROPERTY_NOTIFY;
import static android.bluetooth.BluetoothGattCharacteristic.WRITE_TYPE_NO_RESPONSE;
import static android.content.ContentValues.TAG;
import static com.bes.bessdk.BesSdkConstants.BES_BLE_OTA_AUTO_TEST;
import static com.bes.bessdk.BesSdkConstants.BES_BLE_OTA_AUTO_TEST_VALUE;
import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_ERROR;
import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_SUCCESS;
import static com.bes.bessdk.BesSdkConstants.BES_NOTIFY_ERROR;
import static com.bes.bessdk.BesSdkConstants.BES_NOTIFY_SUCCESS;
import static com.bes.bessdk.BesSdkConstants.BES_SAVE_LOG_OTA;
import static com.bes.bessdk.BesSdkConstants.BesConnectState.BES_CONFIG_ERROR;
import static com.bes.bessdk.BesSdkConstants.BesConnectState.BES_CONNECT;
import static com.bes.bessdk.BesSdkConstants.BesConnectState.BES_CONNECT_NOTOTA;
import static com.bes.bessdk.BesSdkConstants.BesConnectState.BES_CONNECT_TOTA;
import static com.bes.bessdk.BesSdkConstants.BesConnectState.BES_NO_CONNECT;
import static com.bes.bessdk.BesSdkConstants.DEFAULT_MTU;
import static com.bes.bessdk.BesSdkConstants.BesConnectState;
import static com.bes.sdk.utils.DeviceProtocol.PROTOCOL_BLE;
import static com.bes.sdk.utils.DeviceProtocol.PROTOCOL_GATT_BR_EDR;

import androidx.annotation.NonNull;

public class BleConnector implements DeviceConnector {
    protected final String TAG = getClass().getSimpleName();

    private static volatile BleConnector mBleConnector;

    private static BesBaseConnectListener mBesBaseConnectListener;
    private static Context mContext;

    private static List<BesServiceConfig> mServiceConfigs;
    private static List<BluetoothGatt> mBluetoothGattList;
    private static List<BluetoothGattCharacteristic> mCharacteristics;
    private static List<UUID> mDescriptors;
    private static List<HmDevice> mHmDevices;
    private static List<ConnectionListener> mConnectListeners;
    private static List<byte[]> mTotaAesKeys;

    private Object mListenerLock = new Object();

    private ConnectionListener mConnectListener;
    private HmDevice mHmDevice;

    private Timer connectTimer;
    private int connectTimerCount = 0;
    private TimerTask connectTimerTask;
    private int connectMark = -1;

    private int mMtu = 0;
    private boolean isBleConnect = false;

    public static BleConnector getsConnector(Context context, BesBaseConnectListener besBaseConnectListener, BesServiceConfig serviceConfig) {
        if (mBleConnector == null) {
            synchronized (BleConnector.class) {
                if (mBleConnector == null) {
                    mBleConnector = new BleConnector();
                    mServiceConfigs = new ArrayList<>();
                    mConnectListeners = new ArrayList<>();
                    mBluetoothGattList = new ArrayList<>();
                    mCharacteristics = new ArrayList<>();
                    mDescriptors = new ArrayList<>();
                    mHmDevices = new ArrayList<>();
                    mTotaAesKeys = new ArrayList<>();
                }
            }
        }
        if (context != null) {
            mContext = context;
        }
        if (besBaseConnectListener != null) {
            mBesBaseConnectListener = besBaseConnectListener;
        }
        if (serviceConfig != null && serviceConfig.getDevice() != null && mBleConnector.isNewDeviceWithConfig(serviceConfig)) {
            mHmDevices.add(serviceConfig.getDevice());
            mServiceConfigs.add(serviceConfig);
            mBluetoothGattList.add(null);
            mConnectListeners.add(null);
            mCharacteristics.add(null);
            mDescriptors.add(null);
            mTotaAesKeys.add(null);
        }
        if (serviceConfig != null) {
            ArrayUtil.addObjectIndex(mServiceConfigs, serviceConfig, mBleConnector.getCurIndex(mBleConnector.getCurDevice(serviceConfig.getDevice())));
        }

        return mBleConnector;
    }

    private void initReceiver() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED);
        mContext.registerReceiver(mReceiver, filter);
    }

    private BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            switch (intent.getAction()) {
                case BluetoothAdapter.ACTION_DISCOVERY_FINISHED:
                    onStatusChanged(getCurDevice(mHmDevice), false);
                    break;
            }
        }
    };

    public ArrayList<HmDevice> getCurConnectDevices() {
        ArrayList<HmDevice> curConDevices = new ArrayList<>();
        for (int i = 0; i < mHmDevices.size(); i ++) {
            BesServiceConfig config = mServiceConfigs.get(i);
            if (mCharacteristics.get(i) != null && config != null && (config.getTotaConnect() ? mTotaAesKeys.get(i) != null : true)) {
                curConDevices.add(mHmDevices.get(i));
            }
        }
        return curConDevices;
    }

    public BesConnectState getDeviceConnectState(BesServiceConfig serviceConfig) {
        if (!isBleConnect) {
            return BES_NO_CONNECT;
        }
        if (serviceConfig.getDevice() == null || serviceConfig.getServiceUUID() == null || serviceConfig.getCharacteristicsUUID() == null || serviceConfig.getDescriptorUUID() == null) {
            return BES_CONFIG_ERROR;
        }
        BluetoothDevice device = getCurDevice(serviceConfig.getDevice());
        BesServiceConfig oldConfig = getCurServiceConfig(device);
        if (!isNewDeviceWithConfig(serviceConfig) && getCurCharacteristic(device) != null && oldConfig != null && (oldConfig.getTotaConnect() ? getCurTotaAesKey(device) != null : true)) {
            if (serviceConfig.getServiceUUID().toString().equals(oldConfig.getServiceUUID().toString()) && serviceConfig.getCharacteristicsUUID().toString().equals(oldConfig.getCharacteristicsUUID().toString()) && serviceConfig.getDescriptorUUID().toString().equals(oldConfig.getDescriptorUUID().toString())) {
                return BES_CONNECT;
            }
            return oldConfig.getTotaConnect() ? BES_CONNECT_TOTA : BES_CONNECT_NOTOTA;
        }
        return BES_NO_CONNECT;
    }

    private boolean isNewDeviceWithConfig(BesServiceConfig serviceConfig) {
        HmDevice hmDevice = serviceConfig.getDevice();
        for (int i = 0; i < mHmDevices.size(); i ++) {
            HmDevice oldDevice = mHmDevices.get(i);
            if (serviceConfig.getDeviceProtocol() == PROTOCOL_BLE && hmDevice.getBleAddress() != null && hmDevice.getBleAddress().equals(oldDevice.getBleAddress())) {
                return false;
            } else if (serviceConfig.getDeviceProtocol() == PROTOCOL_GATT_BR_EDR && hmDevice.getDeviceMAC() != null && hmDevice.getDeviceMAC().equals(oldDevice.getDeviceMAC())) {
                return false;
            }
        }
        return true;
    }

    public void refreshConnectListener(ConnectionListener connectionListener, BluetoothDevice device) {
        ArrayUtil.addObjectIndex(mConnectListeners, connectionListener, getCurIndex(device));
    }

    public void saveTotaAesKey(byte[] data, BluetoothDevice device) {
        ArrayUtil.addObjectIndex(mTotaAesKeys, data, getCurIndex(device));
    }

    public byte[] getTotaAesKey(BluetoothDevice device) {
        return getCurTotaAesKey(device);
    }

    private BluetoothDevice getCurDevice(HmDevice hmDevice) {
        String address = hmDevice.getPreferredProtocol() == PROTOCOL_BLE ? hmDevice.getBleAddress() : hmDevice.getDeviceMAC();
        BluetoothDevice device = BtHeleper.getBluetoothAdapter(mContext).getRemoteDevice(address);
        return device;
    }

    private int getCurIndex(BluetoothDevice device) {
        for (int i = 0; i < mHmDevices.size(); i ++) {
            HmDevice hmDevice = mHmDevices.get(i);
            String address = hmDevice.getPreferredProtocol() == PROTOCOL_BLE ? hmDevice.getBleAddress() : hmDevice.getDeviceMAC();
            if (address.equals(device.getAddress())) {
                return i;
            }
        }
        return 10000;
    }

    private BesServiceConfig getCurServiceConfig(BluetoothDevice device) {
        if (mServiceConfigs.size() < getCurIndex(device)) {
            return null;
        }
        return mServiceConfigs.get(getCurIndex(device));
    }

    private HmDevice getCurHmDevice(BluetoothDevice device) {
        if (mHmDevices.size() < getCurIndex(device)) {
            return null;
        }
        return mHmDevices.get(getCurIndex(device));
    }

    private BluetoothGatt getCurGatt(BluetoothDevice device) {
        if (mBluetoothGattList.size() < getCurIndex(device)) {
            return null;
        }
        return mBluetoothGattList.get(getCurIndex(device));
    }

    private ConnectionListener getCurListener(BluetoothDevice device) {
        if (mConnectListeners.size() < getCurIndex(device)) {
            return null;
        }
        return mConnectListeners.get(getCurIndex(device));
    }

    private BluetoothGattCharacteristic getCurCharacteristic(BluetoothDevice device) {
        if (mCharacteristics.size() < getCurIndex(device)) {
            return null;
        }
        return mCharacteristics.get(getCurIndex(device));
    }

    private byte[] getCurTotaAesKey(BluetoothDevice device) {
        if (mTotaAesKeys.size() < getCurIndex(device)) {
            return null;
        }
        return mTotaAesKeys.get(getCurIndex(device));
    }

    @Override
    public List<DeviceProtocol> getSupportedProtocols() {
        List<DeviceProtocol> sL = new ArrayList<>();
        sL.add(PROTOCOL_BLE);
        return sL;
    }

    @Override
    public boolean isProtocolSupported(DeviceProtocol deviceProtocol) {
        if (deviceProtocol == PROTOCOL_BLE) {
            return true;
        }
        return false;
    }

    @Override
    public void connect(HmDevice device) {
        if (device == null || mContext == null) {
            return;
        }
        LOG(TAG, "connect: -------------NO connectionListener>");
        connectMark = -1;
        startConnect(null, device);
    }

    @Override
    public void connect(HmDevice device, ConnectionListener connectionListener) {
        if (device == null || connectionListener == null || mContext == null) {
            return;
        }
        LOG(TAG, "connect: -------------connectionListener>");
        connectMark = -1;
        startConnect(connectionListener, device);
    }

    private void stopConnenctTimer() {
        if (connectTimer != null) {
            connectTimer.cancel();
            connectTimer = null;
        }
    }

    @SuppressLint("MissingPermission")
    private void startConnect(ConnectionListener connectionListener, HmDevice device) {
        if (device == null) {
            return;
        }
//        initReceiver();
        mConnectListener = connectionListener;
        mHmDevice = device;
        connectMark ++;
        if (connectMark < 4) {
            connectTimerCount = 0;
            connectTimer = new Timer();
            connectTimerTask = new TimerTask() {
                @Override
                public void run() {
                    connectTimerCount ++;
                    if (connectTimerCount > 8) {
                        stopConnenctTimer();
                        LOG(TAG, "connectTimerTask: --------------retry");
                        startConnect(mConnectListener, mHmDevice);
                    }
                }
            };
            connectTimer.schedule(connectTimerTask, 0, 1000);
        }
        if (connectMark > 5) {
            stopConnenctTimer();
            return;
        }
        LOG(TAG, "connectMark: ------------->" + connectMark);

        BluetoothGatt bluetoothGatt = null;

        BluetoothDevice mDevice = getCurDevice(device);
        if (device.getPreferredProtocol() == PROTOCOL_GATT_BR_EDR) {
            bluetoothGatt = mDevice.connectGatt(mContext, false, mBluetoothGattCallback, BluetoothDevice.TRANSPORT_BREDR);
        } else {
            if (connectMark == 1) {
                bluetoothGatt = mDevice.connectGatt(mContext, false, mBluetoothGattCallback, BluetoothDevice.TRANSPORT_LE, BluetoothDevice.PHY_LE_2M_MASK, null);
                bluetoothGatt.setPreferredPhy(BluetoothDevice.PHY_LE_2M_MASK, BluetoothDevice.PHY_LE_2M_MASK, BluetoothDevice.PHY_OPTION_NO_PREFERRED);
            } else if (connectMark == 2) {
                bluetoothGatt = mDevice.connectGatt(mContext, false, mBluetoothGattCallback);
            } else if (connectMark == 3) {
                bluetoothGatt = mDevice.connectGatt(mContext,
                        false, mBluetoothGattCallback, BluetoothDevice.TRANSPORT_LE);
            } else if (connectMark == 4) {
                bluetoothGatt = mDevice.connectGatt(mContext, false, mBluetoothGattCallback);
            } else {
                boolean useNormalConnect = (boolean) SPHelper.getPreference(mContext, BesSdkConstants.BES_USE_NORMAL_CONNECT, BesSdkConstants.BES_USE_NORMAL_CONNECT_VALUE);
                boolean usePHY2M = (boolean) SPHelper.getPreference(mContext, BesSdkConstants.BES_USE_PHY_2M, BesSdkConstants.BES_USE_PHY_2MVALUE);
                if (useNormalConnect) {
                    LOG(TAG, "startConnect: ------useNormalConnect-NO_MASK");
                    bluetoothGatt = mDevice.connectGatt(mContext, false, mBluetoothGattCallback);
                } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && BtHeleper.getBluetoothAdapter(mContext).isLe2MPhySupported() && usePHY2M) {
                    LOG(TAG, "startConnect: ------PHY_LE_2M_MASK");
                    bluetoothGatt = mDevice.connectGatt(mContext, false, mBluetoothGattCallback, BluetoothDevice.TRANSPORT_LE, BluetoothDevice.PHY_LE_2M_MASK, null);
                    bluetoothGatt.setPreferredPhy(BluetoothDevice.PHY_LE_2M_MASK, BluetoothDevice.PHY_LE_2M_MASK, BluetoothDevice.PHY_OPTION_NO_PREFERRED);
                } else {
                    LOG(TAG, "startConnect: -------PHY_LE_1M_MASK");
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        bluetoothGatt = mDevice.connectGatt(mContext,
                                false, mBluetoothGattCallback, BluetoothDevice.TRANSPORT_LE);
                    } else {
                        LOG(TAG, "startConnect: -------NO_MASK");
                        bluetoothGatt = mDevice.connectGatt(mContext, false, mBluetoothGattCallback);
                    }
                }
            }
        }
        ArrayUtil.addObjectIndex(mBluetoothGattList, bluetoothGatt, getCurIndex(mDevice));
        ArrayUtil.addObjectIndex(mConnectListeners, connectionListener, getCurIndex(mDevice));

//        BluetoothGatt bluetoothGatt = null;
//        mHmDevice = device;
//        BluetoothDevice mDevice = getCurDevice(device);
//
////        bluetoothGatt = mDevice.connectGatt(mContext,
////                false, mBluetoothGattCallback, BluetoothDevice.TRANSPORT_LE);
//
////        bluetoothGatt = mDevice.connectGatt(mContext, false, mBluetoothGattCallback, BluetoothDevice.TRANSPORT_LE, BluetoothDevice.PHY_LE_2M_MASK, null);
////        bluetoothGatt.setPreferredPhy(BluetoothDevice.PHY_LE_2M_MASK, BluetoothDevice.PHY_LE_2M_MASK, BluetoothDevice.PHY_OPTION_NO_PREFERRED);
//
//        bluetoothGatt = mDevice.connectGatt(mContext, false, mBluetoothGattCallback);
//
//        ArrayUtil.addObjectIndex(mBluetoothGattList, bluetoothGatt, getCurIndex(mDevice));
//        ArrayUtil.addObjectIndex(mConnectListeners, connectionListener, getCurIndex(mDevice));
    }

    private boolean isDiscoverServices = false;
    private Thread isDiscoverServicesThread;
    private BluetoothGattCallback mBluetoothGattCallback = new BluetoothGattCallback() {

        @Override
        public void onServiceChanged(@NonNull BluetoothGatt gatt) {
            super.onServiceChanged(gatt);
            Log.i(TAG, "onServiceChanged: -------------");
        }

        @Override
        public void onConnectionStateChange(BluetoothGatt gatt, int status, int newState) {
            super.onConnectionStateChange(gatt, status, newState);
            LOG(TAG, "00000---onConnectionStateChange: -------------->" + status + "<--->" + newState);
            stopConnenctTimer();
            isBleConnect = (status == BluetoothGatt.GATT_SUCCESS);
            if (status != BluetoothGatt.GATT_SUCCESS) {
                LOG(TAG, "onConnectionStateChange: --------------retry");
//                startConnect(mConnectListener, mHmDevice);
//                onStatusChanged(gatt.getDevice(), false);

                ConnectionListener connectionListener = getCurListener(gatt.getDevice());
                if (connectionListener != null) {
                    connectionListener.onStatusChanged(getCurHmDevice(gatt.getDevice()), BES_CONNECT_ERROR, PROTOCOL_BLE);
                }
                return;
            }
            ArrayUtil.addObjectIndex(mBluetoothGattList, gatt, getCurIndex(gatt.getDevice()));
            LOG(TAG, "onConnectionStateChange: --------------" + gatt.getDevice().getAddress());
            LOG(TAG, "onConnectionStateChange status-----" + status);
            LOG(TAG, "onConnectionStateChange newState-----" + newState);

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && BtHeleper.getBluetoothAdapter(mContext).isLe2MPhySupported()) {
                gatt.setPreferredPhy(BluetoothDevice.PHY_LE_2M_MASK, BluetoothDevice.PHY_LE_2M_MASK, BluetoothDevice.PHY_OPTION_NO_PREFERRED);
            }
            LOG(TAG, "onConnectionStateChange: -----" + status);
//            if (newState == BluetoothGatt.STATE_CONNECTED && gatt.discoverServices()) {
//
//            } else {
//                onStatusChanged(gatt.getDevice(), false);
//                close(gatt.getDevice());
//            }
            isDiscoverServices = false;
            if (status == BluetoothGatt.GATT_SUCCESS && newState == BluetoothGatt.STATE_CONNECTED && gatt.discoverServices()) {
//                boolean isDisService = gatt.discoverServices();
//                LOG(TAG, "isDiscover Service: -----" + isDisService);
//                while (!isDisService) {
//                    try {
//                        Thread.sleep(500);
//                        isDisService = gatt.discoverServices();
//                        LOG(TAG, "while isDiscover Service: -----" + isDisService);
//                    } catch (InterruptedException e) {
//                        throw new RuntimeException(e);
//                    }
//                }

                boolean bleOtaAutoTest = (boolean) SPHelper.getPreference(mContext, BES_BLE_OTA_AUTO_TEST, BES_BLE_OTA_AUTO_TEST_VALUE);
                if (bleOtaAutoTest == false) {
                    return;
                }
                isDiscoverServicesThread = new Thread(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            Thread.sleep(10000);
                            if (isDiscoverServices == false) {
                                onStatusChanged(gatt.getDevice(), false);
                            }
                        } catch (InterruptedException e) {
                            throw new RuntimeException(e);
                        }
                    }
                });
                isDiscoverServicesThread.start();
            } else {
                onStatusChanged(gatt.getDevice(), false);
            }
        }

        @Override
        public void onServicesDiscovered(BluetoothGatt gatt, int status) {
            super.onServicesDiscovered(gatt, status);
            isDiscoverServices = true;
            LOG(TAG, "onServicesDiscovered status: -----" + status);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && BtHeleper.getBluetoothAdapter(mContext).isLe2MPhySupported()) {
                gatt.setPreferredPhy(BluetoothDevice.PHY_LE_2M_MASK, BluetoothDevice.PHY_LE_2M_MASK, BluetoothDevice.PHY_OPTION_NO_PREFERRED);
            }
            BesServiceConfig serviceConfig = getCurServiceConfig(gatt.getDevice());
            if (serviceConfig != null && status == BluetoothGatt.GATT_SUCCESS && setWriteCharacteristic(serviceConfig.getServiceUUID(), serviceConfig.getCharacteristicsTX() == null ? serviceConfig.getCharacteristicsUUID() : serviceConfig.getCharacteristicsTX(), gatt.getDevice())) {
                if (!requestMtu(DEFAULT_MTU, gatt.getDevice())) {
                    enableCharacteristicNotification(serviceConfig.getServiceUUID(), serviceConfig.getCharacteristicsUUID(), serviceConfig.getDescriptorUUID(), gatt.getDevice());
                }
            } else {
                onStatusChanged(gatt.getDevice(), false);
            }
        }

        @Override
        public void onCharacteristicWrite(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic, int status) {
            super.onCharacteristicWrite(gatt, characteristic, status);
            if (getCurListener(gatt.getDevice()) != null) {
                getCurListener(gatt.getDevice()).notifyWrite(status == BluetoothGatt.GATT_SUCCESS ? BES_NOTIFY_SUCCESS : BES_NOTIFY_ERROR);
            }
        }

        @Override
        public void onCharacteristicChanged(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic) {
            super.onCharacteristicChanged(gatt, characteristic);
            LOG(TAG, "onCharacteristicChanged: -----" + ArrayUtil.toHex(characteristic.getValue()));
            if (characteristic != null) {
                notifyReceive(characteristic.getValue(), gatt.getDevice());
            }
        }

        @Override
        public void onDescriptorWrite(BluetoothGatt gatt, BluetoothGattDescriptor descriptor, int status) {
            super.onDescriptorWrite(gatt, descriptor, status);
            LOG(TAG, "onDescriptorWrite: -----" + gatt.getDevice().getAddress());
            if (descriptor.getUuid().equals(getCurServiceConfig(gatt.getDevice()).getDescriptorUUID())) {
                if (status == BluetoothGatt.GATT_SUCCESS) {
                    onStatusChanged(gatt.getDevice(), true);

//                    boolean bond = gatt.getDevice().createBond();
//                    LOG(TAG, "createBond-----" + bond);
                    return;
                }
            }
            LOG(TAG, "onDescriptorWrite wrong: -----" + status);
            onStatusChanged(gatt.getDevice(), false);
        }

        @Override
        public void onMtuChanged(BluetoothGatt gatt, int mtu, int status) {
            super.onMtuChanged(gatt, mtu, status);
            LOG(TAG, "onMtuChanged: -----" + mtu + " status:" + status);
            if (enableCharacteristicNotification(getCurServiceConfig(gatt.getDevice()).getServiceUUID(), getCurServiceConfig(gatt.getDevice()).getCharacteristicsUUID(), getCurServiceConfig(gatt.getDevice()).getDescriptorUUID(), gatt.getDevice()) && status == BluetoothGatt.GATT_SUCCESS) {
                mMtu = mtu;
            }
        }
    };

    private boolean setWriteCharacteristic(UUID service, UUID characteristic, BluetoothDevice device) {
        LOG(TAG, "setWriteCharacteristic service----- " + service.toString() + "; characteristic " + characteristic.toString());
        BluetoothGatt gatt = getCurGatt(device);
        if (gatt == null) {
            return false;
        }
        BluetoothGattService gattService = gatt.getService(service);
        if (gattService == null) {
            return false;
        }
        BluetoothGattCharacteristic mCharacteristicTx = gattService.getCharacteristic(characteristic);
        if (mCharacteristicTx == null) {
            return false;
        }
        ArrayUtil.addObjectIndex(mCharacteristics, mCharacteristicTx, getCurIndex(device));
        return true;
    }

    private boolean requestMtu(int mtu, BluetoothDevice device) {
        LOG(TAG, "requestMtu");
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
            return false;
        }
        BluetoothGatt gatt = getCurGatt(device);
        if (gatt != null) {
            return gatt.requestMtu(mtu);
        }
        return false;
    }

    private boolean enableCharacteristicNotification(UUID service, UUID rxCharacteristic, UUID descriptor, BluetoothDevice device) {
        LOG(TAG, "enableCharacteristicNotification service----- " + service.toString() + "; characteristic " + rxCharacteristic.toString() + "; descriptor " + descriptor.toString());
        BluetoothGatt gatt = getCurGatt(device);
        if (gatt != null) {
            BluetoothGattService gattService = gatt.getService(service);
            if (gattService == null) {
                return false;
            }
            BluetoothGattCharacteristic gattCharacteristic = gattService.getCharacteristic(rxCharacteristic);
            if (gattCharacteristic == null) {
                return false;
            }
            if ((gattCharacteristic.getProperties() & PROPERTY_NOTIFY) == 0) {
                onStatusChanged(gatt.getDevice(), true);
                return true;
            }
            BluetoothGattDescriptor gattDescriptor = gattCharacteristic.getDescriptor(descriptor);
            LOG(TAG, "enableCharacteristicNotification000: --------" + gattDescriptor);
            if (gattDescriptor == null) {
                return false;
            }
            if (!gatt.setCharacteristicNotification(gattCharacteristic, true)) {
                return false;
            }
            LOG(TAG, "enableCharacteristicNotificationaaa111: --------");

            if (!gattDescriptor.setValue(BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE)) {
                LOG(TAG, "gattDescriptor.ENABLE fail: --------");

                try {
                    Thread.sleep(3000);
                    if (!gattDescriptor.setValue(BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE)) {
                        return false;
                    }
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
            LOG(TAG, "enableCharacteristicNotificationaaa222: --------");

            ArrayUtil.addObjectIndex(mDescriptors, descriptor, getCurIndex(device));
            return gatt.writeDescriptor(gattDescriptor);
        }
        return false;
    }

    public boolean write(byte[] data, BluetoothDevice device) {
        LOG(TAG, "write: ------" + ArrayUtil.toHex(data));
        LOG(TAG, "write: ------" + device.getAddress());
        BluetoothGatt gatt = getCurGatt(device);
        BluetoothGattCharacteristic characteristic = getCurCharacteristic(device);
        if (gatt != null && characteristic != null) {
            characteristic.setValue(data);
            boolean ret;
            ret = gatt.writeCharacteristic(characteristic);
            LOG(TAG, "write: ---------" + device.getAddress() + "---" + characteristic.getUuid().toString() + "-----" + ret);
            int count = 0;
            while (!ret && count < 6) {
                count ++;
                LOG(TAG, "write: ---------" + device.getAddress() + "-----" + ret);
                ret = gatt.writeCharacteristic(characteristic);
            }
            return ret;
        }
        return false;
    }

    public boolean writeWithoutResponse(byte[] data, BluetoothDevice device) {
        LOG(TAG, "write: ------" + ArrayUtil.toHex(data));
        BluetoothGatt gatt = getCurGatt(device);
        BluetoothGattCharacteristic characteristic = getCurCharacteristic(device);
        if (gatt != null && characteristic != null) {
            characteristic.setWriteType(WRITE_TYPE_NO_RESPONSE);
            characteristic.setValue(data);
            boolean ret = gatt.writeCharacteristic(characteristic);
            LOG(TAG, "write: ---------" + device.getAddress() + "---" + characteristic.getUuid().toString() + "-----" + ret);
            return ret;
        }
        return false;
    }

    private void notifyReceive(byte[] data, BluetoothDevice device) {
        LOG(TAG, "notifyReceive: ------" + device.getAddress());
        BaseMessage baseMessage = new BaseMessage();
        baseMessage.setPush(true);
        baseMessage.setMsgContent(data);
        ConnectionListener connectionListener = getCurListener(device);
        if (connectionListener != null) {
            connectionListener.onDataReceived(baseMessage);
        }
    }

    public void close(BluetoothDevice device) {
        LOG(TAG, "close: ----" + device.getAddress());;
        BluetoothGatt gatt = getCurGatt(device);
        if (gatt != null) {
            gatt.disconnect();
            gatt.close();
        }
        boolean bleOtaAutoTest = (boolean) SPHelper.getPreference(mContext, BES_BLE_OTA_AUTO_TEST, BES_BLE_OTA_AUTO_TEST_VALUE);
//        boolean bleOtaAutoTest = true;
        if (bleOtaAutoTest) {
            ConnectionListener connectionListener = getCurListener(device);
            if (connectionListener != null) {
                connectionListener.onStatusChanged(getCurHmDevice(device), BES_CONNECT_ERROR, PROTOCOL_BLE);
            }
            mServiceConfigs = new ArrayList<>();
            mConnectListeners = new ArrayList<>();
            mBluetoothGattList = new ArrayList<>();
            mCharacteristics = new ArrayList<>();
            mDescriptors = new ArrayList<>();
            mHmDevices = new ArrayList<>();
            mTotaAesKeys = new ArrayList<>();
            return;
        }
        ArrayUtil.resetObjectAtIndex(mServiceConfigs, getCurIndex(device));
        ArrayUtil.resetObjectAtIndex(mBluetoothGattList, getCurIndex(device));
        ArrayUtil.resetObjectAtIndex(mCharacteristics, getCurIndex(device));
        ArrayUtil.resetObjectAtIndex(mDescriptors, getCurIndex(device));
        ConnectionListener connectionListener = getCurListener(device);
        if (connectionListener != null) {
            connectionListener.onStatusChanged(getCurHmDevice(device), BES_CONNECT_ERROR, PROTOCOL_BLE);
        }
    }

    @Override
    public void disconnect(HmDevice device) {
        LOG(TAG, "disconnect: -------");
        close(getCurDevice(device));
        onStatusChanged(getCurDevice(device), false);
    }

    @Override
    public void registerConnectionListener(ConnectionListener connectionListener) {
        synchronized (mListenerLock) {
            if (!mConnectListeners.contains(connectionListener)) {
                mConnectListeners.add(connectionListener);
            }
        }
    }

    @Override
    public void unregisterConnectionListener(ConnectionListener connectionListener) {
        synchronized (mListenerLock) {
            if (mConnectListeners.contains(connectionListener)) {
                mConnectListeners.remove(connectionListener);
            }
        }
    }

    @SuppressLint("MissingPermission")
    private void onStatusChanged(BluetoothDevice device, boolean isSuccess) {
        LOG(TAG, "onStatusChanged---" + device + "---" + isSuccess);
        if (!isSuccess && connectMark < 4) {
            BluetoothGatt gatt = getCurGatt(device);
            if (gatt != null) {
                gatt.disconnect();
                gatt.close();
            }
            startConnect(mConnectListener, mHmDevice);
        } else {
            mConnectListener = null;
            connectMark = -1;
        }
        ConnectionListener connectionListener = getCurListener(device);
        if (connectionListener != null) {
            connectionListener.onStatusChanged(getCurHmDevice(device), isSuccess ? BES_CONNECT_SUCCESS : BES_CONNECT_ERROR, PROTOCOL_BLE);
        }
        if (!isSuccess) {
            close(device);
        }
    }

    public void LOG(String TAG, String msg) {
        if (mContext == null) {
            return;
        }
        Log.i(TAG, "LOG:" + msg);
        String saveLogName = (String) SPHelper.getPreference(mContext, BesSdkConstants.BES_SAVE_LOG_NAME, "");
        boolean saveLog = (boolean) SPHelper.getPreference(mContext, BesSdkConstants.BES_SAVE_LOG_KEY, BesSdkConstants.BES_SAVE_LOG_VALUE);
        if (saveLog) {
            if (saveLogName.equals(BES_SAVE_LOG_OTA)) {
                LogUtils.writeForOTA(TAG, msg, saveLogName);
            } else if (saveLogName.length() > 0){
                LogUtils.writeForLOG(TAG, msg, saveLogName);
            }
        }
    }
}
