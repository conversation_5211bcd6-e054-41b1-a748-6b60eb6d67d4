package com.besall.allbase.view.activity.chipstoollevel4.logdump;

import android.content.Context;
import android.content.Intent;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;
import com.besall.allbase.bluetooth.service.log_dump.LogDumpService;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.activity.chipstoollevel4.audiodump.AudioDumpActivity;
import com.besall.allbase.view.activity.tools.FileActivity.FilelistActivity;
import com.besall.allbase.view.activity.tools.FileActivity.LogFilelistActivity;
import com.besall.allbase.view.base.BasePresenter;

import static com.besall.allbase.common.Constants.FILE_CODE;

/**
 * <AUTHOR>
 * @time $ $
 */
class LogDumpPresenter extends BasePresenter<ILogDumpActivity> implements ILogDumpPresenter {

    private LogDumpService logDumpService;

    @Override
    public void pickDecice(LogDumpActivity context, int scan) {
        Intent intent = new Intent();
        intent.putExtra(BluetoothConstants.Scan.BES_SCAN, scan);
        ActivityUtils.gotoActForResult(intent, BluetoothConstants.Scan.REQUEST_CODE_SCAN, context, ScanActivity.class);
    }

    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        logDumpService = new LogDumpService(serviceConfig, listener, context);
    }

    @Override
    public void startReadLog() {
        if (logDumpService != null) {
            logDumpService.logDumpFlashRead();
        }
    }

    @Override
    public void stopSpp() {
        if (logDumpService != null) {
            logDumpService.disconnected();
        }
    }

    @Override
    public void getCurTotalSize() {
        if (logDumpService != null) {
            logDumpService.logDumpTotalSize();
        }
    }

    @Override
    public void selectfile(LogDumpActivity context, int file) {
        Intent intent = new Intent();
        ActivityUtils.gotoActForResult(intent, FILE_CODE, context, LogFilelistActivity.class);
    }

}
