package com.besall.allbase.view.activity.chipstoollevel4.health.sleep;

import android.content.Context;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.besall.allbase.bluetooth.service.health.sleep.SleepService;
import com.besall.allbase.view.base.BasePresenter;

public class SleepPresenter extends BasePresenter<ISleepActivity> implements ISleepPresenter {

    private SleepService sleepService;

    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        sleepService = new SleepService(serviceConfig, listener, context);

    }

    @Override
    public void sendSleepInfo() {
        sleepService.OPSleepInfo(0);
    }
}
