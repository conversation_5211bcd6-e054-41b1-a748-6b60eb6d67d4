package com.besall.allbase.view.activity.chipstoollevel4.throughput;

import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.os.Build;
import android.os.Environment;
import android.os.Handler;
import android.os.Message;
import android.text.Editable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.ScrollingMovementMethod;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.Toolbar;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.connect.BTService;
import com.bes.bessdk.scan.BtHeleper;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.R;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.service.throughput.ThroughputConstants;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.common.utils.BlePreferenceUtil;

import com.besall.allbase.view.base.BaseActivity;
import com.suke.widget.SwitchButton;

import java.io.File;
import java.io.FileOutputStream;
import java.lang.reflect.Field;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


import static com.bes.bessdk.BesSdkConstants.MSG_TIME_OUT;
import static com.besall.allbase.bluetooth.BluetoothConstants.Scan.BES_SCAN_RESULT;
import static com.besall.allbase.bluetooth.service.throughput.ThroughputConstants.BLE_RECEIVED_INI_CONFIG_RET;
import static com.besall.allbase.bluetooth.service.throughput.ThroughputConstants.EDIT_OK;
import static com.besall.allbase.bluetooth.service.throughput.ThroughputConstants.REAL_BLE_DATA_SIZE;
import static com.besall.allbase.bluetooth.service.throughput.ThroughputConstants.REAL_SPP_DATA_SIZE;
import static com.besall.allbase.bluetooth.service.throughput.ThroughputConstants.SPP_WAIT_INI_CONFIG_ACK_TIMEOUT;
import static com.besall.allbase.bluetooth.service.throughput.ThroughputConstants.THROUGHPUT_SAVE_FOLDER;
import static com.besall.allbase.common.utils.FileUtils.getFolderPath;
import static com.besall.allbase.common.utils.FileUtils.isExist;

/**
 * <AUTHOR>
 * @time $ $
 */
public class ThroughputActivity extends BaseActivity<IThroughputActivity, ThroughputPresenter> implements IThroughputActivity, BesServiceListener, View.OnClickListener {
    public static ThroughputActivity instance;
    public String cur_title = "THROUGH PUT";
    BluetoothDevice mDevice;
    HmDevice mHmDevice;
    BesServiceConfig mServiceConfig;

    private TextView device_address;
    private TextView device_name;
    private Button pick_device;
    private Button connect_device;
    private Button pick_device_ble;
    private int spporble = 0;
    private int datasize = 0;
    private int lasttimeinsec = 0;
    long startTime = 0 ;
    long totalBytesCount = 0  ;
    boolean isUpThrough = false ;
    boolean isTestDone = true ;

    Button upThroughStartBtn , downThroughStartBtn ;
    Button intplus,intminus;
    Button dataplus,dataminus;

    TextView dateSizeText ;
    TextView responseTypeText ;
    TextView upThroughRateText ;
    TextView downThroughRateText ;
    TextView connectionIntervalValueText;
    ProgressBar progressBar ;
    RadioGroup data_pattern1,data_pattern2,data_pattern3;
    RadioButton radioButton00 , radioButton01 , radioButton02 ,radioButton03 ,
            radioButton04 , radioButton05 ,radioButton06 ;
    EditText mdatasize;
    EditText ltis;
    EditText configMinConnectionIntervalEdit , configMaxConnectionIntervalEdit ;
    TextView configDataSizeTx , configLastingTimeTx ;

    SwitchButton configIsWithResponseSwitch, configIsSpecificConnectionParamSwitch;
    String sppdata = "970";
    String bledata = "512";
    String spplasttimeinsec = "10";
    String blelasttimeinsec = "10";
    View ble_interval,ble_config_view;

    @Override
    protected ThroughputPresenter createPresenter() {
        return new ThroughputPresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        mServiceConfig = new BesServiceConfig();
    }

    @Override
    protected int getContentViewId() {
        return R.layout.act_connect;
    }

    @Override
    protected void bindView() {
//        type_spp = (Button)findViewById(R.id.type_spp);
//        type_ble = (Button)findViewById(R.id.type_ble);

        tv_title = (TextView) findViewById(R.id.tv_title);
        mToolbar = (Toolbar) findViewById(R.id.toolbar);
        pick_device = (Button) findViewById(R.id.pick_device);
        connect_device = (Button) findViewById(R.id.connect_device);
        device_address = (TextView) findViewById(R.id.device_address);
        device_name = (TextView) findViewById(R.id.device_name);
        pick_device_ble = (Button) findViewById(R.id.pick_device_ble);

    }

    @Override
    protected void initView() {
//        type_spp.setOnClickListener(instance);
//        type_ble.setOnClickListener(instance);

        pick_device.setOnClickListener(instance);
        connect_device.setOnClickListener(instance);
        pick_device_ble.setOnClickListener(instance);
        pick_device_ble.setVisibility(View.VISIBLE);
        inittoolbar(cur_title);
        loadanimdrawable();

        logV = (TextView) findViewById(R.id.logV);
        done = (Button) findViewById(R.id.done);
        scr_policy = (ScrollView) findViewById(R.id.scr_policy);
        loginfo = (View) findViewById(R.id.loginfo);

        tv_title.setOnClickListener(instance);
        done.setOnClickListener(instance);

        logV.setMovementMethod(ScrollingMovementMethod.getInstance());
        scr_policy.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                v.getParent().requestDisallowInterceptTouchEvent(true);
                return false;
            }
        });
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == BluetoothConstants.Scan.REQUEST_CODE_SCAN) {
            onPickDevice(resultCode, data);
        }
    }

    private void onPickDevice(int resultCode, Intent data) {
        if (resultCode == RESULT_OK) {
            mHmDevice = (HmDevice) data.getSerializableExtra(BES_SCAN_RESULT);
            mDevice = BtHeleper.getBluetoothAdapter(instance).getRemoteDevice(mHmDevice.getPreferredProtocol() == DeviceProtocol.PROTOCOL_BLE ? mHmDevice.getBleAddress() : mHmDevice.getDeviceMAC());

            Log.i(TAG, "onPickDevice: " + mDevice.getName());
            Log.i(TAG, "onPickDevice: " + mDevice.getAddress());
            device_address.setText(mDevice.getAddress());
            mServiceConfig.setDevice(mHmDevice);
            String name = mDevice.getName();
            SpannableString ss = new SpannableString(name);
            BesSdkConstants.BesConnectState state = BTService.getDeviceConnectState(instance, mServiceConfig);
            if (state == BesSdkConstants.BesConnectState.BES_CONNECT) {
                ss.setSpan(new ForegroundColorSpan(Color.rgb(103, 200, 77)), 0, name.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
            device_name.setText(ss);
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                mPresenter.stopSpp();
                spporble = 0;
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.pick_device:
                spporble = 0;
                mPresenter.pickDecice(instance, BluetoothConstants.Scan.SCAN_SPP);
                mServiceConfig.setServiceUUID(BesSdkConstants.BES_SPP_CONNECT);
                mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
                Log.i(TAG, "onPickDevice: " + mHmDevice);
                break;
            case R.id.pick_device_ble:
                spporble = 1;
                mPresenter.pickDecice(instance, BluetoothConstants.Scan.SCAN_BLE);
                mServiceConfig.setServiceUUID(BesSdkConstants.THROUGHPUT_SERVICE_UUID);
                mServiceConfig.setCharacteristicsUUID(BesSdkConstants.THROUGHPUT_CHARACTERISTIC_RX_UUID);
                mServiceConfig.setCharacteristicsTX(BesSdkConstants.THROUGHPUT_CHARACTERISTIC_TX_UUID);
                mServiceConfig.setDescriptorUUID(BesSdkConstants.BES_DATA_DESCRIPTOR_UUID);
                mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_BLE);
                Log.i(TAG, "onPickDevice: " + mHmDevice);
                break;
            case R.id.connect_device:
                loadinganim();
                if (mHmDevice == null) {
                    loadingDialog.dismiss();
                    ActivityUtils.showToast(R.string.connect_failed);
                    return;
                }
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        mServiceConfig.setDevice(mHmDevice);
                        Log.i(TAG, "onPickDevice:1111 " + mDevice.getAddress());
                        mServiceConfig.setTotaConnect(false);
                        mPresenter.connectDevice(mServiceConfig, instance, instance);

                    }
                });
                break;
            case R.id.spp_through_up_btn:
                if(checkAndSaveConfig()) {
                    if (isTestDone) {
                        isTestDone = false ;
                        resetRealData();
                        isUpThrough = true ;
                        BlePreferenceUtil.put(instance , ThroughputConstants.KEY_TRAN_DRIECTION , ThroughputConstants.UP + "");
                        if (spporble == 0) {
                            mPresenter.throughupStart(0);
                        } else if (spporble == 1) {
                            mPresenter.throughupStart(1);
                        }
                    } else {
                        ActivityUtils.showToast("Testing...Please wait");
                    }
                }
                break;
            case R.id.spp_through_down_btn:
                if(checkAndSaveConfig()) {
                    if (isTestDone) {
                        isTestDone = false ;
                        resetRealData();
                        isUpThrough = false ;
                        BlePreferenceUtil.put(instance , ThroughputConstants.KEY_TRAN_DRIECTION , ThroughputConstants.DOWN + "");
                        if (spporble == 0) {
                            mPresenter.throughDownStart(0);
                        } else if (spporble == 1) {
                            mPresenter.throughDownStart(1);
                        }
                    } else {
                        ActivityUtils.showToast("Testing...Please wait");
                    }
                }


                break;
            case R.id.dataminus:
                Log.i(TAG, "onClick: mdatasize.getText().toString();" + mdatasize.getText().toString());
                if (spporble == 0) {
                    if (sppdata == "") {
                        datasize = 20;
                        mdatasize.setText(String.valueOf(datasize));
                        return;
                    } else if(sppdata != "") {
                        datasize = Integer.valueOf(sppdata);
                        if (Integer.valueOf(sppdata) <= 20) {
                            datasize = 20;
                            mdatasize.setText(String.valueOf(datasize));
                        } else if (Integer.valueOf(sppdata) > 20) {
                            datasize--;
                            mdatasize.setText(String.valueOf(datasize));
                        }
                    }
                }
                if (spporble == 1) {
                    if (bledata == "") {
                        datasize = 20;
                        mdatasize.setText(String.valueOf(datasize));
                        return;
                    } else if(bledata != "") {
                        datasize = Integer.valueOf(bledata);
                        if (Integer.valueOf(bledata) <= 20) {
                            datasize = 20;
                            mdatasize.setText(String.valueOf(datasize));
                        } else if (Integer.valueOf(bledata) > 20) {
                            datasize--;
                            mdatasize.setText(String.valueOf(datasize));
                        }
                    }
                }
                break;
            case R.id.dataplus:
                if (spporble == 0) {
                    if (sppdata == "") {
                        datasize = 20;
                        mdatasize.setText(String.valueOf(datasize));
                        return;
                    } else if (sppdata != "") {
                        datasize = Integer.valueOf(sppdata);
                        if (datasize < 970) {
                            datasize++;
                            mdatasize.setText(String.valueOf(datasize));
                        } else if (datasize > 970) {
                            datasize = 970;
                            mdatasize.setText(String.valueOf(datasize));
                        }
                    }
                }
                if (spporble == 1) {
                    if (bledata == "") {
                        datasize = 20;
                        mdatasize.setText(String.valueOf(datasize));
                        return;
                    } else if(bledata != "") {
                        datasize = Integer.valueOf(bledata);
                        if (datasize < 512) {
                            datasize++;
                            mdatasize.setText(String.valueOf(datasize));
                        } else if (datasize > 512) {
                            datasize = 512;
                            mdatasize.setText(String.valueOf(datasize));
                        }
                    }
                }
                break;
            case R.id.intminus:
                if (spporble == 0) {
                    if (spplasttimeinsec == "") {
                        lasttimeinsec = 10;
                        ltis.setText(String.valueOf(lasttimeinsec));
                        return;
                    } else if(spplasttimeinsec != "") {
                        lasttimeinsec = Integer.valueOf(spplasttimeinsec);
                        if (lasttimeinsec <= 10) {
                            lasttimeinsec = 10;
                            ltis.setText(String.valueOf(lasttimeinsec));
                        } else if (lasttimeinsec > 10 ) {
                            lasttimeinsec--;
                            ltis.setText(String.valueOf(lasttimeinsec));
                        }
                    }
                }
                if (spporble == 1) {
                    if (blelasttimeinsec == "") {
                        lasttimeinsec = 10;
                        ltis.setText(String.valueOf(lasttimeinsec));
                        return;
                    } else if(blelasttimeinsec != "") {
                        lasttimeinsec = Integer.valueOf(blelasttimeinsec);
                        if (lasttimeinsec <= 10) {
                            lasttimeinsec = 10;
                            ltis.setText(String.valueOf(lasttimeinsec));
                        } else if (lasttimeinsec > 10) {
                            lasttimeinsec--;
                            ltis.setText(String.valueOf(lasttimeinsec));
                        }
                    }
                }
                break;
            case R.id.intplus:
                if (spporble == 0) {
                    if (spplasttimeinsec == "") {
                        lasttimeinsec = 10;
                        ltis.setText(String.valueOf(lasttimeinsec));
                        return;
                    } else if(spplasttimeinsec != "") {
                        lasttimeinsec = Integer.valueOf(spplasttimeinsec);
                        if (lasttimeinsec < 300) {
                            lasttimeinsec ++;
                            ltis.setText(String.valueOf(lasttimeinsec));
                        } else if (lasttimeinsec > 300) {
                            lasttimeinsec = 300;
                            ltis.setText(String.valueOf(lasttimeinsec));
                        }
                    }
                }
                if (spporble == 1) {
                    if (blelasttimeinsec == "") {
                        lasttimeinsec = 10;
                        ltis.setText(String.valueOf(lasttimeinsec));
                        return;
                    } else if(blelasttimeinsec != "") {
                        lasttimeinsec = Integer.valueOf(blelasttimeinsec);
                        if (lasttimeinsec < 300) {
                            lasttimeinsec++;
                            ltis.setText(String.valueOf(lasttimeinsec));
                        } else if (lasttimeinsec > 300) {
                            lasttimeinsec = 300;
                            ltis.setText(String.valueOf(lasttimeinsec));
                        }
                    }
                }
                break;
            case R.id.done:
                loginfo.setVisibility(View.GONE);
                break;
            case R.id.tv_title:
                loginfo.setVisibility(View.VISIBLE);
                break;
            default:
                break;

        }

    }

    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {

    }

    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {
        if (msg == SPP_WAIT_INI_CONFIG_ACK_TIMEOUT ) {
            isTestDone = true ;
            ActivityUtils.showToast("WAIT_INI_CONFIG_ACK_TIMEOUT");
        }
        if (msg == BesSdkConstants.BES_TOTA_ERROR) {
            ActivityUtils.showToast(R.string.connect_failed);
        }
    }

    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {
        Log.i(TAG, "onStateChangedMessage: +" + msgStr);
        if(msgStr == "error") {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    loadingDialog.dismiss();
                    ActivityUtils.showToast(R.string.connect_failed);
                }
            });
        } else if(msgStr == "success" && spporble == 0) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    loadsppview();
                    loadingDialog.dismiss();
                }
            });
        } else if(msgStr == "success" && spporble == 1) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    loadbleview();
                    loadingDialog.dismiss();
                }
            });
        }
        if (msg == ThroughputConstants.RECEIVED_INI_CONFIG_RET) {
            receivedINIconfig();
        }
        if (msg == BLE_RECEIVED_INI_CONFIG_RET){
            receivedINIconfig();
        }
        if (msg == ThroughputConstants.SPP_SEND_THROUGH_PUT_NEXT) {
            sendthroughtputnext();
        }
        if (msg == ThroughputConstants.GET_THROUGH_PUT_PACKAGE) {
            getThroughputpackage();
        }
        if (msg == ThroughputConstants.GET_THROUGH_PUT_DONE_MSG) {
            getthroughputdonemsg();
        }
        if (msg == ThroughputConstants.BLE_GET_THROUGH_PUT_DONE_MSG) {
            getthroughputdonemsg();
        }
        if (msg == BesSdkConstants.TOTA_LOG_INFO) {
            addlog(msgStr);
        }
    }

    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {

    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        mPresenter.stopSpp();
        spporble = 0;
    }


    private Handler edhandler = new Handler() {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            if (EDIT_OK == msg.what) {
                Log.i(TAG, "handleMessage:  1111111ok");
                if (spporble == 0) {
                    sppdatasize();
                    sppltis();
                } else if (spporble ==1) {
                    bledatasize();
                    bleltis();
                }
            }
        }
    };

    private Runnable edrunnable = new Runnable() {
        @Override
        public void run() {
            edhandler.sendEmptyMessage(EDIT_OK);
        }
    };


    public void loadsppview() {

        setContentView(R.layout.fragment_spp);
        inittoolbar(cur_title);
        instance = this;
        configDataSizeTx = (TextView) findViewById(R.id.spp_config_data_size_text);
        configLastingTimeTx = (TextView) findViewById(R.id.spp_config_lasting_time_in_second_in_ms) ;
        configIsWithResponseSwitch = (SwitchButton) findViewById(R.id.spp_config_is_with_reponse_switch);

        radioButton00 = (RadioButton) findViewById(R.id.pattern_00);
        radioButton01 = (RadioButton) findViewById(R.id.pattern_01);
        radioButton02 = (RadioButton) findViewById(R.id.pattern_02);
        radioButton03 = (RadioButton) findViewById(R.id.pattern_03);
        radioButton04 = (RadioButton) findViewById(R.id.pattern_04);
        radioButton05 = (RadioButton) findViewById(R.id.pattern_05);
        radioButton06 = (RadioButton) findViewById(R.id.pattern_06);
        data_pattern1 = (RadioGroup) findViewById(R.id.data_pattern1);
        data_pattern2 = (RadioGroup) findViewById(R.id.data_pattern2);
        data_pattern3 = (RadioGroup) findViewById(R.id.data_pattern3);
        dateSizeText = (TextView) findViewById(R.id.spp_data_size_text);
        responseTypeText = (TextView) findViewById(R.id.spp_response_type_text);
        upThroughRateText = (TextView) findViewById(R.id.spp_up_through_rate_text);
        downThroughRateText = (TextView) findViewById(R.id.spp_down_through_rate_text);
        upThroughStartBtn = (Button) findViewById(R.id.spp_through_up_btn);
        downThroughStartBtn = (Button) findViewById(R.id.spp_through_down_btn);
        mdatasize = (EditText)findViewById(R.id.datasize);
        ltis = (EditText)findViewById(R.id.ltis);
        intplus =(Button)findViewById(R.id.intplus);
        intminus = (Button)findViewById(R.id.intminus);
        dataplus =(Button)findViewById(R.id.dataplus);
        dataminus = (Button)findViewById(R.id.dataminus);

        upThroughStartBtn.setOnClickListener(this);
        downThroughStartBtn.setOnClickListener(this);
        intminus.setOnClickListener(this);
        intplus.setOnClickListener(this);
        dataminus.setOnClickListener(this);
        dataplus.setOnClickListener(this);


        mdatasize.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                Log.i(TAG, "afterTextChanged: " + mdatasize.getText().toString());
                edhandler.removeCallbacks(edrunnable);
                edhandler.postDelayed(edrunnable, 800);

            }
        });

        ltis.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                edhandler.removeCallbacks(edrunnable);
                edhandler.postDelayed(edrunnable, 800);

            }
        });

        mToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                backtoconnect();
            }
        });

        radioButton00.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                data_pattern2.clearCheck();
                data_pattern3.clearCheck();
            }
        });
        radioButton01.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                data_pattern2.clearCheck();
                data_pattern3.clearCheck();
            }
        });
        radioButton02.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                data_pattern2.clearCheck();
                data_pattern3.clearCheck();
            }
        });
        radioButton03.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                data_pattern1.clearCheck();
                data_pattern3.clearCheck();
            }
        });
        radioButton04.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                data_pattern1.clearCheck();
                data_pattern3.clearCheck();
            }
        });
        radioButton05.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                data_pattern1.clearCheck();
                data_pattern3.clearCheck();

            }
        });
        radioButton06.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                data_pattern2.clearCheck();
                data_pattern1.clearCheck();

            }
        });

        tv_title.setOnClickListener(instance);
        logV = (TextView) findViewById(R.id.logV);
        done = (Button) findViewById(R.id.done);
        scr_policy = (ScrollView)findViewById(R.id.scr_policy);
        loginfo = (View) findViewById(R.id.loginfo);
        logV.setMovementMethod(ScrollingMovementMethod.getInstance());
        done.setOnClickListener(instance);
        scr_policy.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                v.getParent().requestDisallowInterceptTouchEvent(true);
                return false;
            }
        });
    }

    public void sppdatasize() {
        sppdata = mdatasize.getText().toString();
        if (mdatasize != null) {
            try {
                if (Integer.valueOf(sppdata) > 970) {
                    datasize = 970;
                    mdatasize.setText("970");
                    ActivityUtils.showToast("Data size range 20-970");
                } else if (Integer.valueOf(sppdata) < 20) {
                    datasize = 20;
                    mdatasize.setText("20");
                    ActivityUtils.showToast("Data size range 20-970");
                } else {
                    datasize = Integer.valueOf(sppdata);
                }
            } catch (NumberFormatException e) {
                e.printStackTrace();
                datasize = 20;
                mdatasize.setText("20");
                ActivityUtils.showToast("Data size range 20-970");
            }
        }
    }

    public void sppltis() {
        spplasttimeinsec = ltis.getText().toString();
        if (ltis !=null) {
            try {
                if (Integer.valueOf(spplasttimeinsec) < 10) {
                    lasttimeinsec = 10;
                    ltis.setText("10");
                    ActivityUtils.showToast("time range 10-300");
                } else if (Integer.valueOf(spplasttimeinsec) > 300) {
                    lasttimeinsec = 300;
                    ltis.setText("300");
                    ActivityUtils.showToast("time range 10-300");
                } else {
                    lasttimeinsec = Integer.valueOf(spplasttimeinsec);
                }
            } catch (Exception e) {
                e.printStackTrace();
                lasttimeinsec = 10;
                ltis.setText("10");
                ActivityUtils.showToast("time range 10-300");
            }
        }
    }

    public void loadbleview() {

        setContentView(R.layout.fragment_spp);
        inittoolbar(cur_title);
        instance = this;
        configDataSizeTx = (TextView) findViewById(R.id.spp_config_data_size_text);
        configLastingTimeTx = (TextView) findViewById(R.id.spp_config_lasting_time_in_second_in_ms) ;
        configIsWithResponseSwitch = (SwitchButton) findViewById(R.id.spp_config_is_with_reponse_switch);
        configIsSpecificConnectionParamSwitch = (SwitchButton) findViewById(R.id.ble_config_is_use_specific_connection_param_switch);
        radioButton00 = (RadioButton) findViewById(R.id.pattern_00);
        radioButton01 = (RadioButton) findViewById(R.id.pattern_01);
        radioButton02 = (RadioButton) findViewById(R.id.pattern_02);
        radioButton03 = (RadioButton) findViewById(R.id.pattern_03);
        radioButton04 = (RadioButton) findViewById(R.id.pattern_04);
        radioButton05 = (RadioButton) findViewById(R.id.pattern_05);
        radioButton06 = (RadioButton) findViewById(R.id.pattern_06);
        data_pattern1 = (RadioGroup) findViewById(R.id.data_pattern1);
        data_pattern2 = (RadioGroup) findViewById(R.id.data_pattern2);
        data_pattern3 = (RadioGroup) findViewById(R.id.data_pattern3);
        dateSizeText = (TextView) findViewById(R.id.spp_data_size_text);
        responseTypeText = (TextView) findViewById(R.id.spp_response_type_text);
        upThroughRateText = (TextView) findViewById(R.id.spp_up_through_rate_text);
        downThroughRateText = (TextView) findViewById(R.id.spp_down_through_rate_text);
        upThroughStartBtn = (Button) findViewById(R.id.spp_through_up_btn);
        downThroughStartBtn = (Button) findViewById(R.id.spp_through_down_btn);
        mdatasize = (EditText)findViewById(R.id.datasize);
        ltis = (EditText)findViewById(R.id.ltis);
        intplus = (Button)findViewById(R.id.intplus);
        intminus = (Button)findViewById(R.id.intminus);
        dataplus = (Button)findViewById(R.id.dataplus);
        dataminus = (Button)findViewById(R.id.dataminus);
        connectionIntervalValueText = (TextView) findViewById(R.id.ble_connection_interval_value_text);
        configMinConnectionIntervalEdit = (EditText) findViewById(R.id.ble_config_min_connection_interval_in_ms_edit);
        configMaxConnectionIntervalEdit = (EditText) findViewById(R.id.ble_config_max_connection_interval_in_ms_edit);
        connectionIntervalValueText.setVisibility(View.VISIBLE);
        ble_interval = (View) findViewById(R.id.ble_interval);
        ble_config_view = (View) findViewById(R.id.ble_config_view);
        ble_interval.setVisibility(View.VISIBLE);
        ble_config_view.setVisibility(View.VISIBLE);
        upThroughStartBtn.setOnClickListener(this);
        downThroughStartBtn.setOnClickListener(this);
        intminus.setOnClickListener(this);
        intplus.setOnClickListener(this);
        dataminus.setOnClickListener(this);
        dataplus.setOnClickListener(this);
        intplus = (Button)findViewById(R.id.intplus);
        intminus = (Button)findViewById(R.id.intminus);
        dataplus = (Button)findViewById(R.id.dataplus);
        dataminus = (Button)findViewById(R.id.dataminus);
        mdatasize.setText("512");

        mdatasize.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                edhandler.removeCallbacks(edrunnable);
                edhandler.postDelayed(edrunnable, 800);

            }
        });

        ltis.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                edhandler.removeCallbacks(edrunnable);
                edhandler.postDelayed(edrunnable, 800);
            }
        });

        mToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                backtoconnect();
            }
        });

        radioButton00.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                data_pattern2.clearCheck();
                data_pattern3.clearCheck();
            }
        });
        radioButton01.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                data_pattern2.clearCheck();
                data_pattern3.clearCheck();

            }
        });
        radioButton02.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                data_pattern2.clearCheck();
                data_pattern3.clearCheck();

            }
        });
        radioButton03.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                data_pattern1.clearCheck();
                data_pattern3.clearCheck();
            }
        });
        radioButton04.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                data_pattern1.clearCheck();
                data_pattern3.clearCheck();
            }
        });
        radioButton05.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                data_pattern1.clearCheck();
                data_pattern3.clearCheck();
            }
        });
        radioButton06.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                data_pattern2.clearCheck();
                data_pattern1.clearCheck();
            }
        });
        configIsSpecificConnectionParamSwitch.setOnClickListener(this);
        configIsSpecificConnectionParamSwitch.setOnCheckedChangeListener(new SwitchButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(SwitchButton view, boolean isChecked) {
                Log.i(TAG, "onCheckedChanged: " + isChecked);
                if(isChecked) {
                    Log.i(TAG, "onCheckedChanged: isd" + isChecked);
                    configMaxConnectionIntervalEdit.setEnabled(true);
                    configMinConnectionIntervalEdit.setEnabled(true);
                }else{
                    configMaxConnectionIntervalEdit.setEnabled(false);
                    configMinConnectionIntervalEdit.setEnabled(false);
                }
            }
        });
        tv_title.setOnClickListener(instance);
        logV = (TextView) findViewById(R.id.logV);
        done = (Button) findViewById(R.id.done);
        scr_policy = (ScrollView)findViewById(R.id.scr_policy);
        loginfo = (View) findViewById(R.id.loginfo);
        logV.setMovementMethod(ScrollingMovementMethod.getInstance());
        done.setOnClickListener(instance);
        scr_policy.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                v.getParent().requestDisallowInterceptTouchEvent(true);
                return false;
            }
        });
    }

    public void bledatasize() {
        if (mdatasize !=null) {
            bledata = mdatasize.getText().toString();
            try {
                if (Integer.valueOf(bledata) < 20) {
                    datasize = 20;
                    mdatasize.setText("20");
                    ActivityUtils.showToast("Data size range 20-512");
                } else if (Integer.valueOf(bledata) > 512) {
                    datasize = 512;
                    mdatasize.setText("512");
                    ActivityUtils.showToast("Data size range 20-512");
                } else {
                    datasize = Integer.valueOf(bledata);
                }
            } catch (Exception e) {
                e.printStackTrace();
                datasize = 20;
                mdatasize.setText("20");
                ActivityUtils.showToast("Data size range 20-512");
            }
        }
    }

    public void bleltis() {
        if (ltis != null) {
            blelasttimeinsec = ltis.getText().toString();
            try {
                if (Integer.valueOf(blelasttimeinsec) < 10) {
                    lasttimeinsec = 10;
                    ltis.setText("10");
                    ActivityUtils.showToast("time range 10-300");
                } else if (Integer.valueOf(blelasttimeinsec) > 300) {
                    lasttimeinsec = 300;
                    ltis.setText("300");
                    ActivityUtils.showToast("time range 10-300");
                } else {
                    lasttimeinsec = Integer.valueOf(blelasttimeinsec);
                }
            } catch (Exception e) {
                e.printStackTrace();
                lasttimeinsec = 10;
                ltis.setText("10");
                ActivityUtils.showToast("time range 10-300");
            }
        }
    }

    public void backtoconnect() {
        setContentView(R.layout.act_connect);
        bindView();
        initView();
        mPresenter.stopSpp();
    }

    private boolean checkAndSaveConfig() {
        int dataPattern = 0;
        if(radioButton00.isChecked()) {
            dataPattern = 0;
        }else if(radioButton01.isChecked()) {
            dataPattern = 1;
        }else if(radioButton02.isChecked()) {
            dataPattern = 2;
        }else if(radioButton03.isChecked()) {
            dataPattern = 3;
        }else if(radioButton04.isChecked()) {
            dataPattern = 4;
        }else if(radioButton05.isChecked()) {
            dataPattern = 5;
        }else if(radioButton06.isChecked()) {
            dataPattern = 6;
        }
        BlePreferenceUtil.put(instance , ThroughputConstants.KEY_TEST_DATA_PATTERN , dataPattern + "");
        if(datasize != 0) {
            Log.i(TAG, "checkAndSaveConfig: +++datasize  " + datasize);
            int dataprogress = datasize ;
            if (spporble == 0) {
                BlePreferenceUtil.put(instance , ThroughputConstants.KEY_SPP_DATA_SIZE , dataprogress + "");
            } else if (spporble == 1) {
                BlePreferenceUtil.put(instance , ThroughputConstants.KEY_BLE_DATA_SIZE , dataprogress + "");
            }
        }
        if (lasttimeinsec != 0) {
            int progress = lasttimeinsec ;
            BlePreferenceUtil.put(instance , ThroughputConstants.KEY_LASTING_TIME_SECOND , progress + "");
        }
        if(configIsWithResponseSwitch != null) {
            int isWithRepsone = configIsWithResponseSwitch.isChecked() ? ThroughputConstants.RESPONSE:ThroughputConstants.NO_REPSONSE;
            BlePreferenceUtil.put(instance , ThroughputConstants.KEY_IS_WITH_RESPONSE , isWithRepsone + "");
        }
        if (spporble == 0) {
            return true;
        } else if (spporble == 1) {
            if(configIsSpecificConnectionParamSwitch != null) {
                int isSpecific = configIsSpecificConnectionParamSwitch.isChecked()? ThroughputConstants.IS_USER_SPECIFIC:ThroughputConstants.IS_NO_USER_SPECIFIC;
                BlePreferenceUtil.put(instance , ThroughputConstants.KEY_IS_USE_SPECIFIC_BLE_CONNECTION , isSpecific + "");
                if(configIsSpecificConnectionParamSwitch.isChecked()) {
                    if(configMinConnectionIntervalEdit != null && !TextUtils.isEmpty(configMinConnectionIntervalEdit.getText())
                            && configMaxConnectionIntervalEdit != null && !TextUtils.isEmpty(configMaxConnectionIntervalEdit.getText())) {
                        int minValue = Integer.valueOf(configMinConnectionIntervalEdit.getText().toString());
                        int maxValue = Integer.valueOf(configMaxConnectionIntervalEdit.getText().toString());
                        if(maxValue <= minValue) {
                            ActivityUtils.showToast("The Max One must big than Min One");
                            return  false ;
                        }else{
                            BlePreferenceUtil.put(instance , ThroughputConstants.KEY_MIN_CONNECTION_INTERVAL_IN_MS , minValue + "");
                            BlePreferenceUtil.put(instance , ThroughputConstants.KEY_MAX_CONNECTION_INTERVAL_IN_MS , maxValue + "");
                            return true ;
                        }
                    }else{
                        ActivityUtils.showToast("The Min or the Max Connection interval is illegal");

                        return false ;
                    }
                }else{
                    BlePreferenceUtil.put(instance , ThroughputConstants.KEY_MIN_CONNECTION_INTERVAL_IN_MS , 10 + "");
                    BlePreferenceUtil.put(instance , ThroughputConstants.KEY_MAX_CONNECTION_INTERVAL_IN_MS , 20 + "");
                    return true ;
                }
            }

        }
        return false;
    }

    private void resetRealData() {
        if (spporble == 1) {
            connectionIntervalValueText.setText("Connection interval in ms : [ Min = --ms,Max = --ms ]");
        }
        dateSizeText.setText("Data size  : [ len = -- bytes ]");
        responseTypeText.setText("Response type  : [ -- ]");
        upThroughRateText.setText("Up-Throughput rate  : [ -- byte/second ]");
        downThroughRateText.setText("Down-Throughput rate  : [ -- byte/second ]");
        BlePreferenceUtil.put(instance , ThroughputConstants.REAL_MAX_CONNECTION_INTERVAL_IN_MS , 0);
        BlePreferenceUtil.put(instance , ThroughputConstants.REAL_MIN_CONNECTION_INTERVAL_IN_MS , 0);
        BlePreferenceUtil.put(instance , REAL_SPP_DATA_SIZE , 0);
        BlePreferenceUtil.put(instance , REAL_BLE_DATA_SIZE , 0);
        BlePreferenceUtil.put(instance , ThroughputConstants.REAL_IS_WITH_RESPONSE , 0);
        startTime = 0 ;
        totalBytesCount = 0 ;
    }


    private void autoSaveReport(String folderName) {
        StringBuffer sb = new StringBuffer();
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss");
        Map<String, String> infos = new HashMap<String, String>();
        collectDeviceInfo(this , infos);
        int min = (int) BlePreferenceUtil.get(instance , ThroughputConstants.REAL_MIN_CONNECTION_INTERVAL_IN_MS , 10);
        int max = (int) BlePreferenceUtil.get(instance , ThroughputConstants.REAL_MAX_CONNECTION_INTERVAL_IN_MS , 20);
        int dataSize = 0;
        if (spporble == 0){
            dataSize = (int) BlePreferenceUtil.get(instance , REAL_SPP_DATA_SIZE , 20);
        } else if (spporble == 1) {
            dataSize = (int) BlePreferenceUtil.get(instance , REAL_BLE_DATA_SIZE , 20);
        }
        int responseTyep  =  Integer.valueOf((String)BlePreferenceUtil.get(instance , ThroughputConstants.KEY_IS_WITH_RESPONSE , ThroughputConstants.NO_REPSONSE + "" ));
        int lastingTime  =  Integer.valueOf((String)BlePreferenceUtil.get(instance , ThroughputConstants.KEY_LASTING_TIME_SECOND , "10" ));
        if (isUpThrough) {
            sb.append("This is Up-Through Report\n");
        } else {
            sb.append("This is Down-Through Report\n");
        }
        if(responseTyep == ThroughputConstants.NO_REPSONSE) {
            sb.append("Response type  : [ no response ]\n");
        }else{
            sb.append("Response type  : [ with response ]\n");
        }
        sb.append("Connection interval in ms : [ Min = " + min + "ms,Max = " + max + "ms ]\n");
        sb.append("Data size  : [ len = " + dataSize + " bytes ]\n");
        sb.append("Lating Time is  : [  " + lastingTime + " s ]\n");
        sb.append(upThroughRateText.getText().toString() + "\n");
        sb.append(downThroughRateText.getText().toString() + "\n");
        for (Map.Entry<String, String> entry : infos.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            sb.append(key + "=" + value + "\n");
        }
        Log.e(TAG , sb.toString());
        try {
            String time = formatter.format(new Date());
            String fileName = isUpThrough ? "SPP_Up_" + time  + ".txt" : "SPP_Down_" + time  + ".txt";
            if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {

                String path = getFolderPath() + "Android/" + "data/" + ActivityUtils.getPackageName() + "/" + "files/";
                isExist(path);
                path = path + folderName + "/";
                isExist(path);
                path = path + fileName;
                isExist(path);
                File dir = new File(path);
                if (!dir.exists()) {
                    dir.mkdirs();
                }
                FileOutputStream fos = new FileOutputStream(path + fileName);
                fos.write(sb.toString().getBytes());
                fos.close();
            }
        } catch (Exception e) {
            Log.e(TAG, "an error occured while writing file...", e);
        }
    }

    public void collectDeviceInfo(Context ctx , Map<String, String> infos) {
        try {
            PackageManager pm = ctx.getPackageManager();
            PackageInfo pi = pm.getPackageInfo(ctx.getPackageName(), PackageManager.GET_ACTIVITIES);
            if (pi != null) {
                String versionName = pi.versionName == null ? "null" : pi.versionName;
                String versionCode = pi.versionCode + "";
                infos.put("versionName", versionName);
                infos.put("versionCode", versionCode);
            }
        } catch (PackageManager.NameNotFoundException e) {
            Log.e(TAG, "an error occured when collect package info", e);
        }
        Field[] fields = Build.class.getDeclaredFields();
        for (Field field : fields) {
            try {
                field.setAccessible(true);
                infos.put(field.getName(), field.get(null).toString());
                Log.d(TAG, field.getName() + " : " + field.get(null));
            } catch (Exception e) {
                Log.e(TAG, "an error occured when collect crash info", e);
            }
        }
    }

    private void setRealData() {
        Log.i(TAG, "setRealData: setRealData");
        if (spporble == 0) {
            int dataSize =  (int) BlePreferenceUtil.get(instance, REAL_SPP_DATA_SIZE , 0);
            int responseTyep  =  Integer.valueOf((String)BlePreferenceUtil.get(instance , ThroughputConstants.KEY_IS_WITH_RESPONSE , ThroughputConstants.NO_REPSONSE+"" ));
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if(responseTyep == ThroughputConstants.NO_REPSONSE) {
                        responseTypeText.setText("Response type  : [ no response ]");
                    }else{
                        responseTypeText.setText("Response type  : [ with response ]");
                    }
                    dateSizeText.setText("Data size  : [ len = "+dataSize+" bytes ]");
                }
            });


        } else if (spporble == 1) {
            int min = (int) BlePreferenceUtil.get(instance , ThroughputConstants.REAL_MIN_CONNECTION_INTERVAL_IN_MS , 0);
            int max = (int) BlePreferenceUtil.get(instance , ThroughputConstants.REAL_MAX_CONNECTION_INTERVAL_IN_MS , 0);
            int dataSize = (int) BlePreferenceUtil.get(instance , REAL_BLE_DATA_SIZE , 0 );
            Log.i(TAG, "setRealData: ble " + dataSize);
            int responseTyep  =  Integer.valueOf((String)BlePreferenceUtil.get(instance , ThroughputConstants.KEY_IS_WITH_RESPONSE , ThroughputConstants.NO_REPSONSE+"" ));
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if(responseTyep == ThroughputConstants.NO_REPSONSE) {
                        responseTypeText.setText("Response type  : [ no response ]");
                    } else {
                        responseTypeText.setText("Response type  : [ with response ]");
                    }
                    connectionIntervalValueText.setText("Connection interval in ms : [ Min = "+min+"ms,Max = "+max+"ms ]");
                    dateSizeText.setText("Data size  : [ len = "+dataSize+" bytes ]");
                }
            });


        }

    }

    private void sendthroughtputnext() {
        if(startTime != 0 && !isTestDone) {
            int dataSize = 0;
            if (spporble == 0 ) {
                dataSize = (int) BlePreferenceUtil.get(instance, ThroughputConstants.REAL_SPP_DATA_SIZE , 20);
                Log.i(TAG, "sendthroughtputnext: spp" + dataSize);
            } else if (spporble == 1) {
                dataSize = (int) BlePreferenceUtil.get(instance, REAL_BLE_DATA_SIZE , 20 );
                Log.i(TAG, "sendthroughtputnext: ble" + dataSize);
            }
            if(dataSize < 20) {
                dataSize = 20 ;
            }
            if(dataSize > 512 - 3) {
                dataSize = 512 - 3 ;
            }
            totalBytesCount+= dataSize ;
            Log.i(TAG, "sendthroughtputnext: ----------" + totalBytesCount);
            long spantimeForDown = (System.currentTimeMillis() - startTime) /1000;
            if(spantimeForDown > 1) {
                int downRate = (int)(totalBytesCount/spantimeForDown);
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        downThroughRateText.setText("Down-Throughput rate  : [ " + downRate + " byte/second ]");
                    }
                });
            }
            if (spporble == 0 ) {
                mPresenter.sendThroughputPackage(0);
            } else if (spporble == 1) {
                mPresenter.sendThroughputPackage(1);
            }
        }
    }

    private void getThroughputpackage() {
        if(startTime == 0) {
            startTime = System.currentTimeMillis() ;
        }
        if (spporble == 0) {
            int datalen = (Integer) BlePreferenceUtil.get(instance, ThroughputConstants.SPPGET_THROUGH_PUT_PACKAGE,0);
            totalBytesCount+= datalen ;
            Log.i(TAG, "getThroughputpackage: --" + totalBytesCount);
        } else if (spporble == 1) {
            int datalen = (Integer) BlePreferenceUtil.get(instance, ThroughputConstants.BLEGET_THROUGH_PUT_PACKAGE,0);
            totalBytesCount+= datalen ;
        }
        long spantimeForUp = (System.currentTimeMillis()  - startTime)/1000 ;
        if (spantimeForUp > 1) {
            int upRate = (int)(totalBytesCount / spantimeForUp);
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    upThroughRateText.setText("Up-Throughput rate  : [ "+upRate+" byte/second ]");
                }
            });
        }
    }

    private void getthroughputdonemsg() {
        isTestDone = true ;
        downThroughHandler.removeMessages(DOWN_TIME_DONE);
        if(totalBytesCount != 0) {
            autoSaveReport(THROUGHPUT_SAVE_FOLDER);
        }
    }



    final int DOWN_TIME_DONE = 0X0099 ;
    Handler downThroughHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            if(msg.what == DOWN_TIME_DONE) {
                isTestDone = true ;
                if(totalBytesCount != 0) {
                    autoSaveReport(THROUGHPUT_SAVE_FOLDER);
                }
                mPresenter.sendDoneMsg(0);
            }
        }
    };



    private void receivedINIconfig() {
        setRealData();
        if(!isUpThrough) {
            startTime = System.currentTimeMillis() ;
            Log.i(TAG, "receivedINIconfig:" + startTime);
            int span = Integer.parseInt((String) BlePreferenceUtil.get(instance , ThroughputConstants.REAL_LASTING_TIME_SECOND , 10 + ""));
            downThroughHandler.sendEmptyMessageDelayed(DOWN_TIME_DONE , span * 1000l);
            sendthroughtputnext();
        }
    }





}
