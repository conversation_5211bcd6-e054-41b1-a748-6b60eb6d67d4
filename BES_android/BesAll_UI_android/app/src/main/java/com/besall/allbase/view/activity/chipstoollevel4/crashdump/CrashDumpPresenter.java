package com.besall.allbase.view.activity.chipstoollevel4.crashdump;

import android.content.Context;
import android.content.Intent;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;
import com.besall.allbase.bluetooth.service.crashdump.CrashDumpService;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.activity.tools.FileActivity.CrashFilelistActivity;
import com.besall.allbase.view.base.BasePresenter;

import static com.besall.allbase.common.Constants.FILE_CODE;

public class CrashDumpPresenter extends BasePresenter<ICrashDumpActivity> implements ICrashDumpPresenter {

    CrashDumpService crashDumpService;

    @Override
    public void pickDecice(CrashDumpActivity context, int scan) {
        Intent intent = new Intent();
        intent.putExtra(BluetoothConstants.Scan.BES_SCAN, scan);
        ActivityUtils.gotoActForResult(intent, BluetoothConstants.Scan.REQUEST_CODE_SCAN, context, ScanActivity.class);
    }

    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        crashDumpService = new CrashDumpService(serviceConfig, listener, context);
    }

    @Override
    public void dumpstart() {
        if (crashDumpService != null) {
            crashDumpService.CrashDumpStart();
        }
    }

    @Override
    public void stopSpp() {
        if (crashDumpService != null) {
            crashDumpService.disconnected();
        }
    }

    @Override
    public void selectfile(CrashDumpActivity context, int file) {
        Intent intent = new Intent();
        ActivityUtils.gotoActForResult(intent, FILE_CODE, context, CrashFilelistActivity.class);
    }

}
