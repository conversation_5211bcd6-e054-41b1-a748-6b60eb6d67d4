package com.besall.allbase.bluetooth.service.rssiextend;

public class RssiExtendConstants {

    //cmd
    public static final short              NEW_OP_TOTA_RSSI_READ_CMD = (short) 0x6306;
    public static final short                  OP_TOTA_RSSI_READ_CMD = (short) 0x6309;
    public static final short              OLD_OP_TOTA_RSSI_READ_CMD = (short) 0x900b;

    public static final short               OP_TOTA_RAW_DATA_SET_CMD = (short)0x9100;

    //msg id
    public static final int                               RSSI_START = 0x00001000;



    public static final String                       RSSI_SAVE_FOLDER = "RssiExtendLog";
    public static final String                          RSSI_PROTOCOL = "RSSI PROTOCOL";
    public final static boolean               BES_RSSI_PROTOCOL_VALUE = true;

}
