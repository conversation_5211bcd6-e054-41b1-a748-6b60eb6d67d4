package com.besall.allbase.bluetooth.service.rssiextend;

import android.content.Context;
import android.util.Log;

import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.CmdInfo;
import com.bes.bessdk.utils.SPHelper;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.service.rssi.RssiConstants;
import com.besall.allbase.common.utils.FileUtils;

import java.text.SimpleDateFormat;
import java.util.Date;

public class RssiExtendCMD {
    static String TAG = "RssiCMD";

    static String startTime = "";
    public static String rssiinfo = "";

    public static byte[] RssiReadStart(Context context) {
        int Protocol = (int) SPHelper.getPreference(context, RssiExtendConstants.RSSI_PROTOCOL, 0);
        Log.i(TAG, "RssiReadStart:     " + Protocol);
        byte flashType = (byte) BluetoothConstants.Connect.CMD_CONFIRM;
        if (Protocol == 0){
            CmdInfo flashreadcmdInfo = new CmdInfo(RssiExtendConstants.NEW_OP_TOTA_RSSI_READ_CMD, new byte[]{flashType});
            return flashreadcmdInfo.toBytes();
        } else if (Protocol == 1){
            CmdInfo flashreadcmdInfo = new CmdInfo(RssiExtendConstants.OP_TOTA_RSSI_READ_CMD, new byte[]{flashType});
            return flashreadcmdInfo.toBytes();
        }
        CmdInfo flashreadcmdInfo = new CmdInfo(RssiExtendConstants.OLD_OP_TOTA_RSSI_READ_CMD, new byte[]{flashType});
        return flashreadcmdInfo.toBytes();

    }



    public static String receiveData(byte[] data, Context context) {
        if (data.length < 10) {
            return "";
        }
        String retleft = "";
        String retright = "";
        int max_rssi = 0;
        int min_rssi = 0;
        String ret = "";
        int agc = 0 ;
        int rssi = 0;
        int flag = 0;
        int ser = 0;
        String extra_data = "";
        int fa_gain = 0;
        int L_buffer_size = 0;
        int R_buffer_size = 0;
        byte sample_ref_data[] = new byte[4];
        float sample_ref = 0;
        byte mobile_diff_us_data[] = new byte[4];
        int mobile_diff_us = 0;
        byte tws_diff_us_data[] = new byte[4];
        int tws_diff_us = 0;
        int L_error_sum = 0;
        int L_Total_sum = 0;
        int R_error_sum = 0;
        int R_Total_sum = 0;
        byte L_error_sum_data[] = new byte[2];
        byte L_Total_sum_data[] = new byte[2];
        byte R_error_sum_data[] = new byte[2];
        byte R_Total_sum_data[] = new byte[2];
        byte[] info = new byte[data.length-4];
        Log.i(TAG, "receiveData: rssistart" + ArrayUtil.toHex(data));
        System.arraycopy(data,4,info,0,data.length-4);

        Log.i(TAG, "receiveData: info" + ArrayUtil.toHex(info));
        if (data.length > 4) {
            for (int i = 0; i < (data.length - 4); i ++) {
                info[i] = data[4 + i];
            }
        }

        if((info == null)|| (info.length == 0)) {
            return "no recv data";
        } else if (info[0] == 0x0b && (info[1]&0xff) == 0x90 ) {
            agc = (int)info[6]&0xff;
            rssi = ArrayUtil.getRssiValue(info[7]);
            Log.e("agc" + agc,"rssi"+ rssi+"");
            max_rssi = ArrayUtil.getRssiValue(info[8]);
            min_rssi = ArrayUtil.getRssiValue(info[9]);
            ret = "AGC:"+agc+";"+"RSSI:"+rssi+";"+"max_rssi:"+max_rssi+";"+"min_rssi:"+min_rssi+";";
            agc = (int)info[10]&0xff;
            rssi = ArrayUtil.getRssiValue(info[11]);
            Log.e("agc" + agc,"rssi"+ rssi+"");
            max_rssi = ArrayUtil.getRssiValue(info[12]);
            min_rssi = ArrayUtil.getRssiValue(info[13]);
            ser = ArrayUtil.getRssiValue(info[14]);
            ret = ret + "AGC:"+agc+";"+"RSSI:"+rssi+";"+"max_rssi:"+max_rssi+";"+"min_rssi:"+min_rssi+";"+"PACKET LOSS RATE(%):"+ser+";";
            agc = (int)info[15]&0xff;
            rssi = ArrayUtil.getRssiValue(info[16]);
            Log.e("agc" + agc,"rssi"+ rssi+"");
            max_rssi = ArrayUtil.getRssiValue(info[17]);
            min_rssi = ArrayUtil.getRssiValue(info[18]);
            ret = ret + "AGC:"+agc+";"+"RSSI:"+rssi+";"+"max_rssi:"+max_rssi+";"+"min_rssi:"+min_rssi+";";
            agc = (int)info[19]&0xff;
            rssi = ArrayUtil.getRssiValue(info[20]);
            Log.e("agc" + agc,"rssi"+ rssi+"");
            max_rssi = ArrayUtil.getRssiValue(info[21]);
            min_rssi = ArrayUtil.getRssiValue(info[22]);
            ser = ArrayUtil.getRssiValue(info[23]);
            ret = ret + "AGC:"+agc+";"+"RSSI:"+rssi+";"+"max_rssi:"+max_rssi+";"+"min_rssi:"+min_rssi+";"+"PACKET LOSS RATE(%):"+ser+";";
            flag = (int)info[24]&0xff;
            ret = ret + "flag:"+flag+";";
            fa_gain = (int)info[25]&0xff;
            L_buffer_size = (int)info[26]&0xff;
            R_buffer_size = (int)info[27]&0xff;
            sample_ref_data[0] = info[28];
            sample_ref_data[1] = info[29];
            sample_ref_data[2] = info[30];
            sample_ref_data[3] = info[31];
            sample_ref = ArrayUtil.byte2float(sample_ref_data);
            mobile_diff_us_data[0] = info[32];
            mobile_diff_us_data[1] = info[33];
            mobile_diff_us_data[2] = info[34];
            mobile_diff_us_data[3] = info[35];
            mobile_diff_us = ArrayUtil.bytesToIntLittle(mobile_diff_us_data);
            tws_diff_us_data[0] = info[36];
            tws_diff_us_data[1] = info[37];
            tws_diff_us_data[2] = info[38];
            tws_diff_us_data[3] = info[39];
            tws_diff_us = ArrayUtil.bytesToIntLittle(tws_diff_us_data);
            L_error_sum_data[0] = info[40];
            L_error_sum_data[1] = info[41];
            L_error_sum = ArrayUtil.bytesToShortLittle(L_error_sum_data);
            L_Total_sum_data[0] = info[42];
            L_Total_sum_data[1] = info[43];
            L_Total_sum= ArrayUtil.bytesToShortLittle(L_Total_sum_data);
            R_error_sum_data[0] = info[44];
            R_error_sum_data[1] = info[45];
            R_error_sum= ArrayUtil.bytesToShortLittle(R_error_sum_data);
            R_Total_sum_data[0] = info[46];
            R_Total_sum_data[1] = info[47];
            R_Total_sum= ArrayUtil.bytesToShortLittle(R_Total_sum_data);
            ret = ret + "extra data:"+"\n"+"fa gain = "+fa_gain+"\n"+" L_buffer_size = "+L_buffer_size
                    +"\n" + " R_buffer_size = "+R_buffer_size+"\n"+" sample_ref = "
                    + sample_ref+"\n"+" mobile_diff_us =  "+mobile_diff_us+"\n"+" tws_diff_us = "+tws_diff_us
                    +"\n"+" L_error_sum = "+ L_error_sum +"\n"+ " L_Total_sum " +L_Total_sum
                    +"\n"+" R_error_sum =" +R_error_sum+"\n"+
                    " R_Total_sum = " + R_Total_sum;
            byte[] rssidata = new byte[42];
            System.arraycopy(info,6, rssidata,  0,  42);
            saveData(rssidata, context);
            rssiinfo = ret;
            return ret;
        }
        return "tota error";
    }

    private static void saveData(byte[] data, Context context) {
        FileUtils fileUtils = new FileUtils(context);
        if (startTime.length() == 0) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss");
            Date date = new Date(System.currentTimeMillis());
            startTime = simpleDateFormat.format(date);
        }
        String rssianalysedata = startTime+rssiinfo+"\n";
        rssianalysedata = rssianalysedata.replace("AGC:",",");
        rssianalysedata = rssianalysedata.replace("RSSI:",",");
        rssianalysedata = rssianalysedata.replace("min_rssi:",",");
        rssianalysedata = rssianalysedata.replace("max_rssi:",",");
        rssianalysedata = rssianalysedata.replace(";","");
        rssianalysedata = rssianalysedata.replace("PACKET LOSS RATE(%):",",");
        rssianalysedata = rssianalysedata.replace("flag:",",");
        rssianalysedata = rssianalysedata.replace("extra data:\n",",");
        fileUtils.RssiextendAnalyseFile(rssianalysedata,RssiExtendConstants.RSSI_SAVE_FOLDER,startTime+"_rssi_analyse.csv", startTime+"_rssi_analyse.csv");
        String rssidata = (startTime + rssiinfo.replace(";"," ")+"\n");
        fileUtils.HandleFileReport(rssidata,RssiExtendConstants.RSSI_SAVE_FOLDER,startTime + "_rssi.txt",startTime + "_rssi.txt" );
    }


//    public static String showlog(byte[] data){
//        String ret = "";
//        if (data.length > 0) {
//            ret = "OnReceived: " +startTime + ArrayUtil.toHex(data) + "\n" + ret;
//        }
//        return ret;
//    }
}