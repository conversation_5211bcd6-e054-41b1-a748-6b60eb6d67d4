package com.besall.allbase.view.activity.chipstoollevel4.AvsLwa;

import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LANGUAGE_DIDSELECT_KEY;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LANGUAGE_SELECT;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.annotation.UiThread;
import androidx.appcompat.app.AppCompatActivity;

import com.besall.allbase.R;
import java.util.ArrayList;
import java.util.Map;

public class AvsLwaLanguageSelectActivity extends AppCompatActivity implements View.OnClickListener, AdapterView.OnItemClickListener {

    private ArrayList<String> languages;
    private Button language_select_cancel;
    private Button language_select_save;
    private ListView language_listview;
    private LanguageSelectAdapter mAdapter;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_avslwa_language_select);

        languages = getIntent().getStringArrayListExtra(AVS_LANGUAGE_SELECT);

        language_select_cancel = (Button) findViewById(R.id.language_select_cancel);
        language_select_cancel.setOnClickListener(this);
        language_select_save = (Button) findViewById(R.id.language_select_save);
        language_select_save.setOnClickListener(this);
        language_listview = (ListView) findViewById(R.id.language_listview);

        mAdapter = new LanguageSelectAdapter(this, languages, 0);
        language_listview.setAdapter(mAdapter);
        language_listview.setOnItemClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.language_select_cancel:
                Intent intent = new Intent();
                setResult(RESULT_CANCELED, intent);
                finish();
                break;
            case R.id.language_select_save:
                Intent intent1 = new Intent();
                intent1.putExtra(AVS_LANGUAGE_DIDSELECT_KEY, mAdapter.getCurSelectItem());
                setResult(RESULT_OK, intent1);
                finish();
                break;
            default:
                break;
        }
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        mAdapter.didSelectItem(position);
    }

    public class LanguageSelectAdapter extends BaseAdapter {

        private Context mContext;
        private ArrayList<String> mData;
        private boolean[] mIsSelect;

        public LanguageSelectAdapter(Context context, ArrayList<String> list, int defaultIndex) {
            mContext = context;
            mData = list;
            mIsSelect = new boolean[mData.size()];
            mIsSelect[defaultIndex] = true;
        }

        @Override
        public int getCount() {
            return mData.size();
        }

        @Override
        public Object getItem(int position) {
            return mData.get(position);
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            ViewHolder holder;
            if (convertView == null) {
                convertView = LayoutInflater.from(mContext).inflate(R.layout.language_select_item, parent, false);
                holder = new ViewHolder();
                holder.language = (TextView) convertView.findViewById(R.id.language);
                holder.language_select_image = (ImageView) convertView.findViewById(R.id.language_select_image);
                convertView.setTag(holder);
            } else {
                holder = (ViewHolder) convertView.getTag();
            }
            holder.language_select_image.setImageDrawable(mIsSelect[position] ? getDrawable(R.drawable.ota_top_sele) : getDrawable(R.drawable.ota_top_nor));
            holder.language.setText(mData.get(position));
            return convertView;
        }

        @UiThread
        public void didSelectItem(int position) {
            mIsSelect = new boolean[mData.size()];
            mIsSelect[position] = true;
            notifyDataSetChanged();
        }

        @UiThread
        public int getCurSelectItem() {
            for (int i = 0; i < mIsSelect.length; i ++) {
                Log.i("TAG", "getCurSelectItem: ------" + i);
                if (mIsSelect[i]) {
                    return i;
                }
            }
            return 0;
        }

        public class ViewHolder {
            TextView language;
            ImageView language_select_image;
        }
    }
}
