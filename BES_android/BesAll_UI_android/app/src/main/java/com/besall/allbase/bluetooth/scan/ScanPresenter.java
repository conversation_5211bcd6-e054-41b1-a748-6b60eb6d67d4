package com.besall.allbase.bluetooth.scan;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.os.Build;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bes.bessdk.connect.UsbConnector;
import com.bes.bessdk.scan.BesScanManager;
import com.bes.sdk.scan.ScanManager;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.common.manager.PermissionManager;
import com.besall.allbase.view.base.BasePresenter;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;


public class ScanPresenter extends BasePresenter<IScanActivity> implements IScanPresenter{

    private final String TAG = getClass().getSimpleName();

    private ScanManager mScanner;
    private Collection mCollection;
    private int onGrantedCount = 0;

    @Override
    public void starScanWithScanType(Context context, DeviceProtocol deviceProtocol) {
        if (deviceProtocol == DeviceProtocol.PROTOCOL_USB) {
            UsbConnector.scanDevice(context, (ScanManager.ScanListener) context);
            return;
        }

        String[] permissions = new String[]{};
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            permissions = new String[]{Manifest.permission.BLUETOOTH_SCAN, Manifest.permission.BLUETOOTH_ADVERTISE, Manifest.permission.BLUETOOTH_CONNECT};
        } else {
            permissions = new String[]{Manifest.permission.ACCESS_COARSE_LOCATION, Manifest.permission.ACCESS_FINE_LOCATION};
        }

        PermissionManager.getInstance().requestPermissions((Activity) context, new PermissionManager.PermissionUtilListener() {
            @Override
            public void onGranted() {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                } else {
                    onGrantedCount ++;
                    if (onGrantedCount < 2) {
                        return;
                    }
                }
                if (mCollection != null) {
                    return;
                }
                mCollection = new Collection() {
                    @Override
                    public int size() {
                        return 1;
                    }

                    @Override
                    public boolean isEmpty() {
                        return false;
                    }

                    @Override
                    public boolean contains(@Nullable Object o) {
                        if (deviceProtocol == DeviceProtocol.PROTOCOL_BLE) {
                            if (o == DeviceProtocol.PROTOCOL_BLE) {
                                return true;
                            }
                        } else {
                            if (o == DeviceProtocol.PROTOCOL_SPP) {
                                return true;
                            }
                        }
                        return false;
                    }

                    @NonNull
                    @Override
                    public Iterator iterator() {
                        return null;
                    }

                    @NonNull
                    @Override
                    public Object[] toArray() {
                        return new Object[0];
                    }

                    @NonNull
                    @Override
                    public Object[] toArray(@NonNull Object[] a) {
                        return new Object[0];
                    }

                    @Override
                    public boolean add(Object o) {
                        return false;
                    }

                    @Override
                    public boolean remove(@Nullable Object o) {
                        return false;
                    }

                    @Override
                    public boolean containsAll(@NonNull Collection c) {
                        return false;
                    }

                    @Override
                    public boolean addAll(@NonNull Collection c) {
                        return false;
                    }

                    @Override
                    public boolean removeAll(@NonNull Collection c) {
                        return false;
                    }

                    @Override
                    public boolean retainAll(@NonNull Collection c) {
                        return false;
                    }

                    @Override
                    public void clear() {

                    }
                };
                if (mScanner != null) {
                    mScanner.stopScan(mCollection);
                    mScanner = null;
                }
                mScanner = new BesScanManager(context);
                mScanner.startScan(mCollection, (ScanManager.ScanListener)context, null);
            }

            @Override
            public void onUngranted(String msg) {
            }

            @Override
            public void onError(String msg) {
            }
        }, permissions);

    }

    @Override
    public void stopScanWithScanType(Context context, DeviceProtocol deviceProtocol) {
        if (mScanner != null) {
            mScanner.stopScan(mCollection);
        }
    }


}
