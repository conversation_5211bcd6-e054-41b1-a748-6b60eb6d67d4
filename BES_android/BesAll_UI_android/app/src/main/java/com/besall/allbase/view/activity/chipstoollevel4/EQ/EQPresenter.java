package com.besall.allbase.view.activity.chipstoollevel4.EQ;

import android.content.Context;
import android.content.Intent;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;
import com.besall.allbase.bluetooth.service.EQ.EQConstants;
import com.besall.allbase.bluetooth.service.EQ.EQService;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.activity.tools.FileActivity.CustomCmdFilelistActivity;
import com.besall.allbase.view.base.BasePresenter;

import java.util.ArrayList;

import static com.besall.allbase.common.Constants.FILE_CODE;

/**
 * <AUTHOR>
 * @time $ $
 */
class EQPresenter extends BasePresenter<IEQActivity> implements IEQPresenter {

    private EQService eqService;

    @Override
    public void pickDecice(EQActivity context, int scan) {
        Intent intent = new Intent();
        intent.putExtra(BluetoothConstants.Scan.BES_SCAN, scan);
        ActivityUtils.gotoActForResult(intent, BluetoothConstants.Scan.REQUEST_CODE_SCAN, context, ScanActivity.class);
    }

    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        eqService = new EQService(serviceConfig, listener, context);
    }

    @Override
    public void eqSet(int prefix, String name, float gain0, float gain1, ArrayList<EQListBeans> eqList) {
        eqService.eqSet(prefix, name, gain0, gain1, eqList);
    }

    @Override
    public void importfile(EQActivity context) {
        Intent intent = new Intent();
        intent.putExtra(FILE_CODE + "", EQConstants.EQ_SAVE_DATA);
        ActivityUtils.gotoActForResult(intent, FILE_CODE, context, CustomCmdFilelistActivity.class);
    }


}
