package com.besall.allbase.bluetooth.service.watchavs;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_ERROR;
import static com.besall.allbase.bluetooth.service.watchavs.WatchAvsConstants.MSG_WATCH_AVS_SEND_AUDIO_DATA;
import static com.besall.allbase.bluetooth.service.watchavs.WatchAvsConstants.OP_TOTA_ALEXA_AUDIO_DATA;
import static com.besall.allbase.bluetooth.service.watchavs.WatchAvsConstants.OP_TOTA_ALEXA_TTS_DATA;
import static com.besall.allbase.bluetooth.service.watchavs.WatchAvsConstants.WATCH_ALEXA_STATE_CHANGED;
import static com.besall.allbase.bluetooth.service.watchavs.WatchAvsConstants.WATCH_AVS_PARSE_ERROR;
import static com.besall.allbase.bluetooth.service.watchavs.WatchAvsConstants.WATCH_AVS_RECEIVE_ALEXA_AUDIO_DATA;
import static com.besall.allbase.bluetooth.service.watchavs.WatchAvsConstants.WATCH_AVS_RECEIVE_DATA;
import static com.besall.allbase.bluetooth.service.watchavs.WatchAvsConstants.WATCH_AVS_RECEIVE_ALEXA_CMD_CTL;
import static com.besall.allbase.common.manager.AlexaHttpManager.ALEXA_STATE_LOGIN_SUCCESS;
import static com.besall.allbase.common.manager.AlexaHttpManager.ALEXA_STATE_NETWORK_DISCONNECT;
import static com.besall.allbase.common.manager.AlexaHttpManager.ALEXA_STATE_NETWORK_RECONNECT;
import static com.besall.allbase.common.manager.AlexaHttpManager.ALEXA_STATE_RECEIVE_DIRECTIVES_SPEAK;
import static com.besall.allbase.common.manager.AlexaHttpManager.ALEXA_STATE_RECEIVE_TEXT;
import static com.besall.allbase.common.manager.AlexaHttpManager.ALEXA_STATE_SPEECH_START;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;

import androidx.annotation.NonNull;

import com.amazon.alexa.accessory.protocol.Accessories;
import com.amazon.alexa.accessory.protocol.Common;
import com.bes.bessdk.service.base.BesBaseService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.message.BaseMessage;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.bluetooth.service.log_dump.LogDumpConstants;
import com.besall.allbase.common.manager.AlexaHttpManager;
import com.besall.allbase.common.opus.OpusDecoder;
import com.besall.allbase.common.utils.FileUtils;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.text.SimpleDateFormat;
import java.util.Date;

public class WatchAvsService extends BesBaseService implements AlexaHttpManager.AlexaHttpListener {

    private AlexaHttpManager alexaHttpManager;
    private OpusDecoder opusDecoder;

    private short curCmd;
    private byte[] curTotalData;
    private int curSendLength;
    private int curMtu = 664 / 16 * 16 - 16 - 4;
    private String startTime = "";

    public WatchAvsService(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        super(serviceConfig, listener, context);
        alexaHttpManager = AlexaHttpManager.getHttpManager(context, this);
        OpusDecoder.init(-1, 160, 320, -1);
        opusDecoder = new OpusDecoder();
        startConnect(serviceConfig);
    }

    @Override
    public void onStatusChanged(HmDevice device, int status, DeviceProtocol protocol) {
        super.onStatusChanged(device, status, protocol);
        if (status == BES_CONNECT_ERROR) {
            callBackStateChangedMessage(BES_CONNECT_ERROR,"error");
        }
    }

    @Override
    public void callBackTotaConnectState(boolean state) {
        super.callBackTotaConnectState(state);
        if (state == true) {
            sendData(WatchAvsCMD.getConnectStateCmd(alexaHttpManager.curAlexaState == ALEXA_STATE_LOGIN_SUCCESS));
        }
    }

    @Override
    public void onDataReceived(BaseMessage deviceMessage) {
        super.onDataReceived(deviceMessage);
        if (mConfig.getTotaConnect() && !totauccess) {
            return;
        }

//        callBackStateChangedMessage(WATCH_AVS_RECEIVE_DATA, "after decode:" + ArrayUtil.toHex((byte[]) deviceMessage.getMsgContent()));

        Log.i(TAG, "onDataReceived: ---------"  + ArrayUtil.toHex((byte[]) deviceMessage.getMsgContent()));
        int result = WatchAvsCMD.receiveData((byte[]) deviceMessage.getMsgContent());
        callBackStateChangedMessage(WATCH_AVS_RECEIVE_DATA, "result--------" + result);
        Log.i(TAG, "onDataReceived: ---------"  + result);
        if (result == WATCH_AVS_RECEIVE_ALEXA_AUDIO_DATA) {
//            callBackStateChangedMessage(WATCH_AVS_RECEIVE_DATA, "addData----------");
//            callBackStateChangedMessage(WATCH_AVS_RECEIVE_DATA, ArrayUtil.toHex(WatchAvsCMD.getAudioData()));
            byte[] audioData = WatchAvsCMD.getAudioData();
            FileUtils.writeTOFile(audioData, "WatchAVS",startTime + "_opus","opus");
            byte[] decodeData = opusDecoder.decodeAll(audioData);
            FileUtils.writeTOFile(audioData, "WatchAVS",startTime + "_pcm","pcm");
//            callBackStateChangedMessage(WATCH_AVS_RECEIVE_DATA, "decodeData----------" + (decodeData == null ? "null" : decodeData.length));
            alexaHttpManager.addData(decodeData);
        } else if (result == WATCH_AVS_RECEIVE_ALEXA_CMD_CTL) {
            Accessories.ControlEnvelope controlEnvelope = WatchAvsCMD.getmControlEnvelope();
            if (controlEnvelope.getCommand() == Accessories.Command.START_SPEECH) {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss");
                Date date = new Date(System.currentTimeMillis());
                startTime = simpleDateFormat.format(date);

                alexaHttpManager.ready();
                sendData(WatchAvsCMD.getResponseCmd(Accessories.Command.START_SPEECH, Common.ErrorCode.SUCCESS));
            } else if (controlEnvelope.getCommand() == Accessories.Command.STOP_SPEECH) {
//                sendData(WatchAvsCMD.getResponseCmd(Accessories.Command.STOP_SPEECH, Common.ErrorCode.SUCCESS));
                alexaHttpManager.stopRecording();
            }
        } else if (result == WATCH_AVS_PARSE_ERROR) {
//            callBackStateChangedMessage(WATCH_AVS_RECEIVE_DATA, "data parse error");
        }
    }

    @Override
    public void onAlexaStateChange(int state, Object data) {
        callBackStateChangedMessage(WATCH_AVS_RECEIVE_DATA, "onAlexaStateChange----------" + state);
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                if (state == ALEXA_STATE_RECEIVE_DIRECTIVES_SPEAK) {
                    sendData(WatchAvsCMD.getStopSpeechCommandCmd(Accessories.Command.STOP_SPEECH, Common.ErrorCode.SUCCESS));
                } else if (state == ALEXA_STATE_SPEECH_START) {
//                    curAudioData = data;
//                    curSendLength = 0;
//                    sendAudioData();
                } else if (state == ALEXA_STATE_RECEIVE_TEXT) {
                    Log.i(TAG, "runtts: ---------" + data);
                    String content = (String) data;
                    curTotalData = ArrayUtil.toBytes(ArrayUtil.str2HexStr(content));
                    curSendLength = 0;
                    curCmd = OP_TOTA_ALEXA_TTS_DATA;
                    sendAudioData();
                } else if (state == ALEXA_STATE_NETWORK_DISCONNECT) {
                    sendData(WatchAvsCMD.getConnectStateCmd(false));
                } else if (state == ALEXA_STATE_NETWORK_RECONNECT) {
                    sendData(WatchAvsCMD.getConnectStateCmd(true));
                }
            }
        });
        callBackStateChangedMessage(WATCH_ALEXA_STATE_CHANGED, "" + state);
    }

    private void sendAudioData() {
        watchAvsSendDataDelay(MSG_WATCH_AVS_SEND_AUDIO_DATA, 15);
    }

    private void watchAvsSendDataDelay(int what, long millis) {
        sendDataDelay(besWatchAvsMsgHandler, what, millis);
    }

    private Handler besWatchAvsMsgHandler = new Handler() {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            LOG(TAG, "besWatchAvsMsgHandler-----");
            switch (msg.what) {
                case MSG_WATCH_AVS_SEND_AUDIO_DATA:
                    int l = curMtu;
                    boolean needContinue = true;
                    if (curSendLength + curMtu >= curTotalData.length) {
                        l = curTotalData.length - curSendLength;
                        needContinue = false;
                    }
                    byte[] curData = new byte[l];
                    System.arraycopy(curTotalData, curSendLength, curData, 0, l);
                    sendDataWithoutResponse(WatchAvsCMD.getTransferDataCmd(curData, curCmd));
                    if (needContinue) {
                        curSendLength +=l;
                        sendAudioData();
                    }
                    break;

                default:
                    break;
            }
        }
    };

}
