package com.besall.allbase.bluetooth.service.log_dump;

import android.content.Context;
import android.util.Log;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.CmdInfo;
import com.bes.bessdk.utils.SPHelper;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.common.utils.FileUtils;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

import static com.bes.bessdk.utils.ArrayUtil.getFloat;
import static com.bes.bessdk.utils.ArrayUtil.int2byte;

public class LogDumpCMD {

    static byte[] flashdatainfo = new byte[4];
    static byte[] flashtotallength = new byte[4];
    static int packagelength = 550;
    static byte[] curlength = int2byte(640);
    static byte[] curaddress = new byte[4];
    private static int dumpTimes = 0;

    static String startTime = "";

    public static void init() {
        flashdatainfo = new byte[4];
        flashtotallength = new byte[4];
        packagelength = 550;
        curlength = int2byte(640);
        curaddress = new byte[4];
        dumpTimes = 0;
        startTime = "";
    }

    public static byte[] logDumpFlashRead(Context context) {
        byte flashType = (byte) BluetoothConstants.Connect.CMD_CONFIRM;
        boolean useTotaV2 = (boolean) SPHelper.getPreference(context, BesSdkConstants.BES_TOTA_USE_TOTAV2, BesSdkConstants.BES_TOTA_USE_TOTAV2_VALUE);
        CmdInfo flashreadcmdInfo = new CmdInfo(useTotaV2 ? LogDumpConstants.OP_TOTA_READ_FLASH_CMD : LogDumpConstants.OP_TOTA_READ_FLASH_CMD_OLD, new byte[]{flashType});
        return flashreadcmdInfo.toBytes();
    }

    public static byte[] logDumpStart() {
        byte[] flashcmd = new byte[6];
        System.arraycopy(curaddress,0, flashcmd,0,4);
        System.arraycopy(int2byte(packagelength),0, flashcmd,4,2);
        CmdInfo logdumpcmd = new CmdInfo(LogDumpConstants.OP_TOTA_GET_DUMP_INFO, flashcmd);
        return logdumpcmd.toBytes();
    }

    public static byte[] logDumpLastPackage() {
        byte[] finalcmd = new byte[6];
        System.arraycopy(curaddress, 0, finalcmd,  0,  4);
        System.arraycopy(curlength, 0, finalcmd, 4,  2);
        CmdInfo flogdumpcmd = new CmdInfo(LogDumpConstants.OP_TOTA_READ_FLASH_CMD,finalcmd);
        return flogdumpcmd.toBytes();
    }

    public static byte[] logDumpStop() {
        byte stopType = (byte) BluetoothConstants.Connect.CMD_CONFIRM;
        CmdInfo stopcmdInfo = new CmdInfo(LogDumpConstants.OP_TOTA_AUDIO_DUMP_STOP, new byte[]{stopType});
        return stopcmdInfo.toBytes();
    }

    public static byte[] logDumpFinish() {
        byte finishcmd = (byte) BluetoothConstants.Connect.CMD_CONFIRM;
        CmdInfo flogdumpcmd = new CmdInfo(LogDumpConstants.OP_TOTA_GET_DUMP_FINISH,new byte[]{finishcmd});
        return flogdumpcmd.toBytes();
    }

    public static int receiveData(byte[] data, Context context) {
        Log.i("TAG", "logDump receive: ----" + ArrayUtil.toHex(data));
        if (data.length < 10 ) {
            return 0;
        }
        boolean useTotaV2 = (boolean) SPHelper.getPreference(context, BesSdkConstants.BES_TOTA_USE_TOTAV2, BesSdkConstants.BES_TOTA_USE_TOTAV2_VALUE);
        byte[] info = new byte[data.length - 4];
        if (data.length < 5 ) {
            return 0;
        }
        for (int i = 0; i < (data.length - 4); i ++) {
            info[i] = data[4 + i];
        }

        System.arraycopy(info, 6, flashdatainfo, 0, 4);
        LOG("flashdatainfo:" + ArrayUtil.toHex(flashdatainfo));
        if (info[0] == (useTotaV2 ? 0x07 : 0x0a) && (info[1] & 0xff) == 0x63 ) {
            System.arraycopy(info, 10, flashtotallength, 0, 4);
            curaddress = int2byte(getFloat(flashdatainfo));
            LOG("curaddress:" + ArrayUtil.toHex(curaddress));
            return LogDumpConstants.LOGDUMP_START;
        } else if (info[0] == 0x02 && (info[1] & 0xff) == 0x62 ) {
            dumpTimes ++;
            LOG("dumpTimes:" + dumpTimes);

            int totallength = (int) getFloat(flashtotallength);
            if ((totallength - packagelength * dumpTimes) > packagelength) {
                curlength = int2byte((getFloat(curlength) * dumpTimes));
                curaddress = int2byte(getFloat(flashdatainfo) + packagelength);
                byte[] logdata = new byte[packagelength];
                System.arraycopy(info, 12, logdata, 0, packagelength);
                saveData(logdata, context);
                LOG("curlength:" + curlength);
                LOG("curaddress:" + curaddress);
                return LogDumpConstants.LOGDUMP_START;
            } else if (((totallength - packagelength * dumpTimes) < packagelength && (totallength - packagelength * dumpTimes) > 0) || ((totallength - packagelength * dumpTimes) == packagelength)) {
                curlength = int2byte(totallength - dumpTimes * packagelength);
                curaddress = int2byte(getFloat(flashdatainfo) + (totallength - packagelength * dumpTimes));
                byte[] logdata = new byte[packagelength];
                System.arraycopy(info, 12, logdata, 0, packagelength);
                saveData(logdata, context);
                LOG("curlength:" + curlength);
                LOG("curaddress:" + curaddress);
                return LogDumpConstants.LOGDUMP_LAST_PACKAGE;
            } else if ((totallength - packagelength * dumpTimes) < 0 || (totallength - packagelength * dumpTimes) == 0) {
                byte[] lastlogdata = new byte[totallength - packagelength * (dumpTimes - 1)];
                System.arraycopy(info, 12, lastlogdata, 0, totallength - packagelength * (dumpTimes - 1));
                saveData(lastlogdata, context);
                startTime = "";
                LOG("curlength:" + curlength);
                LOG("curaddress:" + curaddress);
                return LogDumpConstants.LOGDUMP_FINISH;
            }
        }
        return 0;
    }

    private static void saveData(byte[] data, Context context) {
//        FileUtils fileUtils = new FileUtils(context);
        if (startTime.length() == 0) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss");
            Date date = new Date(System.currentTimeMillis());
            startTime = simpleDateFormat.format(date);
        }
//        fileUtils.saveBytesToFile(startTime + ".txt", LogDumpConstants.LOGDUMP_SAVE_FOLDER, data);
        FileUtils.writeTOFile(data,LogDumpConstants.LOGDUMP_SAVE_FOLDER,startTime + "_logdump","txt");
    }

    public static String logDumpProgress() {
        int total = (int) getFloat(flashtotallength);
        if (total == 0) {
            return "0.00";
        }
        Log.i("TAG", "total: -------" + total);
        Log.i("TAG", "dumpTimes: -------" + dumpTimes);
        double v1 = packagelength * dumpTimes * 100;
        if (packagelength * dumpTimes > total) {
            v1 = total * 100;
        }
        return ArrayUtil.div(v1, total, 2) + "";
    }

    public static int logDumpTootalSize() {
        int total = (int) getFloat(flashtotallength);
        return total;
    }

    private static void LOG(String msg) {
        FileUtils.writeTOFile(msg, "LogDump", "log", "txt");
    }
}