package com.besall.allbase.bluetooth.service.EQ;

import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.CmdInfo;
import com.besall.allbase.view.activity.chipstoollevel4.EQ.EQListBeans;

import java.util.ArrayList;


public class EQCMD {

    public static byte[] getEqSetCMD(int prefix, String name, float gain0, float gain1, ArrayList<EQListBeans> eqList) {
        int num = 0;
        byte[] params = new byte[0];
        for (int i = 0; i < eqList.size(); i ++) {
            EQListBeans eqListBeans = eqList.get(i);
            if (eqListBeans.checked) {
                int type = eqListBeans.getEnableFilterType();
                byte[] typeB = ArrayUtil.int2byte(type);

                String gainStr = eqListBeans.getGain().length() == 0 ? "0" : eqListBeans.getGain();
                float gainF = Float.parseFloat(gainStr);
                byte[] gainB = ArrayUtil.float2byte(gainF);

                String fcStr = eqListBeans.getFreq().length() == 0 ? "0" : eqListBeans.getFreq();
                float fcF = Float.parseFloat(fcStr);
                byte[] fcB = ArrayUtil.float2byte(fcF);

                String qStr = eqListBeans.getQ().length() == 0 ? "0" : eqListBeans.getQ();
                float qF = Float.parseFloat(qStr);
                byte[] qB = ArrayUtil.float2byte(qF);

                byte[] param = ArrayUtil.bytesSplic(typeB, gainB, fcB, qB);

                num ++;
                params = ArrayUtil.byteMerger(params, param);
            } else {
                byte[] fc = new byte[]{(byte) 0xcc, (byte) 0xcc, (byte) 0xcc, (byte) 0xcc};
                byte[] param = ArrayUtil.bytesSplic(fc, fc, fc, fc);
                params = ArrayUtil.byteMerger(params, param);
            }
        }

        byte[] prefixB = ArrayUtil.intToBytesLittle(prefix);
        byte[] crc = new byte[]{(byte) 0xff, (byte) 0xff, (byte) 0xff, (byte) 0xff};

        String name16 = ArrayUtil.str2HexStr(name);
        byte[] name16B = ArrayUtil.toBytes(name16);
        byte[] nameB = new byte[12];
        System.arraycopy(name16B, 0, nameB, 0, name16B.length);

        byte[] gain0B = ArrayUtil.float2byte(gain0);
        byte[] gain1B = ArrayUtil.float2byte(gain1);

        byte[] numB = ArrayUtil.intToBytesLittle(num);

        byte[] len = ArrayUtil.intToBytesLittle(gain0B.length + gain1B.length + numB.length + params.length);

        byte[] eqSetData = ArrayUtil.byteMerger(ArrayUtil.bytesSplic(prefixB, crc, nameB, len), ArrayUtil.bytesSplic(gain0B, gain1B, numB, params));
        CmdInfo eqSet = new CmdInfo(EQConstants.OP_TOTA_EQ_SET_CMD, eqSetData);
        return eqSet.toBytes();
    }


}
