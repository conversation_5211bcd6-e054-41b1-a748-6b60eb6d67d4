package com.besall.allbase.bluetooth.service.avslwa;

import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_GET_WHOLE_SETTING_DATA;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_RECEIVE_LWA_CONNECT_FAIL;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_RECEIVE_LWA_CONNECT_SUCCESS;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_RECEIVE_LWA_STATE_CONNECT;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_RECEIVE_LWA_STATE_OFLINE;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_RECEIVE_PRODUCTID;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_RECEIVE_WIFI_STATE_CONNECT;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_RECEIVE_WIFI_STATE_CONNECT_FAIL;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_RECEIVE_WIFI_STATE_CONNECT_SUCCESS;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_RECEIVE_WIFI_STATE_OFLINE;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_SET_ERROR;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_SET_SUCCESS;

import android.content.Context;
import android.util.Log;

import com.bes.bessdk.utils.ArrayUtil;

import java.util.HashMap;
import java.util.Map;

public class AvsLwaCMD {
    private static byte[] avsLinkSta = ArrayUtil.toBytes(ArrayUtil.str2HexStr("avslinksta"));
    private static byte[] wifiConfig = ArrayUtil.toBytes(ArrayUtil.str2HexStr("wificonfig"));
    private static byte[] avslwastat = ArrayUtil.toBytes(ArrayUtil.str2HexStr("avslwastat"));
    private static byte[] productsid = ArrayUtil.toBytes(ArrayUtil.str2HexStr("productsid"));
    private static byte[] authorcode = ArrayUtil.toBytes(ArrayUtil.str2HexStr("authorcode"));
    private static byte[] redirecUri = ArrayUtil.toBytes(ArrayUtil.str2HexStr("redirecUri"));
    private static byte[] clientidst = ArrayUtil.toBytes(ArrayUtil.str2HexStr("clientidst"));
    private static byte[] avssetting = ArrayUtil.toBytes(ArrayUtil.str2HexStr("avssetting"));
    private static String avssettingStr = "avssetting";
    private static String readStr = "1";
    private static String writeStr = "2";

    private static Map<String, String> mProductSid;
    private static String curType = "";
    private static String curValue = "";
    private static String oldValue = "0";
    private static String curWholeSettingData = "";


    public static byte[] getAvsLinkStaData() {
        return avsLinkSta;
    }

    public static byte[] getAvsLwaStateData() {
        return avslwastat;
    }

    public static byte[] getProductSidData() {
        return productsid;
    }

    public static byte[] getWifiData(String name, String psw) {
        byte[] bytes = new byte[wifiConfig.length + 32 + (psw.length() > 0 ? 65 : 0)];
        //将字符串转化为16进制
        String name16 = ArrayUtil.str2HexStr(name);

        for (int i = 0; i < wifiConfig.length; i ++) {
            bytes[i] = wifiConfig[i];
        }

        byte[] nameBytes = ArrayUtil.toBytes(name16);
        for (int i = 0; i < nameBytes.length; i ++) {
            bytes[i + wifiConfig.length] = nameBytes[i];
        }

        if (psw.length() > 0) {
            String pw16 = ArrayUtil.str2HexStr(psw);
            byte[] pwBytes = ArrayUtil.toBytes(pw16);
            for (int i = 0; i < pwBytes.length; i ++) {
                bytes[i + wifiConfig.length + 32] = pwBytes[i];
            }
        }
        return bytes;
    }

    public static byte[] getSetLanguageData(String language) {
        return new byte[]{0x00, (byte) 0xff, (byte) 0xff};
    }

    public static byte[] getCurSettingData(int type, int value) {
        oldValue = value + "";
        String typeStr = type + "";
        if (typeStr.length() == 1) {
            typeStr = "0" + typeStr;
        }
        return ArrayUtil.toBytes(ArrayUtil.str2HexStr(avssettingStr + writeStr + typeStr + value));
    }

    public static byte[] getWholeSettingData() {
        return ArrayUtil.toBytes(ArrayUtil.str2HexStr(avssettingStr  + readStr));
    }

    public static byte[] getAuthorizeResultData(String authorizationCode, String redirectUri, String clientId) {
        byte[] resultData = new byte[authorcode.length + 2 + authorizationCode.length() + redirecUri.length + 2 + redirectUri.length() + clientidst.length + 2 + clientId.length()];
        String authorizationCodeLengthStr = authorizationCode.length() + "";
        byte[] authorizationCodeLengthByte = ArrayUtil.toBytes(ArrayUtil.str2HexStr(authorizationCodeLengthStr.length() == 1 ? "0" : "" + authorizationCodeLengthStr));
        byte[] authorizationCodeByte = ArrayUtil.toBytes(ArrayUtil.str2HexStr(authorizationCode));
        String redirectUriLengthStr = redirectUri.length() + "";
        byte[] redirectUriLengthByte = ArrayUtil.toBytes(ArrayUtil.str2HexStr(redirectUriLengthStr.length() == 1 ? "0" : "" + redirectUriLengthStr));
        byte[] redirectUriByte = ArrayUtil.toBytes(ArrayUtil.str2HexStr(redirectUri));
        String clientIdStr = clientId.length() + "";
        byte[] clientIdLengthByte = ArrayUtil.toBytes(ArrayUtil.str2HexStr(clientIdStr.length() == 1 ? "0" : "" + clientIdStr));
        byte[] clientIdByte = ArrayUtil.toBytes(ArrayUtil.str2HexStr(clientId));
        Log.i("TAG", "authorizationCodeByte: ----" + ArrayUtil.toHex(authorizationCodeByte));
        Log.i("TAG", "redirectUriByte: ----" + ArrayUtil.toHex(redirectUriByte));
        Log.i("TAG", "clientIdByte: ----" + ArrayUtil.toHex(clientIdByte));
        System.arraycopy(authorcode, 0, resultData, 0, authorcode.length);
        System.arraycopy(authorizationCodeLengthByte, 0, resultData, authorcode.length, 2);
        System.arraycopy(authorizationCodeByte, 0, resultData, authorcode.length + 2, authorizationCodeByte.length);
        System.arraycopy(redirecUri, 0, resultData, authorcode.length + 2 + authorizationCode.length(), redirecUri.length);
        System.arraycopy(redirectUriLengthByte, 0, resultData, authorcode.length + 2 + authorizationCode.length() + redirecUri.length, 2);
        System.arraycopy(redirectUriByte, 0, resultData, authorcode.length + 2 + authorizationCode.length() + redirecUri.length + 2, redirectUriByte.length);
        System.arraycopy(clientidst, 0, resultData, authorcode.length + 2 + authorizationCode.length() + redirecUri.length + 2 + redirectUri.length(), clientidst.length);
        System.arraycopy(clientIdLengthByte, 0, resultData, authorcode.length + 2 + authorizationCode.length() + redirecUri.length + 2 + redirectUri.length() + clientidst.length, 2);
        System.arraycopy(clientIdByte, 0, resultData, authorcode.length + 2 + authorizationCode.length() + redirecUri.length + 2 + redirectUri.length() + clientidst.length + 2, clientIdByte.length);
        return resultData;
    }

    public static int receiveData(byte[] data, Context context) {
        Log.i("length", "receiveData: ----" + data.length);
        Log.i("length", "receiveData: ----" + ArrayUtil.toHex(data));

        if (data.length < 11) {
            return 0;
        }
        if (ArrayUtil.startsWith(data, avsLinkSta)) {
            Log.i("TAG", "receiveData: -----avsLinkSta");
            byte wifiState = data[avsLinkSta.length];
            if (wifiState == (byte) 0x37) {
                return AVS_LWA_RECEIVE_WIFI_STATE_CONNECT;
            } else {
                return AVS_LWA_RECEIVE_WIFI_STATE_OFLINE;
            }
        }
        else if (ArrayUtil.startsWith(data, wifiConfig)) {
            Log.i("TAG", "receiveData: -----wifiConfig");
            if (data.length > 11) {
                return 0;
            }
            byte wifiState = data[wifiConfig.length];
            if (wifiState == (byte) 0x37) {
                return AVS_LWA_RECEIVE_WIFI_STATE_CONNECT_SUCCESS;
            } else {
                return AVS_LWA_RECEIVE_WIFI_STATE_CONNECT_FAIL;
            }
        }
        else if (ArrayUtil.startsWith(data, avslwastat)) {
            Log.i("TAG", "receiveData: -----avslwastat");
            byte wifiState = data[avslwastat.length];
            if (wifiState == (byte) 0x30) {
                return AVS_LWA_RECEIVE_LWA_STATE_CONNECT;
            } else if (wifiState == (byte) 0x31) {
                return AVS_LWA_RECEIVE_LWA_STATE_OFLINE;
            }
        }
        else if (ArrayUtil.startsWith(data, productsid)) {
            mProductSid = new HashMap<>();
            Log.i("TAG", "receiveData: -----productsid");
            byte[] productIdByte = new byte[7];
            byte[] codeChallengeByte = new byte[43];
            byte[] productDsnByte = new byte[data.length - productsid.length - productIdByte.length - productsid.length - codeChallengeByte.length - productsid.length];
            System.arraycopy(data, productsid.length, productIdByte, 0, productIdByte.length);
            Log.i("TAG", "productIdByte: -----" + ArrayUtil.toHex(productIdByte));
            Log.i("TAG", "receiveData: -----" + ArrayUtil.toASCII(productIdByte));
            System.arraycopy(data, productsid.length + productIdByte.length + productsid.length, codeChallengeByte, 0, codeChallengeByte.length);
            Log.i("TAG", "codeChallengeByte: -----" + ArrayUtil.toHex(codeChallengeByte));
            Log.i("TAG", "receiveData: -----" + ArrayUtil.toASCII(codeChallengeByte));
            System.arraycopy(data, productsid.length + productIdByte.length + productsid.length + codeChallengeByte.length + productsid.length , productDsnByte, 0, productDsnByte.length);
            Log.i("TAG", "productDsnByte: -----" + ArrayUtil.toHex(productDsnByte));
            Log.i("TAG", "receiveData: -----" + ArrayUtil.toASCII(productDsnByte).replace(",", ""));

            mProductSid.put("PRODUCT_ID", ArrayUtil.toASCII(productIdByte));
            mProductSid.put("PRODUCT_DSN", ArrayUtil.toASCII(productDsnByte).replace(",", ""));
            mProductSid.put("CODE_CHALLENGE", ArrayUtil.toASCII(codeChallengeByte));
            return AVS_LWA_RECEIVE_PRODUCTID;
        }
        else if (ArrayUtil.startsWith(data, authorcode)) {
            byte lwaState = data[authorcode.length];
            if (lwaState == (byte) 0x31) {
                return AVS_LWA_RECEIVE_LWA_CONNECT_FAIL;
            } else if (lwaState == (byte) 0x30) {
                return AVS_LWA_RECEIVE_LWA_CONNECT_SUCCESS;
            }
        }
        else if (ArrayUtil.startsWith(data, avssetting)) {
            String dataStr = ArrayUtil.hexStr2Str(ArrayUtil.toHex(data).replace(",", ""));
            String markStr = dataStr.substring(avssetting.length, avssetting.length + 1);
            Log.i("TAG", "receiveData dataStr: ------" + dataStr);
            Log.i("TAG", "receiveData markStr: ------" + markStr);

            int length = dataStr.length();
            if (markStr.equals(readStr)) {
                curWholeSettingData = dataStr.substring(avssetting.length + 1, length);
                Log.i("TAG", "receiveData: -----1111---" + curWholeSettingData);
                return AVS_LWA_GET_WHOLE_SETTING_DATA;
            } else if (markStr.equals(writeStr)) {
                Log.i("TAG", "receiveData: -----2222");

                curType = dataStr.substring(avssetting.length + 1, avssetting.length + 1 + 2);
                Log.i("TAG", "receiveData curType: ------" + curType);
                Log.i("TAG", "receiveData: ------" + dataStr);

                curValue = dataStr.substring(avssetting.length + 1 + 2, length);
                Log.i("TAG", "receiveData: ------" + curValue);
                if (Integer.valueOf(oldValue) == Integer.valueOf(curValue)) {
                    Log.i("TAG", "receiveData: ----AVS_LWA_SET_SUCCESS");

                    return AVS_LWA_SET_SUCCESS;
                } else {
                    Log.i("TAG", "receiveData: ----AVS_LWA_SET_ERROR");
                    return AVS_LWA_SET_ERROR;
                }
            }

            Log.i("TAG", "receiveData: ------" + curWholeSettingData);


//            byte[] markByte = new byte[]{data[avssetting.length]};
//            String markStr = ArrayUtil.hexStr2Str(ArrayUtil.toHex(markByte).replace(",", ""));
//            if (markStr.equals(readStr)) {
//                curWholeSettingData = new byte[data.length - avssetting.length - 1];
//                System.arraycopy(data, avssetting.length + 1, curWholeSettingData, 0, curWholeSettingData.length);
//                return AVS_LWA_GET_WHOLE_SETTING_DATA;
//            } else if (markStr.equals(writeStr)) {
//                curType = data[avssetting.length + 2];
//                byte reslut = data[avssetting.length + 2 + 1];
//                if (reslut == (byte) 0x00) {
//                    return AVS_LWA_SET_SUCCESS;
//                } else {
//                    return AVS_LWA_SET_ERROR;
//                }
//            }
        }

        return 0;
    }

    public static Map<String, String> getProductSid() {
        return mProductSid;
    }

    public static String getCurType() {
        return curType;
    }

    public static String getCurValue() {
        return curValue;
    }

    public static String getCurWholeSettingData() {
        Log.i("TAG", "getCurWholeSettingData: ------" + curWholeSettingData);
        return curWholeSettingData;
    }

}