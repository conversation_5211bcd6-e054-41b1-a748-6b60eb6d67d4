package com.besall.allbase.bluetooth.service.watchavs;

public class WatchAvsConstants {

    public static final short                        OP_TOTA_ALEXA_CONNECT_STATUS =  (short)0x7001;
    public static final short                               OP_TOTA_ALEXA_CMD_CTL =  (short)0x7002;
    public static final short                              OP_TOTA_ALEXA_TTS_DATA =  (short)0x7FFE;
    public static final short                            OP_TOTA_ALEXA_AUDIO_DATA =  (short)0x7FFF;



    //message
    public static final int                             WATCH_ALEXA_STATE_CHANGED = 0x00000600;
    public static final int                                WATCH_AVS_RECEIVE_DATA = 0x00000601;
    public static final int                                 WATCH_AVS_PARSE_ERROR = 0x00000602;
    public static final int                       WATCH_AVS_RECEIVE_ALEXA_CMD_CTL = 0x00000603;
    public static final int                    WATCH_AVS_RECEIVE_ALEXA_AUDIO_DATA = 0x00000604;

    //handle_msg
    public static final int                         MSG_WATCH_AVS_SEND_AUDIO_DATA = 0x00000650;

}
