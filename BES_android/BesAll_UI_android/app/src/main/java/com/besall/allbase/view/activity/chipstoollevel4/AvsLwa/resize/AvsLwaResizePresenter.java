package com.besall.allbase.view.activity.chipstoollevel4.AvsLwa.resize;

import android.content.Context;
import android.util.Log;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.besall.allbase.bluetooth.service.avslwa.AvsLwaService;
import com.besall.allbase.view.base.BasePresenter;

public class AvsLwaResizePresenter extends BasePresenter<IAvsLwaResizeActivity> implements IAvsLwaResizePresenter {

    AvsLwaService avsLwaService;

    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        avsLwaService = new AvsLwaService(serviceConfig, listener, context);
        Log.i("TAG", "connectDevice: ---" + avsLwaService);
        avsLwaService.sendGetWholeSettingData();
    }

    @Override
    public void sendCurSettingData(int type, int value) {
        if (avsLwaService != null)
            avsLwaService.sendCurSettingData(type, value);
    }

    @Override
    public void getWholeSettingData() {
        Log.i("TAG", "getWholeSettingData: ----11");
        if (avsLwaService != null) {
            avsLwaService.sendGetWholeSettingData();
        }

    }

    @Override
    public String getCurType() {
        if (avsLwaService != null)
            return avsLwaService.getCurType();
        return "0";
    }

    @Override
    public String getCurValue() {
        if (avsLwaService != null)
            return avsLwaService.getCurValue();
        return "1";
    }

    @Override
    public void disconnect() {
        if (avsLwaService != null)
            avsLwaService.disconnected();
    }
}
