package com.besall.allbase.view.activity.chipstoollevel4.audiodump;

import android.Manifest;
import android.app.Activity;
import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.AnimationDrawable;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.SystemClock;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.method.ScrollingMovementMethod;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Button;
import android.widget.Chronometer;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RadioGroup;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.annotation.IdRes;
import androidx.appcompat.widget.Toolbar;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.scan.BtHeleper;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.R;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants;
import com.besall.allbase.common.manager.PermissionManager;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.common.utils.FileUtils;
import com.besall.allbase.view.base.BaseActivity;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_ERROR;
import static com.bes.bessdk.BesSdkConstants.BES_TOTA_SUCCESS;
import static com.besall.allbase.bluetooth.BluetoothConstants.Scan.BES_SCAN_RESULT;
import static com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants.AUDIODUMP_CUSTOM_TYPE_KEY;
import static com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants.AUDIODUMP_SAVE_FILE_TYPE_KEY;
import static com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants.AUDIODUMP_SAVE_FILE_TYPE_VALUE_CUSTOM;
import static com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants.AUDIODUMP_SAVE_FILE_TYPE_VALUE_FLAV;
import static com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants.AUDIODUMP_SAVE_FILE_TYPE_VALUE_PCM;
import static com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants.AUDIODUMP_SAVE_FILE_TYPE_VALUE_WAV;
import static com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants.AUDIODUMP_STREAM_START_TEXT_KEY;
import static com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants.AUDIODUMP_STREAM_START_TEXT_VALUE;
import static com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants.BES_INSERT_BYTE_LOST;
import static com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants.BES_INSERT_BYTE_MAX;
import static com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants.BES_INSERT_BYTE_ZERO;
import static com.besall.allbase.common.Constants.CHIPS_FILE_PATH_RESULT;
import static com.besall.allbase.common.Constants.FILE_CODE;

import java.io.File;
import java.io.IOException;

/**
 * <AUTHOR>
 * @time $ $
 */
public class AudioDumpActivity extends BaseActivity<IAudioDumpActivity, AudioDumpPresenter> implements IAudioDumpActivity, BesServiceListener, View.OnClickListener {
    private static AudioDumpActivity instance;
    public String cur_title = "AUDIO DUMP";
    private Button start_audio_dump;
    private Button stop_audio_dump;
    private Button spp_stop;
    private AnimationDrawable animationDrawable;
    private ImageView iv;
    private Chronometer  timer;
    private RadioGroup insertData;
    private RadioGroup fileType;

    private boolean totaState;

    private TextView device_text;

    private TextView package_lost_text, package_receive_text;
    private EditText stream_start_text, et_file_type;

    private int packageLostCount = 0;
    private int packageReceiveCount = 0;

    @Override
    protected AudioDumpPresenter createPresenter() {
        return new AudioDumpPresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        totaState = (boolean) SPHelper.getPreference(instance, BesSdkConstants.BES_TOTA_ENCRYPTION_KEY, BesSdkConstants.BES_TOTA_ENCRYPTION_VALUE);
        mPresenter.audioDumpSetInsertData(BES_INSERT_BYTE_MAX);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_audiodump;
    }

    @Override
    protected void bindView() {
        timer = (Chronometer) findViewById(R.id.recordtime);
        start_audio_dump = (Button) findViewById(R.id.start_audio_dump);
        start_audio_dump.setOnClickListener(instance);
        stop_audio_dump = (Button) findViewById(R.id.stop_audio_dump);
        stop_audio_dump.setOnClickListener(instance);
        spp_stop = (Button)findViewById(R.id.spp_stop);

        iv = (ImageView) findViewById(R.id.iv);

        insertData = (RadioGroup) findViewById(R.id.insert_data);
        fileType = (RadioGroup) findViewById(R.id.save_file_type);
        package_lost_text = (TextView) findViewById(R.id.package_lost_text);
        package_receive_text = (TextView) findViewById(R.id.package_receive_text);

        device_text = (TextView) findViewById(R.id.device_text);

        stream_start_text = (EditText) findViewById(R.id.stream_start_text);
        et_file_type = (EditText) findViewById(R.id.et_file_type);

    }

    @Override
    protected void initView() {
        inittoolbar(cur_title);

        logV = (TextView) findViewById(R.id.logV);
        done = (Button) findViewById(R.id.done);
        scr_policy = (ScrollView)findViewById(R.id.scr_policy);
        loginfo = (View) findViewById(R.id.loginfo);

        tv_title.setOnClickListener(instance);
        done.setOnClickListener(instance);

        String streamText = (String) SPHelper.getPreference(instance, AUDIODUMP_STREAM_START_TEXT_KEY, AUDIODUMP_STREAM_START_TEXT_VALUE);
        stream_start_text.setText(streamText);

        logV.setMovementMethod(ScrollingMovementMethod.getInstance());
        scr_policy.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                v.getParent().requestDisallowInterceptTouchEvent(true);
                return false;
            }
        });

        iv.setImageResource(R.drawable.soundwave0);

        insertData.setOnCheckedChangeListener(insertDataCheckedChangedListener);

        fileType.setOnCheckedChangeListener(insertDataCheckedChangedListener);
        String type = (String) SPHelper.getPreference(instance, AUDIODUMP_SAVE_FILE_TYPE_KEY, AUDIODUMP_SAVE_FILE_TYPE_VALUE_PCM);
        if (type.equals(AUDIODUMP_SAVE_FILE_TYPE_VALUE_PCM)) {
            fileType.check(R.id.file_type_pcm);
        } else if (type.equals(AUDIODUMP_SAVE_FILE_TYPE_VALUE_WAV)) {
            fileType.check(R.id.file_type_wav);
        } else if (type.equals(AUDIODUMP_SAVE_FILE_TYPE_VALUE_FLAV)) {
            fileType.check(R.id.file_type_flac);
        } else if (type.equals(AUDIODUMP_SAVE_FILE_TYPE_VALUE_CUSTOM)) {
            fileType.check(R.id.file_type_custom);
        }

        String customType = (String) SPHelper.getPreference(instance, AUDIODUMP_CUSTOM_TYPE_KEY, "");
        et_file_type.setText(customType);

        spp_stop.setOnClickListener(instance);

        File file = new File("/storage/emulated/0/Android/data/com.bes.besall/files/AudioDump");
        if (!file.exists()) {
            file.mkdir();
        }

        refreshPackageLostText();
        refreshPackageReceiveText();

        new Thread(new Runnable() {
            @Override
            public void run() {
                loadanimdrawable();

                animationDrawable = new AnimationDrawable();
                for (int i = 1; i <= 69; i ++) {
                    int id = getResources().getIdentifier("soundwave" + i, "drawable", getPackageName());
                    Drawable drawable = getResources().getDrawable(id);
                    animationDrawable.addFrame(drawable, 50);
                }
            }
        }).start();

        String[] permissions = new String[]{};
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            permissions = new String[]{Manifest.permission.BLUETOOTH_SCAN, Manifest.permission.BLUETOOTH_ADVERTISE, Manifest.permission.BLUETOOTH_CONNECT};
        } else {
            permissions = new String[]{Manifest.permission.ACCESS_COARSE_LOCATION, Manifest.permission.ACCESS_FINE_LOCATION};
        }
        PermissionManager.getInstance().requestPermissions(instance, new PermissionManager.PermissionUtilListener() {
            @Override
            public void onGranted() {
                refreshBtState();
            }

            @Override
            public void onUngranted(String msg) {

            }

            @Override
            public void onError(String msg) {

            }
        }, permissions);
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data)
    {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == FILE_CODE) {
            if(resultCode == CHIPS_FILE_PATH_RESULT) {
                final String curfile = data.getStringExtra("getFilePath");
                Log.i(TAG, "onActivityResult: +++++++++" + data.getIntExtra("getFilePath",100));

            }
        }

    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                Log.i(TAG, "onOptionsItemSelected: -----------");
//                mPresenter.audioDumpStop();
//                mPresenter.stopSpp();
                finish();
                break;
            case R.id.menu_file:
                mPresenter.selectfile(instance, FILE_CODE);
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    private final RadioGroup.OnCheckedChangeListener insertDataCheckedChangedListener = new RadioGroup.OnCheckedChangeListener() {
        @Override
        public void onCheckedChanged(RadioGroup group, @IdRes int checkedId) {
            switch (checkedId) {
                case R.id.insert_data_max:
                    mPresenter.audioDumpSetInsertData(BES_INSERT_BYTE_MAX);
                    break;
                case R.id.insert_data_zero:
                    mPresenter.audioDumpSetInsertData(BES_INSERT_BYTE_ZERO);
                    break;
                case R.id.insert_data_lost:
                    mPresenter.audioDumpSetInsertData(BES_INSERT_BYTE_LOST);
                    break;
                case R.id.file_type_pcm:
                    SPHelper.putPreference(instance, AUDIODUMP_SAVE_FILE_TYPE_KEY, AUDIODUMP_SAVE_FILE_TYPE_VALUE_PCM);
                    break;
                case R.id.file_type_wav:
                    SPHelper.putPreference(instance, AUDIODUMP_SAVE_FILE_TYPE_KEY, AUDIODUMP_SAVE_FILE_TYPE_VALUE_WAV);
                    break;
                case R.id.file_type_flac:
                    SPHelper.putPreference(instance, AUDIODUMP_SAVE_FILE_TYPE_KEY, AUDIODUMP_SAVE_FILE_TYPE_VALUE_FLAV);
                    break;
                case R.id.file_type_custom:
                    SPHelper.putPreference(instance, AUDIODUMP_SAVE_FILE_TYPE_KEY, AUDIODUMP_SAVE_FILE_TYPE_VALUE_CUSTOM);
                    break;
                default:
                    break;
            }
        }
    };

    @Override
    public void onClick(View v) {
        SPHelper.putPreference(instance, AUDIODUMP_CUSTOM_TYPE_KEY, et_file_type.getText().toString());
        switch (v.getId()) {
            case R.id.start_audio_dump:

                refreshBtState();

                if (timer.getCurrentTextColor() == getResources().getColor(R.color.ff087ec2)) {
                    return;
                }
                SPHelper.putPreference(instance, AUDIODUMP_STREAM_START_TEXT_KEY, stream_start_text.getText().toString());

                timer.setTextColor(getResources().getColor(R.color.ff087ec2));
                SPHelper.putPreference(instance, BesSdkConstants.BES_TOTA_ENCRYPTION_KEY, true);
                mPresenter.startRecord(stream_start_text.getText().toString());
                SPHelper.putPreference(instance, BesSdkConstants.BES_TOTA_ENCRYPTION_KEY, false);
                timerstart();
                soundwave();
                break;
            case R.id.stop_audio_dump:
                if (timer.getCurrentTextColor() == getResources().getColor(R.color.ffd8e2ee)) {
                    return;
                }
                timer.setTextColor(getResources().getColor(R.color.ffd8e2ee));
                SPHelper.putPreference(instance, BesSdkConstants.BES_TOTA_ENCRYPTION_KEY, true);
                mPresenter.stopRecord();
                SPHelper.putPreference(instance, BesSdkConstants.BES_TOTA_ENCRYPTION_KEY, false);
                Log.i(TAG, "onClick: stop");
                animationDrawable.stop();
                iv.setImageResource(R.drawable.soundwave0);
                timer.stop();
                break;
            case R.id.spp_stop:
                Log.i(TAG, "onClick: -------111111" + mPresenter);
//                mPresenter.stopSpp();
                break;
            case R.id.done:
                Log.i(TAG, "onClick: 112313132");
                loginfo.setVisibility(View.GONE);
                break;
            case R.id.tv_title:
                loginfo.setVisibility(View.VISIBLE);
                break;
            default:
                break;

        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
//        mPresenter.stopSpp();
        SPHelper.putPreference(this, BesSdkConstants.BES_TOTA_ENCRYPTION_KEY, totaState);
    }

    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (state == true) {
                    loadingDialog.dismiss();
                }
            }
        });
    }

    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {

    }

    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                Log.i(TAG, "onStateChangedMessage: +" + msgStr);
                if(msg == BES_CONNECT_ERROR) {
                    Log.i(TAG, "run: failed");
                    loadingDialog.dismiss();
                    ActivityUtils.showToast(R.string.connect_failed);
                }
//                if (msg == BesSdkConstants.TOTA_LOG_INFO) {
//                    addlog(msgStr);
//                }
            }
        });

    }

    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {

    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_documents, menu);

        return true;
    }

    private void refreshPackageLostText() {
        String string = "Packet Lost: ";
        SpannableStringBuilder builder = new SpannableStringBuilder(string + packageLostCount);
        ForegroundColorSpan buleSpan1 = new ForegroundColorSpan(getColor(R.color.ff068acd));
        builder.setSpan(buleSpan1, string.length(), string.length() + (packageLostCount + "").length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (package_lost_text != null) {
                    package_lost_text.setText(builder);
                }
            }
        });
    }

    private void refreshPackageReceiveText() {
        String string = "Packet Rx: ";
        SpannableStringBuilder builder = new SpannableStringBuilder(string + packageReceiveCount);
        ForegroundColorSpan buleSpan1 = new ForegroundColorSpan(getColor(R.color.ff068acd));
        builder.setSpan(buleSpan1, string.length(), string.length() + (packageReceiveCount + "").length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (package_receive_text != null) {
                    package_receive_text.setText(builder);
                }
            }
        });
    }

    public void soundwave() {
        iv.setImageDrawable(animationDrawable);
        animationDrawable.start();
    }

    public void timerstart(){
        timer.setBase(SystemClock.elapsedRealtime());
        int hour = (int) ((SystemClock.elapsedRealtime() - timer.getBase()) / 1000 / 60);
        timer.setFormat("0"+String.valueOf(hour)+":%s");
        timer.start();
    }

    private void refreshBtState() {
        String findChannel = mPresenter.findSppChannel(instance);
        Log.i(TAG, "refreshBtState: ------>>" + findChannel);
        if (device_text == null) {
            return;
        }
        if (findChannel.contains("already connect")) {

            return;
        }
        if (findChannel.contains("Channel:")) {
            device_text.setTextColor(getColor(R.color.green));
        } else {
            device_text.setTextColor(Color.RED);
        }
        device_text.setText(getString(R.string.current_device) + ":" + findChannel);
    }

    @Override
    public void refreshPackageLostCount(boolean up) {
        if (up) {
            packageLostCount ++;
        } else {
            packageLostCount = 0;
        }
        refreshPackageLostText();
    }

    @Override
    public void setSreamStartText(String text) {

    }

    @Override
    public void refreshPackageReceiveCount(boolean up) {
        if (up) {
            packageReceiveCount ++;
        } else {
            packageReceiveCount = 0;
        }
        refreshPackageReceiveText();
    }
}
