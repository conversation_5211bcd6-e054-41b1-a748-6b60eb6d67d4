package com.besall.allbase.view.activity.chipstoollevel4.AuracastAssistant;

import static android.bluetooth.le.ScanSettings.SCAN_MODE_LOW_LATENCY;

import static com.besall.allbase.bluetooth.service.auracastassistant.AuracastAssistantConstants.OP_TOTA_BIS_ADV;
import static com.besall.allbase.bluetooth.service.auracastassistant.AuracastAssistantConstants.OP_TOTA_BIS_ADV_STOP;
import static com.besall.allbase.bluetooth.service.auracastassistant.AuracastAssistantConstants.OP_TOTA_QR_RESULT;
import static com.besall.allbase.bluetooth.service.auracastassistant.AuracastAssistantConstants.parcelUuid_id;
import static com.besall.allbase.bluetooth.service.auracastassistant.AuracastAssistantConstants.parcelUuid_service;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.le.BluetoothLeScanner;
import android.bluetooth.le.ScanResult;
import android.bluetooth.le.ScanSettings;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.ParcelUuid;
import android.util.Log;

import com.bes.bessdk.scan.BtHeleper;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.CmdInfo;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;
import com.besall.allbase.bluetooth.service.auracastassistant.AuracastAssistantService;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.base.BasePresenter;

import java.util.Map;

/**
 * <AUTHOR>
 * @time $ $
 */
class AuracastAssistantPresenter extends BasePresenter<IAuracastAssistantActivity> implements IAuracastAssistantPresenter {
    public final String TAG = getClass().getSimpleName();

    private BluetoothLeScanner mLeScanner;
    private BluetoothAdapter mBluetoothAdapter;
    private BisScanListener mBisScanListener;

    AuracastAssistantService auracastAssistantService;

    @Override
    public void pickDecice(AuracastAssistantActivity context, int scan) {
        Intent intent = new Intent();
        intent.putExtra(BluetoothConstants.Scan.BES_SCAN, scan);
        ActivityUtils.gotoActForResult(intent, BluetoothConstants.Scan.REQUEST_CODE_SCAN, context, ScanActivity.class);
    }

    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        auracastAssistantService = new AuracastAssistantService(serviceConfig, listener, context);
    }

    @SuppressLint("MissingPermission")
    @Override
    public void startScanBisAdv(Context context, BisScanListener bisScanListener) {
        Log.i(TAG, "startScanBisAdv: ----------------");
        mBisScanListener = bisScanListener;
        mBluetoothAdapter = BtHeleper.getBluetoothAdapter(context);
        mLeScanner = mBluetoothAdapter.getBluetoothLeScanner();
        ScanSettings scanSettings = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            scanSettings = new ScanSettings.Builder()
                    .setScanMode(SCAN_MODE_LOW_LATENCY)
                    .setLegacy(false)
                    .setPhy(ScanSettings.PHY_LE_ALL_SUPPORTED)
                    .setNumOfMatches(ScanSettings.MATCH_NUM_MAX_ADVERTISEMENT)
                    .setCallbackType(ScanSettings.CALLBACK_TYPE_ALL_MATCHES)
                    .setMatchMode(ScanSettings.MATCH_MODE_AGGRESSIVE)
                    .build();
        }
        mLeScanner.startScan(null, scanSettings, mLeScanCallBack);
    }

    @SuppressLint("MissingPermission")
    @Override
    public void stopScanBisAdv() {
        if (mLeScanner != null) {
            mLeScanner.stopScan(mLeScanCallBack);
        }
    }

    @Override
    public void sendJoinData(byte[] data, byte[] psw) {
        if (auracastAssistantService != null) {
            auracastAssistantService.sendJoinData(data, psw);
        }
    }

    @Override
    public void refreshJoinState() {
        if (auracastAssistantService != null) {
            auracastAssistantService.refreshJoinState();
        }
    }

    @Override
    public void sendQRData(byte[] data) {
        if (auracastAssistantService != null) {
            auracastAssistantService.auracastAssistantSendData(new CmdInfo(OP_TOTA_QR_RESULT, data).toBytes());
        }
    }

    @Override
    public void sendStopData(byte[] data) {
        if (auracastAssistantService != null) {
            auracastAssistantService.sendStopData(data);
        }
    }

    @Override
    public void disconnect() {
        if (auracastAssistantService != null) {
            auracastAssistantService.disconnected();
        }
    }

    @SuppressLint("MissingPermission")
    private android.bluetooth.le.ScanCallback mLeScanCallBack = new android.bluetooth.le.ScanCallback() {

        @Override
        public void onScanFailed(int errorCode) {
            super.onScanFailed(errorCode);
            Log.i(TAG, "onScanFailed: -------" + errorCode);
            ActivityUtils.showToast("scan failed");
        }

        @Override
        public void onScanResult(int callbackType, ScanResult result) {
            BluetoothDevice device = result.getDevice();
            Map<ParcelUuid, byte[]> serviceData = result.getScanRecord().getServiceData();

//            if (device.getName() != null) {
//                String bitStraa = "00000110";
//                mBisScanListener.onBisScanResult(device, device.getName(), bitStraa, new byte[]{0x02, 0x01, 0x02, 0x02});
//            }

            if (serviceData != null && serviceData.size() > 0) {
                Log.i(TAG, "onScanResult00: ----------" + device.getName());
                Log.i(TAG, "onScanResult11: ----------" + result.getScanRecord().getServiceData());

                byte[] idBytes = serviceData.get(parcelUuid_id);
                byte[] serviceBytes = serviceData.get(parcelUuid_service);
                if (idBytes != null && serviceBytes != null) {
                    String bitStr = ArrayUtil.byteToBit(serviceBytes[0]);
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        mBisScanListener.onBisScanResult(device, ArrayUtil.toASCII(result.getScanRecord().getAdvertisingDataMap().get(48)), bitStr, new byte[]{idBytes[0], idBytes[1], idBytes[2], serviceBytes[0]});
                    }
                }
            }
        }
    };


}
