package com.besall.allbase.bluetooth.service.throughput;


import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.service.base.BesBaseService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.message.BaseMessage;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.common.utils.BlePreferenceUtil;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_ERROR;
import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_SUCCESS;
import static com.bes.bessdk.BesSdkConstants.BES_TOTA_ERROR;

import static com.besall.allbase.bluetooth.service.throughput.ThroughputConstants.SPP_WAIT_INI_CONFIG_ACK_TIMEOUT;

public class ThrougputService extends BesBaseService {

    public int blestatus = 0;

    public ThrougputService(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        super(serviceConfig, listener, context);
        Log.i(TAG, "ThrougputService: +++++" + serviceConfig.getDeviceProtocol());
        if (serviceConfig.getDeviceProtocol() == DeviceProtocol.PROTOCOL_BLE) {
            blestatus = 1;
        } else if (serviceConfig.getDeviceProtocol() == DeviceProtocol.PROTOCOL_SPP) {
            blestatus = 0;
        }
        startConnect(serviceConfig);
    }

    @Override
    public void onStatusChanged(HmDevice device, int status, DeviceProtocol protocol) {
        super.onStatusChanged(device, status, protocol);
        Log.i(TAG, "onStatusChanged: ---------" + status);
        if (status == BES_CONNECT_SUCCESS) {
            callBackStateChangedMessage(status, "success");
        } else if (status == BES_CONNECT_ERROR) {
            callBackStateChangedMessage(status, "error");
        } else if (status == BES_TOTA_ERROR) {
            callBackTotaConnectState(false);
        }

    }

    @Override
    public void onDataReceived(BaseMessage deviceMessage) {
        super.onDataReceived(deviceMessage);
        byte[] data = (byte[]) deviceMessage.getMsgContent();
        Log.i(TAG, "onDataReceived: " + ArrayUtil.toHex(data));
        if (data == null) {
            return;
        }

        removeTimeoutMsg(mMsgHandler, BesSdkConstants.MSG_TIME_OUT);
        int ret = ThroughputCMD.receiveData(data, mContext, blestatus);
        callBackStateChangedMessage(BesSdkConstants.TOTA_LOG_INFO, ArrayUtil.toHex((byte[]) deviceMessage.getMsgContent()));
        Log.i(TAG, "onDataReceived: " + blestatus);

        if (blestatus == 0) {
            if (data != null) {
                if (ret == ThroughputConstants.RECEIVED_INI_CONFIG_RET) {
                    Log.i(TAG, "handleDeviceControlCmd: spp");
                    removeTimeoutMsg(mMsgHandler, BesSdkConstants.MSG_TIME_OUT);
                    callBackStateChangedMessage(ThroughputConstants.RECEIVED_INI_CONFIG_RET, "RECEIVED_INI_CONFIG_RET");
                }
                if (ret == ThroughputConstants.GET_THROUGH_PUT_PACKAGE) {  //收到吞吐量数据流
                    callBackStateChangedMessage(ThroughputConstants.GET_THROUGH_PUT_PACKAGE, "SPP_GET_THROUGH_PUT_PACKAGE");
                    int isWithResponse = Integer.valueOf((String) BlePreferenceUtil.get(mContext , ThroughputConstants.KEY_IS_WITH_RESPONSE , ThroughputConstants.RESPONSE+""));
                    if (isWithResponse == ThroughputConstants.RESPONSE) {
                        sendThroughputAck();
                    }
                } else if (ret == ThroughputConstants.SPP_SEND_THROUGH_PUT_NEXT ) {
                    callBackStateChangedMessage(ThroughputConstants.SPP_SEND_THROUGH_PUT_NEXT,"SPP_SEND_THROUGH_PUT_NEXT");
                } else if (ret == ThroughputConstants.GET_THROUGH_PUT_DONE_MSG) {    //收到结束信息
                        callBackStateChangedMessage(ThroughputConstants.GET_THROUGH_PUT_DONE_MSG, "SPP_GET_THROUGH_PUT_DONE_MSG");
                } else if (ret == ThroughputConstants.INI_CONFIG_RETURN) { //配置信息返回
                        Log.i(TAG, "RESPONE: ");
                    }
                } else {
                    Log.i(TAG, "NO DATA ");
                }
                return;
        }

        if (blestatus == 1) {
            if (data != null) {
                if (ret == ThroughputConstants.RECEIVED_INI_CONFIG_RET) {
                    Log.i(TAG, "handleDeviceControlCmd: ble");
                    removeTimeoutMsg(mMsgHandler, BesSdkConstants.MSG_TIME_OUT);
                    callBackStateChangedMessage(ThroughputConstants.BLE_RECEIVED_INI_CONFIG_RET, "RECEIVED_INI_CONFIG_RET");
                }
                if (ret == ThroughputConstants.GET_THROUGH_PUT_PACKAGE) {  //收到吞吐量数据流
                    Log.i(TAG, "onDataReceived: GET_THROUGH_PUT_PACKAGE");
                    callBackStateChangedMessage(ThroughputConstants.GET_THROUGH_PUT_PACKAGE,"GET_THROUGH_PUT_PACKAGE");
                    int isWithResponse = Integer.valueOf((String) BlePreferenceUtil.get(mContext , ThroughputConstants.KEY_IS_WITH_RESPONSE , ThroughputConstants.RESPONSE+""));
                    if (isWithResponse == ThroughputConstants.RESPONSE) {
                        sendThroughputAck();
                    }
                } else if (ret == ThroughputConstants.SPP_SEND_THROUGH_PUT_NEXT ) {
                    callBackStateChangedMessage(ThroughputConstants.SPP_SEND_THROUGH_PUT_NEXT,"BLE_SEND_THROUGH_PUT_NEXT");
                } else if (ret == ThroughputConstants.GET_THROUGH_PUT_DONE_MSG) {    //收到结束信息
                    callBackStateChangedMessage(ThroughputConstants.GET_THROUGH_PUT_DONE_MSG,"BLE_GET_THROUGH_PUT_DONE_MSG");
                } else if (ret == ThroughputConstants.INI_CONFIG_RETURN ){ //配置信息返回
                    Log.i(TAG, "handleDeviceControlCmd: RESPONE");
                }
            } else {

            }


        }

    }



    public void throughupStart(int status) {

        if (status == 0) {
            blestatus = 0;
            Log.i(TAG, "throughupStart: spp");
            if (isConnect) {
                addTimeOut(2000);
                sendData(ThroughputCMD.sendInitConfigToDevice(mContext, status));
            } else {
                callBackErrorMessage(BesSdkConstants.BES_CONNECT_ERROR);
            }
        } else if (status == 1) {
            blestatus = 1;
            Log.i(TAG, "throughupStart: ble start" + isConnect);
            if (isConnect) {
                addTimeOut(10000);
                sendData(ThroughputCMD.sendInitConfigToDevice(mContext, status));
            } else {
                Log.i(TAG, "SEND_INI_CONFIG_TO_DEVICE bleManager == null || !bleManager.isConnected() ");
                callBackErrorMessage(BES_CONNECT_ERROR);
            }
        }

    }

    public void throughDownStart(int status) {
        if (status == 0) {
            if (isConnect) {
                addTimeOut(2000);
                sendData(ThroughputCMD.sendInitConfigToDevice(mContext, status));
            } else {
                callBackErrorMessage(BesSdkConstants.BES_CONNECT_ERROR);
            }
        } else if (status == 1) {
            Log.i(TAG, "throughDownStart: ");
            if (isConnect) {
                addTimeOut(10000);
                sendData(ThroughputCMD.sendInitConfigToDevice(mContext, status));
            } else {
                Log.i(TAG, "SEND_INI_CONFIG_TO_DEVICE bleManager == null || !bleManager.isConnected() ");
                callBackErrorMessage(BES_CONNECT_ERROR);
            }
        }

    }

    public void sendPackage(int status) {
        if (isConnect) {
            if (status == 0) {
                sendData(ThroughputCMD.sendThroughputPackage(mContext, 0));
            } else if (status == 1){
                new Handler(Looper.getMainLooper()).post(new Runnable() {
                    @Override
                    public void run() {
                        boolean ret = sendData(ThroughputCMD.sendThroughputPackage(mContext, 1));
                        if (ret){
                            callBackStateChangedMessage(ThroughputConstants.SPP_SEND_THROUGH_PUT_NEXT,"BLE_SEND_THROUGH_PUT_NEXT");
                        }
                    }
                });
            }
        } else {
            Log.i(TAG, "sendpackage bleManager == null || !bleManager.isConnected() ");
            callBackErrorMessage(BES_CONNECT_ERROR);
        }
    }

    public void sendDone() {
        Log.i(TAG, "senddone: ");
        if (isConnect) {
            sendData(ThroughputCMD.sendPackageDone());
        } else {
            Log.i(TAG, "senddone bleManager == null || !bleManager.isConnected() ");
            callBackErrorMessage(BES_CONNECT_ERROR);
        }
    }

    public void sendThroughputAck() {
        Log.i(TAG, "sendthroughputack: ");
        if (isConnect) {
            sendData(ThroughputCMD.sendack());
        } else {
            Log.i(TAG, "senddone bleManager == null || !bleManager.isConnected() ");
            callBackErrorMessage(BES_CONNECT_ERROR);
        }
    }

}
