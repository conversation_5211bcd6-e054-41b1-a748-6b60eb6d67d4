package com.besall.allbase.bluetooth.service.BleWifi;

import com.bes.bessdk.utils.ArrayUtil;

import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_RECEIVE_FILE_PATH;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_RECEIVE_OTA_PATH;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_RECEIVE_WIFI_CONFIG;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_RECEIVE_WIFI_OPEN_WIFI_FAIL;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_WIFI_CMD;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_WIFI_FILE_PATH;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_WIFI_OPEN_WIFI_FAIL;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_WIFI_OPEN_WIFI_RSP;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_WIFI_OTA_PATH;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_WIFI_RESPONSE;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.HEADER_GETWORKPAT;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.HEADER_TOOPENWIFI;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.HEADER_WIFICONFIG;

import android.content.Context;
import android.util.Log;

public class BleWifiCMD {
    private String TAG = "BleWifiCMD";
    private String curFilePath = "";
    private String curOtaPath = "";

    private String curWifiInfo = "";


    private static byte[] wifiConfig = ArrayUtil.toBytes(ArrayUtil.str2HexStr("wificonfig"));

    public byte[] getWifiData(String name, String psw) {
        byte[] bytes = new byte[wifiConfig.length + 32 + 65];
        //将字符串转化为16进制
        String name16 = ArrayUtil.str2HexStr(name);
        String pw16 = ArrayUtil.str2HexStr(psw);

        for (int i = 0; i < wifiConfig.length; i ++) {
            bytes[i] = wifiConfig[i];
        }

        byte[] nameBytes = ArrayUtil.toBytes(name16);
        for (int i = 0; i < nameBytes.length; i ++) {
            bytes[i + wifiConfig.length] = nameBytes[i];
        }

        byte[] pwBytes = ArrayUtil.toBytes(pw16);
        for (int i = 0; i < pwBytes.length; i ++) {
            bytes[i + wifiConfig.length + 32] = pwBytes[i];
        }

        return bytes;
    }

    public String getCurOtaPath() {
        return curOtaPath;
    }

    public String getCurFilePath() {
        return curFilePath;
    }

    public String getCurWifiInfo() {
        return curWifiInfo;
    }

    public byte[] getWorkPathCMD(int type) {
        return ArrayUtil.bytesSplic(HEADER_GETWORKPAT, getL2(1), new byte[]{(byte) type}, new byte[0]);
    }

    public byte[] getOpenWifiCMD() {
        return ArrayUtil.bytesSplic(HEADER_TOOPENWIFI, getL2(1), new byte[]{BES_BLE_WIFI_CMD}, new byte[0]);
    }

    public int receiveData(Context context, byte[] data) {
        Log.i(TAG, "receiveData: --------" + ArrayUtil.toHex(data));
        if (ArrayUtil.startsWith(data, HEADER_GETWORKPAT)) {
            int dataL = ArrayUtil.byte2int(new byte[]{data[HEADER_GETWORKPAT.length], data[HEADER_GETWORKPAT.length + 1]});
            Log.i(TAG, "dataL: ------" + dataL);
            byte[] curData = new byte[dataL - 1];
            System.arraycopy(data, HEADER_GETWORKPAT.length + 2 + 1, curData, 0, dataL - 1);
            Log.i(TAG, "curData: ------" + ArrayUtil.toHex(curData));
            String pathStr = ArrayUtil.toASCII(curData);
            Log.i(TAG, "pathStr: ------" + pathStr);
            if (data[HEADER_GETWORKPAT.length - 1] == BES_BLE_WIFI_OTA_PATH) {
                curOtaPath = pathStr;
                return BES_BLE_RECEIVE_OTA_PATH;
            } else if (data[HEADER_GETWORKPAT.length - 1] == BES_BLE_WIFI_FILE_PATH) {
                curFilePath = pathStr;
                return BES_BLE_RECEIVE_FILE_PATH;
            }
        } else if (ArrayUtil.startsWith(data, HEADER_TOOPENWIFI)) {
            if (data[HEADER_TOOPENWIFI.length + 2] == BES_BLE_WIFI_RESPONSE) {
                return BES_BLE_WIFI_OPEN_WIFI_RSP;
            } else if (data[HEADER_TOOPENWIFI.length + 2] == BES_BLE_WIFI_OPEN_WIFI_FAIL) {
                return BES_BLE_RECEIVE_WIFI_OPEN_WIFI_FAIL;
            }
        } else if (ArrayUtil.startsWith(data, HEADER_WIFICONFIG)) {
            int httpL = ArrayUtil.byte2int(new byte[]{data[HEADER_WIFICONFIG.length + 2 + 5], data[HEADER_WIFICONFIG.length + 2 + 5 + 1]});
            Log.i(TAG, "httpL: --------->" + httpL);
            byte[] httpData = new byte[httpL];
            System.arraycopy(data, HEADER_WIFICONFIG.length + 2 + 5 + 2, httpData, 0, httpL);
            Log.i(TAG, "httpData------->" + ArrayUtil.toHex(httpData));
            String httpDataStr = ArrayUtil.toASCII(httpData);
            Log.i(TAG, "httpDataStr------->" + httpDataStr);

            int ssidL = ArrayUtil.byte2int(new byte[]{data[HEADER_WIFICONFIG.length + 2 + 5 + 2 + httpL + 5], data[HEADER_WIFICONFIG.length + 2 + 5 + 2 + httpL + 5 + 1]});
            Log.i(TAG, "ssidL: --------->" + ssidL);
            byte[] ssidData = new byte[ssidL];
            System.arraycopy(data, HEADER_WIFICONFIG.length + 2 + 5 + 2 + httpL + 5 + 2, ssidData, 0, ssidL);
            Log.i(TAG, "ssidData------->" + ArrayUtil.toHex(ssidData));
            String ssidDataStr = ArrayUtil.toASCII(ssidData);
            Log.i(TAG, "ssidDataStr------->" + ssidDataStr);

            int pswL = ArrayUtil.byte2int(new byte[]{data[HEADER_WIFICONFIG.length + 2 + 5 + 2 + httpL + 5 + 2 + ssidL + 5], data[HEADER_WIFICONFIG.length + 2 + 5 + 2 + httpL + 5 + 2 + ssidL + 5 + 1]});
            Log.i(TAG, "pswL: --------->" + pswL);
            byte[] pswData = new byte[pswL];
            System.arraycopy(data, HEADER_WIFICONFIG.length + 2 + 5 + 2 + httpL + 5 + 2 + ssidL + 5 + 2, pswData, 0, pswL);
            Log.i(TAG, "pswData------->" + ArrayUtil.toHex(pswData));
            String pswDataStr = ArrayUtil.toASCII(pswData);
            Log.i(TAG, "pswDataStr------->" + pswDataStr);
            curWifiInfo = httpDataStr + "\n" + ssidDataStr + "\n" + pswDataStr;
            return BES_BLE_RECEIVE_WIFI_CONFIG;
        }
        return 0;
    }

    private byte[] getL2(int length) {
        return ArrayUtil.intToBytes2(length);
    }
}