package com.besall.allbase.app;

import android.app.Application;
import android.content.Context;

import com.besall.allbase.common.Initialize.InitializeUtils;

/**
 * Created by <PERSON>yu on 2019-04-18
 */
public class MyApplication extends Application {

    private static MyApplication instance;
    private Context applicationContext;
    CrashExceptionHandler crashExceptionHandler ;

    public static MyApplication getInstance() {
        return instance;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        instance = this;

        applicationContext = getApplicationContext();
        crashExceptionHandler = CrashExceptionHandler.getInstance();
        crashExceptionHandler.init(applicationContext);
        // 执行初始化操作
        InitializeUtils.init();
    }

}
