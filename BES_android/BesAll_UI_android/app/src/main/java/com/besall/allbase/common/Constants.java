package com.besall.allbase.common;

public class Constants {
    //ota
    public final static String OTA_SERVICE_CONFIG                   = "FunctionOtaActivity_OTA_SERVICE_CONFIG";

    //SPHelper
    public final static String OTA_USER_TYPE                        = "OtaActivity_OTA_USER_TYPE";
    public final static String OTA_UPGRADE_STYLE                    = "OtaActivity_OTA_UPGRADE_STYLE";
    public final static String OTA_IS_BTH                           = "OtaActivity_OTA_IS_BTH";
    public final static String OTA_ACK_STYLE                        = "OtaActivity_OTA_ACK_STYLE";
    public final static String OTA_EARPHONE_STYLE                   = "OtaActivity_OTA_EARPHONE_STYLE";
    public final static String OTA_HISTOTY_DEVICE_ADDRESS           = "OtaActivity_OTA_HISTOTY_DEVICE_ADDRESS";
    public final static String OTA_HISTOTY_DEVICE_NAME              = "OtaActivity_OTA_HISTOTY_DEVICE_NAME";
    public final static String OTA_HISTOTY_FILE_PATH                = "OtaActivity_OTA_OTA_HISTOTY_FILE_PATH";
    public final static String OTA_HISTOTY_FILE_PATH_S              = "OtaActivity_OTA_OTA_HISTOTY_FILE_PATH_S";
    public final static String GET_FILE_NAME                        = "getFileName";
    public final static String GET_FILE_PATH                        = "getFilePath";
    public final static String CHOOSE_FILE_FOLDER_INTENT_KEY        = "CHOOSE_FILE_FOLDER_INTENT_KEY";



    public final static int OTA_CHOOSE_FILE_PATH_RESULT             = 0x00000500;
    public final static int FILE_CODE                               = 0x00000501;
    public final static int CHIPS_FILE_PATH_RESULT                  = 0x00000502;


//    public final static String DEFAULT_INTERVALS                    = "default_intervals";
//    public final static String DEFAULT_SERVICE_UUIDS                = "DEFAULT_SERVICE_UUIDS";
//    public final static String DEFAULT_characteristicTx_UUIDS       = "DEFAULT_characteristicTx_UUIDS";
//    public final static String DEFAULT_characteristicRx_UUIDS       = "DEFAULT_characteristicRx_UUIDS";
//    public final static String DEFAULT_descriptor_UUIDS             = "DEFAULT_descriptor_UUIDS";

}
