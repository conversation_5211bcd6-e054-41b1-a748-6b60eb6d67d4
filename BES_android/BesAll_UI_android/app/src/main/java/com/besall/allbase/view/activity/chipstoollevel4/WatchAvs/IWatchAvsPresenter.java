package com.besall.allbase.view.activity.chipstoollevel4.WatchAvs;

import android.app.Activity;
import android.content.Context;

import com.amazon.alexa.accessory.protocol.Accessories;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.besall.allbase.common.manager.AlexaHttpManager;

import java.util.Map;

public interface IWatchAvsPresenter {

    void pickDecice(WatchAvsActivity context, int scan);

    void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context);

    Map<String, String> getProductSid();

    void startRecord(Activity activity, Context context, recordStateListener listener, AlexaHttpManager manager);

    void stopRecord(Context context);
    public interface recordStateListener {
        void onRecordStateChanged(int state, String msg);
    }
}
