package com.besall.allbase.view.activity.chipstoollevel4.ota.filepathadapter;

import static com.besall.allbase.common.Constants.OTA_CHOOSE_FILE_PATH_RESULT;

import android.app.Activity;
import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.UiThread;

import com.besall.allbase.R;
import com.besall.allbase.common.utils.FileUtils;

import java.util.ArrayList;
import java.util.List;

public class FilePathAdapter extends BaseAdapter implements View.OnClickListener {
    private List<String> mNames;
    private List<String> mPaths;
    private Context mContext;
    private Activity mActivity;

    public FilePathAdapter(Context context, Activity activity) {
        mNames = new ArrayList<String>();
        mPaths = new ArrayList<String>();
        mContext = context;
        mActivity = activity;
    }

    @Override
    public int getCount() {
        return mNames.size();
    }

    @Override
    public Object getItem(int position) {
        return position;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        if (convertView == null) {
            convertView = LayoutInflater.from(mContext).inflate(R.layout.otafilepath_item, parent, false);
            holder = new ViewHolder();
            holder.mName = (TextView) convertView.findViewById(R.id.name);
            holder.mPath = (TextView) convertView.findViewById(R.id.path);
            holder.choose_file = (Button) convertView.findViewById(R.id.choose_file);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }

        holder.mName.setText(mNames.get(position));
        holder.mPath.setText(mPaths.get(position));
        holder.choose_file.setTag(position + OTA_CHOOSE_FILE_PATH_RESULT);
        holder.choose_file.setOnClickListener(this);
        return convertView;
    }

    @UiThread
    public void add(String name, String path, int index) {
        synchronized (mNames) {
            if (mNames.size() > index) {
                mNames.remove(index);
            }
            mNames.add(index, name);
            if (mPaths.size() > index) {
                mPaths.remove(index);
            }
            mPaths.add(index, path);
            notifyDataSetChanged();
        }
    }
    public void clear() {
        synchronized (mNames) {
            mNames.clear();
            mNames = new ArrayList<String>();
            mPaths = new ArrayList<String>();
            notifyDataSetChanged();
        }
    }

    @Override
    public void onClick(View v) {
        Log.i("TAG", "onClick: --------" + ((int)v.getTag() - OTA_CHOOSE_FILE_PATH_RESULT));
        FileUtils.fileToHex(mActivity, (int)v.getTag());
    }

    private class ViewHolder {
        TextView mName;
        TextView mPath;
        Button choose_file;
    }
}
