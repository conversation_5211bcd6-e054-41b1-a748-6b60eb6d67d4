package com.besall.allbase.view.activity.chipstoollevel4.WatchAvs;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_ERROR;
import static com.besall.allbase.bluetooth.BluetoothConstants.Scan.BES_SCAN_RESULT;
import static com.besall.allbase.bluetooth.service.watchavs.WatchAvsConstants.WATCH_ALEXA_STATE_CHANGED;
import static com.besall.allbase.bluetooth.service.watchavs.WatchAvsConstants.WATCH_AVS_RECEIVE_DATA;
import static com.besall.allbase.common.manager.AlexaHttpManager.ALEXA_STATE_LOGIN_SUCCESS;
import static com.besall.allbase.common.manager.AlexaHttpManager.ALEXA_STATE_NO_LOGIN;
import static com.besall.allbase.common.manager.AlexaHttpManager.ALEXA_STATE_NO_NETWORK;
import static com.besall.allbase.common.manager.AlexaHttpManager.ALEXA_STATE_RECEIVE_DIRECTIVES_SPEAK;
import static com.besall.allbase.common.manager.AlexaHttpManager.ALEXA_STATE_RECEIVE_TEXT;
import static com.besall.allbase.common.manager.AlexaHttpManager.ALEXA_STATE_SPEECH_RECOGNIZER;
import static com.besall.allbase.common.manager.AlexaHttpManager.ALEXA_STATE_SPEECH_START;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.media.MediaPlayer;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.speech.SpeechRecognizer;
import android.text.method.ScrollingMovementMethod;
import android.util.Log;
import android.view.Gravity;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import com.amazon.identity.auth.device.AuthError;
import com.amazon.identity.auth.device.api.Listener;
import com.amazon.identity.auth.device.api.authorization.AuthCancellation;
import com.amazon.identity.auth.device.api.authorization.AuthorizationManager;
import com.amazon.identity.auth.device.api.authorization.AuthorizeListener;
import com.amazon.identity.auth.device.api.authorization.AuthorizeRequest;
import com.amazon.identity.auth.device.api.authorization.AuthorizeResult;
import com.amazon.identity.auth.device.api.authorization.ProfileScope;
import com.amazon.identity.auth.device.api.authorization.Scope;
import com.amazon.identity.auth.device.api.authorization.ScopeFactory;
import com.amazon.identity.auth.device.api.workflow.RequestContext;
import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.scan.BtHeleper;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.R;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.common.manager.AlexaHttpManager;
import com.besall.allbase.common.opus.OpusDecoder;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.base.BaseActivity;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.ArrayList;
import java.util.Map;

public class WatchAvsActivity extends BaseActivity<IWatchAvsActivity, WatchAvsPresenter> implements IWatchAvsActivity, View.OnClickListener, IWatchAvsPresenter.recordStateListener, BesServiceListener {

    public String cur_title = "WATCH AVS";
    public String cur_title_login = "Login with Amazon";

    private static WatchAvsActivity instance;

    //BlueTooth
    HmDevice mHmDevice;
    BesServiceConfig mServiceConfig;

    //AVS
    private RequestContext requestContext;
    private boolean mIsLoggedIn;
    private View mLoginButton;
    private TextView jump_alexa_app_textview;
//    private String PRODUCT_ID = "productbbbbbb";
    private String PRODUCT_ID = "AVK2022";

    private String PRODUCT_DSN = "112233445566";
    private String CODE_CHALLENGE = "E9Melhoa2OwvFrEMTJguCHaoeK1t8URWbuGJSstw-cM";

    private String CODE_VERIFIER = "dBjftJeZ4CVP-mB92K27uhbUJU1p1r_wW1gFWFOEjXk";

    private static final String CODE_CHALLENGE_METHOD = "S256";
    private MediaPlayer mediaPlayer;

    private Button watch_avs_login_btn;
    private Button watch_avs_record_btn;
    private TextView watch_avs_login_text;
    private TextView watch_avs_device_state_text;
    private Button watch_avs_connect_device_btn;


    @Override
    protected WatchAvsPresenter createPresenter() {
        return new WatchAvsPresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        mServiceConfig = new BesServiceConfig();
        mServiceConfig.setServiceUUID(BesSdkConstants.BES_SPP_CONNECT);
        mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
        mServiceConfig.setTotaConnect(true);
        boolean useTotaV2 = (boolean) SPHelper.getPreference(instance, BesSdkConstants.BES_TOTA_USE_TOTAV2, BesSdkConstants.BES_TOTA_USE_TOTAV2_VALUE);
        mServiceConfig.setUseTotaV2(useTotaV2);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_watchavs;
    }

    @Override
    protected void bindView() {
        bindBaseUI();
    }

    @SuppressLint("ResourceAsColor")
    @Override
    protected void initView() {
        initBaseUI();

        //02,70,18,00,08,0b,5a,14,0a,04,08,01,10,01,12,04,08,03,12,00,1a,02,08,01,20,01,28,01,00,00,00,00,

        byte[] data = ArrayUtil.toBytes("02,70,18,00,08,0b,5a,14,0a,04,08,01,10,01,12,04,08,03,12,00,1a,02,08,01,20,01,28,01,00,00,00,00,");
//
//        try {
//            Accessories.ControlEnvelope controlEnvelope = Accessories.ControlEnvelope.parseFrom(data);
//            Log.i(TAG, "initView: --------" + controlEnvelope.getCommand());
//            Log.i(TAG, "initView: --------" + controlEnvelope.getStartSpeech().getSettings().getAudioFormat());
//
//        } catch (InvalidProtocolBufferException e) {
//            throw new RuntimeException(e);
//        }


    }

    private void bindBaseUI() {
        done = (Button) findViewById(R.id.done);
        loginfo = (View) findViewById(R.id.loginfo);
        logV = (TextView) findViewById(R.id.logV);

        watch_avs_login_btn = (Button) findViewById(R.id.watch_avs_login_btn);
        watch_avs_record_btn = (Button) findViewById(R.id.watch_avs_record_btn);
        watch_avs_login_text = (TextView) findViewById(R.id.watch_avs_login_text);
        watch_avs_connect_device_btn = (Button) findViewById(R.id.watch_avs_connect_device_btn);
        watch_avs_device_state_text = (TextView) findViewById(R.id.watch_avs_device_state_text);
    }

    private void initBaseUI() {
        inittoolbar(cur_title);
        tv_title.setOnClickListener(instance);
        logV.setMovementMethod(ScrollingMovementMethod.getInstance());
        done.setOnClickListener(instance);
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);
        loadanimdrawable();

        watch_avs_login_btn.setOnClickListener(instance);
        watch_avs_record_btn.setOnClickListener(instance);
        watch_avs_connect_device_btn.setOnClickListener(instance);

        int state = AlexaHttpManager.getHttpManager(instance, null).checkCurAlexaState();
        Log.i(TAG, "initBaseUI: -------" + state);
        if (state == ALEXA_STATE_LOGIN_SUCCESS) {
            watch_avs_login_text.setText(R.string.already_logged_in);
        } else if (state == ALEXA_STATE_NO_LOGIN) {
            watch_avs_login_text.setText(R.string.not_log_in);
        } else if (state == ALEXA_STATE_NO_NETWORK) {
            watch_avs_login_text.setText(R.string.please_check_network);
        }
    }

    private void initializeBaseUI() {
        setContentView(R.layout.activity_watchavs);
        bindBaseUI();
        initBaseUI();
    }

    private void initializeLoginUI() {
        setContentView(R.layout.activity_avslwa_login);
        inittoolbar(cur_title_login);

        mLoginButton = findViewById(R.id.login_with_amazon);
        mLoginButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                final JSONObject scopeData = new JSONObject();
                final JSONObject productInstanceAttributes = new JSONObject();
                Log.i(TAG, "onClick: ------" + PRODUCT_DSN);
                Log.i(TAG, "onClick: ------" + PRODUCT_ID);
                Log.i(TAG, "onClick: ------" + CODE_CHALLENGE);
                try {
                    productInstanceAttributes.put("deviceSerialNumber", PRODUCT_DSN);
                    scopeData.put("productInstanceAttributes", productInstanceAttributes);
                    scopeData.put("productID", PRODUCT_ID);

                    AuthorizationManager.authorize(new AuthorizeRequest.Builder(requestContext)
                            .addScopes(ScopeFactory.scopeNamed("alexa:voice_service:pre_auth"),
                                    ScopeFactory.scopeNamed("alexa:all", scopeData))
                            .forGrantType(AuthorizeRequest.GrantType.AUTHORIZATION_CODE)
                            .withProofKeyParameters(CODE_CHALLENGE, CODE_CHALLENGE_METHOD)
                            .build());
                } catch (JSONException e) {
                    // handle exception here
                }
            }
        });
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                if (tv_title.getText().equals(cur_title)) {
                    finish();
                    return false;
                }
                initializeBaseUI();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.done:
                loginfo.setVisibility(View.GONE);
                break;
            case R.id.tv_title:
                loginfo.setVisibility(View.VISIBLE);
                break;
            case R.id.watch_avs_connect_device_btn:
                mPresenter.pickDecice(instance, BluetoothConstants.Scan.SCAN_SPP);
                break;
            case R.id.watch_avs_login_btn:
                startLoginAvs();

                break;
            case R.id.watch_avs_record_btn:
                AlexaHttpManager mHttpManager = AlexaHttpManager.getHttpManager(instance, null);
                mHttpManager.stopRecording();
//                if (watch_avs_login_text.getText() != getString(R.string.already_logged_in)) {
//                    showAuthToast("please check alexa state");
//                    return;
//                }
//                if (watch_avs_record_btn.getText().equals(getString(R.string.ready_record))) {
//                    AlexaHttpManager mHttpManager = AlexaHttpManager.getHttpManager(instance, null);
//                    mHttpManager.ready();
//
////                    mPresenter.startRecord(instance, instance, instance, mHttpManager);
//
//                    String path = "/storage/emulated/0/Android/data/com.bes.besall/files/WatchAvs(2).txt";
//                    String aaa = readToText(path).replace("\n", "");
//                    byte[] allData = ArrayUtil.toBytes(aaa);
//                    OpusDecoder.init(-1, 160, 320, -1);
//                    OpusDecoder opusDecoder = new OpusDecoder();
//                    byte[] decodeData = opusDecoder.decodeAll(allData);
//                    Log.i(TAG, "onClick: -------" + decodeData.length);
//
//
//                    mHttpManager.addData(ArrayUtil.byteMerger(decodeData, new byte[decodeData.length * 20]));
//                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == BluetoothConstants.Scan.REQUEST_CODE_SCAN) {
            onPickDevice(resultCode, data);
        }

    }

    private void onPickDevice(int resultCode, Intent data) {
        if (resultCode == RESULT_OK) {
            mHmDevice = (HmDevice) data.getSerializableExtra(BES_SCAN_RESULT);
            BluetoothDevice mDevice = BtHeleper.getBluetoothAdapter(instance).getRemoteDevice(mHmDevice.getPreferredProtocol() == DeviceProtocol.PROTOCOL_BLE ? mHmDevice.getBleAddress() : mHmDevice.getDeviceMAC());
            watch_avs_device_state_text.setText(mDevice.getName());

            loadinganim();
            mServiceConfig.setDevice(mHmDevice);
            mPresenter.connectDevice(mServiceConfig, instance, instance);
        }
    }

    private void startLoginAvs() {
        initRequestContext();
        Map<String, String> productSid = mPresenter.getProductSid();
        if (productSid != null) {
            PRODUCT_ID = productSid.get("PRODUCT_ID");
            PRODUCT_DSN = productSid.get("PRODUCT_DSN");
            CODE_CHALLENGE = productSid.get("CODE_CHALLENGE");
        }

        addlog("PRODUCT_ID:" + PRODUCT_ID);
        addlog("PRODUCT_DSN:" + PRODUCT_DSN);
        addlog("CODE_CHALLENGE:" + CODE_CHALLENGE);

        initializeLoginUI();
    }

    @Override
    protected void onResume() {
        super.onResume();
        Log.i(TAG, "onResume: --------aaaaa");
        if (requestContext != null) {
            requestContext.onResume();
        }
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    Thread.sleep(3000);
                    onStateChangedMessage(WATCH_ALEXA_STATE_CHANGED, AlexaHttpManager.getHttpManager(instance, null).checkCurAlexaState() + "", null);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }).start();
    }
    private void initRequestContext() {
        Log.i(TAG, "initRequestContext: -----------");
        requestContext = RequestContext.create(this);
        requestContext.registerListener(new AuthorizeListener() {
            /* Authorization was completed successfully. */
            @Override
            public void onSuccess(AuthorizeResult authorizeResult) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        final String authorizationCode = authorizeResult.getAuthorizationCode();
                        final String redirectUri = authorizeResult.getRedirectURI();
                        final String clientId = authorizeResult.getClientId();
                        addlog("authorizationCode: -----" + authorizationCode);
                        addlog("redirectUri: -----" + redirectUri);
                        addlog("clientId: ---" + clientId.length() + "----" + clientId);

                        AlexaHttpManager.saveInformation(clientId, PRODUCT_ID, PRODUCT_DSN);

                        Log.i(TAG, "authorizationCode: ----------" + authorizationCode);

                        AlexaHttpManager.getHttpManager(instance, null).requestTokenData(authorizationCode, redirectUri, clientId);

                        initializeBaseUI();
                    }
                });
            }

            /* There was an error during the attempt to authorize the application */
            @Override
            public void onError(AuthError authError) {
                Log.i(TAG, "AuthError during authorization", authError);
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {

                        showAuthToast("Error during authorization.  Please try again.");
                    }
                });
            }

            /* Authorization was cancelled before it could be completed. */
            @Override
            public void onCancel(AuthCancellation authCancellation) {
                Log.i(TAG, "User cancelled authorization");
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        setContentView(R.layout.activity_avslwa);
                        bindBaseUI();
                        initBaseUI();

                        showAuthToast("Authorization cancelled");
                    }
                });
            }
        });
    }

    @Override
    protected void onStart() {
        super.onStart();
        Scope[] scopes = {ProfileScope.profile(), ProfileScope.postalCode()};
        AuthorizationManager.getToken(this, scopes, new Listener<AuthorizeResult, AuthError>() {
            @Override
            public void onSuccess(AuthorizeResult result) {
                if (result.getAccessToken() != null) {
                    /* The user is signed in */

                } else {
                    /* The user is not signed in */
                }
            }

            @Override
            public void onError(AuthError ae) {
                /* The user is not signed in */
            }
        });
    }

    private void showAuthToast(String authToastMessage) {
        Toast authToast = Toast.makeText(getApplicationContext(), authToastMessage, Toast.LENGTH_LONG);
        authToast.setGravity(Gravity.CENTER, 0, 0);
        authToast.show();
    }

    private MediaPlayer.OnCompletionListener mCompletionListener = new MediaPlayer.OnCompletionListener() {
        @Override
        public void onCompletion(MediaPlayer mp) {
            mediaPlayer.stop();
            mediaPlayer.release();
            mediaPlayer = null;
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    watch_avs_record_btn.setText(R.string.ready_record);
                }
            });
        }
    };

//    @Override
//    public void onAlexaStateChange(int state) {
//        if (state == ALEXA_STATE_SPEECH_START) {
//            mediaPlayer = new MediaPlayer();
//            mediaPlayer.setOnCompletionListener(mCompletionListener);
//            mediaPlayer.reset();
//            try {
//                mediaPlayer.setDataSource("/storage/emulated/0//Android/data/com.bes.besall/files/OTA/oneplusone-alexaresponse.mp3");
//                mediaPlayer.prepare();
//            } catch (IOException e) {
//                throw new RuntimeException(e);
//            }
//            mediaPlayer.start();
//            return;
//        }
//        runOnUiThread(new Runnable() {
//            @Override
//            public void run() {
//                if (state == ALEXA_STATE_LOGIN_SUCCESS) {
//                    watch_avs_login_text.setText(R.string.already_logged_in);
//                } else if (state == ALEXA_STATE_NO_LOGIN) {
//                    watch_avs_login_text.setText(R.string.not_log_in);
//                } else if (state == ALEXA_STATE_NO_NETWORK) {
//                    watch_avs_login_text.setText(R.string.please_check_network);
//                } else if (state == ALEXA_STATE_SPEECH_RECOGNIZER) {
//                    watch_avs_record_btn.setText(R.string.is_recording);
//                } else if (state == ALEXA_STATE_RECEIVE_DIRECTIVES_SPEAK) {
//                    mPresenter.stopRecord(instance);
//                    watch_avs_record_btn.setText(R.string.speech_started);
//                }
//            }
//        });
//    }

    @Override
    public void onRecordStateChanged(int state, String msg) {

    }

    public static String readToText(String filePath) {//按字节流读取可保留原格式，但是有部分乱码情况，根据每次读取的byte数组大小而变化
        StringBuffer txtContent = new StringBuffer();
        byte[] b = new byte[2048];
        InputStream in = null;
        try {
            in = new FileInputStream(filePath);
            int n;
            while ((n = in.read(b)) != -1) {
                txtContent.append(new String(b, 0, n, "utf-8"));
            }
            in.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return txtContent.toString();
    }

    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                loadingDialog.dismiss();

                if (state == true) {
                    watch_avs_device_state_text.setTextColor(getColor(R.color.green));
                } else {
                    watch_avs_device_state_text.setTextColor(Color.RED);
                }
            }
        });
    }

    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {

    }

    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (msg == BES_CONNECT_ERROR) {
                    loadingDialog.dismiss();
                    watch_avs_device_state_text.setTextColor(Color.RED);
                } else if (msg == WATCH_AVS_RECEIVE_DATA) {
                    addlog("---------------");
                    addlog(msgStr);
//                    FileUtils.writeTOfileAndActiveClear("WatchAvs", msgStr);
                } else if (msg == WATCH_ALEXA_STATE_CHANGED) {
                    Log.i(TAG, "run: ------->" + msgStr);
                    int state = Integer.valueOf(msgStr);
                    if (state == ALEXA_STATE_SPEECH_START) {
                        mediaPlayer = new MediaPlayer();
                        mediaPlayer.setOnCompletionListener(mCompletionListener);
                        mediaPlayer.reset();
                        try {
                            mediaPlayer.setDataSource(instance.getExternalFilesDir("").getAbsolutePath() + "/" + "alexa_response.pcm");
                            mediaPlayer.prepare();
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                        mediaPlayer.start();
                    } else if (state == ALEXA_STATE_RECEIVE_TEXT) {
                        ActivityUtils.showToast("收到云端返回");
                    }
                    else if (state == ALEXA_STATE_LOGIN_SUCCESS) {
                        watch_avs_login_text.setText(R.string.already_logged_in);
                    } else if (state == ALEXA_STATE_NO_LOGIN) {
                        watch_avs_login_text.setText(R.string.not_log_in);
                    } else if (state == ALEXA_STATE_NO_NETWORK) {
                        watch_avs_login_text.setText(R.string.please_check_network);
                    } else if (state == ALEXA_STATE_SPEECH_RECOGNIZER) {
                        watch_avs_record_btn.setText(R.string.is_recording);
                    } else if (state == ALEXA_STATE_RECEIVE_DIRECTIVES_SPEAK) {
                        mPresenter.stopRecord(instance);
                        watch_avs_record_btn.setText(R.string.speech_started);
                    }
                }
            }
        });
    }

    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {

    }
}
