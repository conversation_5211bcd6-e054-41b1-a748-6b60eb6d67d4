package com.besall.allbase.view.activity.chipstoollevel4.AvsLwa;

import android.content.Context;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;

import java.util.Map;

public interface IAvsLwaPresenter {

    void pickDecice(AvsLwaActivity context, int scan);

    void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context);

    void pickWifi(AvsLwaActivity context);

    void sendWifiData(String name, String psw);

    Map<String, String> getProductSid();

    void sendAuthorizeResult(String authorizationCode, String redirectUri, String clientId);

    void sendLanguageData(String language);

}
