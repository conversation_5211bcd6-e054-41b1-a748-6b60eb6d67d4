package com.besall.allbase.bluetooth.service.throughput;

public class ThroughputConstants {

    //cmd
    public static final short                             OP_RESPONSE_TO_CMD = (short) 0x8000;
    public static final short               OP_INFORM_THROUGHPUT_TEST_CONFIG = (short) 0x8009;
    public static final short                        OP_THROUGHPUT_TEST_DATA = (short) 0x800A;
    public static final short                    OP_THROUGHPUT_TEST_DATA_ACK = (short) 0x800B;
    public static final short                        OP_THROUGHPUT_TEST_DONE = (short) 0x800C;


    //msg id
    public static final int                                       RSSI_START = 0x00001000;
    public static final int                  SPP_WAIT_INI_CONFIG_ACK_TIMEOUT = 0x00001001;
    public static final int                          RECEIVED_INI_CONFIG_RET = 0x00001007;
    public static final int                        SPP_SEND_THROUGH_PUT_NEXT = 0x00001003;
    public static final int                          GET_THROUGH_PUT_PACKAGE = 0x00001004;
    public static final int                         GET_THROUGH_PUT_DONE_MSG = 0x00001005;

    public static final int                      BLE_RECEIVED_INI_CONFIG_RET = 0x00001006;
    public static final int                      BLE_GET_THROUGH_PUT_PACKAGE = 0x00001007;
    public static final int                     BLE_GET_THROUGH_PUT_DONE_MSG = 0x00001008;

    public static final int                                INI_CONFIG_RETURN = 0x00001009;

    public static final int                                          EDIT_OK = 1;

    //msgstr
    public static final String                    SPPGET_THROUGH_PUT_PACKAGE = "SPPGET_THROUGH_PUT_PACKAGE";
    public static final String                    BLEGET_THROUGH_PUT_PACKAGE = "BLEGET_THROUGH_PUT_PACKAGE";

    /**
     * 吞吐量数据固定包
     */
    public static byte[]                         THROUGH_PUT_PACKAGE_PATTERN = new byte[] {0x01, (byte)0xf0, (byte)0xaa , (byte)0xff ,0x00 , 0x0f , 0x55};

    public static final int                                      NO_REPSONSE = 0;
    public static final int                                         RESPONSE = 1;

    public static final int                                 IS_USER_SPECIFIC = 1;
    public static final int                              IS_NO_USER_SPECIFIC = 0;

    public static final int                                             DOWN = 1;
    public static final int                                               UP = 0;

    public static final int                                BLE_MAX_DATA_SIZE = 512;
    public static final int                                SPP_MAX_DATA_SIZE = 627;

    /**
     * 发送的数据格式
     */
    public static final String                         KEY_TEST_DATA_PATTERN = "test_data_pattern";
    public static final String                        REAL_TEST_DATA_PATTERN = "real_test_data_pattern";

    /**
     * 配置缓存数据 . Default value is 10 seconds 2 BYTES
     */
    public static final String                       KEY_LASTING_TIME_SECOND = "lasting_time_second";
    public static final String                      REAL_LASTING_TIME_SECOND = "real_lasting_time_second";
    /**
     * BLE mtu 默认值 512-3 = 509 2bytes
     */
    public static final String                              KEY_BLE_DATA_SIZE = "ble_data_size";
    public static final String                             REAL_BLE_DATA_SIZE = "real_ble_data_size";
    /**
     * SPP MTU 默认值 627 2 bytes
     */
    public static final String                              KEY_SPP_DATA_SIZE = "spp_data_size";
    public static final String                             REAL_SPP_DATA_SIZE = "real_spp_data_size";

    /**
     * 上行或下行 ， 默认下行  1-down 0-up   1byte
     */
    public static final String                             KEY_TRAN_DRIECTION = "direction";
    public static final String                            REAL_TRAN_DRIECTION = "real_direction";

    /**
     * 是否支持RESPONSE default value is 0 ; 1byte
     */
    public static final String                           KEY_IS_WITH_RESPONSE = "is_with_response";
    public static final String                          REAL_IS_WITH_RESPONSE = "real_is_with_response";

    /**
     * 是否使用自定义参数 default is 1  1byte
     */
    public static final String             KEY_IS_USE_SPECIFIC_BLE_CONNECTION = "is_use_specific_ble_connection";
    public static final String            REAL_IS_USE_SPECIFIC_BLE_CONNECTION = "real_is_use_specific_ble_connection";

    /**
     * 最小连接参数
     */
    public static final String              KEY_MIN_CONNECTION_INTERVAL_IN_MS = "min_connection_interval_in_ms";
    public static final String             REAL_MIN_CONNECTION_INTERVAL_IN_MS = "real_min_connection_interval_in_ms";

    /**
     * 最大连接参数
     */
    public static final String              KEY_MAX_CONNECTION_INTERVAL_IN_MS = "max_connection_interval_in_ms";
    public static final String             REAL_MAX_CONNECTION_INTERVAL_IN_MS = "real_max_connection_interval_in_ms";
    public static final String                         THROUGHPUT_SAVE_FOLDER = "ThroughPutLog";


}
