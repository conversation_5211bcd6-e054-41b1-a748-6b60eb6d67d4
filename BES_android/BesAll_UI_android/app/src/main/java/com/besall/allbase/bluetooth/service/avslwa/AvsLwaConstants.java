package com.besall.allbase.bluetooth.service.avslwa;

public class AvsLwaConstants {

    public static final int                                AVS_LWA_LANGUAGE_SELECT = 0x00004200;
    public static final int                                       AVS_LWA_DATA_LOG = 0x00004201;
    public static final int                     AVS_LWA_RECEIVE_WIFI_STATE_CONNECT = 0x00004202;
    public static final int                      AVS_LWA_RECEIVE_WIFI_STATE_OFLINE = 0x00004203;
    public static final int             AVS_LWA_RECEIVE_WIFI_STATE_CONNECT_SUCCESS = 0x00004204;
    public static final int                AVS_LWA_RECEIVE_WIFI_STATE_CONNECT_FAIL = 0x00004205;
    public static final int                      AVS_LWA_RECEIVE_LWA_STATE_CONNECT = 0x00004206;
    public static final int                       AVS_LWA_RECEIVE_LWA_STATE_OFLINE = 0x00004207;
    public static final int                              AVS_LWA_RECEIVE_PRODUCTID = 0x00004208;
    public static final int                               AVS_LWA_SET_LANGUANGE_OK = 0x00004209;
    public static final int                            AVS_LWA_SET_LANGUANGE_ERROR = 0x00004210;
    public static final int                                      AVS_LWA_SET_ERROR = 0x00004211;
    public static final int                                    AVS_LWA_SET_SUCCESS = 0x00004212;
    public static final int                         AVS_LWA_GET_WHOLE_SETTING_DATA = 0x00004213;
    public static final int                    AVS_LWA_RECEIVE_LWA_CONNECT_SUCCESS = 0x00004214;
    public static final int                       AVS_LWA_RECEIVE_LWA_CONNECT_FAIL = 0x00004215;

    public static final int                            AVS_LWA_REQUEST_CODE_LOGOUT = 200;

    public final static String                                 AVS_LANGUAGE_SELECT = "AVS_LANGUAGE_SELECT";
    public final static String                          AVS_LANGUAGE_DIDSELECT_KEY = "AVS_LANGUAGE_DIDSELECT_KEY";
    public static final int                                 AVS_CHOOSE_WIFI_RESULT = 50;

    public final static String                        AVS_INTENT_SERVICECONFIG_KEY = "AVS_INTENT_SERVICECONFIG_KEY";
    public static final String                                 AVS_IS_LWA_LINK_KEY = "AVS_IS_LWA_LINK_KEY";

}
