package com.besall.allbase.bluetooth.service.auracastassistant;

import android.os.ParcelUuid;

public class AuracastAssistantConstants {

    public static ParcelUuid                                parcelUuid_id = ParcelUuid.fromString("00001852-0000-1000-8000-00805f9b34fb");
    public static ParcelUuid                           parcelUuid_service = ParcelUuid.fromString("00001856-0000-1000-8000-00805f9b34fb");

    public static final short                             OP_TOTA_BIS_ADV = (short)0x8101;
    public static final short                        OP_TOTA_BIS_ADV_STOP = (short)0x8102;
    public static final short                             OP_TOTA_QR_RESULT = (short)0x8103;

    public static final int               AURACAST_ASSISTANT_RECEIVE_DATA = 0x00001200;
    public static final int      AURACAST_ASSISTANT_RECEIVE_SAMPLING_RATE = 0x00001201;
    public static final int      AURACAST_ASSISTANT_RECEIVE_NUMBER_OF_BIS = 0x00001202;

}
