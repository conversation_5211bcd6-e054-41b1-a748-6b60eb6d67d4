package com.besall.allbase.view.activity.chipstoollevel4.throughput;

import android.content.Context;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;

/**
 * <AUTHOR>
 * @time $ $
 */
interface IThroughputPresenter {
    void pickDecice(ThroughputActivity context, int scan);

    void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context);

    void stopSpp();

    void throughupStart(int status);

    void throughDownStart(int status);

    void sendThroughputPackage(int status);

    void sendDoneMsg(int status);

    void sendack();



}
