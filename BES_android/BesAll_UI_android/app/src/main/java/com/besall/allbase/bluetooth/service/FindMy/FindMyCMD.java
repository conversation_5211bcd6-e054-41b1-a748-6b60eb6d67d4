package com.besall.allbase.bluetooth.service.FindMy;

import static com.besall.allbase.bluetooth.service.FindMy.FindMyConstants.BES_WATCH_ACTIVATE_PHONE;
import static com.besall.allbase.bluetooth.service.FindMy.FindMyConstants.BES_WATCH_COMMAND_ID_RECEIVE;
import static com.besall.allbase.bluetooth.service.FindMy.FindMyConstants.BES_WATCH_COMMAND_ID_SEND;
import static com.besall.allbase.bluetooth.service.FindMy.FindMyConstants.BES_WATCH_START_RSP;
import static com.besall.allbase.bluetooth.service.FindMy.FindMyConstants.BES_WATCH_START_SEND;
import static com.besall.allbase.bluetooth.service.FindMy.FindMyConstants.BES_WATCH_VENDOR_ID;
import static com.besall.allbase.bluetooth.service.FindMy.FindMyConstants.BES_WATCH_VERSION;

import com.bes.bessdk.utils.ArrayUtil;

public class FindMyCMD {

    public static byte[] getFimdMyCMD() {

        return getWatchPacket(BES_WATCH_START_SEND, BES_WATCH_COMMAND_ID_SEND, new byte[0]);
    }

    public static byte[] getWatchPacket(byte start, byte[] commandId, byte[] payload) {
        int totalLen = 1 + 1 + 2 + 2 + commandId.length + payload.length + 1;
        byte[] lastData = new byte[totalLen];
        lastData[0] = start;
        lastData[1] = BES_WATCH_VERSION;
        byte[] lengthByte = ArrayUtil.int2byte(totalLen);
        lastData[2] = lengthByte[0];
        lastData[3] = lengthByte[1];
        lastData[4] = BES_WATCH_VENDOR_ID[0];
        lastData[5] = BES_WATCH_VENDOR_ID[1];
        lastData[6] = commandId[0];
        lastData[7] = commandId[1];
        System.arraycopy(payload, 0, lastData, 8, payload.length);
        lastData[totalLen - 1] = (byte) 0x99;
        return lastData;
    }

    public static int receiveData(byte[] data) {
        if (data[0] == BES_WATCH_START_RSP) {
            byte[] commandId = new byte[2];
            System.arraycopy(data, 6, commandId, 0, 2);
            if (commandId == BES_WATCH_COMMAND_ID_RECEIVE) {
                return BES_WATCH_ACTIVATE_PHONE;
            }
        }
        return 0;
    }

}