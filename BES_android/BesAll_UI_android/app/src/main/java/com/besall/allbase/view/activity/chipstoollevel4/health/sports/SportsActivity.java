package com.besall.allbase.view.activity.chipstoollevel4.health.sports;

import android.content.Intent;
import android.graphics.Color;
import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.RadioButton;
import android.widget.RadioGroup;

import androidx.annotation.NonNull;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.R;
import com.besall.allbase.view.activity.chipstoollevel4.rssi.RssiChartBean;
import com.besall.allbase.view.base.BaseActivity;
import com.github.mikephil.charting.charts.BarChart;
import com.github.mikephil.charting.components.Legend;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet;

import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

public class SportsActivity extends BaseActivity<ISportsActivity, SportsPresenter> implements ISportsActivity, View.OnClickListener , BesServiceListener {

    private BarChart barChart;
    private static SportsActivity instance;
    private Button sports_read_start;
    RadioGroup data_pattern1;
    RadioButton radioButton00 , radioButton01 , radioButton02 ,radioButton03;
    List<RssiChartBean> rssiChartBeans = new ArrayList<>();
    public String address = null;
    HmDevice mHmDevice;
    BesServiceConfig mServiceConfig;
    @Override
    protected SportsPresenter createPresenter() {
        return new SportsPresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        mServiceConfig = new BesServiceConfig();
    }

    @Override
    protected int getContentViewId() {
        return R.layout.act_sports;
    }

    @Override
    protected void bindView() {
        inittoolbar("Sports");
        barChart = (BarChart) findViewById(R.id.sportsChart);
        barChart.setBackgroundColor(Color.parseColor("#00000000"));
        barChart.getDescription().setEnabled(true);
        barChart.setExtraOffsets(20,20,20,20);
        radioButton00 = (RadioButton) findViewById(R.id.pattern_00);
        radioButton01 = (RadioButton) findViewById(R.id.pattern_01);
        radioButton02 = (RadioButton) findViewById(R.id.pattern_02);
        sports_read_start = (Button) findViewById(R.id.sports_read_start);

        Intent intent = getIntent();
        address = intent.getStringExtra("device");
        Log.i(TAG, "bindView: " + address);
        mHmDevice = new HmDevice();
        mHmDevice.setBleAddress(address);
        mHmDevice.setPreferredProtocol(DeviceProtocol.PROTOCOL_BLE);
        mHmDevice.setDeviceName(intent.getStringExtra("name"));
        setServiceConfig();
        if (address != null){
            mPresenter.connectDevice(mServiceConfig, instance, instance);
        }    }

    @Override
    protected void initView() {
//        timer.schedule(timerTask,0,500);
        radioButton00.setOnClickListener(instance);
        radioButton01.setOnClickListener(instance);
        radioButton02.setOnClickListener(instance);
        sports_read_start.setOnClickListener(instance);
        setLegend();
        testData();
    }

    private Handler handler = new Handler() {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            if (msg.what == 1){
//                testData();
            }
        }
    };

    Timer timer = new Timer();
    TimerTask timerTask = new TimerTask() {
        @Override
        public void run() {
            Message message = new Message();
            message.what = 1;
            handler.sendMessage(message);
        }
    };
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_setting, menu);

        return true;
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;
            case R.id.menu_setting:
//                mPresenter.goToSettingActivity(instance);
                break;
        }
        return super.onOptionsItemSelected(item);
    }


    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }


    public int checkSportsType(){
        int dataPattern = 0;
        if(radioButton00.isChecked()) {
            dataPattern = 0;
        }else if(radioButton01.isChecked()) {
            dataPattern = 1;
        }else if(radioButton02.isChecked()) {
            dataPattern = 2;
        }
        Log.i(TAG, "checkSportsType: ===" + dataPattern);
        return dataPattern;
    }
    public void testData(){
        List<IBarDataSet> sets = new ArrayList<>();
        // 此处有两个DataSet，所以有两条柱子，BarEntry（）中的x和y分别表示显示的位置和高度
        // x是横坐标，表示位置，y是纵坐标，表示高度
        List<BarEntry> barEntries1 = new ArrayList<>();
        barEntries1.add(new BarEntry(0,390f));
        barEntries1.add(new BarEntry(2, 1100f));
        barEntries1.add(new BarEntry(4, 900f));
        barEntries1.add(new BarEntry(6, 700f));
        barEntries1.add(new BarEntry(8, 300f));
        BarDataSet barDataSet1 = new BarDataSet(barEntries1, "sleep1");
        barDataSet1.setValueTextColor(Color.RED);
        barDataSet1.setValueTextSize(16f);
        barDataSet1.setColor(Color.parseColor("#FF1EBBFF"));
        sets.add(barDataSet1);

        List<BarEntry> barEntries2 = new ArrayList<>();
        barEntries2.add(new BarEntry(1, 210f));
        barEntries2.add(new BarEntry(3, 450f));
        barEntries2.add(new BarEntry(5, 430f));
        barEntries2.add(new BarEntry(7, 440f));
        barEntries2.add(new BarEntry(9, 180f));
        BarDataSet barDataSet2 = new BarDataSet(barEntries2, "sleep2");
        barDataSet2.setValueTextColor(Color.RED);
        barDataSet2.setValueTextSize(16f);
        barDataSet2.setColor(Color.parseColor("#ffff5d5d"));
        sets.add(barDataSet2);

        BarData barData = new BarData(sets);
        barData.setBarWidth(0.4f);
        barChart.setData(barData);
    }

    private void setLegend() {
        Legend legend = barChart.getLegend();
        legend.setFormSize(12f); // 图例的图形大小
        legend.setTextSize(15f); // 图例的文字大小
        legend.setDrawInside(true); // 设置图例在图中
        legend.setOrientation(Legend.LegendOrientation.VERTICAL); // 图例的方向为垂直
        legend.setHorizontalAlignment(Legend.LegendHorizontalAlignment.RIGHT); //显示位置，水平右对齐
        legend.setVerticalAlignment(Legend.LegendVerticalAlignment.TOP); // 显示位置，垂直上对齐
        // 设置水平与垂直方向的偏移量
        legend.setYOffset(55f);
        legend.setXOffset(30f);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.sports_read_start:
                if (address != null){
                    mPresenter.sendSportType(checkSportsType());
                }
                break;
        }
    }

    public void setServiceConfig(){
        mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_BLE);
        mServiceConfig.setServiceUUID(BesSdkConstants.BES_TOTA_SERVICE_OTA_UUID);
        mServiceConfig.setCharacteristicsUUID(BesSdkConstants.BES_TOTA_CHARACTERISTI_OTA_UUID);
        mServiceConfig.setDescriptorUUID(BesSdkConstants.BES_TOTA_DESCRIPTOR_OTA_UUID);
        mServiceConfig.setDevice(mHmDevice);
        mServiceConfig.setTotaConnect(true);
        boolean useTotaV2 = (boolean) SPHelper.getPreference(instance, BesSdkConstants.BES_TOTA_USE_TOTAV2, BesSdkConstants.BES_TOTA_USE_TOTAV2_VALUE);
        mServiceConfig.setUseTotaV2(useTotaV2);

//        mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
//        mServiceConfig.setServiceUUID(BesSdkConstants.BES_SPP_CONNECT);
    }

    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {

    }

    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {

    }

    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {

    }

    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {

    }
}
