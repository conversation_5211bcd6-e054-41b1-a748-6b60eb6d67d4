package com.besall.allbase.view.activity.chipstoollevel4.health;

import android.content.Context;
import android.content.Intent;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;
import com.besall.allbase.bluetooth.service.health.HealthService;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.activity.tools.SettingActivity.SettingActivity;
import com.besall.allbase.view.base.BasePresenter;

/**
 * <AUTHOR>
 * @time $ $
 */
public class HealthPresenter extends BasePresenter<IHealthActivity> implements IHealthPresenter{

    private HealthService mHealthService;

    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        mHealthService = new HealthService(serviceConfig, listener, context);

    }

    @Override
    public void goToSettingActivity(HealthActivity activity) {
        ActivityUtils.gotoAct(new Intent(), activity, SettingActivity.class);

    }

    @Override
    public void pickDecice(HealthActivity context, int scan) {
        Intent intent = new Intent();
        intent.putExtra(BluetoothConstants.Scan.BES_SCAN, scan);
        ActivityUtils.gotoActForResult(intent, BluetoothConstants.Scan.REQUEST_CODE_SCAN, context, ScanActivity.class);
    }
}
