package com.besall.allbase.view.activity.chipstoollevel4.commandset;

import android.app.Activity;
import android.content.Context;
import android.media.MediaPlayer;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;

/**
 * <AUTHOR>
 * @time $ $
 */

interface ICommandSetPresenter {

    void pickDecice(CommandSetActivity context, int scan);

    void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context);

    void sendTestData(Byte cmdType, boolean hasType, Byte...type);
    void sendMimiSetPresentData(byte[] data);

    void sendGetVersionCrcData();
    void stopSpp();

    int getBtState();
    
    void startPlayVideoWithType(Context context, int type, MediaPlayer.OnCompletionListener mediaListener);

    void checkMicState(Activity activity, Context context, CheckMicStateListener listener);


    public interface CheckMicStateListener {
        void onMicStateChanged(int state, String msg);
    }
}


