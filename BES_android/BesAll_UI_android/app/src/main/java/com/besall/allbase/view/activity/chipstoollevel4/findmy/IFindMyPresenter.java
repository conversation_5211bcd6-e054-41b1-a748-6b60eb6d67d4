package com.besall.allbase.view.activity.chipstoollevel4.findmy;

import android.content.Context;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;

/**
 * <AUTHOR>
 * @time $ $
 */
interface IFindMyPresenter {

    void pickDecice(FindMyActivity context, int scan);

    void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context);

    void sendFindMyData();

    void disconnect();
}
