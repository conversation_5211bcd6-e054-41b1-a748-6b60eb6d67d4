package com.besall.allbase.view.activity.chipstoollevel4.findmy;

import android.content.Context;
import android.content.Intent;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;
import com.besall.allbase.bluetooth.service.FindMy.FindMyService;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.base.BasePresenter;


/**
 * <AUTHOR>
 * @time $ $
 */
class FindMyPresenter extends BasePresenter<IFindMyActivity> implements IFindMyPresenter {
    public final String TAG = getClass().getSimpleName();
    private FindMyService findMyService;

    @Override
    public void pickDecice(FindMyActivity context, int scan) {
        Intent intent = new Intent();
        intent.putExtra(BluetoothConstants.Scan.BES_SCAN, scan);
        ActivityUtils.gotoActForResult(intent, BluetoothConstants.Scan.REQUEST_CODE_SCAN, context, ScanActivity.class);
    }

    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        if (findMyService == null) {
            findMyService = new FindMyService(serviceConfig, listener, context);
        } else {
            findMyService.changeDevice(serviceConfig);
        }
    }

    @Override
    public void sendFindMyData() {
        if (findMyService != null) {
            findMyService.sendFindMyData();
        }
    }

    @Override
    public void disconnect() {
        if (findMyService != null) {
            findMyService.disconnected();
        }
    }


}
