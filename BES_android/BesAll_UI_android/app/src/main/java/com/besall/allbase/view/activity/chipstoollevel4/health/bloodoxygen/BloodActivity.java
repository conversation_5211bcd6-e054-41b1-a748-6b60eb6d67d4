package com.besall.allbase.view.activity.chipstoollevel4.health.bloodoxygen;

import android.content.Intent;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;

import androidx.annotation.NonNull;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.R;
import com.besall.allbase.view.base.BaseActivity;

public class BloodActivity extends BaseActivity<IBloodActivity, BloodPresenter> implements IBloodActivity, BesServiceListener, View.OnClickListener {

    private static BloodActivity instance;
    HmDevice mHmDevice;
    BesServiceConfig mServiceConfig;
    private Button blood_read_start;
    public String address = null;
    @Override
    protected BloodPresenter createPresenter() {
        return new BloodPresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        mServiceConfig = new BesServiceConfig();
    }

    @Override
    protected int getContentViewId() {
        return R.layout.act_blood;
    }

    @Override
    protected void bindView() {
        Intent intent = getIntent();
        address = intent.getStringExtra("device");
        Log.i(TAG, "bindView: " + address);
        mHmDevice = new HmDevice();
        mHmDevice.setBleAddress(address);
        mHmDevice.setPreferredProtocol(DeviceProtocol.PROTOCOL_BLE);
        mHmDevice.setDeviceName(intent.getStringExtra("name"));
        Log.i(TAG, "bindView: " + mHmDevice);
        setServiceConfig();

        if (address != null){
            mPresenter.connectDevice(mServiceConfig, instance, instance);
        }

        inittoolbar("Blood Oxygen");
        blood_read_start = (Button) findViewById(R.id.blood_read_start);
    }

    @Override
    protected void initView() {
        blood_read_start.setOnClickListener(instance);
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_setting, menu);

        return true;
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;
            case R.id.menu_setting:
//                mPresenter.goToSettingActivity(instance);
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    public void setServiceConfig(){
        mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_BLE);
        mServiceConfig.setServiceUUID(BesSdkConstants.BES_TOTA_SERVICE_OTA_UUID);
        mServiceConfig.setCharacteristicsUUID(BesSdkConstants.BES_TOTA_CHARACTERISTI_OTA_UUID);
        mServiceConfig.setDescriptorUUID(BesSdkConstants.BES_TOTA_DESCRIPTOR_OTA_UUID);
        mServiceConfig.setDevice(mHmDevice);
        mServiceConfig.setTotaConnect(true);
        boolean useTotaV2 = (boolean) SPHelper.getPreference(instance, BesSdkConstants.BES_TOTA_USE_TOTAV2, BesSdkConstants.BES_TOTA_USE_TOTAV2_VALUE);
        mServiceConfig.setUseTotaV2(useTotaV2);

//        mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
//        mServiceConfig.setServiceUUID(BesSdkConstants.BES_SPP_CONNECT);
    }

    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {

    }

    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {

    }

    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {

    }

    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.blood_read_start:
                if (address != null){
                    mPresenter.sendBOS();
                }
                break;
        }
    }
}
