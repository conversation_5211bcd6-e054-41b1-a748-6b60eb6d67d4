package com.besall.allbase.bluetooth.service.rssi;

import android.content.Context;
import android.util.Log;

import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.CmdInfo;
import com.bes.bessdk.utils.SPHelper;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.common.utils.FileUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

import static android.webkit.ConsoleMessage.MessageLevel.LOG;
import static com.bes.bessdk.utils.ArrayUtil.getFloat;
import static com.bes.bessdk.utils.ArrayUtil.int2byte;
import static com.besall.allbase.bluetooth.service.rssi.RssiConstants.RSSI_SAVE_FOLDER;

public class RssiCMD {
    static String TAG = "RssiCMD";

    static String startTime = "";
    public static String rssiinfo = "";

    public static byte[] RssiReadStart(Context context) {
        int Protocol = (int) SPHelper.getPreference(context, RssiConstants.RSSI_PROTOCOL, 0);
        Log.i(TAG, "RssiReadStart:     " + Protocol);
        byte flashType = (byte) BluetoothConstants.Connect.CMD_CONFIRM;
        if (Protocol == 0) {
            CmdInfo flashreadcmdInfo = new CmdInfo(RssiConstants.NEW_OP_TOTA_RSSI_READ_CMD, new byte[]{flashType});
            return flashreadcmdInfo.toBytes();
        } else if (Protocol == 1){
            CmdInfo flashreadcmdInfo = new CmdInfo(RssiConstants.OP_TOTA_RSSI_READ_CMD, new byte[]{flashType});
            return flashreadcmdInfo.toBytes();
        }
        CmdInfo flashreadcmdInfo = new CmdInfo(RssiConstants.OLD_OP_TOTA_RSSI_READ_CMD, new byte[]{flashType});
        return flashreadcmdInfo.toBytes();
    }

    public static String receiveData(byte[] data, Context context) {
        int typelen = 4;
        int Protocol = (int) SPHelper.getPreference(context, RssiConstants.RSSI_PROTOCOL, 0);
        if (Protocol == 0) {
            typelen = 4;
        } else {
            typelen = 10;
        }
        if (data.length < 10) {
            return "";
        }
        int max_rssi = 0;
        int min_rssi = 0;
        String ret = "";
        int agc = 0 ;
        int rssi = 0;
        int flag = 0;
        int ser = 0;
        int fa_idx_left = 0;
        int fa_idx_right = 0;
        byte[] info = new byte[data.length-typelen];
        System.arraycopy(data,typelen, info, 0, data.length-typelen);
        Log.i(TAG, "receiveData: rssistart" + ArrayUtil.toHex(data));
        Log.i(TAG, "receiveData: info" + ArrayUtil.toHex(info));
        if (data.length > 4) {
            for (int i = 0; i < (data.length - typelen); i ++) {
                info[i] = data[typelen + i];
            }
        }

        if((info == null)|| (info.length == 0)) {
            return "no recv data";
        }



//        1302
        else if(info[0] == 0x0b && (info[1]&0xff) == 0x90 && (info[2]&0xff) == 0x01 && (info[3] &0xff)== 0x00 && (info[4]&0xff)==0x15 && (info[5]&0xff)==0x00 && (info[6]&0xff)==0x01)
        {

            agc = (int)info[7]&0xff;
            rssi = ArrayUtil.getRssiValue(info[8]);
            Log.e("agc" + agc,"rssi"+ rssi+"");
            max_rssi = ArrayUtil.getRssiValue(info[9]);
            min_rssi = ArrayUtil.getRssiValue(info[10]);
            ret = "AGC:"+ agc + ";" + "RSSI:" + rssi + ";" + "max_rssi:" + max_rssi + ";" + "min_rssi:" + min_rssi + ";";
            agc = (int)info[11]&0xff;
            rssi = ArrayUtil.getRssiValue(info[12]);
            Log.e("agc" + agc ,"rssi"+ rssi+"");
            max_rssi = ArrayUtil.getRssiValue(info[13]);
            min_rssi = ArrayUtil.getRssiValue(info[14]);
            ser = ArrayUtil.getRssiValue(info[15]);
            fa_idx_left = ArrayUtil.getRssiValue(info[16]);
            ret = ret + "AGC:"+ agc + ";" + "RSSI:" + rssi + ";" + "max_rssi:" + max_rssi + ";" + "min_rssi:" + min_rssi + ";"+"PACKET LOSS RATE(%):" + ser + ";" + "FA_IDX_LEFT:" + fa_idx_left + ";";
            agc = (int)info[17]&0xff;
            rssi = ArrayUtil.getRssiValue(info[18]);
            Log.e("agc" +  agc,"rssi"+ rssi+"");
            max_rssi = ArrayUtil.getRssiValue(info[19]);
            min_rssi = ArrayUtil.getRssiValue(info[20]);
            ret = ret+ "AGC:"+ agc +";"+"RSSI:"+ rssi +";"+ "max_rssi:"+ max_rssi + ";" + "min_rssi:" + min_rssi + ";";
            agc = (int)info[21]&0xff;
            rssi = ArrayUtil.getRssiValue(info[22]);
            Log.e("agc" + agc,"rssi"+ rssi +"");
            max_rssi = ArrayUtil.getRssiValue(info[23]);
            min_rssi = ArrayUtil.getRssiValue(info[24]);
            ret = ret + "AGC:"+ agc + ";" + "RSSI:" + rssi + ";" + "max_rssi:" + max_rssi + ";" + "min_rssi:" + min_rssi + ";";
            agc = (int)info[25]&0xff;
            rssi = ArrayUtil.getRssiValue(info[26]);
            Log.e("agc" + agc,"rssi"+ rssi+"");
            max_rssi = ArrayUtil.getRssiValue(info[27]);
            min_rssi = ArrayUtil.getRssiValue(info[28]);
            ser = ArrayUtil.getRssiValue(info[29]);
            fa_idx_right = ArrayUtil.getRssiValue(info[30]);
            ret = ret + "AGC:"+ agc + ";" + "RSSI:" + rssi + ";" + "max_rssi:" + max_rssi + ";" + "min_rssi:" + min_rssi + ";" + "PACKET LOSS RATE(%):" + ser + ";" + "FA_IDX_RIGHT:" + fa_idx_right + ";" ;
            agc = (int)info[31]&0xff;
            rssi = ArrayUtil.getRssiValue(info[32]);
            Log.e("agc" + agc, "rssi" + rssi + "");
            max_rssi = ArrayUtil.getRssiValue(info[33]);
            min_rssi = ArrayUtil.getRssiValue(info[34]);
            ret = ret + "AGC:"+ agc+";"+"RSSI:"+ rssi + ";" + "max_rssi:" + max_rssi + ";" + "min_rssi:" + min_rssi + ";";
            flag = (int)info[35]&0xff;
            ret = ret + "flag:"+ flag;

            byte[] rssidata = new byte[29];
            System.arraycopy(info,7, rssidata, 0, 29);
            saveData(rssidata,context);
            rssiinfo = ret;
            return ret;

        }
        //1501单机
        //  06,63,24,00,0b,90,01,00,22,00,01,04,ce,da,c4,00,00,80,7f,06,00,00,00,00,00,00,00,00,00,00,00,80,7f,06,00,00,00,00,00,01,00,00,00,00,00,00,00,00,
        else if(info[0] == 0x0b && (info[1]&0xff) == 0x90 && (info[2]&0xff) == 0x01 && (info[3] &0xff)== 0x00 && (info[4]&0xff)==0x22 && (info[5]&0xff)==0x00 && (info[6]&0xff)==0x01)
        {
            agc = (int)info[7]&0xff;
            rssi = ArrayUtil.getRssiValue(info[8]);
            Log.e("agc" + agc,"rssi"+ rssi+"");
            max_rssi = ArrayUtil.getRssiValue(info[9]);
            min_rssi = ArrayUtil.getRssiValue(info[10]);
            ret = "AGC:"+ agc + ";" + "RSSI:" + rssi + ";" + "max_rssi:" + max_rssi + ";" + "min_rssi:" + min_rssi + ";";
            agc = (int)info[11]&0xff;
            rssi = ArrayUtil.getRssiValue(info[12]);
            Log.e("agc" + agc,"rssi"+ rssi+"");
            max_rssi = ArrayUtil.getRssiValue(info[13]);
            min_rssi = ArrayUtil.getRssiValue(info[14]);
            ser = ArrayUtil.getRssiValue(info[15]);
            fa_idx_left = ArrayUtil.getRssiValue(info[16]);
            ret = ret + "AGC:"+ agc + ";" + "RSSI:"+ rssi + ";" + "max_rssi:" + max_rssi + ";" + "min_rssi:" + min_rssi + ";" + "PACKET LOSS RATE(%):" +  ser + ";" + "FA_IDX_LEFT:" + fa_idx_left + ";";
            agc = (int)info[17]&0xff;
            rssi = ArrayUtil.getRssiValue(info[18]);
            Log.e("agc" + agc,"rssi"+ rssi+"");
            max_rssi = ArrayUtil.getRssiValue(info[19]);
            min_rssi = ArrayUtil.getRssiValue(info[20]);
            ret = ret+ "AGC:"+agc+";" + "RSSI:" + rssi + ";" + "max_rssi:" + max_rssi + ";" + "min_rssi:" + min_rssi + ";";
            agc = (int)info[21]&0xff;
            rssi = ArrayUtil.getRssiValue(info[22]);
            Log.e("agc" + agc,"rssi"+ rssi+"");
            max_rssi = ArrayUtil.getRssiValue(info[23]);
            min_rssi = ArrayUtil.getRssiValue(info[24]);
            ret = ret + "AGC:" + agc + ";" + "RSSI:" + rssi + ";" + "max_rssi:" + max_rssi + ";" + "min_rssi:" + min_rssi + ";";
            agc = (int)info[25]&0xff;
            rssi = ArrayUtil.getRssiValue(info[26]);
            Log.e("agc" + agc,"rssi"+ rssi+"");
            max_rssi = ArrayUtil.getRssiValue(info[27]);
            min_rssi = ArrayUtil.getRssiValue(info[28]);
            ser = ArrayUtil.getRssiValue(info[29]);
            fa_idx_right = ArrayUtil.getRssiValue(info[30]);
            ret = ret + "AGC:"+ agc + ";" + "RSSI:" + rssi + ";" + "max_rssi:" + max_rssi + ";" + "min_rssi:" + min_rssi + ";" + "PACKET LOSS RATE(%):" + ser + ";" + "FA_IDX_RIGHT:" + fa_idx_right + ";";
            agc = (int)info[31]&0xff;
            rssi = ArrayUtil.getRssiValue(info[32]);
            Log.e("agc" + agc,"rssi"+ rssi+"");
            max_rssi = ArrayUtil.getRssiValue(info[33]);
            min_rssi = ArrayUtil.getRssiValue(info[34]);
            ret = ret + "AGC:"+ agc +";"+"RSSI:" + rssi +";" +"max_rssi:"+ max_rssi +";"+"min_rssi:"+ min_rssi+";";
            flag = (int)info[35]&0xff;
            ret = ret + "flag:"+flag;

            byte[] rssidata = new byte[29];
            System.arraycopy(info,7, rssidata, 0, 29);
            saveData(rssidata,context);
            rssiinfo = ret;
            return ret;
        }

        //1501 2台手机
        //info0b,90,01,00,22,00,02,05,d0,da,b4,00,00,80,7f,02,00,06,dc,df,c6,00,00,00,00,00,00,80,7f,02,00,00,00,00,00,01,00,00,00,00,00,00,00,00,
        else if(info[0] == 0x0b && (info[1]&0xff) == 0x90 && (info[2]&0xff) == 0x01 && (info[3] &0xff)== 0x00 && (info[4]&0xff)==0x22 && (info[5]&0xff)==0x00 && (info[6]&0xff)==0x02)
        {

            agc = (int)info[7]&0xff;
            rssi = ArrayUtil.getRssiValue(info[8]);
            Log.e("agc" + agc,"rssi"+ rssi+"");
            max_rssi = ArrayUtil.getRssiValue(info[9]);
            min_rssi = ArrayUtil.getRssiValue(info[10]);
            ret = "AGC:"+agc+";"+"RSSI:"+rssi+";"+"max_rssi:"+max_rssi+";"+"min_rssi:"+min_rssi+";";
            agc = (int)info[11]&0xff;
            rssi = ArrayUtil.getRssiValue(info[12]);
            Log.e("agc" + agc,"rssi"+ rssi+"");
            max_rssi = ArrayUtil.getRssiValue(info[13]);
            min_rssi = ArrayUtil.getRssiValue(info[14]);
            ser = ArrayUtil.getRssiValue(info[15]);
            fa_idx_left = ArrayUtil.getRssiValue(info[16]);
            ret = ret + "AGC:"+agc+";"+"RSSI:"+rssi+";"+"max_rssi:"+max_rssi+";"+"min_rssi:"+min_rssi+";"+"PACKET LOSS RATE(%):"+ ser +";"+"FA_IDX_LEFT:"+fa_idx_left+";";
            agc = (int)info[17]&0xff;
            rssi = ArrayUtil.getRssiValue(info[18]);
            Log.e("agc" + agc,"rssi"+ rssi+"");
            max_rssi = ArrayUtil.getRssiValue(info[19]);
            min_rssi = ArrayUtil.getRssiValue(info[20]);
            ret = ret+ "AGC:"+agc+";"+"RSSI:"+rssi+";"+"max_rssi:"+max_rssi+";"+"min_rssi:"+min_rssi+";";
            agc = (int)info[21]&0xff;
            rssi = ArrayUtil.getRssiValue(info[22]);
            Log.e("agc" + agc,"rssi"+ rssi+"");
            max_rssi = ArrayUtil.getRssiValue(info[23]);
            min_rssi = ArrayUtil.getRssiValue(info[24]);
            ret = ret + "AGC:"+agc+";"+"RSSI:"+rssi+";"+"max_rssi:"+max_rssi+";"+"min_rssi:"+min_rssi+";";
            agc = (int)info[25]&0xff;
            rssi = ArrayUtil.getRssiValue(info[26]);
            Log.e("agc" + agc,"rssi"+ rssi+"");
            max_rssi = ArrayUtil.getRssiValue(info[27]);
            min_rssi = ArrayUtil.getRssiValue(info[28]);
            ser = ArrayUtil.getRssiValue(info[29]);
            fa_idx_right = ArrayUtil.getRssiValue(info[30]);
            ret = ret + "AGC:"+agc+";"+"RSSI:"+rssi+";"+"max_rssi:"+max_rssi+";"+"min_rssi:"+min_rssi+";"+"PACKET LOSS RATE(%):"+ser+";"+"FA_IDX_RIGHT:"+fa_idx_right+";";
            agc = (int)info[31]&0xff;
            rssi = ArrayUtil.getRssiValue(info[32]);
            Log.e("agc" + agc,"rssi"+ rssi+"");
            max_rssi = ArrayUtil.getRssiValue(info[33]);
            min_rssi = ArrayUtil.getRssiValue(info[34]);
            ret = ret + "AGC:"+agc+";"+"RSSI:"+rssi+";"+"max_rssi:"+max_rssi+";"+"min_rssi:"+min_rssi+";";
            flag = (int)info[35]&0xff;
            ret = ret + "flag:"+flag;

            byte[] rssidata = new byte[29];
            System.arraycopy(info,7, rssidata, 0, 29);

            saveData(rssidata,context);
            rssiinfo = ret;
            return ret;

        }
        return "error";
    }

    private static void saveData(byte[] data, Context context) {
        FileUtils fileUtils = new FileUtils(context);
        if (startTime.length() == 0) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss");
            Date date = new Date(System.currentTimeMillis());
            startTime = simpleDateFormat.format(date);
        }
        String rssianalysedata = startTime+rssiinfo+"\n";
        rssianalysedata = rssianalysedata.replace("AGC:", ",");
        rssianalysedata = rssianalysedata.replace("RSSI:", ",");
        rssianalysedata = rssianalysedata.replace("min_rssi:", ",");
        rssianalysedata = rssianalysedata.replace("max_rssi:", ",");
        rssianalysedata = rssianalysedata.replace(";", "");
        rssianalysedata = rssianalysedata.replace("PACKET LOSS RATE(%):", ",");
        rssianalysedata = rssianalysedata.replace("FA_IDX_LEFT:", ",");
        rssianalysedata = rssianalysedata.replace("FA_IDX_RIGHT:", ",");
        rssianalysedata = rssianalysedata.replace("flag:", ",");
        fileUtils.HandleAnalyseFile(rssianalysedata, RssiConstants.RSSI_SAVE_FOLDER, startTime+"_rssi_analyse.csv", startTime+"_rssi_analyse.csv");
        String rssidata = (startTime + rssiinfo.replace(";", " ")+"\n");
        fileUtils.HandleFileReport("rssidata",  RssiConstants.RSSI_SAVE_FOLDER, startTime + "_rssi.txt", startTime + "_rssi.txt");
    }

    public static String showlog(byte[] data){
        String ret = "";
        if (data.length > 0) {
            ret = "OnReceived: " +startTime + ArrayUtil.toHex(data) + "\n" + ret;
        }
        return ret;
    }
}