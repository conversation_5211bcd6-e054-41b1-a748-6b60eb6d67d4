package com.besall.allbase.bluetooth.service.customcmd;


import android.content.Context;
import android.os.Handler;
import android.os.Message;
import android.text.style.AlignmentSpan;
import android.util.Log;

import androidx.annotation.NonNull;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.service.base.BesBaseService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.message.BaseMessage;
import com.bes.sdk.utils.DeviceProtocol;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_ERROR;
import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_SUCCESS;

public class CustomCmdService extends BesBaseService {

    public CustomCmdService(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        super(serviceConfig, listener, context);
        startConnect(serviceConfig);
    }

    @Override
    public void onStatusChanged(HmDevice device, int status, DeviceProtocol protocol) {
        super.onStatusChanged(device, status, protocol);
        if (status == BES_CONNECT_ERROR) {
            callBackStateChangedMessage(BES_CONNECT_ERROR,"error");
        }
    }

    @Override
    public void onDataReceived(BaseMessage deviceMessage) {
        super.onDataReceived(deviceMessage);
        Log.i(TAG, "onDataReceived: -----" + ArrayUtil.toHex((byte[]) deviceMessage.getMsgContent()));
        if (mConfig.getTotaConnect() && !totauccess) {
            return;
        }

        String result = CustomCmdCMD.receiveData((byte[]) deviceMessage.getMsgContent(), mContext);
        if(result.length() > 20) {
            callBackStateChangedMessage(CustomCmdConstants.OP_TOTA_GET_CUSTOMER_CMD, result);

        }
        callBackStateChangedMessage(BesSdkConstants.TOTA_LOG_INFO, ArrayUtil.toHex((byte[]) deviceMessage.getMsgContent()));
    }

    public void sendCustomCmd(String data, int i) {
        if (totauccess) {
            sendData(CustomCmdCMD.SendCustomCmd(data, i), 5000);
//            String show = CustomCmdCMD.showcmd();
//            callBackStateChangedMessage(CustomCmdConstants.showcmdinfo, show);
        } else {
            callBackErrorMessage(BesSdkConstants.BES_TOTA_ERROR);
        }
    }

    public void sendCustomCommand(byte[] data) {
        if (totauccess) {
            sendData(data, 5000);
        } else {
            callBackErrorMessage(BesSdkConstants.BES_TOTA_ERROR);
        }
    }

}
