package com.besall.allbase.view.activity.chipstoollevel4.audiodump;

import android.Manifest;
import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothSocket;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.os.ParcelUuid;
import android.text.format.DateFormat;
import android.util.Log;

import com.bes.bessdk.scan.BtHeleper;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.SPHelper;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;
import com.besall.allbase.bluetooth.service.audiodump.AudioDumpService;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.common.utils.FileUtils;
import com.besall.allbase.common.utils.PcmUtils;
import com.besall.allbase.view.activity.tools.AudioListActivity.AudioListActivity;
import com.besall.allbase.view.activity.tools.FileActivity.FilelistActivity;
import com.besall.allbase.view.base.BasePresenter;

import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.RandomAccessFile;
import java.lang.ref.WeakReference;
import java.lang.reflect.Method;
import java.nio.ByteBuffer;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants.AUDIODUMP_CUSTOM_TYPE_KEY;
import static com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants.AUDIODUMP_SAVE_FILE_TYPE_KEY;
import static com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants.AUDIODUMP_SAVE_FILE_TYPE_VALUE_CUSTOM;
import static com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants.AUDIODUMP_SAVE_FILE_TYPE_VALUE_PCM;
import static com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants.AUDIODUMP_SAVE_FILE_TYPE_VALUE_WAV;
import static com.besall.allbase.common.Constants.CHOOSE_FILE_FOLDER_INTENT_KEY;
import static com.besall.allbase.common.Constants.FILE_CODE;
import static com.besall.allbase.view.activity.chipstoollevel4.capsensor.CapSensorActivity.DIRNAME;

import androidx.core.app.ActivityCompat;

/**
 * <AUTHOR>
 * @time $ $
 */
public class AudioDumpPresenter extends BasePresenter<IAudioDumpActivity> implements IAudioDumpPresenter {

    BluetoothAdapter bluetoothAdapter = null;
    BluetoothDevice bluetoothDevice = null;
    Context mContext;
    int spp_uuid_cnt = 0;
    final int SPP_UUID_MAX_NUM = 6;
    UUID[] SPP_UUID = new UUID[SPP_UUID_MAX_NUM];
    BluetoothSocket[] bt_socket = new BluetoothSocket[SPP_UUID_MAX_NUM];

    private SppRxThread g_sppRxThread;
    private boolean ifRunTime = true;
    private boolean isStart = false;
    int SPP_PACKET_SIZE = 988;
    final int SPP_PACKET_INFO_SIZE = 4;
    final int SPP_PACKET_PCM_SIZE = SPP_PACKET_SIZE - SPP_PACKET_INFO_SIZE;
    private byte[] pcm_buf = new byte[SPP_PACKET_PCM_SIZE];
    static String startTime = "";
    private int receiveCount = 0;
    private final Object threadObj = new Object();

    PcmUtils pcmUtils;
    static String TAG = "AudioDumpPresenter";

    private int curSampleRate = 0, curChannels = 0, curSampleBits = 0;

    @SuppressLint("MissingPermission")
    @Override
    public String findSppChannel(Context context) {
        mContext = context;
        bluetoothAdapter = BtHeleper.getBluetoothAdapter(mContext).getDefaultAdapter();
        if (bluetoothAdapter.isEnabled()) {
            Log.i(TAG, "findSppChannel: ------11111");
            if (dev_is_connected(bluetoothDevice) == false) {
                Log.i(TAG, "findSppChannel: ------2222");

                // Get connected BT device
                bluetoothDevice = get_connected_bt_dev(bluetoothAdapter);
                Log.i(TAG, "findSppChannel: ------33333");

                if (bluetoothDevice != null) {
                    Log.i(TAG, "findSppChannel: ------444444");

                    bluetoothDevice.fetchUuidsWithSdp();
                    ParcelUuid[] uuids = (ParcelUuid[]) bluetoothDevice.getUuids();
                    // Use connected Socket to check.
                    spp_uuid_cnt = 0;
                    for (ParcelUuid uuid: uuids) {
                        String UUID_func_name = stringToBytes(uuid.getUuid().getLeastSignificantBits());

                        Log.i(TAG, "findSppChannel: ------55555-->" + UUID_func_name);

                        if (UUID_func_name.regionMatches(0, "Aud_Dump", 0, 8)) {
                            if (spp_uuid_cnt < SPP_UUID_MAX_NUM) {
                                SPP_UUID[spp_uuid_cnt] = uuid.getUuid();
                                spp_uuid_cnt ++;
                            } else {
                                return "spp_uuid_cnt is too large: " + spp_uuid_cnt;
                            }
                        }
                    }
                    Log.i(TAG, "findSppChannel: ------666666");

                    for (int i = 0; i < spp_uuid_cnt; i++) {
                        if (bt_socket[i] == null || bt_socket[i].isConnected() == false) {
                            // Connect Socket
                            bt_socket[i] = connect_bt_socket(bluetoothDevice, SPP_UUID[i]);
                            if (bt_socket[i] == null) {
                                return "BT Device: Can not connect SPP on " + bluetoothDevice.getName();
                            }
                        }
                    }
                    return bluetoothDevice.getName() + "  Channel:" + spp_uuid_cnt;
                } else {
                    return "BT is not enabled";
                }
            }
        }
        return "already connect";
    }

    @Override
    public void openSppRxThread() {
        ifRunTime = true;
        if (g_sppRxThread != null) {
            return;
        }
        g_sppRxThread = new SppRxThread();
        g_sppRxThread.start();
    }

    @Override
    public void closeSppRxThread() {
        isStart = false;
        ifRunTime = false;
        g_sppRxThread = null;
    }

    @Override
    public void startRecord(String streamStartText) {
        getView().refreshPackageLostCount(false);
        getView().refreshPackageReceiveCount(false);
        isStart = true;
        startTime = "";

        receiveCount = 0;
        curIndex = 0;
        markList = new ArrayList<>();
        dataList = new ArrayList<>();
        markList.add(0);
        dataList.add(new byte[0]);

        openSppRxThread();

        try {
            for (int i = 0; i < spp_uuid_cnt; i ++) {
                byte[] ping_msg = ("Ping from socket" + Integer.toString(i)).getBytes();
                bt_socket[i].getOutputStream().write(ping_msg);
            }
        } catch (Exception e) { e.printStackTrace(); }

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    Thread.sleep(1000);
                    try {
                        String msg_str;
                        if (streamStartText.length() == 0) {
                            msg_str = "Stream_start";
                        } else {
                            msg_str = "Stream_start: " + streamStartText;
                        }
                        byte[] bytes_msg = msg_str.getBytes();

                        bt_socket[0].getOutputStream().write(bytes_msg);
                    } catch (Exception e) { e.printStackTrace(); }
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }).start();
    }

    @Override
    public void stopRecord() {
        if (bt_socket[0] != null && bt_socket[0].isConnected()) {
            try {
                String msg_str = "Stream_stop";
                byte[] bytes_msg = msg_str.getBytes();
                bt_socket[0].getOutputStream().write(bytes_msg);
            } catch (Exception e) { e.printStackTrace(); }
        }

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    Thread.sleep(100);

                    closeSppRxThread();

                    String type = (String) SPHelper.getPreference(mContext, AUDIODUMP_SAVE_FILE_TYPE_KEY, AUDIODUMP_SAVE_FILE_TYPE_VALUE_PCM);
                    if (type.equals(AUDIODUMP_SAVE_FILE_TYPE_VALUE_PCM)) {
                            File oldFile = new File("/storage/emulated/0/Android/data/com.bes.besall/files/AudioDump/" + startTime + "." + type);
                            File newFile = new File("/storage/emulated/0/Android/data/com.bes.besall/files/AudioDump/" + startTime + "_" + curChannels + "ch" + "." + type);
                        boolean rename = oldFile.renameTo(newFile);
                        Log.i(TAG, "run: --------" + rename);
                    } else if (type.equals(AUDIODUMP_SAVE_FILE_TYPE_VALUE_WAV)) {
                        File file = new File("/storage/emulated/0/Android/data/com.bes.besall/files/AudioDump/" + startTime + "." + type);
                        addHeaderBytesToFile(new WavHeader((int) file.length(), curSampleRate, curChannels, curSampleBits).getHeader());
                    }
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }).start();
    }

    @Override
    public void audioDumpSetInsertData(byte data) {
        for (int j = 0; j < SPP_PACKET_PCM_SIZE; j++) {
            pcm_buf[j] = data;
        }
    }

    @Override
    public void player(String path, int type) {
//        ShowProgressDialog.wait.dismiss();
        pcmUtils = PcmUtils.getInstant();
        //直接播放
        byte[] decodeData = getbytesFromeLocalFile(path);
        Log.i(TAG, "player: ++++" + decodeData);
        pcmUtils.resetPcmFile();
        pcmUtils.stopPlay();
        pcmUtils.addData(null, decodeData);
        pcmUtils.play(type);
    }

    @Override
    public void selectfile(AudioDumpActivity context, int file) {
        Intent intent = new Intent();
        ActivityUtils.gotoActForResult(intent, FILE_CODE, context, AudioListActivity.class);
    }

    //将本地文件转化为byte[]
    private byte[] getbytesFromeLocalFile(String path) {
        byte[] bytes = new byte[]{};
        try {
            InputStream in = new FileInputStream(path);
            bytes = toByteArray(in);
            in.close();
        } catch (IOException e) {
            Log.i(TAG, "++++" + e.toString());
            e.printStackTrace();
        }
        return bytes;
    }

    private byte[] toByteArray(InputStream in) throws IOException {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024 * 4];
        int n = 0;
        while ((n = in.read(buffer)) != -1) {
            out.write(buffer, 0, n);
        }
        return out.toByteArray();
    }

    @SuppressLint("MissingPermission")
    private BluetoothDevice get_connected_bt_dev(BluetoothAdapter bt_adapter) {
        BluetoothDevice ret_dev = null;
        try {
            Set<BluetoothDevice> devices = bt_adapter.getBondedDevices();
            Log.i("BLUETOOTH", "Found devices number: " + devices.size());
            for (BluetoothDevice device : devices) {
                if (dev_is_connected(device)) {
                    Log.i("BLUETOOTH", "Connected device: " + device.getName());
                    ret_dev = device;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return ret_dev;
    }

    private boolean dev_is_connected(BluetoothDevice dev) {
        if (dev == null) {
            return false;
        } else {
            try {
                Method isConnectedMethod = BluetoothDevice.class.getDeclaredMethod("isConnected", (Class[]) null);
                boolean isConnected = (boolean) isConnectedMethod.invoke(dev, (Object[]) null);
                return isConnected;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return false;
    }

    @SuppressLint("MissingPermission")
    private BluetoothSocket connect_bt_socket(BluetoothDevice bt_dev, UUID SPP_UUID) {
        Log.i("BLUETOOTH2","UUID: " + bluetoothDevice.getUuids().toString());

        BluetoothSocket bt_socket;

        try {
            bt_socket = bt_dev.createRfcommSocketToServiceRecord(SPP_UUID);

            // 检查有没有获取到
            if (bt_socket == null) {
                Log.i("BLUETOOTH","Create socket fail");
                return null;
            }

            // 等待连接，会阻塞线程
            bt_socket.connect();
            Log.i("BLUETOOTH","Connect OK");
        } catch (Exception connectException) {
            connectException.printStackTrace();
            Log.i("BLUETOOTH","Connect Fail");
            return null;
        }

        return bt_socket;
    }

    public static String stringToBytes(long l){
        return new String(longToBytes(l));
    }

    public static byte[] longToBytes(long l){
        ByteBuffer byteBuffer = ByteBuffer.allocate(Long.SIZE/Byte.SIZE);
        byteBuffer.putLong(l);
        return byteBuffer.array();
    }

    class SppRxThread extends Thread {
        @Override
        public void run() {
            super.run();
            while (ifRunTime) {
                if (isStart) {
                    try {
                        byte ret_bytes[] = new byte[1024 * 9];
                        int len;
                        for (int i = 0; i < spp_uuid_cnt; i ++) {
                            len = bt_socket[i].getInputStream().read(ret_bytes);
                            byte[] data = new byte[len];
                            System.arraycopy(ret_bytes, 0, data, 0, len);
                            receiveData(data);
                        }
                    } catch (Exception e) { e.printStackTrace(); }
                }
            }
        }
    }

    private byte curIndex = (byte) 0xff;
    private ArrayList<Integer> markList = new ArrayList<>();
    private ArrayList<byte[]> dataList = new ArrayList<>();

    private void receiveData(byte[] receiveData) {
        synchronized (threadObj) {
            if (receiveData.length == 20) {
                byte[] psBytes = new byte[4];
                System.arraycopy(receiveData, 4, psBytes, 0, 4);
                SPP_PACKET_SIZE = ArrayUtil.bytesToIntLittle(psBytes);
                byte[] spBytes = new byte[4];
                System.arraycopy(receiveData, 8, spBytes, 0, 4);
                curSampleRate = ArrayUtil.bytesToIntLittle(spBytes);

                byte[] bitBytes = new byte[4];
                System.arraycopy(receiveData, 12, bitBytes, 0, 4);
                curSampleBits = ArrayUtil.bytesToIntLittle(bitBytes);

                byte[] cnBytes = new byte[4];
                System.arraycopy(receiveData, 16, cnBytes, 0, 4);
                curChannels = ArrayUtil.bytesToIntLittle(cnBytes);
                return;
            }
            if (receiveData[0] != (byte) 0x7f || (receiveData.length % SPP_PACKET_SIZE) != 0) {
                LOG(TAG, "head: ---" + ArrayUtil.toHex(new byte[]{receiveData[0]}) + "---length-->" + receiveData.length);
                return;
            }

            for (int i = 0; i < receiveData.length / SPP_PACKET_SIZE; i++) {
                receiveCount ++;
                getView().refreshPackageReceiveCount(true);

                byte[] data = new byte[SPP_PACKET_SIZE - 4];
                System.arraycopy(receiveData, i * SPP_PACKET_SIZE + 4, data, 0, SPP_PACKET_SIZE - 4);
                byte index = receiveData[i * SPP_PACKET_SIZE + 3];

                byte diff = (byte) (index - curIndex);
                LOG(TAG, "index:<-debug->" + (int)(index & 0xFF) + "<-debug->   curIndex:" + (int)(curIndex & 0xFF) + "   diff:" + diff + "   markList.size():" + markList.toString());

                if (diff > 14) {
                    LOG(TAG, "------diff > 14");
                    boolean hasMark = false;
                    int saveCount = 0;
                    for (int j = 7; j < markList.size(); j ++) {
                        if (markList.get(j) == 0) {
                            hasMark = true;
                            saveCount = j;
                        } else if (!hasMark) {
                            saveCount = j + 1;
                        }
                    }
                    List<byte[]> saveDataList = dataList.subList(0, saveCount);
                    saveData(saveDataList);

                    List<byte[]> lastData = dataList.subList(saveCount, dataList.size());
                    List<Integer> lastMark = markList.subList(saveCount, markList.size());

                    curIndex = (byte) (curIndex + saveCount + 1);
                    markList = new ArrayList<>();
                    markList.addAll(lastMark);
                    dataList = new ArrayList<>();
                    dataList.addAll(lastData);
                    diff = (byte) (index - curIndex);
                }

                double sign = Math.signum(diff);
                if (sign < 0) {
                    LOG(TAG, "sign0000:---------->" + sign);
                    curIndex = index;
                    for (int j = 0; j < Math.abs(diff); j ++) {
                        markList.add(j, j == 0 ? 1 : 0);
                        dataList.add(j, j == 0 ? data : new byte[0]);
                    }
                } else {
                    LOG(TAG, "sign1111:---------->" + sign);
                    while (markList.size() < diff) {
                        markList.add(markList.lastIndexOf(1) + 1, 0);
                        dataList.add(markList.lastIndexOf(1) + 1, new byte[0]);
                    }
                    if (diff < markList.size()) {
                        markList.remove(diff);
                        dataList.remove(diff);
                    }
                    markList.add(diff, 1);
                    dataList.add(diff, data);
                }
            }

            if (markList.size() > 4 && !markList.contains(0)) {
                saveData(dataList);

                curIndex = (byte) (curIndex + dataList.size());
                markList = new ArrayList<>();
                dataList = new ArrayList<>();
                markList.add(0);
                dataList.add(new byte[0]);
            }
        }
    }

    private void saveData(ArrayList<byte[]> dataSource) {
        byte[] allData = new byte[0];
        for (int i = 0; i < dataSource.size(); i ++) {
            byte[] data = dataSource.get(i);
            if (data.length == 0) {
                allData = ArrayUtil.byteMerger(allData, pcm_buf);
                getView().refreshPackageLostCount(true);
                LOG(TAG, "Package Lost");
            } else {
                allData = ArrayUtil.byteMerger(allData, data);
            }
        }
        saveBytesToFile(allData);
    }

    private void saveData(List<byte[]> dataSource) {
        byte[] allData = new byte[0];
        for (int i = 0; i < dataSource.size(); i ++) {
            byte[] data = dataSource.get(i);
            if (data.length == 0) {
                allData = ArrayUtil.byteMerger(allData, pcm_buf);
                getView().refreshPackageLostCount(true);
                LOG(TAG, "Package Lost");
            } else {
                allData = ArrayUtil.byteMerger(allData, data);
            }
        }
        saveBytesToFile(allData);
    }

    private void saveBytesToFile(byte[] data) {
        if (startTime.length() == 0) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss");
            Date date = new Date(System.currentTimeMillis());
            startTime = simpleDateFormat.format(date);
        }
        String type = (String) SPHelper.getPreference(mContext, AUDIODUMP_SAVE_FILE_TYPE_KEY, AUDIODUMP_SAVE_FILE_TYPE_VALUE_PCM);
        if (type.equals(AUDIODUMP_SAVE_FILE_TYPE_VALUE_CUSTOM)) {
            type = (String) SPHelper.getPreference(mContext, AUDIODUMP_CUSTOM_TYPE_KEY, "");
        }
        File file = new File("/storage/emulated/0/Android/data/com.bes.besall/files/AudioDump/" + startTime + "." + type);
        if (!file.exists()) {
            try {
                boolean isCreate = file.createNewFile();
                Log.i(TAG, "saveBytesToFile: ---------" + isCreate);
                Log.i(TAG, "saveBytesToFile: ---------" + file.length());
                if (type.equals(AUDIODUMP_SAVE_FILE_TYPE_VALUE_WAV)) {
                    addHeaderBytesToFile(new WavHeader(100, 16000, 1, 16).getHeader());
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        BufferedOutputStream outStream = null;
        try {
            outStream = new BufferedOutputStream(new FileOutputStream(file, true));
            outStream.write(data);
            outStream.flush();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (null != outStream) {
                try {
                    outStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void addHeaderBytesToFile(byte[] headerData) {
        String type = (String) SPHelper.getPreference(mContext, AUDIODUMP_SAVE_FILE_TYPE_KEY, AUDIODUMP_SAVE_FILE_TYPE_VALUE_PCM);
        File file = new File("/storage/emulated/0/Android/data/com.bes.besall/files/AudioDump/" + startTime + "." + type);
        try {
            RandomAccessFile accessFile = new RandomAccessFile(file, "rw");
            accessFile.seek(0);
            accessFile.write(headerData);
            accessFile.close();
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private void LOG(String TAG, String msg) {
        Log.i(TAG, "LOG: ---------" + msg);
        FileUtils.writeTOFile(msg, "AudioDump_Log", startTime, "txt");
    }

}
