package com.besall.allbase.view.activity.tools.SettingActivity;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.besall.allbase.R;

import java.util.List;

public class SettingAdapter extends ArrayAdapter<SettingBean> implements View.OnClickListener {

    private int resourceId;
    private Context mContext;

    public SettingAdapter(Context context, int textViewResourceId, List<SettingBean> objects) {
        super(context, textViewResourceId, objects);
        resourceId = textViewResourceId;
        mContext = context;
    }

    @NonNull
    @Override
    public View getView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
        SettingBean settingBean = getItem(position);
        View view = LayoutInflater.from(getContext()).inflate(resourceId,parent,false);
        TextView path = (TextView)view.findViewById(R.id.setting_path);
        path.setText(settingBean.getPath());
        TextView size = (TextView)view.findViewById(R.id.setting_size);
        size.setText(settingBean.getSize());
        Button button = (Button) view.findViewById(R.id.delete_button);
        button.setTag(settingBean.getTag());
        button.setOnClickListener((View.OnClickListener) mContext);
        Button button2 = (Button) view.findViewById(R.id.path_button);
        button2.setTag(settingBean.getTag() + 100);
        button2.setOnClickListener((View.OnClickListener) mContext);
        return view;
    }

    @Override
    public void onClick(View v) {

    }
}
