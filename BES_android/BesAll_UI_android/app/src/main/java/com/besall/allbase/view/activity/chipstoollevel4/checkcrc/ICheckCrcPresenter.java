package com.besall.allbase.view.activity.chipstoollevel4.checkcrc;

import android.content.Context;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;

/**
 * <AUTHOR>
 * @time $ $
 */
interface ICheckCrcPresenter {
    void pickDecice(CheckCrcActivity context, int scan);

    void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context);

    void getCrc();

    void factoryReset();
}
