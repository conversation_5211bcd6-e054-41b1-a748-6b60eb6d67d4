package com.besall.allbase.view.activity.chipstoollevel4.throughput;

import android.content.Context;
import android.content.Intent;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;

import com.besall.allbase.bluetooth.service.throughput.ThrougputService;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.base.BasePresenter;

import java.util.Timer;
import java.util.TimerTask;

/**
 * <AUTHOR>
 * @time $ $
 */
class ThroughputPresenter extends BasePresenter<IThroughputActivity> implements IThroughputPresenter {

    private ThrougputService ThrougputService;
    private Timer timer;                  //创建一个定时器对象
    private TimerTask task;
    static String TAG = "ThroughputPRESENTER";
    @Override
    public void pickDecice(ThroughputActivity context, int scan) {
        Intent intent = new Intent();
        intent.putExtra(BluetoothConstants.Scan.BES_SCAN, scan);
        ActivityUtils.gotoActForResult(intent, BluetoothConstants.Scan.REQUEST_CODE_SCAN, context, ScanActivity.class);
    }

    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        ThrougputService = new ThrougputService(serviceConfig, listener, context);
    }



    @Override
    public void throughDownStart(int status) {
        if (ThrougputService != null) {
            if (status == 0) {
                ThrougputService.throughDownStart(0);
            } else if (status == 1) {
                ThrougputService.throughDownStart(1);
            }
        }
    }

    @Override
    public void stopSpp() {
        if (ThrougputService != null) {
            ThrougputService.disconnected();
        }
    }

    @Override
    public void throughupStart(int status) {
        if (ThrougputService != null) {
            if (status == 0) {
                ThrougputService.throughupStart(0);
            } else if (status == 1){
                ThrougputService.throughupStart(1);
            }
        }
    }

    @Override
    public void sendThroughputPackage(int status) {
        if (status == 0) {
            ThrougputService.sendPackage(0);
        } else if (status == 1) {
            ThrougputService.sendPackage(1);
        }

    }

    @Override
    public void sendDoneMsg(int status) {
        if (ThrougputService != null) {
            ThrougputService.sendDone();
        }
    }

    @Override
    public void sendack() {
        ThrougputService.sendThroughputAck();
    }





}
