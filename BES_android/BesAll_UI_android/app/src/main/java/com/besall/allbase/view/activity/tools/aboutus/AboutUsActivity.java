package com.besall.allbase.view.activity.tools.aboutus;


import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.utils.SPHelper;
import com.besall.allbase.R;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.activity.level3.FunctionOtaActivity;
import com.besall.allbase.view.base.BaseActivity;

/**
 * <AUTHOR>
 * @time $ $
 */
public class AboutUsActivity extends BaseActivity<IAboutUsActivity, AboutUsPresenter> implements IAboutUsActivity, View.OnClickListener {

    private static AboutUsActivity instance;
    private Button privacy_policy;
    private View agree_view;
    private TextView agreeTV;
    private Button agree;
    private Button disagree;
    private TextView version_text;
    private SharedPreferences preferences;
    private SharedPreferences.Editor editor;
    private final String AGREE_KEY = "Bes_Agree_Key";



    @Override
    protected AboutUsPresenter createPresenter() {
        return new AboutUsPresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        preferences = getSharedPreferences("AGREE_KEY", 0);
        editor = preferences.edit();
    }

    @Override
    protected int getContentViewId() {
        return R.layout.act_aboutus;
    }

    @Override
    protected void bindView() {
        privacy_policy = (Button)findViewById(R.id.privacy_policy);
        agree_view = (View)findViewById(R.id.agree_view);
        privacy_policy = (Button)findViewById(R.id.privacy_policy);
        agree = (Button)findViewById(R.id.agree);
        disagree = (Button)findViewById(R.id.disagree);
        agreeTV = (TextView)findViewById(R.id.agreeTV);
        version_text = (TextView)findViewById(R.id.version_text);
        tv_title = (TextView) findViewById(R.id.tv_title);
        mToolbar = (Toolbar) findViewById(R.id.toolbar);
    }

    @Override
    protected void initView() {
        privacy_policy.setOnClickListener(this);
        privacy_policy.setOnClickListener(instance);
        agree.setOnClickListener(instance);
        disagree.setOnClickListener(instance);

        agreeTV.setText(getString(R.string.agreement));

        version_text.setText(getString(R.string.appVersion) + ": " + getVersionName(instance));

        boolean show = preferences.getBoolean(AGREE_KEY, true);
        if (show) {
            agree_view.setVisibility(View.VISIBLE);
        } else {
            agree_view.setVisibility(View.GONE);
        }
        tv_title.setText("BES TOOLS");
        mToolbar.setTitle("");
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);



}

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {

    }

    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.main, menu);
        //getMenuInflater().inflate(R.menu.menu_toolbar, menu);
        //getMenuInflater().inflate(R.menu.menu_toolbar, menu);
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;

        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.privacy_policy:
                agree_view.setVisibility(View.VISIBLE);
                break;
            case R.id.agree:
                agree_view.setVisibility(View.GONE);
                editor.putBoolean(AGREE_KEY, false);
                editor.commit();
                break;
            case R.id.disagree:
                editor.putBoolean(AGREE_KEY, true);
                editor.commit();
                finish();
                break;
            default:
                break;
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        //log
        SPHelper.removePreference(this, BesSdkConstants.BES_SAVE_LOG_NAME);
    }

    public static String getVersionName(Context mContext) {
        String versionName = "";
        try {
//            get AndroidManifest.xml --- android:versionCode
            versionName = mContext.getPackageManager().
                    getPackageInfo(mContext.getPackageName(), 0).versionName;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return versionName;
    }

}
