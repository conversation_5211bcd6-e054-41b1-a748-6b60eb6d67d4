package com.besall.allbase.common.utils;

import android.content.Context;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.util.Log;

import com.bes.bessdk.utils.ArrayUtil;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

public class WifiInfoUtil {
    public static final String TAG = "WifiInfoUtil";
    private static WifiManager mWifiManager;

//       10byte        2byte
//    wificonfig  (后面数据总长度)                          +
//    ssid0   (ssid长度)    ssid                                  +
////    psw00   (password长度)    password            +
////    bssid   (bssid长度)    bssid                             +
////    chl00  (channel长度)  channel                        +
////    SecTp  (security type长度)  security type       +
////    IsHid  (isHidden长度)  is hidden
    private static final byte[] wificonfigHeader = getBytes("wificonfig");
    private static final byte[] ssidHeader = getBytes("ssid0");
    private static byte[] ssidBytes;
    private static final byte[] passwordHeader  = getBytes("psw00");
    private static byte[] passwordBytes;
    private static final byte[] bssidHeader = getBytes("bssid");
    private static byte[] bssidBytes;
    private static final byte[] channelHeader = getBytes("chl00");
    private static byte[] channelBytes;
    private static final byte[] securityTypeHeader = getBytes("SecTp");
    private static byte[] securityTypeBytes;
    private static final byte[] isHiddenHeader = getBytes("IsHid");
    private static byte[] isHiddenBytes;


    public static byte[] getWifiInfoData(Context context, String password) {
        ssidBytes = new byte[0];
        passwordBytes = new byte[0];
        bssidBytes = new byte[0];
        channelBytes = new byte[0];
        securityTypeBytes = new byte[0];
        isHiddenBytes = new byte[0];

        getWifiInfo(context, password);

        byte[] data = new byte[0];
        data = ArrayUtil.bytesSplic(data, ssidHeader, ArrayUtil.intToBytes2(ssidBytes.length), ssidBytes);
        data = ArrayUtil.bytesSplic(data, passwordHeader, ArrayUtil.intToBytes2(passwordBytes.length), passwordBytes);
        data = ArrayUtil.bytesSplic(data, bssidHeader, ArrayUtil.intToBytes2(bssidBytes.length), bssidBytes);
        data = ArrayUtil.bytesSplic(data, channelHeader, ArrayUtil.intToBytes2(channelBytes.length), channelBytes);
        data = ArrayUtil.bytesSplic(data, securityTypeHeader, ArrayUtil.intToBytes2(securityTypeBytes.length), securityTypeBytes);
        data = ArrayUtil.bytesSplic(data, isHiddenHeader, ArrayUtil.intToBytes2(isHiddenBytes.length), isHiddenBytes);

        return ArrayUtil.bytesSplic(wificonfigHeader, ArrayUtil.intToBytes2(data.length), data, new byte[0]);
    }

    private static void getWifiInfo(Context context, String password) {
        mWifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
        WifiInfo info = mWifiManager.getConnectionInfo();
        StringBuilder sb  = new StringBuilder();
        if (info.getSSID().contains("unknown ssid")) {
            return;
        }
        sb.append("SSID: ").append(info.getSSID()).append(" \n");
        sb.append("BSSID: ").append(info.getBSSID()).append(" \n");
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            sb.append("security: ").append(info.getCurrentSecurityType()).append(" \n");
        }
        sb.append("Freq: ").append(info.getFrequency()).append(" \n");
//        String password = getConnectedNetworkWithPassword();
//        Log.i(TAG, "password: " + password);
        Log.i(TAG, "sb: " + sb);
        ssidBytes = getBytes(info.getSSID());
        passwordBytes = getBytes(password);
        bssidBytes = getBytes(info.getBSSID());
        channelBytes = getBytes(info.getFrequency() + "");
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            securityTypeBytes = getBytes(info.getCurrentSecurityType() + "");
        }
        isHiddenBytes = new byte[0];
    }

    private static String getConnectedNetworkWithPassword() {
//        final WifiEnterpriseConfig config = mWifiManager.getPrivilegedConnectedNetwork();
        WifiConfiguration config = null;
        try {
            Method method = WifiManager.class.getMethod("getPrivilegedConnectedNetwork");
            config = (WifiConfiguration) method.invoke(mWifiManager);
        } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
            Log.d(TAG, "getPrivilegedConnectedNetwork failed ", e);
        }
        if (config != null) {
            Log.d(TAG, "preSharedKey = " + config.preSharedKey);
            if (config.allowedKeyManagement.get(WifiConfiguration.KeyMgmt.NONE)
                    && config.allowedAuthAlgorithms.get(
                    WifiConfiguration.AuthAlgorithm.SHARED)) {
                return config
                        .wepKeys[config.wepTxKeyIndex];
            } else {
                return config.preSharedKey;
            }
        }
        return "";
    }

    private static byte[] getBytes(String str) {
        return ArrayUtil.toBytes(ArrayUtil.str2HexStr(str));
    }
}
