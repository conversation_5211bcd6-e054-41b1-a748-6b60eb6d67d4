package com.besall.allbase.view.activity.chipstoollevel4.AvsLwa;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_ERROR;
import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_SUCCESS;
import static com.bes.bessdk.BesSdkConstants.BES_WIFI_CHARACTERISTIC_RX_UUID;
import static com.bes.bessdk.BesSdkConstants.BES_WIFI_CHARACTERISTIC_TX_UUID;
import static com.bes.bessdk.BesSdkConstants.BES_WIFI_DESCRIPTOR_UUID;
import static com.bes.bessdk.BesSdkConstants.BES_WIFI_SERVICE_UUID;
import static com.besall.allbase.bluetooth.BluetoothConstants.Scan.BES_SCAN_RESULT;
import static com.besall.allbase.bluetooth.BluetoothConstants.Scan.CHOOSE_WIFI_RESULT_KEY;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_CHOOSE_WIFI_RESULT;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_INTENT_SERVICECONFIG_KEY;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_IS_LWA_LINK_KEY;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LANGUAGE_SELECT;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_DATA_LOG;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_LANGUAGE_SELECT;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_RECEIVE_LWA_CONNECT_FAIL;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_RECEIVE_LWA_CONNECT_SUCCESS;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_RECEIVE_LWA_STATE_CONNECT;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_RECEIVE_PRODUCTID;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_RECEIVE_WIFI_STATE_CONNECT_FAIL;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_RECEIVE_WIFI_STATE_OFLINE;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_REQUEST_CODE_LOGOUT;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_SET_LANGUANGE_ERROR;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_SET_LANGUANGE_OK;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.net.Uri;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.UiThread;

import com.amazon.identity.auth.device.AuthError;
import com.amazon.identity.auth.device.api.Listener;
import com.amazon.identity.auth.device.api.authorization.AuthCancellation;
import com.amazon.identity.auth.device.api.authorization.AuthorizationManager;
import com.amazon.identity.auth.device.api.authorization.AuthorizeListener;
import com.amazon.identity.auth.device.api.authorization.AuthorizeRequest;
import com.amazon.identity.auth.device.api.authorization.AuthorizeResult;
import com.amazon.identity.auth.device.api.authorization.ProfileScope;
import com.amazon.identity.auth.device.api.authorization.Scope;
import com.amazon.identity.auth.device.api.authorization.ScopeFactory;
import com.amazon.identity.auth.device.api.authorization.User;
import com.amazon.identity.auth.device.api.workflow.RequestContext;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.R;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.service.avslwa.AvsLwaCMD;
import com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.activity.chipstoollevel4.AvsLwa.resize.AvsLwaResizeActivity;
import com.besall.allbase.view.activity.tools.confirmdialog.AvsLwaWifiDialog;
import com.besall.allbase.view.activity.tools.confirmdialog.AvsLwaWifiDialoglistener;
import com.besall.allbase.view.base.BaseActivity;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AvsLwaActivity extends BaseActivity<IAvsLwaActivity, AvsLwaPresenter> implements IAvsLwaActivity, View.OnClickListener, AdapterView.OnItemClickListener, BesServiceListener {

    private static AvsLwaActivity instance;
    public String cur_title = "AVS LWA";
    public String cur_title_login = "Login with Amazon";
    public String cur_title_guide = "Guide with Amazon";

    //AVS login
    private RequestContext requestContext;
    private boolean mIsLoggedIn;
    private View mLoginButton;
    private TextView jump_alexa_app_textview;
    private String PRODUCT_ID = "INSERT YOUR PRODUCT ID FROM AMAZON DEVELOPER CONSOLE";
    private String PRODUCT_DSN = "INSERT UNIQUE DSN FOR YOUR DEVICE";
    private String CODE_CHALLENGE = "INSERT CODE CHALLENGE FROM DEVICE FOR THIS REQUEST";
    private static final String CODE_CHALLENGE_METHOD = "S256";

    //AVS guide
    private Button avs_guide_next;
    private ArrayList<String> languages;
    //ui
    private static String MAP_KEY_PRODUCT_NAME = "MAP_KEY_PRODUCT_NAME";
    private static String MAP_KEY_PRODUCT_BLE_ADDRESS = "MAP_KEY_PRODUCT_BLE_ADDRESS";
    private static String PREFERENCE_KEY_PRODUCT = "PREFENCE_KEY_PRODUCT";

    private ListView product_list;
    private AvsProductAdapter mAdapter;

    private HmDevice mHmDevice;
    private BesServiceConfig mServiceConfig;


    @Override
    protected AvsLwaPresenter createPresenter() {
        return new AvsLwaPresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        SPHelper.putPreference(instance, AVS_IS_LWA_LINK_KEY, false);
        mServiceConfig = new BesServiceConfig();
        mServiceConfig.setServiceUUID(BES_WIFI_SERVICE_UUID);
        mServiceConfig.setCharacteristicsUUID(BES_WIFI_CHARACTERISTIC_RX_UUID);
        mServiceConfig.setCharacteristicsTX(BES_WIFI_CHARACTERISTIC_TX_UUID);
        mServiceConfig.setDescriptorUUID(BES_WIFI_DESCRIPTOR_UUID);
        mServiceConfig.setTotaConnect(false);
        mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_BLE);


    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_avslwa;
    }

    @Override
    protected void bindView() {
        bindBaseUI();
    }

    @SuppressLint("ResourceAsColor")
    @Override
    protected void initView() {
       initBaseUI();
//        initializeGuideUI();

//        startLoginAvs();

    }

    private void bindBaseUI() {
        done = (Button) findViewById(R.id.done);
        loginfo = (View) findViewById(R.id.loginfo);
        logV = (TextView) findViewById(R.id.logV);
        product_list = (ListView) findViewById(R.id.product_list);

    }

    private void initBaseUI() {
        inittoolbar(cur_title);
        tv_title.setOnClickListener(instance);
        done.setOnClickListener(instance);
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);
        loadanimdrawable();

        mAdapter = new AvsProductAdapter(instance, turnStringToProductList((String) SPHelper.getPreference(getApplicationContext(), PREFERENCE_KEY_PRODUCT, "")));
        product_list.setAdapter(mAdapter);
        product_list.setOnItemClickListener(instance);
    }

    private void initializeBaseUI() {
        setContentView(R.layout.activity_avslwa);
        bindBaseUI();
        initBaseUI();
    }

    public boolean onCreateOptionsMenu(Menu menu) {
        if (tv_title.getText().equals(cur_title)) {
            getMenuInflater().inflate(R.menu.menu_add, menu);
        } else {
            getMenuInflater().inflate(R.menu.main, menu);
        }
        return true;
    }

    private String turnProductListToString(List<Map<String, String>> list) {
        String str = "";
        String mapKeySplitMark = "-=s8u=-";
        String mapSplitMark = "-=l98=-";
        String listSplitMark = "-=8n7=-";
        for (int i = 0; i < list.size(); i ++) {
            Map<String, String> map = list.get(i);
            str = str + MAP_KEY_PRODUCT_NAME + mapKeySplitMark + map.get(MAP_KEY_PRODUCT_NAME) + mapSplitMark + MAP_KEY_PRODUCT_BLE_ADDRESS + mapKeySplitMark + map.get(MAP_KEY_PRODUCT_BLE_ADDRESS) + ((i == (list.size() - 1)) ? "" : listSplitMark);
        }
        return str;
    }

    private List<Map<String, String>> turnStringToProductList(String str) {
        if (str.length() == 0) return new ArrayList<>();
        List<Map<String, String>> productList = new ArrayList<>();
        String mapKeySplitMark = "-=s8u=-";
        String mapSplitMark = "-=l98=-";
        String listSplitMark = "-=8n7=-";
        String[] data = str.split(listSplitMark);
        for (int i = 0; i < data.length; i ++) {
            Map<String, String> map = new HashMap<>();
            String[] mapData = data[i].split(mapSplitMark);
            for (int j = 0; j < mapData.length; j ++) {
                String[] keyData = mapData[j].split(mapKeySplitMark);
                map.put(keyData[0], keyData[1]);
            }
            productList.add(map);
        }
        return productList;
    }

    private void deleteItem(int index) {
        List<Map<String, String>> mList = turnStringToProductList((String) SPHelper.getPreference(getApplicationContext(), PREFERENCE_KEY_PRODUCT, ""));
        mList.remove(index);
        SPHelper.putPreference(getApplicationContext(), PREFERENCE_KEY_PRODUCT, turnProductListToString(mList));
        mAdapter.setProducts(mList);
    }

    private boolean isNewProduct(HmDevice device, List<Map<String, String>> list) {
        for (int i = 0; i < list.size(); i ++) {
            Map<String, String> map = list.get(i);
            if (map.get(MAP_KEY_PRODUCT_BLE_ADDRESS).equals(device.getBleAddress())) {
                map.put(MAP_KEY_PRODUCT_NAME, device.getDeviceName());
                map.put(MAP_KEY_PRODUCT_BLE_ADDRESS, device.getBleAddress());
                list.add(i, map);
                SPHelper.putPreference(getApplicationContext(), PREFERENCE_KEY_PRODUCT, turnProductListToString(list));
                return false;
            }
        }
        return true;
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                if (tv_title.getText().equals(cur_title)) {
                    finish();
                    return false;
                }
                initializeBaseUI();
                break;
            case R.id.menu_add:
//                startLoginAvs();
                mPresenter.pickDecice(instance, BluetoothConstants.Scan.SCAN_BLE);
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.done:
                loginfo.setVisibility(View.GONE);
                break;
            case R.id.tv_title:
                loginfo.setVisibility(View.VISIBLE);
                break;
            default:
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == BluetoothConstants.Scan.REQUEST_CODE_SCAN) {
            if (resultCode != RESULT_OK) return;
            onPickDevice(resultCode, data);
        } else if (requestCode == AVS_CHOOSE_WIFI_RESULT) {
            if (resultCode != RESULT_OK) return;
            String str = data.getStringExtra(CHOOSE_WIFI_RESULT_KEY);
            showWifiDialog(str);
        } else if (requestCode == AVS_LWA_LANGUAGE_SELECT) {
            initializeBaseUI();
            if (resultCode != RESULT_OK) return;

//                Log.i(TAG, "onActivityResult: ------" + data.getIntExtra(AVS_LANGUAGE_DIDSELECT_KEY, 0));
//                loadinganim(getString(R.string.setting_language));
//                mPresenter.sendLanguageData(languages.get(data.getIntExtra(AVS_LANGUAGE_DIDSELECT_KEY, 0)));
            goToAvsLwaResizeActivity();
        } else if (requestCode == AVS_LWA_REQUEST_CODE_LOGOUT) {
            if (resultCode != RESULT_OK) return;
            BesServiceConfig serviceConfig = (BesServiceConfig)data.getSerializableExtra(AVS_INTENT_SERVICECONFIG_KEY);
            if (serviceConfig != null) {
                String address = serviceConfig.getDevice().getBleAddress();
                List<Map<String, String>> mList = turnStringToProductList((String) SPHelper.getPreference(getApplicationContext(), PREFERENCE_KEY_PRODUCT, ""));
                for (int i = 0; i < mList.size(); i ++) {
                    Map<String, String> map = mList.get(i);
                    if (map.get(MAP_KEY_PRODUCT_BLE_ADDRESS).equals(address)) {
                        mList.remove(i);
                    }
                }
                SPHelper.putPreference(getApplicationContext(), PREFERENCE_KEY_PRODUCT, turnProductListToString(mList));
                mAdapter.setProducts(mList);
            }
        }
    }

    private void onPickDevice(int resultCode, Intent data) {
        if (resultCode == RESULT_OK) {
            mHmDevice = (HmDevice) data.getSerializableExtra(BES_SCAN_RESULT);
            List<Map<String, String>> mList = turnStringToProductList((String) SPHelper.getPreference(getApplicationContext(), PREFERENCE_KEY_PRODUCT, ""));
            if (isNewProduct(mHmDevice, mList)) {
                Map<String, String> map = new HashMap<>();
                map.put(MAP_KEY_PRODUCT_NAME, mHmDevice.getDeviceName());
                map.put(MAP_KEY_PRODUCT_BLE_ADDRESS, mHmDevice.getBleAddress());
                mList.add(map);
                SPHelper.putPreference(getApplicationContext(), PREFERENCE_KEY_PRODUCT, turnProductListToString(mList));
                mAdapter.addProduct(map);
            }
            connectDevice();
        }
    }

    private void connectDevice() {
        loadinganim();
        mServiceConfig.setDevice(mHmDevice);
        mPresenter.connectDevice(mServiceConfig, instance, instance);
    }

    @Override
    protected void onResume() {
        super.onResume();
        SPHelper.putPreference(instance, AVS_IS_LWA_LINK_KEY, false);

        if (requestContext != null) {
            requestContext.onResume();
        }
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        onItemClick(position);

//        mServiceConfig.setDevice(mHmDevice);
//        goToAvsLwaResizeActivity();
//        initializeGuideUI();
    }

    private void onItemClick(int position) {
        List<Map<String, String>> list = turnStringToProductList((String) SPHelper.getPreference(getApplicationContext(), PREFERENCE_KEY_PRODUCT, ""));
        Map<String, String> map = list.get(position);
        mHmDevice = new HmDevice();
        mHmDevice.setDeviceName((String) map.get(MAP_KEY_PRODUCT_NAME));
        mHmDevice.setBleAddress((String) map.get(MAP_KEY_PRODUCT_BLE_ADDRESS));
        mHmDevice.setPreferredProtocol(DeviceProtocol.PROTOCOL_BLE);
        connectDevice();
    }

    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {

    }

    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {
        showToast(R.string.connect_failed);
    }

    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {
        Log.i(TAG, "onStateChangedMessage: -------------" + msg);
        Log.i(TAG, "onStateChangedMessage: -------------" + msgStr);
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (msg == AVS_LWA_DATA_LOG) {
                    addlog(msgStr);
                    return;
                }
                if (msg == BES_CONNECT_SUCCESS) {
                    loadingDialog.dismiss();
                    showToast(R.string.connect_success);

                } else if (msg == BES_CONNECT_ERROR) {
                    loadingDialog.dismiss();
                    showToast(R.string.connect_failed);

                } else if (msg == AVS_LWA_RECEIVE_WIFI_STATE_OFLINE) {
                    loadingDialog.dismiss();
                    mPresenter.pickWifi(instance);
                } else if (msg == AVS_LWA_RECEIVE_PRODUCTID) {
                    loadingDialog.dismiss();
                    startLoginAvs();

                } else if (msg == AVS_LWA_RECEIVE_WIFI_STATE_CONNECT_FAIL) {
                    loadingDialog.dismiss();
                    showToast(R.string.wifiConnectFail);
                } else if (msg == AVS_LWA_SET_LANGUANGE_OK) {
                    loadingDialog.dismiss();
                    goToAvsLwaResizeActivity();
                } else if (msg == AVS_LWA_SET_LANGUANGE_ERROR) {
                    loadingDialog.dismiss();
                    showToast(R.string.setting_language_fail);
                } else if (msg == AVS_LWA_RECEIVE_LWA_STATE_CONNECT) {
                    loadingDialog.dismiss();
                    //直接进设置页
                    goToAvsLwaResizeActivity();
                } else if (msg == AVS_LWA_RECEIVE_LWA_CONNECT_SUCCESS) {
                    initializeGuideUI();
                } else if (msg == AVS_LWA_RECEIVE_LWA_CONNECT_FAIL) {
                    showToast(R.string.setting_language_fail);
                }
            }
        });
    }

    private void goToAvsLwaResizeActivity() {
        Intent intent = new Intent();
        intent.putExtra(AVS_INTENT_SERVICECONFIG_KEY, (Serializable) mServiceConfig);
//        ActivityUtils.gotoAct(intent, AvsLwaActivity.this, AvsLwaResizeActivity.class);
        ActivityUtils.gotoActForResult(intent, AVS_LWA_REQUEST_CODE_LOGOUT, AvsLwaActivity.this, AvsLwaResizeActivity.class);
    }

    protected void showToast(int msg) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (instance != null) {
                    Toast.makeText(instance, msg, Toast.LENGTH_SHORT).show();
                }
            }
        });
    }

    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {

    }


    public class AvsProductAdapter extends BaseAdapter {
        private Context mContext;
        private List<Map<String, String>> mProducts;

        public AvsProductAdapter(Context context, List<Map<String, String>> prodicts) {
            mContext = context;
            mProducts = prodicts == null ? new ArrayList<>() : prodicts;
            Log.i(TAG, "AvsProductAdapter: -----" + mProducts);
        }

        @Override
        public int getCount() {
            return mProducts.size();
        }

        @Override
        public Object getItem(int position) {
            return mProducts.get(position);
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            ViewHolder holder;
            if (convertView == null) {
                convertView = LayoutInflater.from(mContext).inflate(R.layout.avslwa_item, parent, false);
                holder = new ViewHolder();
                holder.mName = (TextView) convertView.findViewById(R.id.name);
                holder.Content =(View) convertView.findViewById(R.id.content);
                holder.dv = (TextView) convertView.findViewById(R.id.delete_button);
                convertView.setTag(holder);
            } else {
                holder = (ViewHolder) convertView.getTag();
            }

            Log.i(TAG, "getView: ------" + (String) mProducts.get(position).get(MAP_KEY_PRODUCT_NAME));
            holder.mName.setText((String) mProducts.get(position).get(MAP_KEY_PRODUCT_NAME));

            holder.Content.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onItemClick(position);
                }
            });
            holder.dv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    deleteItem(position);
                }
            });
            return convertView;
        }

        @UiThread
        public void addProduct(Map<String, String> map) {
            if (map != null) {
                mProducts.add(map);
            }
            notifyDataSetChanged();

        }

        @UiThread
        public void setProducts(List<Map<String, String>> products) {
            mProducts = products;
            notifyDataSetChanged();

        }

        private class ViewHolder {
            TextView mName;
            View Content;
            TextView dv;
        }

    }

    private void startLoginAvs() {
        initRequestContext();
        Map<String, String> productSid = mPresenter.getProductSid();
        if (productSid != null) {
            PRODUCT_ID = productSid.get("PRODUCT_ID");
            PRODUCT_DSN = productSid.get("PRODUCT_DSN");
            CODE_CHALLENGE = productSid.get("CODE_CHALLENGE");
        }

        addlog("PRODUCT_ID:" + PRODUCT_ID);
        addlog("PRODUCT_DSN:" + PRODUCT_DSN);
        addlog("CODE_CHALLENGE:" + CODE_CHALLENGE);

        initializeLoginUI();
    }

    private void initRequestContext() {
        requestContext = RequestContext.create(this);
        requestContext.registerListener(new AuthorizeListener() {
            /* Authorization was completed successfully. */
            @Override
            public void onSuccess(AuthorizeResult authorizeResult) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                       initializeBaseUI();
                        // At this point we know the authorization completed, so remove the ability to return to the app to sign-in again
//                        setLoggingInState(true);

                        final String authorizationCode = authorizeResult.getAuthorizationCode();
                        final String redirectUri = authorizeResult.getRedirectURI();
                        final String clientId = authorizeResult.getClientId();
                        mPresenter.sendAuthorizeResult(authorizationCode, redirectUri, clientId);

                        Log.i(TAG, "authorizationCode: -----" + authorizationCode);
                        Log.i(TAG, "redirectUri: -----" + redirectUri);
                        Log.i(TAG, "clientId: -----" + clientId);

                        addlog("authorizationCode: -----" + authorizationCode);
                        addlog("redirectUri: -----" + redirectUri);
                        addlog("clientId: ---" + clientId.length() + "----" + clientId);

//                        requestData(authorizationCode, redirectUri, clientId);

//                        initializeGuideUI();
//                        loadinganim();
                    }
                });
//                fetchUserProfile();
            }

            /* There was an error during the attempt to authorize the application */
            @Override
            public void onError(AuthError authError) {
                Log.e(TAG, "AuthError during authorization", authError);
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                       initializeBaseUI();

                        showAuthToast("Error during authorization.  Please try again.");
                        resetProfileView();
                        setLoggingInState(false);
                    }
                });
            }

            /* Authorization was cancelled before it could be completed. */
            @Override
            public void onCancel(AuthCancellation authCancellation) {
                Log.e(TAG, "User cancelled authorization");
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        setContentView(R.layout.activity_avslwa);
                        bindBaseUI();
                        initBaseUI();

                        showAuthToast("Authorization cancelled");
                        resetProfileView();
                    }
                });
            }
        });
    }

    private void initializeGuideUI() {
        languages = new ArrayList<>();
        languages.add("English");

        setContentView(R.layout.activity_avslwa_guide);
        inittoolbar(cur_title_login);
        avs_guide_next = (Button) findViewById(R.id.avs_guide_next);
        avs_guide_next.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent();
                intent.putStringArrayListExtra(AVS_LANGUAGE_SELECT, languages);
                ActivityUtils.gotoActForResult(intent, AVS_LWA_LANGUAGE_SELECT, instance, AvsLwaLanguageSelectActivity.class);
            }
        });
        jump_alexa_app_textview = (TextView) findViewById(R.id.jump_alexa_app_textview);
        String dataStrs = "To learn more and access additional features download the Alexa App.";
        SpannableStringBuilder builder = new SpannableStringBuilder();
        builder.append(dataStrs);
        ClickableSpan clickableSpan = new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                Uri uri = Uri.parse("https://alexa.amazon.com");
                Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                startActivity(intent);
            }
        };
        builder.setSpan(clickableSpan, dataStrs.length() - 10, dataStrs.length() - 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        ForegroundColorSpan foregroundColorSpan = new ForegroundColorSpan(Color.BLUE);
        builder.setSpan(foregroundColorSpan, dataStrs.length() - 10, dataStrs.length() - 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        jump_alexa_app_textview.setMovementMethod(LinkMovementMethod.getInstance());
        jump_alexa_app_textview.setText(builder);
    }

    private void initializeLoginUI() {
        setContentView(R.layout.activity_avslwa_login);
        inittoolbar(cur_title_login);

        mLoginButton = findViewById(R.id.login_with_amazon);
        mLoginButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view)
//            {
//                AuthorizationManager.authorize(
//                        new AuthorizeRequest.Builder(requestContext)
//                                .addScopes(ProfileScope.profile(), ProfileScope.postalCode())
//                                .build()
//                );
//            }
            {
                final JSONObject scopeData = new JSONObject();
                final JSONObject productInstanceAttributes = new JSONObject();
                Log.i(TAG, "onClick: ------" + PRODUCT_DSN);
                Log.i(TAG, "onClick: ------" + PRODUCT_ID);
                Log.i(TAG, "onClick: ------" + CODE_CHALLENGE);
                try {
                    productInstanceAttributes.put("deviceSerialNumber", PRODUCT_DSN);
                    scopeData.put("productInstanceAttributes", productInstanceAttributes);
                    scopeData.put("productID", PRODUCT_ID);

                    AuthorizationManager.authorize(new AuthorizeRequest.Builder(requestContext)
                            .addScopes(ScopeFactory.scopeNamed("alexa:voice_service:pre_auth"),
                                    ScopeFactory.scopeNamed("alexa:all", scopeData))
                            .forGrantType(AuthorizeRequest.GrantType.AUTHORIZATION_CODE)
                            .withProofKeyParameters(CODE_CHALLENGE, CODE_CHALLENGE_METHOD)
                            .build());
                } catch (JSONException e) {
                    // handle exception here
                }
            }
        });
    }

    @Override
    protected void onStart() {
        super.onStart();
        Scope[] scopes = {ProfileScope.profile(), ProfileScope.postalCode()};
        AuthorizationManager.getToken(this, scopes, new Listener<AuthorizeResult, AuthError>() {
            @Override
            public void onSuccess(AuthorizeResult result) {
                if (result.getAccessToken() != null) {
                    /* The user is signed in */
                    fetchUserProfile();
                } else {
                    /* The user is not signed in */
                }
            }

            @Override
            public void onError(AuthError ae) {
                /* The user is not signed in */
            }
        });
    }

    private void fetchUserProfile() {
        User.fetch(this, new Listener<User, AuthError>() {

            /* fetch completed successfully. */
            @Override
            public void onSuccess(User user) {
                final String name = user.getUserName();
                final String email = user.getUserEmail();
                final String account = user.getUserId();
                final String zipCode = user.getUserPostalCode();

                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        updateProfileData(name, email, account, zipCode);
                    }
                });
            }

            /* There was an error during the attempt to get the profile. */
            @Override
            public void onError(AuthError ae) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        setLoggedOutState();
                        String errorMessage = "Error retrieving profile information.\nPlease log in again";
                        Toast errorToast = Toast.makeText(getApplicationContext(), errorMessage, Toast.LENGTH_LONG);
                        errorToast.setGravity(Gravity.CENTER, 0, 0);
                        errorToast.show();
                    }
                });
            }
        });
    }

    private void updateProfileData(String name, String email, String account, String zipCode) {
        StringBuilder profileBuilder = new StringBuilder();
        profileBuilder.append(String.format("Welcome, %s!\n", name));
        profileBuilder.append(String.format("Your email is %s\n", email));
        profileBuilder.append(String.format("Your zipCode is %s\n", zipCode));
        final String profile = profileBuilder.toString();
        Log.d(TAG, "Profile Response: " + profile);
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                updateProfileView(profile);
                setLoggedInState();
            }
        });
    }

    /**
     * Sets the text in the mProfileText {@link TextView} to the value of the provided String.
     *
     * @param profileInfo the String with which to update the {@link TextView}.
     */
    private void updateProfileView(String profileInfo) {
        Log.d(TAG, "Updating profile view");
    }

    /**
     * Sets the text in the mProfileText {@link TextView} to the prompt it originally displayed.
     */
    private void resetProfileView() {
        setLoggingInState(false);
    }

    /**
     * Sets the state of the application to reflect that the user is currently authorized.
     */
    private void setLoggedInState() {
        mLoginButton.setVisibility(Button.GONE);
        mIsLoggedIn = true;
        setLoggingInState(false);
    }

    /**
     * Sets the state of the application to reflect that the user is not currently authorized.
     */
    private void setLoggedOutState() {
        mLoginButton.setVisibility(Button.VISIBLE);
        mIsLoggedIn = false;
        resetProfileView();
    }

    /**
     * Turns on/off display elements which indicate that the user is currently in the process of logging in
     *
     * @param loggingIn whether or not the user is currently in the process of logging in
     */
    private void setLoggingInState(final boolean loggingIn) {
        if (loggingIn) {
            mLoginButton.setVisibility(Button.GONE);
        } else {
            if (mIsLoggedIn) {

            } else {
                mLoginButton.setVisibility(Button.VISIBLE);
            }
        }
    }

    private void showAuthToast(String authToastMessage) {
        Toast authToast = Toast.makeText(getApplicationContext(), authToastMessage, Toast.LENGTH_LONG);
        authToast.setGravity(Gravity.CENTER, 0, 0);
        authToast.show();
    }

    private void showWifiDialog(String msg) {
        AvsLwaWifiDialog wifiDialog = new AvsLwaWifiDialog(instance, msg, new AvsLwaWifiDialoglistener() {
            @Override
            public void confirmYesWithText(AvsLwaWifiDialog obj, String name, String psw) {
                if (name.length() != 0 && psw.length() != 0) {
                    mPresenter.sendWifiData(name, psw);
                    obj.dismiss();
                    loadinganim(getString(R.string.connecting_wifi));
                }
            }

            @Override
            public void confirmNo() {

            }
        });

        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                wifiDialog.show();
            }
        });
    }

}
