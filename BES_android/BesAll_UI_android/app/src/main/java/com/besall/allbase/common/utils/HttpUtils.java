package com.besall.allbase.common.utils;

import android.util.Log;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;

public class HttpUtils {

    public static boolean checkInternetState(String url) {
        Log.i("TAG", "checkInternetState: ----------");
        try {
            HttpURLConnection connection = null;
            URL requestUrl = new URL(url);
            connection = (HttpURLConnection) requestUrl.openConnection();
            connection.setRequestMethod("PUT");
            connection.setConnectTimeout(2000);
            OutputStream outputStream = connection.getOutputStream();
            outputStream.write("test".getBytes());
            outputStream.flush();
            outputStream.close();
            int responseCode = connection.getResponseCode();
            Log.i("TAG", "responseCode: ------>" + responseCode);
            connection.disconnect();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                return true;
            } else {
                return false;
            }
        } catch (IOException e) {
            return false;
        }
    }
    public static String sendPutRequest(String url, String filePath) throws IOException {
        if (url.charAt(url.length() - 1) != '/') {
            url = url + "/";
        }
        if (!checkInternetState(url)) {
            return "Upload Fail: check connect state";
        }
        try {
            Process process = Runtime.getRuntime().exec("curl -T " + filePath + " " + url);

            // 读取命令行输出
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

            StringBuilder msg = new StringBuilder();
            String line = "";
            while ((line = reader.readLine()) != null) {
                msg.append(line);
            }
            // 等待命令执行完成
            int exitCode = process.waitFor();
            reader.close();
            Log.i("TAG", "msg: ------>" + msg);
            Log.i("TAG", "exitCode: ------>" + exitCode);
            if (exitCode == 18) {
                return "Upload Success";
            } else if (msg.toString().contains("Error")) {
                return "Upload Fail: " + msg;
            } else {
                return "Upload Fail";
            }
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
        return "Fail";
    }
}
