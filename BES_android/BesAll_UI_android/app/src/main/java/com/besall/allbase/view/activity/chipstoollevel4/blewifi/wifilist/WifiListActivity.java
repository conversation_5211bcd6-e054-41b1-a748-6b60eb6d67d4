package com.besall.allbase.view.activity.chipstoollevel4.blewifi.wifilist;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ListView;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.besall.allbase.R;
import com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants;
import com.besall.allbase.view.activity.chipstoollevel4.blewifi.wifilist.adapter.WifiAdapter;
import com.besall.allbase.view.base.BaseActivity;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;


public class WifiListActivity extends BaseActivity<IWifiListActivity, WifiListPresenter> implements IWifiListActivity, SwipeRefreshLayout.OnRefreshListener, AdapterView.OnItemClickListener {

    private final String TAG = getClass().getSimpleName();

    private static WifiListActivity instance;

    TextView tips_text;
    ListView mDevices;
    SwipeRefreshLayout mSwipeRefresh;
    private WifiAdapter mAdapter;
    private WifiManager wifiManager;
    private List<String> listWifi;
    private static BroadcastReceiver mBroadcastReceiver;

    @Override
    protected WifiListPresenter createPresenter() {
        return new WifiListPresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        listWifi = new ArrayList<String>();
    }

    @Override
    protected int getContentViewId() {
        return R.layout.act_scan;
    }

    @Override
    protected void bindView() {
        tips_text = (TextView) findViewById(R.id.tips_text);
        tips_text.setVisibility(View.GONE);
        mAdapter = new WifiAdapter(instance);
        mDevices = (ListView) findViewById(R.id.devices);
        mSwipeRefresh = (SwipeRefreshLayout) findViewById(R.id.swipe_refresh);
        tv_title = (TextView) findViewById(R.id.tv_title);
        mToolbar = (Toolbar) findViewById(R.id.toolbar);
    }

    @Override
    protected void initView() {
        tv_title.setText("WIFI List");
        mToolbar.setTitle("");
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);

        mDevices = (ListView)findViewById(R.id.devices);
        mDevices.setAdapter(mAdapter);
        mDevices.setOnItemClickListener(this);
        mSwipeRefresh.setOnRefreshListener(this);

        startScan();
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        Intent intent = new Intent();
        intent.putExtra(BleWifiConstants.CHOOSE_WIFI_RESULT_KEY, listWifi.get(position));
        setResult(RESULT_OK, intent);
        finish();
    }

    @Override
    public void onRefresh() {
        if (mAdapter != null) {
            mAdapter.clear();
        }
        Log.i(TAG, "onRefresh: ------------");
        mSwipeRefresh.clearAnimation();
        listWifi = new ArrayList<String>();
        startScan();
    }

    private void startScan() {
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.ACCESS_FINE_LOCATION}, PackageManager.PERMISSION_GRANTED);
        }
        Log.i(TAG, "startScan: wifi");
        wifiManager = (WifiManager) instance.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        wifiManager.startScan();
        List<ScanResult> list = wifiManager.getScanResults();
        for (int i = 0; i < list.size(); i ++) {
            ScanResult scanResult = list.get(i);
            Log.i(TAG, "startScan: ---------" + scanResult.SSID);
            if (scanResult.SSID.length() > 0 && !listWifi.contains(scanResult.SSID)) {
                listWifi.add(scanResult.SSID);
                mAdapter.add(scanResult.SSID);
            }
        }
    }

}
