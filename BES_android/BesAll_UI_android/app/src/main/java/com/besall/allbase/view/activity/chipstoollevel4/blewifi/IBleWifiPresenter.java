package com.besall.allbase.view.activity.chipstoollevel4.blewifi;

import android.content.Context;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;

/**
 * <AUTHOR>
 * @time $ $
 */
interface IBleWifiPresenter {

    void pickWifi(BleWifiActivity context);

    void pickDecice(BleWifiActivity context, int scan);

    void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context);

    void sendWifiData(String name, String psw);

    void openWifi();

    void disconnect();
}
