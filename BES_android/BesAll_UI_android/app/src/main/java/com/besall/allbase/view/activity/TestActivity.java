package com.besall.allbase.view.activity;

import static com.besall.allbase.bluetooth.BluetoothConstants.Scan.BES_SCAN_RESULT;

import android.Manifest;
import android.annotation.SuppressLint;
import android.bluetooth.BluetoothDevice;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiManager;
import android.net.wifi.p2p.WifiP2pConfig;
import android.net.wifi.p2p.WifiP2pDevice;
import android.net.wifi.p2p.WifiP2pDeviceList;
import android.net.wifi.p2p.WifiP2pManager;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;

import androidx.core.app.ActivityCompat;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.scan.BtHeleper;
import com.bes.bessdk.service.BesOtaService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.ota.OTADfuInfo;
import com.bes.sdk.ota.OTATask;
import com.bes.sdk.ota.RemoteOTAConfig;
import com.bes.sdk.utils.DeviceProtocol;
import com.bes.sdk.utils.OTAStatus;
import com.besall.allbase.R;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;
import com.besall.allbase.bluetooth.service.log_dump.LogDumpConstants;
import com.besall.allbase.bluetooth.service.log_dump.LogDumpService;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.common.utils.FileUtils;
import com.besall.allbase.view.base.BaseActivity;

import java.util.List;

public class TestActivity extends BaseActivity<ITestActivity, TestPresenter> implements ITestActivity, View.OnClickListener, BesServiceListener, OTATask.StatusListener {

    private static TestActivity instance;

    BluetoothDevice mDevice;
    private Button testBtn;
    private EditText wlan_name;

    BesOtaService besOtaService;
    OTATask otaTask;

    private WifiP2pManager mWifiP2pManager;
    private WifiP2pManager.Channel mChannel;

    WiFiDirectBroadcastReceiver mReceiver;
    @Override
    protected TestPresenter createPresenter() {
        return new TestPresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.ACCESS_FINE_LOCATION}, PackageManager.PERMISSION_GRANTED);
        }
    }

    @Override
    protected int getContentViewId() {
        return R.layout.act_test;
    }

    @Override
    protected void bindView() {
        testBtn = (Button)findViewById(R.id.test_btn);
        wlan_name = (EditText) findViewById(R.id.wlan_name);
    }

    @SuppressLint("ResourceAsColor")
    @Override
    protected void initView() {
        testBtn.setOnClickListener(this);
        testBtn.setBackgroundColor(R.color.color_aa1247c2);

        String name = (String) SPHelper.getPreference(instance, "WLAN_NAME", "");
        wlan_name.setText(name);

        //获取Wifi P2P服务对象
        mWifiP2pManager = (WifiP2pManager) getSystemService(Context.WIFI_P2P_SERVICE);
// 注册应用程序 这一步必须的。
        mChannel = mWifiP2pManager.initialize(this, getMainLooper(), null);

        IntentFilter intentFilter = new IntentFilter();
//监听 Wi-Fi P2P是否开启
        intentFilter.addAction(WifiP2pManager.WIFI_P2P_STATE_CHANGED_ACTION);
//监听 Wi-Fi P2P扫描状态
        intentFilter.addAction(WifiP2pManager.WIFI_P2P_DISCOVERY_CHANGED_ACTION);
//监听 可用的P2P列表发生了改变。
        intentFilter.addAction(WifiP2pManager.WIFI_P2P_PEERS_CHANGED_ACTION);
//监听 Wi-Fi P2P的连接状态发生了改变
        intentFilter.addAction(WifiP2pManager.WIFI_P2P_CONNECTION_CHANGED_ACTION);
//监听 设备的详细配置发生了变化
        intentFilter.addAction(WifiP2pManager.WIFI_P2P_THIS_DEVICE_CHANGED_ACTION);

        mReceiver = new WiFiDirectBroadcastReceiver(mWifiP2pManager, mChannel, this);
//对上述的action 进行注册监听
        registerReceiver(mReceiver, intentFilter);
    }

    public class WiFiDirectBroadcastReceiver extends BroadcastReceiver {
        private WifiP2pManager manager;
        private WifiP2pManager.Channel channel;
        private TestActivity activity;
        public WiFiDirectBroadcastReceiver (WifiP2pManager manager, WifiP2pManager.Channel channel, TestActivity activity) {
            super();
            this.manager = manager;
            this.channel = channel;
            this.activity = activity;
        }
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (WifiP2pManager.WIFI_P2P_STATE_CHANGED_ACTION.equals (action)) {
                //检测 WIFI 功能是否被打开

            } else if (WifiP2pManager.WIFI_P2P_PEERS_CHANGED_ACTION.equals (action)) {
                //获取当前可用连接点的列表
                Log.i(TAG, "onReceive: --------");
                this.manager.requestPeers(this.channel, new WifiP2pManager.PeerListListener() {
                    @Override
                    public void onPeersAvailable(WifiP2pDeviceList wifiP2pDeviceList) {
                        Log.i(TAG, "onPeersAvailable: -------" + wifiP2pDeviceList.getDeviceList());
                        for (WifiP2pDevice wifiP2pDevice : wifiP2pDeviceList.getDeviceList()) {
                            if (wifiP2pDevice.deviceName.equals(wlan_name.getText().toString())) {
                                connectDevice(wifiP2pDevice);
                                return;
                            }
                        }
                    }
                });
            } else if (WifiP2pManager.WIFI_P2P_CONNECTION_CHANGED_ACTION.equals (action)) {
                //建立或者断开连接

            } else if (WifiP2pManager.WIFI_P2P_THIS_DEVICE_CHANGED_ACTION.equals (action)) {
                //当前设备的 WIFI 状态发生变化

            }
        }
    }

    private void connectDevice(WifiP2pDevice device) {
        Log.i(TAG, "connectDevice: ----" + device);
        WifiP2pConfig config = new WifiP2pConfig();
        config.deviceAddress = device.deviceAddress;
        mWifiP2pManager.connect (mChannel, config, new WifiP2pManager.ActionListener() {
            @Override
            public void onSuccess() {
                Log.i(TAG, "connectDevice onSuccess: -------");
                ActivityUtils.showToast("Connect Success");
            }
            @Override
            public void onFailure (int reason) {
                Log.i(TAG, "connectDevice onFailure: -------");
                ActivityUtils.showToast("Connect Failed");
            }
        });
    }


    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.test_btn:
                if (wlan_name.getText().length() == 0) {
                    return;
                }
                SPHelper.putPreference(instance, "WLAN_NAME", wlan_name.getText().toString());
                mWifiP2pManager.discoverPeers(mChannel, new WifiP2pManager.ActionListener() {
                    @Override
                    public void onSuccess() {
                        Log.i(TAG, "onSuccess: -----");
                    }

                    @Override
                    public void onFailure(int i) {

                    }
                });


                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data)
    {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == BluetoothConstants.Scan.REQUEST_CODE_SCAN) {
            onPickDevice(resultCode, data);
        }

    }

    private void onPickDevice(int resultCode, Intent data)
    {
        if (resultCode == RESULT_OK) {
            Log.i(TAG, "onPickDevice: -----" + mDevice.getName());
            HmDevice hmDevice = (HmDevice) data.getSerializableExtra(BES_SCAN_RESULT);
            mDevice = BtHeleper.getBluetoothAdapter(instance).getRemoteDevice(hmDevice.getPreferredProtocol() == DeviceProtocol.PROTOCOL_BLE ? hmDevice.getBleAddress() : hmDevice.getDeviceMAC());


            BesServiceConfig serviceConfig = new BesServiceConfig();
            serviceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_BLE);
            serviceConfig.setDevice(hmDevice);
            serviceConfig.setTotaConnect(false);
            besOtaService = new BesOtaService(serviceConfig, instance, instance);
            otaTask = besOtaService;
        }
    }

//BesServiceListener
    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {
        Log.i(TAG, "onTotaConnectState: -----" + state);

    }

    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {
        Log.i(TAG, "onErrorMessage: -----" + msg);
    }

    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {
        Log.i(TAG, "onStateChangedMessage: ----" + msgStr);
        if (msg == BesSdkConstants.BES_CONNECT_SUCCESS) {
            Log.i(TAG, "onStateChangedMessage: -----BES_CONNECT_SUCCESS");
        } else if (msg == BesSdkConstants.BES_CONNECT_ERROR) {
            
        }

    }

    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {

    }

    @Override
    public void onOTAStatusChanged(OTAStatus newStatus, HmDevice hmDevice) {

    }

    @Override
    public void onOTAProgressChanged(int progress, HmDevice hmDevice) {
        Log.i(TAG, "onOTAProgressChanged: -----------" + progress);
    }
}
