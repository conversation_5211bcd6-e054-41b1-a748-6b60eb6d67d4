package com.besall.allbase.view.activity.level3;

import android.content.Intent;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.R;
import com.besall.allbase.common.Constants;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.activity.chipstoollevel4.ota.OtaUIActivity;
import com.besall.allbase.view.base.BaseActivity;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @time $ $
 */
public class FunctionOtaActivity extends BaseActivity<IFunctionOtaActivity, FunctionOtaPresenter> implements IFunctionOtaActivity, View.OnClickListener
{
    private static FunctionOtaActivity instance;
    private Button usb_tota_ota;
    private Button spp_ota_v1_old;
    private Button spp_ota_v1;
    private Button ble_ota_v1;
    private Button spp_totaota_v1;
    private Button ble_totaota_v1;
    private Button gatt_ota_v1;
    private Button spp_ota_v2;
    private Button ble_ota_v2;
    private Button gatt_ota_v2;
    private Button spp_totaota_v2;
    private Button ble_totaota_v2;

    @Override
    protected FunctionOtaPresenter createPresenter() {
        return new FunctionOtaPresenter();
    }

    @Override
    protected void initBeforeSetContent() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_otafunction;
    }

    @Override
    protected void bindView() {
        usb_tota_ota = (Button)findViewById(R.id.usb_tota_ota);
        spp_ota_v1_old = (Button)findViewById(R.id.spp_ota_v1_old);
        spp_ota_v1 = (Button)findViewById(R.id.spp_ota_v1);
        ble_ota_v1 = (Button)findViewById(R.id.ble_ota_v1);
        spp_totaota_v1 = (Button)findViewById(R.id.spp_totaota_v1);
        ble_totaota_v1 = (Button)findViewById(R.id.ble_totaota_v1);
//        gatt_ota_v1 = (Button)findViewById(R.id.gatt_ota_v1);
        spp_ota_v2 = (Button)findViewById(R.id.spp_ota_v2);
        ble_ota_v2 = (Button)findViewById(R.id.ble_ota_v2);
//		gatt_ota_v2 = (Button)findViewById(R.id.gatt_ota_v2);
        spp_totaota_v2 = (Button)findViewById(R.id.spp_totaota_v2);
        ble_totaota_v2 = (Button)findViewById(R.id.ble_totaota_v2);

        tv_title = (TextView) findViewById(R.id.tv_title);
        mToolbar = (Toolbar) findViewById(R.id.toolbar);
    }

    @Override
    protected void initView() {
//        gatt_ota_v1.setVisibility(View.GONE);
//        gatt_ota_v2.setVisibility(View.GONE);
        usb_tota_ota.setOnClickListener(instance);
        spp_ota_v1_old.setOnClickListener(instance);
        spp_ota_v1.setOnClickListener(instance);
        ble_ota_v1.setOnClickListener(instance);
        spp_totaota_v1.setOnClickListener(instance);
        ble_totaota_v1.setOnClickListener(instance);
//        gatt_ota_v1.setOnClickListener(instance);
        spp_ota_v2.setOnClickListener(instance);
        ble_ota_v2.setOnClickListener(instance);
// 		gatt_ota_v2.setOnClickListener(instance);
        spp_totaota_v2.setOnClickListener(instance);
        ble_totaota_v2.setOnClickListener(instance);

        tv_title.setText("BES OTA TOOLS");
        mToolbar.setTitle("");
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);

    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {

    }



    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onClick(View v) {
        boolean useTotaV2 = (boolean) SPHelper.getPreference(instance, BesSdkConstants.BES_TOTA_USE_TOTAV2, BesSdkConstants.BES_TOTA_USE_TOTAV2_VALUE);
        switch (v.getId()) {
            case R.id.usb_tota_ota:
                Intent intent = new Intent();
                BesServiceConfig serviceConfig = new BesServiceConfig();
                serviceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_USB);
                serviceConfig.setUSER_FLAG(1);
                serviceConfig.setTotaConnect(true);
                serviceConfig.setUseTotaV2(useTotaV2);
                intent.putExtra(Constants.OTA_SERVICE_CONFIG, (Serializable) serviceConfig);
                ActivityUtils.gotoAct(intent, instance, OtaUIActivity.class);
                break;
            case R.id.spp_ota_v1_old:
                Intent intent0 = new Intent();
                BesServiceConfig serviceConfig0 = new BesServiceConfig();
                serviceConfig0.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
                serviceConfig0.setServiceUUID(BesSdkConstants.BES_OTA_SERVICE_OTA_UUID_OLD);
                serviceConfig0.setUSER_FLAG(-1);
                intent0.putExtra(Constants.OTA_SERVICE_CONFIG, (Serializable) serviceConfig0);
                ActivityUtils.gotoAct(intent0, instance, OtaUIActivity.class);
                break;
            case R.id.spp_ota_v1:
                Intent intent1 = new Intent();
                BesServiceConfig serviceConfig1 = new BesServiceConfig();
                serviceConfig1.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
                serviceConfig1.setServiceUUID(BesSdkConstants.BES_OTA_SERVICE_OTA_UUID);
                serviceConfig1.setUSER_FLAG(0);
                intent1.putExtra(Constants.OTA_SERVICE_CONFIG, (Serializable) serviceConfig1);
                ActivityUtils.gotoAct(intent1, instance, OtaUIActivity.class);
                break;
            case R.id.ble_ota_v1:
                Intent intent2 = new Intent();
                BesServiceConfig serviceConfig2 = new BesServiceConfig();
                serviceConfig2.setDeviceProtocol(DeviceProtocol.PROTOCOL_BLE);
                serviceConfig2.setServiceUUID(BesSdkConstants.BES_OTA_SERVICE_OTA_UUID);
                serviceConfig2.setCharacteristicsUUID(BesSdkConstants.BES_OTA_CHARACTERISTIC_OTA_UUID);
                serviceConfig2.setDescriptorUUID(BesSdkConstants.BES_OTA_DESCRIPTOR_OTA_UUID);
                serviceConfig2.setUSER_FLAG(0);
                intent2.putExtra(Constants.OTA_SERVICE_CONFIG, (Serializable) serviceConfig2);
                ActivityUtils.gotoAct(intent2, instance, OtaUIActivity.class);
                break;
            case R.id.spp_totaota_v1:
                Intent intent10 = new Intent();
                BesServiceConfig serviceConfig10 = new BesServiceConfig();
                serviceConfig10.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
                serviceConfig10.setServiceUUID(BesSdkConstants.BES_SPP_CONNECT);
                serviceConfig10.setUSER_FLAG(0);
                serviceConfig10.setTotaConnect(true);
                serviceConfig10.setUseTotaV2(useTotaV2);
                intent10.putExtra(Constants.OTA_SERVICE_CONFIG, (Serializable) serviceConfig10);
                ActivityUtils.gotoAct(intent10, instance, OtaUIActivity.class);
                break;
            case R.id.ble_totaota_v1:
                Intent intent11 = new Intent();
                BesServiceConfig serviceConfig11 = new BesServiceConfig();
                serviceConfig11.setDeviceProtocol(DeviceProtocol.PROTOCOL_BLE);
                serviceConfig11.setServiceUUID(BesSdkConstants.BES_TOTA_SERVICE_OTA_UUID);
                serviceConfig11.setCharacteristicsUUID(BesSdkConstants.BES_TOTA_CHARACTERISTI_OTA_UUID);
                serviceConfig11.setDescriptorUUID(BesSdkConstants.BES_TOTA_DESCRIPTOR_OTA_UUID);
                serviceConfig11.setUSER_FLAG(0);
                serviceConfig11.setTotaConnect(true);
                serviceConfig11.setUseTotaV2(useTotaV2);
                intent11.putExtra(Constants.OTA_SERVICE_CONFIG, (Serializable) serviceConfig11);
                ActivityUtils.gotoAct(intent11, instance, OtaUIActivity.class);
                break;
//            case R.id.gatt_ota_v1:
//                Intent intent22 = new Intent();
//                BesServiceConfig serviceConfig22 = new BesServiceConfig();
//                serviceConfig22.setDeviceProtocol(DeviceProtocol.PROTOCOL_GATT_BR_EDR);
//                serviceConfig22.setUSER_FLAG(0);
//                intent22.putExtra(Constants.OTA_SERVICE_CONFIG, (Serializable) serviceConfig22);
//                ActivityUtils.gotoAct(intent22, instance, OtaActivity.class);
//                break;
            case R.id.spp_ota_v2:
                Intent intent3 = new Intent();
                BesServiceConfig serviceConfig3 = new BesServiceConfig();
                serviceConfig3.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
                serviceConfig3.setServiceUUID(BesSdkConstants.BES_OTA_SERVICE_OTA_UUID);
                serviceConfig3.setUSER_FLAG(1);
                intent3.putExtra(Constants.OTA_SERVICE_CONFIG, (Serializable) serviceConfig3);
                ActivityUtils.gotoAct(intent3, instance, OtaUIActivity.class);
                break;
            case R.id.ble_ota_v2:
                Intent intent4 = new Intent();
                BesServiceConfig serviceConfig4 = new BesServiceConfig();
                serviceConfig4.setDeviceProtocol(DeviceProtocol.PROTOCOL_BLE);
                serviceConfig4.setServiceUUID(BesSdkConstants.BES_OTA_SERVICE_OTA_UUID);
                serviceConfig4.setCharacteristicsUUID(BesSdkConstants.BES_OTA_CHARACTERISTIC_OTA_UUID);
                serviceConfig4.setDescriptorUUID(BesSdkConstants.BES_OTA_DESCRIPTOR_OTA_UUID);
                serviceConfig4.setUSER_FLAG(1);
                intent4.putExtra(Constants.OTA_SERVICE_CONFIG, (Serializable) serviceConfig4);
                ActivityUtils.gotoAct(intent4, instance, OtaUIActivity.class);
                break;
//            case R.id.gatt_ota_v2:
//                Intent intent44 = new Intent();
//                BesServiceConfig serviceConfig44 = new BesServiceConfig();
//                serviceConfig44.setDeviceProtocol(DeviceProtocol.PROTOCOL_GATT_BR_EDR);
//                serviceConfig44.setUSER_FLAG(1);
//                intent44.putExtra(Constants.OTA_SERVICE_CONFIG, (Serializable) serviceConfig44);
//                ActivityUtils.gotoAct(intent44, instance, OtaActivity.class);
//                break;
            case R.id.spp_totaota_v2:
                Intent intent5 = new Intent();
                BesServiceConfig serviceConfig5 = new BesServiceConfig();
                serviceConfig5.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
                serviceConfig5.setServiceUUID(BesSdkConstants.BES_SPP_CONNECT);
                serviceConfig5.setUSER_FLAG(1);
                serviceConfig5.setTotaConnect(true);
                serviceConfig5.setUseTotaV2(useTotaV2);
                intent5.putExtra(Constants.OTA_SERVICE_CONFIG, (Serializable) serviceConfig5);
                ActivityUtils.gotoAct(intent5, instance, OtaUIActivity.class);
                break;
            case R.id.ble_totaota_v2:
                Intent intent6 = new Intent();
                BesServiceConfig serviceConfig6 = new BesServiceConfig();
                serviceConfig6.setDeviceProtocol(DeviceProtocol.PROTOCOL_BLE);
                serviceConfig6.setServiceUUID(BesSdkConstants.BES_TOTA_SERVICE_OTA_UUID);
                serviceConfig6.setCharacteristicsUUID(BesSdkConstants.BES_TOTA_CHARACTERISTI_OTA_UUID);
                serviceConfig6.setDescriptorUUID(BesSdkConstants.BES_TOTA_DESCRIPTOR_OTA_UUID);
                serviceConfig6.setUSER_FLAG(1);
                serviceConfig6.setTotaConnect(true);
                serviceConfig6.setUseTotaV2(useTotaV2);
                intent6.putExtra(Constants.OTA_SERVICE_CONFIG, (Serializable) serviceConfig6);
                ActivityUtils.gotoAct(intent6, instance, OtaUIActivity.class);
                break;

            default:
                break;
        }
    }

}
