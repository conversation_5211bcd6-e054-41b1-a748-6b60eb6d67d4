package com.besall.allbase.view.activity.chipstoollevel4.capsensor;


import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_CHART_LINE_WIDTH_DATA;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_CHART_LINE_WIDTH_DATA_KEY;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_CHART_MAXIMUM_DATA;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_CHART_MAXIMUM_DATA_KEY;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_CHART_USE_CUSTOM_DATA;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_CHART_USE_CUSTOM_DATA_KEY;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_MAX_CN_NUMBER;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.annotation.UiThread;

import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.besall.allbase.R;
import com.besall.allbase.view.activity.tools.FileActivity.FileEntity;
import com.besall.allbase.view.activity.tools.FileActivity.FilelistActivity;

import java.util.ArrayList;
import java.util.List;

public class CapsensorListView extends ListView {

    private CapsensorAdapter mAdapter;
    private Context mContext;
    private List<float[]> mList;

    public CapsensorListView(Context context, AttributeSet attrs) {
        super(context, attrs);

        mContext = context;

        mList = new ArrayList<>();
        if (mAdapter == null) {
            mAdapter = new CapsensorAdapter();
            setAdapter(mAdapter);
        } else {
            mAdapter.notifyDataSetChanged();
        }
    }

    public void addData(float[] data) {
        mAdapter.add(data);
    }
    public void addListData(List<float[]> data) {
        mAdapter.addListData(data);
    }

    public void setShowState(boolean[] data) {
        mAdapter.setShowState(data);
    }

    public float[] getCurPosition(int position) {
        return mAdapter.getCurPosition(position);
    }

    class CapsensorAdapter extends BaseAdapter {
        private LayoutInflater mInflater;
        private boolean[] mShowState = new boolean[CAPSENSOR_MAX_CN_NUMBER];

        public CapsensorAdapter() {
            super();
            mInflater = LayoutInflater.from(mContext);
        }

        @Override
        public int getCount() {
            // TODO Auto-generated method stub
            return mList.size();
        }

        @Override
        public Object getItem(int position) {
            // TODO Auto-generated method stub
            return mList.get(position);
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            ViewHolder holder = null;
            if (convertView == null) {
                holder = new ViewHolder();
                convertView = mInflater.inflate(R.layout.capsensor_list_item, parent, false);
                holder.capsensor_chart_view = (CapsensorChartView) convertView.findViewById(R.id.capsensor_chart_view);
                convertView.setTag(holder);
            } else {
                holder = (ViewHolder) convertView.getTag();
            }
            boolean useCustomData = (boolean) SPHelper.getPreference(mContext, CAPSENSOR_CHART_USE_CUSTOM_DATA_KEY, CAPSENSOR_CHART_USE_CUSTOM_DATA);
            if (useCustomData) {
                holder.capsensor_chart_view.setMinimumHeight((int) SPHelper.getPreference(mContext, CAPSENSOR_CHART_LINE_WIDTH_DATA_KEY, CAPSENSOR_CHART_LINE_WIDTH_DATA));
            }
            holder.capsensor_chart_view.setDataSource(position == 0 ? mList.get(position) : mList.get(position - 1), mList.get(position), mShowState);
            holder.capsensor_chart_view.invalidate();
            return convertView;
        }

        @UiThread
        public void add(float[] data) {
            mList.add(data);
            setSelection(mList.size() - 1);
        }

        @UiThread
        public void addListData(List<float[]> data) {
            mList = data;
            notifyDataSetChanged();
        }

        @UiThread
        public void setShowState(boolean[] showState) {
            mShowState = showState;
        }

        public float[] getCurPosition(int position) {
            return mList.get(position);
        }
    }

    class ViewHolder {
        CapsensorChartView capsensor_chart_view;
    }

    @Override
    public void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {

        int defaultsize = measureHight(Integer.MAX_VALUE >> 2, heightMeasureSpec);
        int expandSpec = MeasureSpec.makeMeasureSpec(defaultsize, MeasureSpec.AT_MOST);

        super.onMeasure(widthMeasureSpec, expandSpec);
    }


    private int measureHight(int size, int measureSpec) {
        int result = 0;
        int specMode = MeasureSpec.getMode(measureSpec);
        int specSize = MeasureSpec.getSize(measureSpec);
        if (specMode == MeasureSpec.EXACTLY) {
            result = specSize;
        } else {
            result = size;//最小值是200px ，自己设定
            if (specMode == MeasureSpec.AT_MOST) {
                result = Math.min(result, specSize);
            }
        }
        return result;
    }


    /**
     * 改listview滑到底端了
     *
     * @return
     */
    public boolean isBottom() {
        int firstVisibleItem = getFirstVisiblePosition();//屏幕上显示的第一条是list中的第几条
        int childcount = getChildCount();//屏幕上显示多少条item
        int totalItemCount = getCount();//一共有多少条
        if ((firstVisibleItem + childcount) >= totalItemCount) {
            return true;
        }
        return false;
    }

    /**
     * 改listview在顶端
     *
     * @return
     */
    public boolean isTop() {
        int firstVisibleItem = getFirstVisiblePosition();
        if (firstVisibleItem ==0) {
            return true;
        }
        return false;
    }

    float down = 0;
    float y;

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                down = event.getRawY();

                getParent().requestDisallowInterceptTouchEvent(true);
                break;
            case MotionEvent.ACTION_MOVE:
                y = event.getRawY();
                if (isTop()) {
                    if (y - down > 1) {
//                        到顶端,向下滑动 把事件教给父类
                        getParent().requestDisallowInterceptTouchEvent(false);
                    } else {
                        //                        到顶端,向上滑动 把事件拦截 由自己处理
                        getParent().requestDisallowInterceptTouchEvent(true);
                    }
                }

                if (isBottom()) {
                    if (y - down > 1) {
//                        到底端,向下滑动 把事件拦截 由自己处理
                        getParent().requestDisallowInterceptTouchEvent(true);
                    } else {
//                        到底端,向上滑动 把事件教给父类
                        getParent().requestDisallowInterceptTouchEvent(false);
                    }
                }
                break;
            default:
                break;
        }

        return super.dispatchTouchEvent(event);
    }
}
