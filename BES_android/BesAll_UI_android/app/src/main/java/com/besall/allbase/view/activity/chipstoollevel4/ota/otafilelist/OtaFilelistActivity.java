package com.besall.allbase.view.activity.chipstoollevel4.ota.otafilelist;

/**
 * <AUTHOR>
 * @time $ $
 */
import static com.bes.bessdk.service.BesOTAConstants.BES_KEY_USE_INTERNAL_FILE_ACCESS_CHOOSE_FILE;

import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;

import com.besall.allbase.R;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.common.utils.FileUtils;
import com.besall.allbase.common.utils.SlideLayout;
import com.besall.allbase.view.activity.tools.FileActivity.FileEntity;
import com.besall.allbase.view.activity.tools.FileActivity.FilelistActivity;
import com.besall.allbase.view.base.BaseActivity;

import java.io.File;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

public class OtaFilelistActivity extends BaseActivity<IOtaFilelistActivity, OtaFilelistPresenter> implements IOtaFilelistActivity, View.OnClickListener, AdapterView.OnItemClickListener {
    private static final String TAG = "OtaFilelistActivity";
    private static OtaFilelistActivity instance;
    private Button ota_file_steps_btn;
    private ListView ota_file_list;
    private View ota_file_steps;
    private TextView ota_file_steps_2;

    private OtaFileAdapter mAdapter;

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected OtaFilelistPresenter createPresenter() {

        return new OtaFilelistPresenter();
    }

    @Override
    protected void initBeforeSetContent() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.ota_file_list;
    }

    @Override
    protected void bindView() {
        ota_file_steps_btn = (Button) findViewById(R.id.ota_file_steps_btn);
        ota_file_list = (ListView) findViewById(R.id.ota_file_list);
        ota_file_steps = (View) findViewById(R.id.ota_file_steps);
        ota_file_steps_2 = (TextView) findViewById(R.id.ota_file_steps_2);
    }

    public void initView() {
        inittoolbar("OTA File");
        ota_file_steps_btn.setOnClickListener(instance);
        String steps2 = getString(R.string.steps2_text);
        ota_file_steps_2.setText(steps2.replace("com.bes.besall", ActivityUtils.getPackageName()));

        String[] fileNames = new FileUtils(instance).getOtaBinFileNames();
        if (fileNames.length == 0) {
            return;
        }
        ota_file_steps_btn.setVisibility(View.VISIBLE);
        ota_file_list.setVisibility(View.VISIBLE);
        ota_file_steps.setVisibility(View.GONE);

        mAdapter = new OtaFileAdapter(instance, fileNames);
        ota_file_list.setAdapter(mAdapter);
        ota_file_list.setOnItemClickListener(instance);
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ota_file_steps_btn:
                if (ota_file_list.getVisibility() == View.GONE) {
                    ota_file_list.setVisibility(View.VISIBLE);
                    ota_file_steps.setVisibility(View.GONE);
                } else {
                    ota_file_list.setVisibility(View.GONE);
                    ota_file_steps.setVisibility(View.VISIBLE);
                }
                break;
            default:
                break;
        }
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        String[] fileNames = new FileUtils(instance).getOtaBinFileNames();
        String filePath = new FileUtils(instance).getOtaFilePath() + "/" + fileNames[position];
        Intent intent = new Intent();
        intent.putExtra(BES_KEY_USE_INTERNAL_FILE_ACCESS_CHOOSE_FILE, filePath);
        setResult(RESULT_OK, intent);
        finish();
    }

    class ViewHolder {
        TextView name;
    }

    class OtaFileAdapter extends BaseAdapter {
        private Context mContext;
        private String[] mAList;
        private LayoutInflater mInflater;

        public OtaFileAdapter(OtaFilelistActivity mContext, String[] mList) {
            super();
            this.mContext = mContext;
            this.mAList = mList;
            mInflater = LayoutInflater.from(mContext);
        }

        @Override
        public int getCount() {
            return mAList.length;
        }

        @Override
        public Object getItem(int position) {
            return mAList[position];
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            ViewHolder holder = null;
            if (convertView == null) {
                holder = new ViewHolder();
                convertView = LayoutInflater.from(mContext).inflate(R.layout.wifi_item, parent, false);
                holder.name = (TextView) convertView.findViewById(R.id.name);
                convertView.setTag(holder);
            } else {
                holder = (ViewHolder) convertView.getTag();
            }
            holder.name.setText(mAList[position]);
            return convertView;
        }
    }
}




