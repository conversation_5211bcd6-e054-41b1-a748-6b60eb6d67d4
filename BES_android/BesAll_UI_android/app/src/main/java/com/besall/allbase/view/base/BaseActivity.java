package com.besall.allbase.view.base;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.PixelFormat;
import android.graphics.drawable.AnimationDrawable;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.besall.allbase.R;
import com.besall.allbase.common.manager.AppManager;
import com.besall.allbase.common.manager.PermissionManager;
import com.besall.allbase.common.utils.LoadingDialog;
import androidx.appcompat.widget.Toolbar;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Created by fanyu on 2019-04-18
 */
public abstract class BaseActivity<V, T extends BasePresenter<V>> extends AppCompatActivity {
    public final String TAG = getClass().getSimpleName();
    private final String AGREE_KEY = "Bes_Agree_Key";

    public String curTiles;

    protected T mPresenter;

    public Toolbar mToolbar;

    public TextView tv_title;

    public AnimationDrawable loadanimationDrawable;

    public LoadingDialog loadingDialog;

    public Button done;
    public TextView logV;
    public ScrollView scr_policy;

    public View loginfo;
    protected abstract T createPresenter();

    protected abstract void initBeforeSetContent();

    protected abstract int getContentViewId();

    protected abstract void bindView();

    protected abstract void initView();

    protected abstract void setInstance();

    protected abstract void removeInstance();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 创建presenter
        mPresenter = createPresenter();
        // 关联View
        mPresenter.attachView((V) this);
        // 设置activity instance
        setInstance();
        // 初始化界面
        initBeforeSetContent();
        getWindow().setNavigationBarColor(Color.parseColor("#00000000"));
//        getWindow().setNavigationBarColor(Color.parseColor("#28a4e1"));
        // 加载资源文件
        setContentView(getContentViewId());
        tv_title = (TextView) findViewById(R.id.tv_title);
        mToolbar = (Toolbar) findViewById(R.id.toolbar);
        loginfo = (View) findViewById(R.id.loginfo);

        scr_policy = (ScrollView)findViewById(R.id.scr_policy);
        logV = (TextView) findViewById(R.id.logV);
        done = (Button) findViewById(R.id.done);
//        logV = (TextView) findViewById(R.id.logV);
//        logV.setMovementMethod(ScrollingMovementMethod.getInstance());
        // view绑定
        bindView();
        // 初始化页面
        initView();



        // 界面管理器
        AppManager.getInstance().addActivity(this);


        SharedPreferences preferences = getSharedPreferences("AGREE_KEY", 0);
        boolean show = preferences.getBoolean(AGREE_KEY, true);
//        if (!show) {
//            PermissionManager.getInstance().requestPermissions(this, null, PermissionManager.Permission.Storage.WRITE_EXTERNAL_STORAGE);
//        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(Color.TRANSPARENT);


//            window.setNavigationBarColor(Color.WHITE);
        }
//        android11管理所有文件权限
//        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.R ||
//                Environment.isExternalStorageManager()) {
////            Toast.makeText(this, "已获得访问所有文件的权限", Toast.LENGTH_SHORT).show();
//        } else {
//            Intent intent1 = new Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION);
//            startActivity(intent1);
//        }

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        removeInstance();
        if (mPresenter != null) {
            // 解绑view
            mPresenter.detachView();
        }
        AppManager.getInstance().finishActivity(this);
    }

    @Override
    protected void onStart() {
        super.onStart();
        getWindow().setFormat(PixelFormat.TRANSLUCENT);
    }

    public void loadinganim() {
        if (loadingDialog == null) {
            //创建
            loadingDialog = new LoadingDialog(this, getString(R.string.loading), loadanimationDrawable);
            Log.i(TAG, "loadinganim: creat");
        }
        //调用
        loadingDialog.show();
//            loadanimationDrawable.stop();
        loadanimationDrawable.start();
    }

    public void loadinganim(String msg) {
        //创建
        loadingDialog = new LoadingDialog(this, (msg != null && msg.length() > 0) ? msg : getString(R.string.loading), loadanimationDrawable);
        Log.i(TAG, "loadinganim: creat");
        //调用
        loadingDialog.show();
//            loadanimationDrawable.stop();
        loadanimationDrawable.start();
    }

    public void loadanimdrawable() {
        loadanimationDrawable = new AnimationDrawable();
        for (int i = 0; i <= 88; i++) {
            int id = getResources().getIdentifier("loading" + i, "drawable", getPackageName());
            Drawable drawable = getResources().getDrawable(id);
            loadanimationDrawable.addFrame(drawable, 0);
        }
    }

    public void inittoolbar(String curtitle) {
        tv_title = (TextView) findViewById(R.id.tv_title);
        mToolbar = (Toolbar) findViewById(R.id.toolbar);
        tv_title.setText(curtitle);
        mToolbar.setTitle("");
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);
    }

    public void addlog(final String msg) {
//        logV = (TextView) findViewById(R.id.logV);
//        logV.setMovementMethod(ScrollingMovementMethod.getInstance());
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                SimpleDateFormat format = new SimpleDateFormat("hh:mm:ss:ssss");
                Date curD = new Date(System.currentTimeMillis());
                String logtime = format.format(curD);
                String msgs = logV.getText().toString().trim();
//                if (msg.equals("")) {
//                    msgs = logtime + msgret;
//                } else {
                    msgs += "\r\n" + "\r\n" + logtime + ": " + msg;
//                }
                logV.setText(msgs);
//                int offset = logV.getLineCount() * logV.getLineHeight();
//                if (offset > (logV.getHeight()- logV.getLineHeight() - 20)){
//                    logV.scrollTo(0, offset -(logV.getHeight()- logV.getLineHeight() - 20));
//                }

//                Log.i(TAG, "run: ---" + msgs.length());

                if (msgs.length() > 50000) {
                    logV.setText("");
                }

            }
        });
    }

}