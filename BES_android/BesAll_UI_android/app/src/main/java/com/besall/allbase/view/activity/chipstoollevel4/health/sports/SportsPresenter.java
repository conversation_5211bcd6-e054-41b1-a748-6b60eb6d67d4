package com.besall.allbase.view.activity.chipstoollevel4.health.sports;

import android.content.Context;
import android.util.Log;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.besall.allbase.bluetooth.service.health.sports.SportsService;
import com.besall.allbase.view.base.BasePresenter;

public class SportsPresenter extends BasePresenter<ISportsActivity> implements ISportsPresent {

    private SportsService sportsService;


    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        Log.i("TAG", "connectDevice: " + serviceConfig);
        sportsService = new SportsService(serviceConfig, listener, context);
    }

    @Override
    public void sendSportType(int type) {
        sportsService.OPSportsInfo(type);
    }
}
