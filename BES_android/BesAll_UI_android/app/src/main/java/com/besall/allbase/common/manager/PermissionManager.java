package com.besall.allbase.common.manager;

import android.Manifest;
import android.app.Activity;
import android.util.Log;

import androidx.fragment.app.FragmentActivity;

import com.besall.allbase.common.Constants;
import com.besall.allbase.common.utils.ActivityUtils;
import com.tbruyelle.rxpermissions2.RxPermissions;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

public class PermissionManager {
    private PermissionManager() {
    }

    private static class SingleTonHolder {
        private static final PermissionManager INSTANCE = new PermissionManager();
    }

    public static PermissionManager getInstance() {
        return SingleTonHolder.INSTANCE;
    }

    /**
     * 权限
     */
    public class Permission {

        public class Calendar {
            public static final String READ_CALENDAR = Manifest.permission.READ_CALENDAR;
            public static final String WRITE_CALENDAR = Manifest.permission.WRITE_CALENDAR;
        }

        public class Camera {
            public static final String CAMERA = Manifest.permission.CAMERA;
        }

        public class Contacts {
            public static final String READ_CONTACTS = Manifest.permission.READ_CONTACTS;
            public static final String WRITE_CONTACTS = Manifest.permission.WRITE_CONTACTS;
            public static final String GET_ACCOUNTS = Manifest.permission.GET_ACCOUNTS;
        }

        public class Location {
            public static final String ACCESS_FINE_LOCATION = Manifest.permission.ACCESS_FINE_LOCATION;
            public static final String ACCESS_COARSE_LOCATION = Manifest.permission.ACCESS_COARSE_LOCATION;
            public static final String ACCESS_BACKGROUND_LOCATION = Manifest.permission.ACCESS_BACKGROUND_LOCATION;
        }

        public class Microphone {
            public static final String RECORD_AUDIO = Manifest.permission.RECORD_AUDIO;
        }

        public class Phone {
            public static final String READ_PHONE_STATE = Manifest.permission.READ_PHONE_STATE;
            public static final String CALL_PHONE = Manifest.permission.CALL_PHONE;
            public static final String READ_CALL_LOG = Manifest.permission.READ_CALL_LOG;
            public static final String WRITE_CALL_LOG = Manifest.permission.WRITE_CALL_LOG;
            public static final String ADD_VOICEMAIL = Manifest.permission.ADD_VOICEMAIL;
            public static final String USE_SIP = Manifest.permission.USE_SIP;
            public static final String PROCESS_OUTGOING_CALLS = Manifest.permission.PROCESS_OUTGOING_CALLS;
        }

        public class Sensors {
            public static final String BODY_SENSORS = Manifest.permission.BODY_SENSORS;
        }

        public class Sms {
            public static final String SEND_SMS = Manifest.permission.SEND_SMS;
            public static final String RECEIVE_SMS = Manifest.permission.RECEIVE_SMS;
            public static final String READ_SMS = Manifest.permission.READ_SMS;
            public static final String RECEIVE_WAP_PUSH = Manifest.permission.RECEIVE_WAP_PUSH;
            public static final String RECEIVE_MMS = Manifest.permission.RECEIVE_MMS;
            public static final String READ_CELL_BROADCASTS = "android.permission.READ_CELL_BROADCASTS";
        }

        public class Storage {
            public static final String READ_EXTERNAL_STORAGE = Manifest.permission.READ_EXTERNAL_STORAGE;
            public static final String WRITE_EXTERNAL_STORAGE = Manifest.permission.WRITE_EXTERNAL_STORAGE;
        }

    }

    /**
     * Activity弱引用
     */
    private WeakReference<Activity> weakReference;
    /**
     * 监听器
     */
    private PermissionUtilListener mListener;

    /**
     * 申请权限
     */
    public void requestPermissions(Activity activity, PermissionUtilListener listener, final String... permissions) {
        weakReference = new WeakReference<>(activity);
        if (listener != null) {
            mListener = listener;
        }
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                RxPermissions rxPermissions = new RxPermissions((FragmentActivity) weakReference.get());
                rxPermissions.requestEachCombined(getPermissions(permissions))
                        .subscribe(new Observer<com.tbruyelle.rxpermissions2.Permission>() {
                            @Override
                            public void onSubscribe(Disposable d) {

                            }

                            @Override
                            public void onNext(com.tbruyelle.rxpermissions2.Permission permission) {
                                if (permission.granted) {
                                    if (mListener != null) {
                                        mListener.onGranted();
                                    }
                                } else {
                                    String msg;
                                    msg = String.format("Permission acquisition failed, please open the appropriate permission");
                                    ActivityUtils.showToast(msg);
                                    if (mListener != null) {
                                        mListener.onUngranted(msg);
                                    }
                                }
                            }

                            @Override
                            public void onError(Throwable e) {
                                if (mListener != null) {
                                    mListener.onError(e.getMessage());
                                }
                            }

                            @Override
                            public void onComplete() {

                            }
                        });
            }
        });
}

    /**
     * 类型转换
     */
    private String[] getPermissions(String[] permissions) {
        List<String> temp = new ArrayList<>();
        for (String per : permissions) {
            temp.add(per);
        }
        return temp.toArray(new String[temp.size()]);
    }

    public interface PermissionUtilListener {

        /**
         * 获得权限
         */
        void onGranted();

        /**
         * 未获得权限
         */
        void onUngranted(String msg);

        /**
         * 获得权限失败
         */
        void onError(String msg);

    }


}
