package com.besall.allbase.bluetooth.service.crashdump;

import android.content.Context;
import android.util.Log;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.service.base.BesBaseService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.message.BaseMessage;
import com.bes.sdk.utils.DeviceProtocol;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_ERROR;
import static com.besall.allbase.bluetooth.service.crashdump.CrashDumpConstants.CMD_6504_ERROR;
import static com.besall.allbase.bluetooth.service.crashdump.CrashDumpConstants.CMD_LENGTH_ERROR;
import static com.besall.allbase.bluetooth.service.crashdump.CrashDumpConstants.CMD_ROLE_ERROR;
import static com.besall.allbase.bluetooth.service.crashdump.CrashDumpConstants.CMD_TOTA_CRASH_DUMP_END;
import static com.besall.allbase.bluetooth.service.crashdump.CrashDumpConstants.CMD_TOTA_CRASH_DUMP_PARAM_REQ;
import static com.besall.allbase.bluetooth.service.crashdump.CrashDumpConstants.CMD_TOTA_CRASH_DUMP_RECEIVED_ACK;
import static com.besall.allbase.bluetooth.service.crashdump.CrashDumpConstants.CMD_TOTA_CRASH_DUMP_START;
import static com.besall.allbase.bluetooth.service.crashdump.CrashDumpConstants.NO_CRASHDATA;

public class CrashDumpService extends BesBaseService {

    public CrashDumpService(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        super(serviceConfig, listener, context);
        Log.i(TAG, "CrashDumpService: startconnect");
        startConnect(serviceConfig);
    }

    @Override
    public void onStatusChanged(HmDevice device, int status, DeviceProtocol protocol) {
        super.onStatusChanged(device, status, protocol);
        if (status == BES_CONNECT_ERROR) {
            callBackStateChangedMessage(BES_CONNECT_ERROR, "");
        }
    }

    @Override
    public void onDataReceived(BaseMessage deviceMessage) {
        super.onDataReceived(deviceMessage);
        Log.i(TAG, "onDataReceived: -----" + ArrayUtil.toHex((byte[]) deviceMessage.getMsgContent()));
        if (mConfig.getTotaConnect() && !totauccess) {
            Log.i(TAG, "onDataReceived: ----tota no connect");
            return;
        }

        int result = CrashDumpCMD.receiveData((byte[]) deviceMessage.getMsgContent(), mContext);
        if (result == CMD_LENGTH_ERROR) {
            callBackErrorMessage(CMD_LENGTH_ERROR);
        } else if (result == NO_CRASHDATA) {
            callBackErrorMessage(NO_CRASHDATA);
        } else if (result == CMD_TOTA_CRASH_DUMP_PARAM_REQ) {
            sendData(CrashDumpCMD.crashDumpCMD());
        } else if (result == CMD_ROLE_ERROR) {
            callBackErrorMessage(CMD_ROLE_ERROR);
        } else if (result == CMD_TOTA_CRASH_DUMP_END) {
            sendData(CrashDumpCMD.crashDumpEnd());
            callBackStateChangedMessage(CMD_TOTA_CRASH_DUMP_END, "");
        } else if (result == CMD_TOTA_CRASH_DUMP_START) {
            sendData(CrashDumpCMD.crashDumpStartDownload());
            callBackStateChangedMessage(CMD_TOTA_CRASH_DUMP_START, CrashDumpCMD.crashDumpTotalProgress());
        } else if (result == CMD_TOTA_CRASH_DUMP_RECEIVED_ACK) {
            sendData(CrashDumpCMD.crashDumpAckCMD());
            callBackStateChangedMessage(CMD_TOTA_CRASH_DUMP_RECEIVED_ACK, CrashDumpCMD.crashDumpCurrentProgress());
        } else if (result == CMD_6504_ERROR) {
            callBackErrorMessage(CMD_6504_ERROR);
        } else if (result == NO_CRASHDATA) {
            callBackErrorMessage(NO_CRASHDATA);
        }
        callBackStateChangedMessage(BesSdkConstants.TOTA_LOG_INFO, ArrayUtil.toHex((byte[]) deviceMessage.getMsgContent()));
    }

    public void CrashDumpStart() {
        if (totauccess) {
            sendData(CrashDumpCMD.crashDumpStart(), 5000);
        } else {
            callBackErrorMessage(BesSdkConstants.BES_TOTA_ERROR);
        }
    }
}
