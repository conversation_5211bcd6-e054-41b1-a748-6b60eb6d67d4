package com.besall.allbase.view.activity.chipstoollevel4.health.temperature;

import android.content.Context;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.besall.allbase.bluetooth.service.health.temperature.TemperatureSerivce;
import com.besall.allbase.view.base.BasePresenter;

public class TemperaturePresenter extends BasePresenter<ITemperatureActivity> implements ITemperaturePresenter {

    public TemperatureSerivce temperatureSerivce;


    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        temperatureSerivce = new TemperatureSerivce(serviceConfig, listener, context);

    }

    @Override
    public void sendHealth() {
        temperatureSerivce.OPHealth();
    }
}
