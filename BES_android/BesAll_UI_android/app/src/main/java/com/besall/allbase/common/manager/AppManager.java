package com.besall.allbase.common.manager;

import android.app.Activity;

import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.R;

import java.util.ArrayList;
import java.util.List;
import java.util.Stack;

/**
 * activity栈管理器
 * Created by fanyu on 2019-04-18
 */
public class AppManager {

    private AppManager() {}

    private static class SingleTonHolder {
        private static final AppManager INSTANCE = new AppManager();
    }

    public static AppManager getInstance() {
        return SingleTonHolder.INSTANCE;
    }

    /**
     * Activity栈
     */
    private Stack<Activity> activityStack = new Stack<>();

    /**
     * 添加Activity到堆栈
     */
    public void addActivity(Activity activity) {
        if (activityStack == null) {
            activityStack = new Stack<>();
        }
        activityStack.push(activity);
    }

    /**
     * 获取当前Activity（堆栈中最后一个压入的）
     */
    public Activity currentActivity() {
        if (activityStack == null) {
            activityStack = new Stack<>();
        }
        return activityStack.lastElement();
    }

    public boolean isNullInActivityStack() {
        if (activityStack == null) {
            activityStack = new Stack<>();
        }
        return activityStack.isEmpty();
    }

    /**
     * 结束当前Activity（堆栈中最后一个压入的）
     */
    public void finishCurrentActivity() {
        if (activityStack == null) {
            activityStack = new Stack<>();
        }
        Activity activity = activityStack.pop();
        activity.finish();
    }

    /**
     * 结束指定的Activity
     */
    public void finishActivity(Activity activity) {
        if (activityStack == null) {
            activityStack = new Stack<>();
        }
        if (activity != null) {
            activityStack.remove(activity);
            if (!activity.isFinishing())
                activity.finish();
        }
    }

    /**
     * 结束指定类名的Activity
     */
    public void finishActivity(Class<?> cls) {
        if (activityStack == null) {
            activityStack = new Stack<>();
        }
        List<Activity> acts = new ArrayList<>();
        for (Activity activity : activityStack) {
            if (activity.getClass().equals(cls)) {
                acts.add(activity);
            }
        }
        for (Activity activity : acts) {
            finishActivity(activity);
        }
    }

    /**
     * 结束所有Activity
     */
    public void finishAllActivity() {
        if (activityStack == null) {
            activityStack = new Stack<>();
        }
        for (Activity activity : activityStack) {
            if (activity != null) {
                activity.finish();
            }
        }
        activityStack.clear();
    }

    /**
     * 退出应用程序
     */
    private long lastClickTime;

    public void appExit() {
        long between = 2000;
        long time = System.currentTimeMillis();
        if (time - lastClickTime < between) {
            try {
                appExitImmediately();
            } catch (Exception e) {

            }
        } else {
            ActivityUtils.showToast(R.string.exit_hint);
        }
        lastClickTime = time;
    }

    /**
     * 立即退出应用程序
     */
    public void appExitImmediately() {
        finishAllActivity();
        System.exit(0);
    }

    /**
     * 是否已打开Activity
     */
    public boolean existActivity(Class<?> cls) {
        if (activityStack == null) {
            activityStack = new Stack<>();
        }
        for (Activity activity : activityStack) {
            if (activity.getClass().equals(cls)) {
                return true;
            }
        }
        return false;
    }

}