package com.besall.allbase.bluetooth.service.FindMy;


import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_SUCCESS;
import static com.besall.allbase.bluetooth.service.FindMy.FindMyConstants.BES_WATCH_ACTIVATE_PHONE;

import android.content.Context;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.service.base.BesBaseService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.message.BaseMessage;
import com.bes.sdk.utils.DeviceProtocol;

public class FindMyService extends BesBaseService {

    public FindMyService(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        super(serviceConfig, listener, context);
        startConnect(serviceConfig);
    }

    @Override
    public void onStatusChanged(HmDevice device, int status, DeviceProtocol protocol) {
        super.onStatusChanged(device, status, protocol);
        if (status == BES_CONNECT_SUCCESS) {
            callBackStateChangedMessage(BES_CONNECT_SUCCESS, "");
        } else if (getDeviceConnectState(mConfig) != BesSdkConstants.BesConnectState.BES_CONNECT) {
            callBackErrorMessage(status);
        }
    }

    @Override
    public void onDataReceived(BaseMessage deviceMessage) {
        super.onDataReceived(deviceMessage);

        int result = FindMyCMD.receiveData((byte[]) deviceMessage.getMsgContent());
        if (result == BES_WATCH_ACTIVATE_PHONE) {
            callBackStateChangedMessage(BES_WATCH_ACTIVATE_PHONE, "");
        }
    }

    public void changeDevice(BesServiceConfig serviceConfig) {
        startConnect(serviceConfig);
    }

    public void sendFindMyData() {
        FindMyCMD.getFimdMyCMD();

    }
}
