package com.besall.allbase.view.activity.chipstoollevel4.capsensor;

import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_CHART_COEFFICIENTS_DATA;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_CHART_COEFFICIENTS_DATA_KEY;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_CHART_MAXIMUM_DATA;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_CHART_MAXIMUM_DATA_KEY;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_CHART_PIECE_DATA;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_CHART_PIECE_DATA_KEY;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_CHART_USE_CUSTOM_DATA;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_CHART_USE_CUSTOM_DATA_KEY;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_MAX_CN_NUMBER;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.DashPathEffect;
import android.graphics.Paint;
import android.graphics.PathEffect;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import com.bes.bessdk.utils.SPHelper;
import com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants;

public class CapsensorChartView extends View {

    private Context mContext;
    private float[] leftPoint = new float[0];
    private float[] rightPoint = new float[0];
    private boolean[] mShowState = new boolean[CAPSENSOR_MAX_CN_NUMBER];

    public CapsensorChartView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
    }

    public void setDataSource(float[] point0, float[] point1, boolean[] show) {
        mShowState = show;
        leftPoint = point0;
        rightPoint = point1;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        int height = canvas.getHeight();
        int width = canvas.getWidth();

        int maximum = CAPSENSOR_CHART_MAXIMUM_DATA;
        int coefficients = CAPSENSOR_CHART_COEFFICIENTS_DATA;
        int piece = CAPSENSOR_CHART_PIECE_DATA;

        boolean useCustomData = (boolean) SPHelper.getPreference(mContext, CAPSENSOR_CHART_USE_CUSTOM_DATA_KEY, CAPSENSOR_CHART_USE_CUSTOM_DATA);
        if (useCustomData) {
            maximum = (int) SPHelper.getPreference(mContext, CAPSENSOR_CHART_MAXIMUM_DATA_KEY, CAPSENSOR_CHART_MAXIMUM_DATA);
            coefficients = (int) SPHelper.getPreference(mContext, CAPSENSOR_CHART_COEFFICIENTS_DATA_KEY, CAPSENSOR_CHART_COEFFICIENTS_DATA);
            piece = (int) SPHelper.getPreference(mContext, CAPSENSOR_CHART_PIECE_DATA_KEY, CAPSENSOR_CHART_PIECE_DATA);
        }
        Paint mPaint = new Paint();
        PathEffect effects = new DashPathEffect(new float[]{2, 2, 2, 2}, 1);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setAntiAlias(false);
        mPaint.setPathEffect(effects);
        for (int i = 0; i < piece; i ++) {
            canvas.drawLine((width / piece) * i, 0, (width / piece) * i, height, mPaint);
        }

        float thresholdValue = maximum - piece * coefficients;
        Paint cPaint = new Paint();
        cPaint.setStyle(Paint.Style.FILL);
        for (int i = 0; i < rightPoint.length; i ++) {
            if (mShowState[i]) {
                cPaint.setColor(CapSensorConstants.CAPSENSOR_COLORS[i]);
                cPaint.setStrokeWidth(2);
                canvas.drawLine((leftPoint[i] - thresholdValue) / coefficients * (width / piece), 0, (rightPoint[i] - thresholdValue) / coefficients * (width / piece), height, cPaint);
                cPaint.setStrokeWidth(10);
                canvas.drawPoint((rightPoint[i] - thresholdValue) / coefficients * (width / piece), height, cPaint);
            }
        }
    }
}
