package com.besall.allbase.view.activity.chipstoollevel4.health.Pulse;

import android.content.Context;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.besall.allbase.bluetooth.service.health.pulse.PulseService;
import com.besall.allbase.view.base.BasePresenter;

public class PulsePresenter extends BasePresenter<IPulseActivity> implements IPulsePresenter{

    private PulseService pulseService;

    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        pulseService = new PulseService(serviceConfig, listener, context);
    }

    @Override
    public void sendRequest() {
        pulseService.sendRequest();
    }
}
