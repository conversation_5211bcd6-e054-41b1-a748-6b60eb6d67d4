package com.besall.allbase.view.activity.chipstoollevel4.rssi;

import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.text.method.ScrollingMovementMethod;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.appcompat.widget.MenuPopupWindow;
import androidx.appcompat.widget.Toolbar;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.scan.BtHeleper;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.R;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.service.rssi.RssiCMD;
import com.besall.allbase.bluetooth.service.rssi.RssiConstants;
import com.besall.allbase.common.utils.FileUtils;
import com.besall.allbase.common.utils.LogUtils;
import com.besall.allbase.view.base.BaseActivity;
import com.github.mikephil.charting.charts.LineChart;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_ERROR;
import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_SUCCESS;
import static com.bes.bessdk.BesSdkConstants.BES_SAVE_LOG_OTA;
import static com.besall.allbase.bluetooth.BluetoothConstants.Scan.BES_SCAN_RESULT;
import static com.besall.allbase.bluetooth.service.rssi.RssiConstants.RSSI_START;

/**
 * <AUTHOR>
 * @time $ $
 */
public class RssiActivity extends BaseActivity<IRssiActivity, RssiPresenter> implements IRssiActivity, BesServiceListener, View.OnClickListener, View.OnFocusChangeListener {
    private static RssiActivity instance;
    private static Context mContext;
    BluetoothDevice mDevice;
    HmDevice mHmDevice;
    BesServiceConfig mServiceConfig;

    private Button connect_spp;
    private Button rssi_read_start;
    private Button rssi_read_stop;
    private Button spp_stop;
    private EditText interval;
    private int intervel_num =1;
    private String RssiData[] = new String[29];

    private TextView left_tws_min_rssi;
    private TextView left_tws_max_rssi;
    private TextView left_tws_rssi;
    private TextView left_tws_agc;
    private TextView left_call_min_rssi;
    private TextView left_call_max_rssi;
    private TextView left_call_rssi;
    private TextView left_call_agc;
    private TextView right_tws_min_rssi;
    private TextView right_tws_max_rssi;
    private TextView right_tws_rssi;
    private TextView right_tws_agc;
    private TextView right_call_min_rssi;
    private TextView right_call_max_rssi;
    private TextView right_call_rssi;
    private TextView right_call_agc;
    private TextView left_packet_loss_rate;
    private TextView right_packet_loss_rate;
    private TextView left_FA_IDX;
    private TextView right_FA_IDX;
    private TextView left_call_min_rssi_m;
    private TextView left_call_max_rssi_m;
    private TextView left_call_rssi_m;
    private TextView left_call_agc_m;
    private TextView right_call_min_rssi_m;
    private TextView right_call_max_rssi_m;
    private TextView right_call_rssi_m;
    private TextView right_call_agc_m;
    boolean rssiprotocol = true;

    private Button done;
    private TextView logV;
    private ScrollView scr_policy;
    private LineChart lineChart;

    List<RssiChartBean> rssiChartBeans = new ArrayList<>();
    List<Entry> leftEntries = new ArrayList<Entry>();
    List<Entry> leftTwsEntries = new ArrayList<Entry>();
    List<Entry> rightEntries = new ArrayList<Entry>();
    List<Entry> rightTwsEntries = new ArrayList<Entry>();
    LineData l = new LineData();

    @Override
    protected RssiPresenter createPresenter() {
        return new RssiPresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        SPHelper.putPreference(instance, BesSdkConstants.BES_SAVE_LOG_NAME, "RSSI");


    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_rssi;
    }

    @Override
    protected void bindView() {
        connect_spp = (Button)findViewById(R.id.connect_spp);
        rssi_read_start = (Button)findViewById(R.id.rssi_read_start);
        rssi_read_stop = (Button)findViewById(R.id.rssi_read_stop);
        spp_stop = (Button)findViewById(R.id.spp_stop);

        left_tws_min_rssi = (TextView)findViewById(R.id.left_tws_min_rssi);
        left_tws_max_rssi = (TextView)findViewById(R.id.left_tws_max_rssi);
        left_tws_rssi = (TextView)findViewById(R.id.left_tws_rssi);
        left_tws_agc = (TextView)findViewById(R.id.left_tws_agc);

        left_call_min_rssi = (TextView)findViewById(R.id.left_call_min_rssi);
        left_call_max_rssi = (TextView)findViewById(R.id.left_call_max_rssi);
        left_call_rssi = (TextView)findViewById(R.id.left_call_rssi);
        left_call_agc = (TextView)findViewById(R.id.left_call_agc);
        right_call_agc = (TextView)findViewById(R.id.right_call_agc);
        right_call_rssi = (TextView)findViewById(R.id.right_call_rssi);
        right_call_min_rssi = (TextView)findViewById(R.id.right_call_min_rssi);
        right_call_max_rssi = (TextView)findViewById(R.id.right_call_max_rssi);
        right_tws_min_rssi = (TextView)findViewById(R.id.right_tws_min_rssi);
        right_tws_max_rssi = (TextView)findViewById(R.id.right_tws_max_rssi);
        right_tws_rssi = (TextView)findViewById(R.id.right_tws_rssi);
        right_tws_agc = (TextView)findViewById(R.id.right_tws_agc);
        right_packet_loss_rate = (TextView) findViewById(R.id.right_packet_loss_rate);
        left_packet_loss_rate = (TextView) findViewById(R.id.left_packet_loss_rate);
        left_FA_IDX=(TextView)findViewById(R.id.left_FA_IDX);
        right_FA_IDX=(TextView)findViewById(R.id.right_FA_IDX);

        left_call_min_rssi_m = (TextView)findViewById(R.id.left_call_min_rssi_mirror);
        left_call_max_rssi_m = (TextView)findViewById(R.id.left_call_max_rssi_mirror);
        left_call_rssi_m = (TextView)findViewById(R.id.left_call_rssi_mirror);
        left_call_agc_m = (TextView)findViewById(R.id.left_call_agc_mirror);
        right_call_agc_m = (TextView)findViewById(R.id.right_call_agc_mirror);
        right_call_rssi_m = (TextView)findViewById(R.id.right_call_rssi_mirror);
        right_call_min_rssi_m = (TextView)findViewById(R.id.right_call_min_rssi_mirror);
        right_call_max_rssi_m = (TextView)findViewById(R.id.right_call_max_rssi_mirror);
        interval = (EditText)findViewById(R.id.interval);
        tv_title = (TextView) findViewById(R.id.tv_title);
        mToolbar = (Toolbar) findViewById(R.id.toolbar);
        scr_policy = (ScrollView)findViewById(R.id.scr_policy);
        done = (Button) findViewById(R.id.done);
        lineChart = (LineChart) findViewById(R.id.rssiChart);
        logV = (TextView) findViewById(R.id.logV);
        logV.setMovementMethod(ScrollingMovementMethod.getInstance());
    }

    @Override
    protected void initView() {
        connect_spp.setOnClickListener(instance);
        rssi_read_start.setOnClickListener(instance);
        rssi_read_stop.setOnClickListener(instance);
        spp_stop.setOnClickListener(instance);
        interval.setOnFocusChangeListener(instance);
        tv_title.setText("RSSI");
        mToolbar.setTitle("");
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);

        tv_title.setOnClickListener(instance);
        done.setOnClickListener(instance);
        scr_policy.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                v.getParent().requestDisallowInterceptTouchEvent(true);
                return false;
            }
        });

    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data)
    {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == BluetoothConstants.Scan.REQUEST_CODE_SCAN) {
            onPickDevice(resultCode, data);
        }
    }

    private void onPickDevice(int resultCode, Intent data)
    {
        if (resultCode == RESULT_OK) {
            mHmDevice = (HmDevice) data.getSerializableExtra(BES_SCAN_RESULT);
            mDevice = BtHeleper.getBluetoothAdapter(instance).getRemoteDevice(mHmDevice.getPreferredProtocol() == DeviceProtocol.PROTOCOL_BLE ? mHmDevice.getBleAddress() : mHmDevice.getDeviceMAC());

            Log.i(TAG, "onPickDevice: " + mDevice.getName());
            Log.i(TAG, "onPickDevice: " + mDevice.getAddress());
            mServiceConfig = new BesServiceConfig();
            mServiceConfig.setDevice(mHmDevice);
            mServiceConfig.setServiceUUID(BesSdkConstants.BES_SPP_CONNECT);
            boolean totaEncryption = (boolean) SPHelper.getPreference(instance, BesSdkConstants.BES_TOTA_ENCRYPTION_KEY, BesSdkConstants.BES_TOTA_ENCRYPTION_VALUE);
            mServiceConfig.setTotaConnect(totaEncryption);
            boolean useTotaV2 = (boolean) SPHelper.getPreference(instance, BesSdkConstants.BES_TOTA_USE_TOTAV2, BesSdkConstants.BES_TOTA_USE_TOTAV2_VALUE);
            Log.i(TAG, "onPickDevice: useTotaV2" + useTotaV2);
            mServiceConfig.setUseTotaV2(useTotaV2);
            mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
            mPresenter.connectDevice(mServiceConfig, instance, instance);
        }
    }

    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_more, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                mPresenter.stopReadRssi();
                mPresenter.stopSpp();
                finish();
                break;
            case R.id.is_usev2totaprotocol:
                Log.i(TAG, "onOptionsItemSelected: checked");
                SPHelper.putPreference(instance, RssiConstants.RSSI_PROTOCOL, 0);
                rssi_read_start.setText("RSSI " + "(6306)");
                break;
            case R.id.is_useOprotocol:
                SPHelper.putPreference(instance, RssiConstants.RSSI_PROTOCOL, 1);
                rssi_read_start.setText("RSSI " + "(6309)");
                break;
            case R.id.is_useoldprotocol:
                Log.i(TAG, "onOptionsItemSelected: old");
                SPHelper.putPreference(instance, RssiConstants.RSSI_PROTOCOL, 2);
                rssi_read_start.setText("RSSI " + "(900b)");
                break;
            case R.id.show_rssiChart:
                lineChart.setVisibility(View.VISIBLE);
                setLineChart();
                break;

        }
        return super.onOptionsItemSelected(item);
    }

    LineDataSet lineDataSet1 = new LineDataSet(leftEntries,"Left Rssi");
    LineDataSet lineDataSet2 = new LineDataSet(leftTwsEntries,"Left TWS");
    LineDataSet lineDataSet3 = new LineDataSet(rightEntries,"Right Rssi");
    LineDataSet lineDataSet4 = new LineDataSet(rightTwsEntries,"Right TWS");

    private void setLineChart(){
        lineChart.setBackgroundColor(Color.WHITE);
        lineChart.getDescription().setEnabled(true);
        lineChart.setTouchEnabled(true);
        lineChart.setDragEnabled(true);
        lineChart.setScaleEnabled(true);
        lineChart.setPinchZoom(true);
        lineChart.animateXY(1000,1000);

        lineDataSet1.setColor(Color.RED);
        lineDataSet1.setCircleColor(Color.RED);
        lineDataSet2.setColor(Color.GREEN);
        lineDataSet2.setCircleColor(Color.GREEN);
        lineDataSet2.setLineWidth(5f);
        lineDataSet3.setColor(Color.BLUE);
        lineDataSet3.setCircleColor(Color.BLUE);
        l.addDataSet(lineDataSet1);
        l.addDataSet(lineDataSet2);
        l.addDataSet(lineDataSet3);
        l.addDataSet(lineDataSet4);
    }


    public void setData(){
//        float data[] = new float[1000];
//
//        for (int i = 0; i < 1000; i++) {
//            leftRssi[i] = (float) Math.random();
//        }
        //在MPAndroidChart一般都是通过List<Entry>对象来装数据的


        //循环取出数据
//        for(int i = 0; i < rssiChartBeans.size(); i++){
//            leftEntries.add(new Entry(i, rssiChartBeans.get(i).getLeftRssi()));
//            leftTwsEntries.add(new Entry(i, rssiChartBeans.get(i).getLeftTWS()));
//            Log.i(TAG, "setData: --------i" + i);
//            Log.i(TAG, "setData: -+++++"+leftEntries);
//            rightEntries.add(new Entry(i, rssiChartBeans.get(i).getRightRssi()));
//            rightTwsEntries.add(new Entry(i, rssiChartBeans.get(i).getRightTWS()));
//        }

        //一个LineDataSet对象就是一条曲线

        //LineData才是正真给LineChart的数据
        l.notifyDataChanged();
        lineChart.moveViewToX(l.getXMax());
        lineChart.setData(l);
    }
    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.connect_spp:
                mPresenter.pickDecice(instance, BluetoothConstants.Scan.SCAN_SPP);
                break;
            case R.id.rssi_read_start:

                if(interval.getText().toString()== null||interval.getText().toString().trim()==""||interval.getText().toString().trim()==" ")
                {
                    interval.setText("1");
                }
                else {
                    if(Integer.valueOf(interval.getText().toString())>10||Integer.valueOf(interval.getText().toString())<0) {
                        interval.setText("1");
                    }
                    else {
                        intervel_num = Integer.parseInt(interval.getText().toString());
                    }
                }
                String rssi = interval.getText().toString();
                mPresenter.startReadRssi(rssi, instance);
                break;
            case R.id.rssi_read_stop:
                mPresenter.stopReadRssi();

                break;
            case R.id.spp_stop:
                mPresenter.stopSpp();
                reSetdata();
                lineChart.setVisibility(View.GONE);
                lineDataSet1.clear();
                lineDataSet2.clear();
                lineDataSet3.clear();
                lineDataSet4.clear();
                timeTap = 0;
                l.clearValues();
                break;
            case R.id.done:
                loginfo.setVisibility(View.GONE);
                break;
            case R.id.tv_title:
                loginfo.setVisibility(View.VISIBLE);
                break;
            default:
                break;

        }

    }

    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (state == false) {
                    connect_spp.setText("  tota error");
                }
            }
        });
    }

    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {

    }

    int timeTap = 0;
    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {
        Log.i(TAG, "onStateChangedMessage: +" + msgStr);
        if(msgStr =="error"){
            return;
        }

        if (msg == BesSdkConstants.TOTA_LOG_INFO) {
            addlog(msgStr);
        }

        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if(msg == RSSI_START) {
                    RssiData = msgStr.split(";");
                    int flag = 0;
                    left_call_agc.setText(RssiData[0].replace(":"," "));
                    left_call_rssi.setText(RssiData[1].replace(":"," "));
                    left_call_min_rssi.setText(RssiData[3].replace(":"," "));
                    left_call_max_rssi.setText(RssiData[2].replace(":"," "));
                    left_tws_agc.setText(RssiData[4].replace(":"," "));
                    left_tws_rssi.setText(RssiData[5].replace(":"," "));
                    left_tws_min_rssi.setText(RssiData[7].replace(":"," "));
                    left_tws_max_rssi.setText(RssiData[6].replace(":"," "));
                    left_packet_loss_rate.setText(RssiData[8].replace(":"," "));
                    left_FA_IDX.setText(RssiData[9].replace(":"," "));
                    left_call_agc_m.setText(RssiData[10].replace(":"," "));
                    left_call_rssi_m.setText(RssiData[11].replace(":"," "));
                    left_call_min_rssi_m.setText(RssiData[13].replace(":"," "));
                    left_call_max_rssi_m.setText(RssiData[12].replace(":"," "));

                    right_call_agc.setText(RssiData[14].replace(":"," "));
                    right_call_rssi.setText(RssiData[15].replace(":"," "));
                    right_call_min_rssi.setText(RssiData[17].replace(":"," "));
                    right_call_max_rssi.setText(RssiData[16].replace(":"," "));
                    right_tws_agc.setText(RssiData[18].replace(":"," "));
                    right_tws_rssi.setText(RssiData[19].replace(":"," "));
                    right_tws_min_rssi.setText(RssiData[21].replace(":"," "));
                    right_tws_max_rssi.setText(RssiData[20].replace(":"," "));
                    right_packet_loss_rate.setText(RssiData[22].replace(":"," "));
                    right_FA_IDX.setText(RssiData[23].replace(":"," "));
                    right_call_agc_m.setText(RssiData[24].replace(":"," "));
                    right_call_rssi_m.setText(RssiData[25].replace(":"," "));
                    right_call_min_rssi_m.setText(RssiData[27].replace(":"," "));
                    right_call_max_rssi_m.setText(RssiData[26].replace(":"," "));

                    flag = Integer.parseInt(RssiData[28].replace( "flag:","").toString());

                    try {
//                        RssiChartBean rssiChartBean = new RssiChartBean();
//                        rssiChartBean.setLeftRssi(Float.parseFloat(RssiData[1].replace("RSSI:","")));
//                        rssiChartBean.setLeftTWS(Float.parseFloat(RssiData[5].replace("RSSI:","")));
//                        rssiChartBean.setRightRssi(Float.parseFloat(RssiData[15].replace("RSSI:","")));
//                        rssiChartBean.setRightTWS(Float.parseFloat(RssiData[19].replace("RSSI:","")));
//                        rssiChartBeans.add(rssiChartBean);

                        lineDataSet1.addEntry(new Entry(timeTap, Float.parseFloat(RssiData[1].replace("RSSI:",""))));
                        lineDataSet2.addEntry(new Entry(timeTap, Float.parseFloat(RssiData[5].replace("RSSI:",""))));
                        lineDataSet3.addEntry(new Entry(timeTap, Float.parseFloat(RssiData[15].replace("RSSI:",""))));
                        lineDataSet4.addEntry(new Entry(timeTap, Float.parseFloat(RssiData[19].replace("RSSI:",""))));
                        timeTap++;
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }

                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            setData();
                        }
                    });



                    if(flag == 1)
                    {
                        left_call_agc.setTextColor(Color.BLACK);
                        left_call_rssi.setTextColor(Color.BLACK);
                        left_call_min_rssi.setTextColor(Color.BLACK);
                        left_call_max_rssi.setTextColor(Color.BLACK);
                        left_tws_agc.setTextColor(Color.BLACK);
                        left_tws_rssi.setTextColor(Color.BLACK);
                        left_tws_min_rssi.setTextColor(Color.BLACK);
                        left_tws_max_rssi.setTextColor(Color.BLACK);
                        left_packet_loss_rate.setTextColor(Color.BLACK);
                        left_FA_IDX.setTextColor(Color.BLACK);
                        left_call_agc_m.setTextColor(Color.BLACK);
                        left_call_rssi_m.setTextColor(Color.BLACK);
                        left_call_min_rssi_m.setTextColor(Color.BLACK);
                        left_call_max_rssi_m.setTextColor(Color.BLACK);
                        right_call_agc.setTextColor(Color.BLUE);
                        right_call_rssi.setTextColor(Color.BLUE);
                        right_call_min_rssi.setTextColor(Color.BLUE);
                        right_call_max_rssi.setTextColor(Color.BLUE);
                        right_tws_agc.setTextColor(Color.BLUE);
                        right_tws_rssi.setTextColor(Color.BLUE);
                        right_tws_min_rssi.setTextColor(Color.BLUE);
                        right_tws_max_rssi.setTextColor(Color.BLUE);
                        right_packet_loss_rate.setTextColor(Color.BLUE);
                        right_FA_IDX.setTextColor(Color.BLUE);
                        right_call_agc_m.setTextColor(Color.BLUE);
                        right_call_rssi_m.setTextColor(Color.BLUE);
                        right_call_min_rssi_m.setTextColor(Color.BLUE);
                        right_call_max_rssi_m.setTextColor(Color.BLUE);
                    }
                    else
                    {
                        left_call_agc.setTextColor(Color.BLUE);
                        left_call_rssi.setTextColor(Color.BLUE);
                        left_call_min_rssi.setTextColor(Color.BLUE);
                        left_call_max_rssi.setTextColor(Color.BLUE);
                        left_tws_agc.setTextColor(Color.BLUE);
                        left_tws_rssi.setTextColor(Color.BLUE);
                        left_tws_min_rssi.setTextColor(Color.BLUE);
                        left_tws_max_rssi.setTextColor(Color.BLUE);
                        left_packet_loss_rate.setTextColor(Color.BLUE);
                        left_FA_IDX.setTextColor(Color.BLUE);
                        left_call_agc_m.setTextColor(Color.BLUE);
                        left_call_rssi_m.setTextColor(Color.BLUE);
                        left_call_min_rssi_m.setTextColor(Color.BLUE);
                        left_call_max_rssi_m.setTextColor(Color.BLUE);
                        right_call_agc.setTextColor(Color.BLACK);
                        right_call_rssi.setTextColor(Color.BLACK);
                        right_call_min_rssi.setTextColor(Color.BLACK);
                        right_call_max_rssi.setTextColor(Color.BLACK);
                        right_tws_agc.setTextColor(Color.BLACK);
                        right_tws_rssi.setTextColor(Color.BLACK);
                        right_tws_min_rssi.setTextColor(Color.BLACK);
                        right_tws_max_rssi.setTextColor(Color.BLACK);
                        right_packet_loss_rate.setTextColor(Color.BLACK);
                        right_FA_IDX.setTextColor(Color.BLACK);
                        right_call_agc_m.setTextColor(Color.BLACK);
                        right_call_rssi_m.setTextColor(Color.BLACK);
                        right_call_min_rssi_m.setTextColor(Color.BLACK);
                        right_call_max_rssi_m.setTextColor(Color.BLACK);
                    }

                } else if (msg == BES_CONNECT_SUCCESS) {
                    connect_spp.setText("  spp connect");
                } else if (msg == BES_CONNECT_ERROR) {
                    connect_spp.setText("  Click To Connect");
                }
            }
        });
    }

    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {

    }


    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        if (hasFocus == true) {
            return;
        }
        else
        {
            int num = Integer.parseInt(interval.getText().toString());
            if (num < 1) {
                interval.setText("1");
            }
            else if(num>10)
            {
                interval.setText("10");
            }
            intervel_num =  Integer.parseInt(interval.getText().toString());
        }
    }

    public void reSetdata() {

        left_call_min_rssi.setText("MIN_RSSI_0");
        left_call_agc.setText("AGC_0");
        left_call_max_rssi.setText("MAX_RSSI_0");
        left_call_rssi.setText("RSSI_0");
        left_tws_agc.setText("AGC_0");
        left_tws_rssi.setText("RSSI_0");
        left_tws_min_rssi.setText("MIN_RSSI_0");
        left_tws_max_rssi.setText("MAX_RSSI_0");
        right_call_min_rssi.setText("MIN_RSSI_0");
        right_call_agc.setText("AGC_0");
        right_tws_agc.setText("AGC_0");
        right_tws_min_rssi.setText("MIN_RSSI_0");
        right_tws_max_rssi.setText("MAX_RSSI_0");
        right_call_max_rssi.setText("MAX_RSSI_0");
        right_call_rssi.setText("RSSI_0");
        right_tws_rssi.setText("RSSI_0");
        right_packet_loss_rate.setText("PACKET LOSS RATE(%):0" );
        left_packet_loss_rate.setText("PACKET LOSS RATE(%):0" );
        right_FA_IDX.setText("FA_IDX_RIGHT:0" );
        left_FA_IDX.setText("FA_IDX_LEFT:0");
        left_call_min_rssi_m.setText("MIN_RSSI_0");
        left_call_agc_m.setText("AGC_0");
        left_call_max_rssi_m.setText("MAX_RSSI_0");
        left_call_rssi_m.setText("RSSI_0");
        right_call_min_rssi_m.setText("MIN_RSSI_0");
        right_call_agc_m.setText("AGC_0");
        right_call_max_rssi_m.setText("MAX_RSSI_0");
        right_call_rssi_m.setText("RSSI_0");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mPresenter.stopSpp();
    }

//    public void LOG(String TAG, String msg) {
//        if (mContext == null) {
//            return;
//        }
//        Log.i(TAG, "LOG:" + msg);
//        String saveLogName = (String) SPHelper.getPreference(mContext, BesSdkConstants.BES_SAVE_LOG_NAME, "");
//        boolean saveLog = (boolean) SPHelper.getPreference(mContext, BesSdkConstants.BES_SAVE_LOG_KEY, BesSdkConstants.BES_SAVE_LOG_VALUE);
//        if (saveLog) {
//            if (saveLogName.equals(BES_SAVE_LOG_OTA)) {
//                LogUtils.writeForOTA(TAG, msg, saveLogName);
//            } else if (saveLogName.length() > 0){
//                LogUtils.writeForLOG(TAG, msg, saveLogName);
//            }
//        }
//    }

//    public void addlog(final String msg) {
//        final String msgret = msg;
//        runOnUiThread(new Runnable() {
//            @Override
//            public void run() {
//                SimpleDateFormat format = new SimpleDateFormat("hh:mm:ss:ssss");
//                Date curD = new Date(System.currentTimeMillis());
//                String logtime = format.format(curD);
//
//                String msgs = logV.getText().toString().trim();
//                if (msg.equals("")) {
//                    msgs = logtime + msgret;
//                } else {
//                    msgs += "\r\n"  + "\r\n" + logtime +": "+ msgret  ;
//                }
//                logV.setText(msgs);
//            }
//        });
//    }
}
