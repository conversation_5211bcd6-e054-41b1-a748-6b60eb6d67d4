package com.besall.allbase.bluetooth.service.log_dump;

public class LogDumpConstants {

    //cmd
    public static final short     OP_TOTA_READ_FLASH_CMD_OLD = (short) 0x630a;
    public static final short         OP_TOTA_READ_FLASH_CMD = (short) 0x6307;
    public static final short        OP_TOTA_AUDIO_DUMP_STOP = (short) 0x6401;
    public static final short          OP_TOTA_GET_DUMP_INFO = (short) 0x6202;
    public static final short        OP_TOTA_GET_DUMP_FINISH = (short) 0x6203;

    //msg id
    public static final int                    LOGDUMP_START = 0x00003000;
    public static final int             LOGDUMP_LAST_PACKAGE = 0x00003001;
    public static final int                 LOGDUMP_PROGRESS = 0x00003002;
    public static final int                 LOGDUMP_FINISH   = 0x00003003;

    public static final String           LOGDUMP_SAVE_FOLDER = "LogDump";

}
