package com.besall.allbase.view.activity.chipstoollevel4.logdump;

import android.content.Context;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.besall.allbase.view.activity.chipstoollevel4.audiodump.AudioDumpActivity;

/**
 * <AUTHOR>
 * @time $ $
 */
interface ILogDumpPresenter {
    void pickDecice(LogDumpActivity context, int scan);

    void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context);

    void startReadLog();

    void stopSpp();

    void getCurTotalSize();

    void selectfile(LogDumpActivity context, int file);
}
