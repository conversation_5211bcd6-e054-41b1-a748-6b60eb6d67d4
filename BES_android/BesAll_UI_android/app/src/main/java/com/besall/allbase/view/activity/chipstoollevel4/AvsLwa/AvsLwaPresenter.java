package com.besall.allbase.view.activity.chipstoollevel4.AvsLwa;

import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_CHOOSE_WIFI_RESULT;

import android.content.Context;
import android.content.Intent;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;
import com.besall.allbase.bluetooth.service.avslwa.AvsLwaService;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.activity.chipstoollevel4.blewifi.wifilist.WifiListActivity;
import com.besall.allbase.view.base.BasePresenter;

import java.util.Map;

public class AvsLwaPresenter extends BasePresenter<IAvsLwaActivity> implements IAvsLwaPresenter {

    private AvsLwaService avsLwaService;

    @Override
    public void pickDecice(AvsLwaActivity context, int scan) {
        Intent intent = new Intent();
        intent.putExtra(BluetoothConstants.Scan.BES_SCAN, scan);
        ActivityUtils.gotoActForResult(intent, BluetoothConstants.Scan.REQUEST_CODE_SCAN, context, ScanActivity.class);
    }

    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        avsLwaService = new AvsLwaService(serviceConfig, listener, context);

    }

    @Override
    public void pickWifi(AvsLwaActivity context) {
        Intent intent = new Intent();
        ActivityUtils.gotoActForResult(intent, AVS_CHOOSE_WIFI_RESULT, context, WifiListActivity.class);
    }

    @Override
    public void sendWifiData(String name, String psw) {
        avsLwaService.sendWifiData(name, psw);
    }

    @Override
    public Map<String, String> getProductSid() {
        if (avsLwaService == null) {
            return null;
        }
        return avsLwaService.getProductSid();
    }

    @Override
    public void sendAuthorizeResult(String authorizationCode, String redirectUri, String clientId) {
        if (avsLwaService != null)
            avsLwaService.sendAuthorizeResult(authorizationCode, redirectUri, clientId);
    }

    @Override
    public void sendLanguageData(String language) {
        avsLwaService.sendLanguageData(language);
    }

}
