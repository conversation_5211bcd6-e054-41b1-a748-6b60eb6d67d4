package com.besall.allbase.bluetooth.service.throughput;

import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.CmdInfo;
import com.besall.allbase.common.utils.LogUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by alloxuweibin on 2017/12/11.
 */

public class SppMessageHelper {

    String TAG = "SppMessageHelper";
    private static SppMessageHelper instant ;
    /**
     * 不满足协议解析，残留上一次的数据，蓝牙断开时需要清除此数据
     */
    byte[] waitMoreDatas ;

    int throughDataSize = 20 ;

    public void setThroughDataSize(int size){
        throughDataSize = size ;
    }

     private SppMessageHelper(){};

     public static SppMessageHelper getInstant(){
         if(instant == null){
             instant = new SppMessageHelper();
         }
         return instant ;
     }

     /**
             * 判断是否满足协议长度。优先判断是否为语音流数据，再判断其他控制指令
     * @return
             */
     synchronized public byte[][] checkDataEnoughAndRetArray(byte[] data){
         List<byte[]> mArrays = new ArrayList<byte[]>();
         if(data == null || data.length == 0){
             return  null ;
         }
         waitMoreDatas = appendData(data);
         if(waitMoreDatas != null && waitMoreDatas.length > 0) {
             LOG("AFTER APPEND DATA = "+ ArrayUtil.toHex(waitMoreDatas));
         }
         boolean isOut = false ;    //退出迭代
         do {
             byte[] temp = checkDataEnoughAndRet(waitMoreDatas);
             if(temp != null){
                 LOG("--------- loop temp != null so continue");
                 mArrays.add(temp);
             } else {
                 LOG("--------- loop temp == null ready go out");
                 isOut = true ;
             }
         } while (!isOut);
         if(mArrays != null && mArrays.size() > 0 ) {
             LOG("--------- loop check total size is "+ mArrays.size());
             byte[][] datas = new byte[mArrays.size()][];
             for (int i = 0 ; i < mArrays.size() ; i++) {
                 datas[i] = mArrays.get(i);
             }
             return datas ;
         } else {
             LOG("--------- loop check total size is zero");
         }
         return null ;
     }

     /*
        迭代判断
     */
     synchronized private byte[] checkDataEnoughAndRet(byte[] data) {
         if (isEnoughDataToCheck(waitMoreDatas)) {
             LOG( ">> EnoughDataToCheck");
             boolean isVoiceData = isVoiceData(waitMoreDatas);
             if (isVoiceData) {
                 boolean isEnoughPerOpusData = isEnoughPerOpusData(waitMoreDatas);
                 if (isEnoughPerOpusData) {
                     LOG("isVoiceData AND EnoughPerOpusData");
                     return  getOpusDatas(waitMoreDatas);
                 } else {
                     LOG("isVoiceData BUT NEED WAIT MORE DATA");
                 }
             } else {
                 boolean isEnoughCmdAndRight = isEnoughCmdAndRight(waitMoreDatas);
                 if (isEnoughCmdAndRight) {
                     LOG("isCmdData AND EnoughCmdAndRight");
                     return  getCmdData(waitMoreDatas);
                 } else {
                     LOG("isCmdData BUT NEED WAIT MORE DATA");
                 }
             }
         } else {
             LOG("is not enough data  to check ");
         }
         return  null ;
     }

    /**
     * 拼接数据流
     * @param data
     * @return
     */
     private byte[] appendData(byte[] data) {
         if (waitMoreDatas != null) {
             byte[] dstData = new byte[waitMoreDatas.length + data.length];
             System.arraycopy(waitMoreDatas , 0 , dstData , 0 , waitMoreDatas.length);
             System.arraycopy(data , 0 , dstData , waitMoreDatas.length , data.length);
             return dstData ;
         } else {
             return data ;
         }
     }

    /**
     * 只有长度大于一个时候才能进行数据有效判断，如果长度少于等于一个，那么则等待更多数据
     * @param datas
     * @return
     */
     private boolean isEnoughDataToCheck(byte[] datas) {
         if (datas != null && datas.length > 1) {
             return  true ;
         }
         return  false ;
     }

    /**
     * 判断是否为语音流数据,语音流的协议其实为0xff 0xff
     * @param data
     * @return
     */
     private boolean isVoiceData(byte[] data) {
         if (data != null && data.length > 1 && (data[0]&0xff) == 0XFF && (data[1]&0xff)==0xff ) {
             return true ;
         }
         return  false ;
     }

    /**
     * 判断是否满足语音流
     * @param datas
     * @return
     */
     private boolean isEnoughPerOpusData(byte[] datas) {
         if (datas != null && datas.length > 1 && (datas[0]&0xff) == 0XFF && (datas[1]&0xff)==0xff ) {
             return true ;
         }
         return  false ;
     }

     private byte[] getOpusDatas(byte[] datas) {
         if (datas != null && datas.length > 1 && (datas[0]&0xff) == 0XFF && (datas[1]&0xff)==0xff) {
             waitMoreDatas = null ;
             return null ;
         }
         LOG("getOpusDatas but ret null ");
         return  null ;
     }



     private boolean isEnoughCmdAndRight(byte[] datas){
         if (datas == null && datas.length <= 1) {
             return false;
         }
         if ((datas[0]&0xff) == 0x07 && datas.length >= 0) {
             return true ;
         } else if ((datas[0]&0xff) == 0x04 && datas.length >= 5) {
             return true ;
         } else if ((datas[0]&0xff) == 0x05 && datas.length >= 0) {
             return true ;
         } else if ((datas[0]&0xff) == 0x01 && datas.length >= 0) {
             return  true ;
         } else if ((datas[0]&0xff) == 0x03 && datas.length >= 0) {
             return  true ;
         } else if ((datas[0]&0xff) == 0x00 && datas.length >= 0) {
            return  true ;
         } else if ((datas[0]&0xff) == 0x09 && datas.length >= 20) {
             return  true ;
         } else if ((datas[0]&0xff) == 0x0a && datas.length >= throughDataSize) {
             return  true ;
         } else if ((datas[0]&0xff) == 0x0b && datas.length >= CmdInfo.HEAD_LEN) {
             return  true ;
         } else if ((datas[0]&0xff) == 0x0C && datas.length >= CmdInfo.HEAD_LEN) {
             return  true ;
         }
         return false ;
     }

     private byte[] getCmdData(byte[] datas) {
         int len = 0 ;
         if ((datas[0]&0xff) == 0x07 && datas.length >= 5) { //07,80,01,00,01,
             LOG("getCmdData start byte is 0x07");
             len = 5  ;
         } else if ((datas[0]&0xff) == 0x04 && datas.length >= 5) { //04,80,01,00,01,  准备开始数据流标志
             LOG("getCmdData start byte is 0x04");
             len = 5  ;
         } else if ((datas[0]&0xff) == 0x05 && datas.length >= 4) {//05,80,00,00  准备结束数据流标志
             LOG("getCmdData  start byte is 0x05");
             len = 4  ;
         } else if ((datas[0]&0xff) == 0x01 && datas.length >= 12) {// 01,80,08,00,00,00,00,00,00,00,00,00,  开始数据流标志
             LOG("getCmdData  start byte is 0x01");
             len = 12  ;
         } else if ((datas[0]&0xff) == 0x03 && datas.length >= 16) {//03,80,0c,00,00,00,00,00,00,00,00,00,00,00,00,00,  结束数据流标志
             LOG("getCmdData  start byte is 0x03");
             len = 16  ;
         } else if ((datas[0]&0xff) == 0x00 && datas.length >= 10) {
             //00,80,07,00,04,80,00,00,01,00,02,
             //00,80,06,00,05,80,00,00,00,00,
             LOG("getCmdData  start byte is 0x00");
             if (datas.length > 3) {
                 if ((datas[2]&0xff) == 0x07) {
                     len = 11 ;
                 } else if ((datas[2]&0xff) == 0x06) {
                     len = 10 ;
                 }
             } else {
                 return  null;
             }
         } else if ((datas[0]&0xff) == 0x09 && datas.length >= 20) {
             len =  20 ;
         } else if ((datas[0]&0xff) == 0x0a && datas.length >= throughDataSize) {
             len =   throughDataSize ;
         } else if ((datas[0]&0xff) == 0x0b && datas.length >= CmdInfo.HEAD_LEN) {
             len =   CmdInfo.HEAD_LEN ;
         } else if ((datas[0]&0xff) == 0x0C && datas.length >= CmdInfo.HEAD_LEN) {
             len =   CmdInfo.HEAD_LEN ;
         } else {
             LOG("getCmdData and i donot known why ret null \n "+ ArrayUtil.toHex(datas));
             return  null ;
         }
         return  getDataAndLeave(datas , len) ;
     }

     private byte[] getDataAndLeave(byte[] datas , int len) {
         if (datas == null || datas.length < len ) {
             LOG("getDataAndLeave datas == null || datas.length < len len = "+ len);
             waitMoreDatas = null ;
             return  null ;
         }
         byte[] dstDatas = new byte[len];
         System.arraycopy(datas , 0 , dstDatas , 0 , len);
         if (datas.length == len) {
             LOG("getDataAndLeave and waitMoreDatas is clear");
             waitMoreDatas = null ;
         } else {
             int leaveDataLen = datas.length  - len ;
             byte[] leaveData = new byte[leaveDataLen];
             System.arraycopy(datas ,len , leaveData , 0 , leaveDataLen  );
             waitMoreDatas = leaveData ;
             LOG("getDataAndLeave and waitMoreDatas has leave some datas \n"+ ArrayUtil.toHex(waitMoreDatas));
         }
         return  dstDatas ;
     }

     public void clearSppData() {
         if(waitMoreDatas != null){
             waitMoreDatas = null ;
         }
     }

     private void LOG(String msg) {
         if(msg != null){
             LogUtils.e(TAG , msg);
         }

     }

}
