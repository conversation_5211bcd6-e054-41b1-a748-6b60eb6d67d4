package com.besall.allbase.view.activity.level3;


import android.app.Activity;
import android.content.Intent;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;

import com.bes.bessdk.utils.ArrayUtil;
import com.besall.allbase.R;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.activity.chipstoollevel4.AuracastAssistant.AuracastAssistantActivity;
import com.besall.allbase.view.activity.chipstoollevel4.AvsLwa.AvsLwaActivity;
import com.besall.allbase.view.activity.chipstoollevel4.EQ.EQActivity;
import com.besall.allbase.view.activity.chipstoollevel4.SmartVoice.SmartVoiceActivity;
import com.besall.allbase.view.activity.chipstoollevel4.audiodump.AudioDumpActivity;
import com.besall.allbase.view.activity.chipstoollevel4.blewifi.BleWifiActivity;
import com.besall.allbase.view.activity.chipstoollevel4.capsensor.CapSensorActivity;
import com.besall.allbase.view.activity.chipstoollevel4.checkcrc.CheckCrcActivity;
import com.besall.allbase.view.activity.chipstoollevel4.commandset.CommandSetActivity;
import com.besall.allbase.view.activity.chipstoollevel4.crashdump.CrashDumpActivity;
import com.besall.allbase.view.activity.chipstoollevel4.customercmd.CustomCmdActivity;
import com.besall.allbase.view.activity.chipstoollevel4.logdump.LogDumpActivity;
import com.besall.allbase.view.activity.chipstoollevel4.rssi.RssiActivity;
import com.besall.allbase.view.activity.chipstoollevel4.throughput.ThroughputActivity;
import com.besall.allbase.view.activity.chipstoollevel4.rssiextend.RssiExtendActivity;
import com.besall.allbase.view.base.BaseActivity;

/**
 * <AUTHOR>
 * @time $ $
 */
public class FunctionToolsActivity extends BaseActivity<IFunctionToolsActivity, FunctionToolsPresenter> implements IFunctionToolsActivity, View.OnClickListener {

    private static FunctionToolsActivity instance;

    private Button avslwa;
    private Button rssi;
    private Button rssi_extend;
    private Button audio_dump;
    private Button log_dump;
    private Button crash_dump;
    private Button custom_command;
//    private Button opus_audio_dump;
    private Button ble_wifi;
    private Button EQ;
    private Button capsensor;
    private Button throughput;
    private Button check_crc;
    private Button command_set;
    private Button auracast_assistant;
    private Button smartvoice;




    @Override
    protected FunctionToolsPresenter createPresenter() {
        return new FunctionToolsPresenter();
    }

    @Override
    protected void initBeforeSetContent() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_chipstollfunction;
    }

    @Override
    protected void bindView() {
        avslwa = (Button)findViewById(R.id.avslwa);
        rssi = (Button)findViewById(R.id.rssi);
        rssi_extend = (Button)findViewById(R.id.rssi_extend);
        audio_dump = (Button)findViewById(R.id.audio_dump);
        log_dump = (Button)findViewById(R.id.log_dump);
        crash_dump = (Button)findViewById(R.id.crash_dump);
        custom_command = (Button)findViewById(R.id.custom_command);
        auracast_assistant = (Button) findViewById(R.id.auracast_assistant);

//        opus_audio_dump = (Button)findViewById(R.id.opus_audio_dump);
        ble_wifi = (Button)findViewById(R.id.ble_wifi);
        tv_title = (TextView) findViewById(R.id.tv_title);
        EQ = (Button)findViewById(R.id.EQ);
        throughput =(Button)findViewById(R.id.throughput);
		capsensor = (Button)findViewById(R.id.capsensor);
        check_crc = (Button)findViewById(R.id.check_crc);
        command_set = (Button)findViewById(R.id.command_set);
        smartvoice = (Button)findViewById(R.id.smartvoice);

        mToolbar = (Toolbar) findViewById(R.id.toolbar);
    }

    @Override
    protected void initView() {
        avslwa.setOnClickListener(instance);
        rssi.setOnClickListener(instance);
        rssi_extend.setOnClickListener(instance);
        audio_dump.setOnClickListener(instance);
        log_dump.setOnClickListener(instance);
        crash_dump.setOnClickListener(instance);
        custom_command.setOnClickListener(instance);
        auracast_assistant.setOnClickListener(instance);
//        opus_audio_dump.setOnClickListener(instance);
        ble_wifi.setOnClickListener(instance);
        EQ.setOnClickListener(instance);
        throughput.setOnClickListener(instance);
        capsensor.setOnClickListener(instance);
        check_crc.setOnClickListener(instance);
        command_set.setOnClickListener(instance);
        smartvoice.setOnClickListener(instance);

        tv_title.setText("BES CHIPS TOOLS");
        mToolbar.setTitle("");
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);

    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.avslwa:
                ActivityUtils.gotoAct(new Intent(), instance, AvsLwaActivity.class);
                break;
            case R.id.rssi:
                ActivityUtils.gotoAct(new Intent(), instance, RssiActivity.class);
                break;
            case R.id.rssi_extend:
                ActivityUtils.gotoAct(new Intent(), instance, RssiExtendActivity.class);
                break;
            case R.id.audio_dump:
                ActivityUtils.gotoAct(new Intent(), instance, AudioDumpActivity.class);
                break;
            case R.id.log_dump:
                ActivityUtils.gotoAct(new Intent(), instance, LogDumpActivity.class);
                break;
            case R.id.crash_dump:
                ActivityUtils.gotoAct(new Intent(), instance, CrashDumpActivity.class);
                break;
            case R.id.custom_command:
                ActivityUtils.gotoAct(new Intent(), instance, CustomCmdActivity.class);
                break;
            case R.id.EQ:
                ActivityUtils.gotoAct(new Intent(), instance, EQActivity.class);
                break;
            case R.id.throughput:
                ActivityUtils.gotoAct(new Intent(), instance, ThroughputActivity.class);
                break;
            case R.id.capsensor:
                ActivityUtils.gotoAct(new Intent(), instance, CapSensorActivity.class);
				break;
            case R.id.ble_wifi:
                ActivityUtils.gotoAct(new Intent(),instance, BleWifiActivity.class);
                break;
            case R.id.check_crc:
                ActivityUtils.gotoAct(new Intent(),instance, CheckCrcActivity.class);
                break;
            case R.id.command_set:
                ActivityUtils.gotoAct(new Intent(),instance, CommandSetActivity.class);
                break;
            case R.id.auracast_assistant:
                ActivityUtils.gotoAct(new Intent(),instance, AuracastAssistantActivity.class);
                break;
            case R.id.smartvoice:
                ActivityUtils.gotoAct(new Intent(),instance, SmartVoiceActivity.class);
                break;
            default:
                break;
        }
    }
}
