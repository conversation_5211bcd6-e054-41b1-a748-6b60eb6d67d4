package com.besall.allbase.bluetooth.service.health.pulse;

import android.content.Context;

import com.bes.bessdk.utils.CmdInfo;
import com.besall.allbase.bluetooth.BluetoothConstants;

public class PulseCMD {
    public static byte[] PulseRequest(Context context, int type) {
//        boolean Protocol = (boolean) SPHelper.getPreference(context, RssiConstants.RSSI_PROTOCOL, RssiConstants.BES_RSSI_PROTOCOL_VALUE);
        byte flashType = (byte) BluetoothConstants.Connect.CMD_CONFIRM;
        byte value = (byte) 0x00;
        switch (type){
            case 0:
                value = (byte) 0x01;
                break;
            case 1:
                value = (byte) 0x02;
                break;
            case 2:
                value = (byte) 0x03;
                break;
        }
//        if (Protocol) {
//            CmdInfo flashreadcmdInfo = new CmdInfo(SportsConstants.OP_TOTA_RING_SPORT_INFO, new byte[]{flashType});
//            return flashreadcmdInfo.toBytes();
//        }
        CmdInfo flashreadcmdInfo = new CmdInfo(PulseConstants.OP_TOTA_RING_PULSE_INFO,null);
        return flashreadcmdInfo.toBytes();
    }
}
