package com.besall.allbase.view.activity.tools.confirmdialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.EditText;

import com.besall.allbase.common.utils.ResourceUtil;

public class AvsLwaWifiDialog extends Dialog {
    private Context context;
    private Button cancel, confirm;
    private EditText avs_lwa_wifi_name;
    private EditText avs_lwa_wifi_psw;
    private AvsLwaWifiDialoglistener avsDialoglistener;
    private String content;


    public AvsLwaWifiDialog(Context context, String content, AvsLwaWifiDialoglistener
            avsDialoglistener) {
        super(context);
        this.context = context;
        this.content = content;
        this.avsDialoglistener = avsDialoglistener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        getWindow().requestFeature(Window.FEATURE_NO_TITLE);
        LayoutInflater inflater = (LayoutInflater) context
                .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View layout = inflater.inflate(ResourceUtil.getLayoutId(context,
                "avs_lwa_dialog"), null);
        setContentView(layout);
        initview();
    }

    private void initview() {
        cancel = findViewById(ResourceUtil.getId(context, "dialog_cancel"));
        confirm = findViewById(ResourceUtil.getId(context, "dialog_confirm"));
        avs_lwa_wifi_name = findViewById(ResourceUtil.getId(context, "avs_lwa_wifi_name"));
        avs_lwa_wifi_name.setText(content);
        avs_lwa_wifi_psw = findViewById(ResourceUtil.getId(context, "avs_lwa_wifi_psw"));
        cancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                avsDialoglistener.confirmNo();
                AvsLwaWifiDialog.this.dismiss();
            }
        });
        confirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                avsDialoglistener.confirmYesWithText(AvsLwaWifiDialog.this, avs_lwa_wifi_name.getText().toString(), avs_lwa_wifi_psw.getText().toString());
            }
        });
    }


}
