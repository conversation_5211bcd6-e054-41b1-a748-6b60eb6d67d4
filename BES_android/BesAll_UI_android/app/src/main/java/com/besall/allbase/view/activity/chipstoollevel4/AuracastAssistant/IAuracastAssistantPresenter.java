package com.besall.allbase.view.activity.chipstoollevel4.AuracastAssistant;


import android.bluetooth.BluetoothDevice;
import android.content.Context;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;

/**
 * <AUTHOR>
 * @time $ $
 */
interface IAuracastAssistantPresenter {

    void pickDecice(AuracastAssistantActivity context, int scan);

    void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context);

    void startScanBisAdv(Context context, BisScanListener bisScanListener);

    void stopScanBisAdv();

    void sendJoinData(byte[] data, byte[] psw);
    void refreshJoinState();

    void sendQRData(byte[] data);

    void sendStopData(byte[] data);

    void disconnect();

    interface BisScanListener {
        void onBisScanResult(BluetoothDevice device, String name, String bitStr, byte[] data);
    }

}
