package com.besall.allbase.bluetooth.service.auracastassistant;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_ERROR;
import static com.besall.allbase.bluetooth.service.auracastassistant.AuracastAssistantConstants.AURACAST_ASSISTANT_RECEIVE_NUMBER_OF_BIS;
import static com.besall.allbase.bluetooth.service.auracastassistant.AuracastAssistantConstants.AURACAST_ASSISTANT_RECEIVE_SAMPLING_RATE;
import static com.besall.allbase.bluetooth.service.auracastassistant.AuracastAssistantConstants.OP_TOTA_BIS_ADV;
import static com.besall.allbase.bluetooth.service.auracastassistant.AuracastAssistantConstants.OP_TOTA_BIS_ADV_STOP;

import android.content.Context;
import android.util.Log;

import com.bes.bessdk.service.base.BesBaseService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.CmdInfo;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.message.BaseMessage;
import com.bes.sdk.utils.DeviceProtocol;

public class AuracastAssistantService extends BesBaseService {
    private byte[] joinData;
    private byte[] joinPsw;

    public AuracastAssistantService(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        super(serviceConfig, listener, context);
        startConnect(serviceConfig);
    }

    @Override
    public void onStatusChanged(HmDevice device, int status, DeviceProtocol protocol) {
        super.onStatusChanged(device, status, protocol);
        if (status == BES_CONNECT_ERROR) {
            callBackStateChangedMessage(BES_CONNECT_ERROR,"error");
        }
    }

    @Override
    public void onDataReceived(BaseMessage deviceMessage) {
        super.onDataReceived(deviceMessage);
        if (mConfig.getTotaConnect() && !totauccess) {
            return;
        }
        Log.i(TAG, "AuracastAssistantService onDataReceived: ----------" + ArrayUtil.toHex((byte[]) deviceMessage.getMsgContent()));
        byte[] data = (byte[]) deviceMessage.getMsgContent();
        if (data.length > 8 && data[0] == (byte) 0x03 && data[1] == (byte) 0x81) {
            byte[] sampling_rate_bytes = new byte[4];
            System.arraycopy(data, 4, sampling_rate_bytes, 0, 4);
            callBackStateChangedMessage(AURACAST_ASSISTANT_RECEIVE_SAMPLING_RATE, ArrayUtil.bytesToIntLittle(sampling_rate_bytes) + "");
            byte[] number_of_bis = new byte[4];
            System.arraycopy(data, 4 + 4, number_of_bis, 0, 4);
            callBackStateChangedMessage(AURACAST_ASSISTANT_RECEIVE_NUMBER_OF_BIS, ArrayUtil.bytesToIntLittle(number_of_bis) + "");
        }
    }

    public void auracastAssistantSendData(byte[] data) {
        sendData(data);
    }

    public void sendJoinData(byte[] data, byte[] psw) {
        joinData = data;
        joinPsw = psw;
        sendData(new CmdInfo(OP_TOTA_BIS_ADV, ArrayUtil.byteMerger(data, psw)).toBytes());
    }

    public void refreshJoinState() {
        sendStopData(joinData);
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    Thread.sleep(100);
                    sendJoinData(joinData, joinPsw);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }).start();
    }

    public void sendStopData(byte[] data) {
        sendData(new CmdInfo(OP_TOTA_BIS_ADV_STOP, data).toBytes());
    }

}
