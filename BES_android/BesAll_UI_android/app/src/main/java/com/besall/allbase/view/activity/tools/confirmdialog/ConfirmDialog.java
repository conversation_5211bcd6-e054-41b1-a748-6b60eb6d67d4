package com.besall.allbase.view.activity.tools.confirmdialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.besall.allbase.common.utils.ResourceUtil;

public class ConfirmDialog extends Dialog {
    private Context context;
    private Button cancel, confirm;
    private EditText accounttext;
    private ConfirmDialoglistener confirmDialoglistener;
    private String content;


    public ConfirmDialog(Context context, String content, ConfirmDialoglistener
            confirmDialoglistener) {
        super(context);
        this.context = context;
        this.content = content;
        this.confirmDialoglistener = confirmDialoglistener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        getWindow().requestFeature(Window.FEATURE_NO_TITLE);
        LayoutInflater inflater = (LayoutInflater) context
                .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View layout = inflater.inflate(ResourceUtil.getLayoutId(context,
                "confirm_dialog"), null);
        setContentView(layout);
        initview();
    }

    private void initview() {
        cancel = findViewById(ResourceUtil.getId(context, "dialog_cancel"));
        confirm = findViewById(ResourceUtil.getId(context, "dialog_confirm"));
        accounttext = findViewById(ResourceUtil.getId(context, "dialog_text"));
        accounttext.setText(content);
        cancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                confirmDialoglistener.confirmNo();
                ConfirmDialog.this.dismiss();
            }
        });
        confirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ConfirmDialog.this.dismiss();
                confirmDialoglistener.confirmYes();
            }
        });
    }


}
