package com.besall.allbase.bluetooth.service.log_dump;


import android.content.Context;
import android.os.Handler;
import android.os.Message;
import android.util.Log;

import androidx.annotation.NonNull;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.service.base.BesBaseService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.message.BaseMessage;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.common.utils.FileUtils;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_ERROR;
import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_SUCCESS;

public class LogDumpService extends BesBaseService {

    public LogDumpService(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        super(serviceConfig, listener, context);
        Log.i(TAG, "LogDumpService: init");
        LogDumpCMD.init();
        startConnect(serviceConfig);
    }

    @Override
    public void onStatusChanged(HmDevice device, int status, DeviceProtocol protocol) {
        super.onStatusChanged(device, status, protocol);
        if (status == BES_CONNECT_ERROR) {
            callBackStateChangedMessage(BES_CONNECT_ERROR,"error");
        }
    }

    @Override
    public void onDataReceived(BaseMessage deviceMessage) {
        super.onDataReceived(deviceMessage);
        callBackStateChangedMessage(BesSdkConstants.TOTA_LOG_INFO, ArrayUtil.toHex((byte[]) deviceMessage.getMsgContent()));
        LOG("receive:" + ArrayUtil.toHex((byte[]) deviceMessage.getMsgContent()));
        Log.i(TAG, "logDump onDataReceived: -----" + ArrayUtil.toHex((byte[]) deviceMessage.getMsgContent()));
        if (mConfig.getTotaConnect() && !totauccess) {
            Log.i(TAG, "onDataReceived: --return");
            return;
        }
        int result = LogDumpCMD.receiveData((byte[]) deviceMessage.getMsgContent(), mContext);
        LOG("result:" + result);
        if (result == LogDumpConstants.LOGDUMP_START) {
            LOG("LogDumpConstants.LOGDUMP_START:" + ArrayUtil.toHex(LogDumpCMD.logDumpStart()));
            sendData(LogDumpCMD.logDumpStart());
        } else if (result == LogDumpConstants.LOGDUMP_LAST_PACKAGE) {
            LOG("LogDumpConstants.LOGDUMP_LAST_PACKAGE:" + ArrayUtil.toHex(LogDumpCMD.logDumpLastPackage()));
            sendData(LogDumpCMD.logDumpLastPackage());
        } else if (result == LogDumpConstants.LOGDUMP_FINISH){
            LOG("LogDumpConstants.LOGDUMP_FINISH:" + ArrayUtil.toHex(LogDumpCMD.logDumpFinish()));
            sendData(LogDumpCMD.logDumpFinish());
        }

        callBackStateChangedMessage(LogDumpConstants.LOGDUMP_PROGRESS, LogDumpCMD.logDumpProgress());
        callBackStateChangedMessage(BesSdkConstants.TOTA_LOG_INFO, ArrayUtil.toHex((byte[]) deviceMessage.getMsgContent()));
    }

    public void logDumpFlashRead() {
        if (totauccess) {
            sendData(LogDumpCMD.logDumpFlashRead(mContext), 2000);
            LOG("send:" + ArrayUtil.toHex(LogDumpCMD.logDumpFlashRead(mContext)));
        } else {
            callBackErrorMessage(BesSdkConstants.BES_TOTA_ERROR);
        }
    }

    public int logDumpTotalSize() {
        return LogDumpCMD.logDumpTootalSize();
    }

    public void logDumpStop() {
        sendData(LogDumpCMD.logDumpStop());
    }

    private Handler logDumpMsgHandler = new Handler() {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {

                default:
                    break;
            }
        }
    };

    private void LOG(String msg) {
        FileUtils.writeTOFile(msg, "LogDump", "log", "txt");
    }
}
