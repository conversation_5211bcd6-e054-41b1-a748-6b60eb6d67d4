package com.besall.allbase.bluetooth.service.rssi;


import android.content.Context;
import android.os.Handler;
import android.os.Message;
import android.util.Log;

import androidx.annotation.NonNull;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.service.base.BesBaseService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.message.BaseMessage;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.bluetooth.service.log_dump.LogDumpCMD;
import com.besall.allbase.bluetooth.service.log_dump.LogDumpConstants;
import com.besall.allbase.common.utils.LogUtils;

import java.text.SimpleDateFormat;
import java.util.Date;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_ERROR;
import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_SUCCESS;
import static com.bes.bessdk.BesSdkConstants.BES_SAVE_LOG_OTA;
import static com.bes.bessdk.BesSdkConstants.BES_TOTA_ERROR;

public class RssiService extends BesBaseService {

    private static Context mContext;
    public RssiService(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        super(serviceConfig, listener, context);
        startConnect(serviceConfig);
        if (context != null) {
            mContext = context;
        }
    }

    @Override
    public void onStatusChanged(HmDevice device, int status, DeviceProtocol protocol) {
        super.onStatusChanged(device, status, protocol);
        LOG(TAG,"RSSI onStatusChanged" + status);
        Log.i(TAG, "onStatusChanged: ---------" + status);
        if (status == BES_CONNECT_SUCCESS) {
            callBackStateChangedMessage(status, "");
        } else if (status == BES_CONNECT_ERROR) {
            callBackStateChangedMessage(status, "");
        } else if (status == BES_TOTA_ERROR) {
            callBackTotaConnectState(false);
        }
    }

    @Override
    public void onDataReceived(BaseMessage deviceMessage) {
        super.onDataReceived(deviceMessage);
        Log.i(TAG, "onDataReceived: -----" + ArrayUtil.toHex((byte[]) deviceMessage.getMsgContent()));
        LOG(TAG,"RSSI onDataReceived" + ArrayUtil.toHex((byte[]) deviceMessage.getMsgContent()));
//        if (mConfig.getTotaConnect() && !totauccess) {
//            return;
//        }
        String result = RssiCMD.receiveData((byte[]) deviceMessage.getMsgContent(), mContext);
        String logret = RssiCMD.showlog((byte[]) deviceMessage.getMsgContent());
        if(result.length() > 20) {
            callBackStateChangedMessage(RssiConstants.RSSI_START, result);
        }
        if (logret.length() > 0) {
            callBackStateChangedMessage(BesSdkConstants.TOTA_LOG_INFO, logret);
        }
    }

    public void RssiReadStart(Context context) {
        int Protocol = (int) SPHelper.getPreference(context, RssiConstants.RSSI_PROTOCOL, 0 );
        if (Protocol == 2) {
            sendData(RssiCMD.RssiReadStart(context), 5000);
            LOG(TAG,"RSSI sendData" + ArrayUtil.toHex(RssiCMD.RssiReadStart(context)));
        }
        if (totauccess) {
            sendData(RssiCMD.RssiReadStart(context), 5000);
            LOG(TAG,"RSSI sendData" + ArrayUtil.toHex(RssiCMD.RssiReadStart(context)));
        } else {
            callBackErrorMessage(BesSdkConstants.BES_TOTA_ERROR);
        }
    }


    public void LOG(String TAG, String msg) {
        if (mContext == null) {
            return;
        }
        Log.i(TAG, "LOG:" + msg);
        String saveLogName = (String) SPHelper.getPreference(mContext, BesSdkConstants.BES_SAVE_LOG_NAME, "");
        boolean saveLog = (boolean) SPHelper.getPreference(mContext, BesSdkConstants.BES_SAVE_LOG_KEY, BesSdkConstants.BES_SAVE_LOG_VALUE);
        if (saveLog) {
            if (saveLogName.equals(BES_SAVE_LOG_OTA)) {
                LogUtils.writeForOTA(TAG, msg, saveLogName);
            } else if (saveLogName.length() > 0){
                LogUtils.writeForLOG(TAG, msg, saveLogName);
            }
        }
    }

}
