package com.besall.allbase.view.activity.chipstoollevel4.EQ;

import android.content.Context;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @time $ $
 */
interface IEQPresenter {
    void pickDecice(EQActivity context, int scan);

    void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context);

    void eqSet(int prefix, String name, float gain0, float gain1, ArrayList<EQListBeans> eqList);

    void importfile(EQActivity context);
}
