package com.besall.allbase.view.activity.chipstoollevel4.blewifi;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;
import com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants;
import com.besall.allbase.bluetooth.service.BleWifi.BleWifiService;
import com.besall.allbase.bluetooth.service.log_dump.LogDumpService;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.activity.chipstoollevel4.blewifi.wifilist.WifiListActivity;
import com.besall.allbase.view.base.BasePresenter;

import static androidx.core.app.ActivityCompat.startActivityForResult;
import static com.besall.allbase.common.utils.ActivityUtils.showToast;

/**
 * <AUTHOR>
 * @time $ $
 */
class BleWifiPresenter extends BasePresenter<IBleWifiActivity> implements IBleWifiPresenter {
    public final String TAG = getClass().getSimpleName();
    private BleWifiService BleWifiService;

    @Override
    public void pickWifi(BleWifiActivity context) {
        Intent intent = new Intent();
        ActivityUtils.gotoActForResult(intent, BleWifiConstants.CHOOSE_WIFI_RESULT, context, WifiListActivity.class);
    }

    @Override
    public void pickDecice(BleWifiActivity context, int scan) {
        Intent intent = new Intent();
        intent.putExtra(BluetoothConstants.Scan.BES_SCAN, scan);
        ActivityUtils.gotoActForResult(intent, BluetoothConstants.Scan.REQUEST_CODE_SCAN, context, ScanActivity.class);
    }

    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        if (BleWifiService == null) {
            BleWifiService = new BleWifiService(serviceConfig, listener, context);
        } else {
            BleWifiService.changeDevice(serviceConfig);
        }
    }

    @Override
    public void sendWifiData(String name, String psw) {
        if (BleWifiService != null) {
            BleWifiService.sendWifiData(name, psw);
        }
    }

    @Override
    public void openWifi() {
        if (BleWifiService != null) {
            BleWifiService.openWifi();
        }
    }

    @Override
    public void disconnect() {
        if (BleWifiService != null) {
            BleWifiService.disconnected();
        }
    }


}
