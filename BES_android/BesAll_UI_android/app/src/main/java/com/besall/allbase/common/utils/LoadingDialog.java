package com.besall.allbase.common.utils;

import android.app.Dialog;
import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.os.Bundle;
import android.widget.ImageView;
import android.widget.TextView;

import com.besall.allbase.R;

/**
 * <AUTHOR>
 * @time $ $
 */
public class LoadingDialog extends Dialog {

    private AnimationDrawable mAnimation;
    private final String mLoadingTitle;
//    private final int layout;
    private AnimationDrawable animationDrawable;



    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //设置窗口布局
        setContentView(R.layout.progress_dialog);

        ImageView mImageView = findViewById(R.id.loadingIv);
        //设置动画资源
        mImageView.setImageDrawable(animationDrawable);

//        mAnimation = (AnimationDrawable) mImageView.getDrawable();
        // 启动动态图话
//        mImageView.postDelayed(mAnimation::start, 1000);
        TextView mLoadingTv = findViewById(R.id.loadingTv);
        //设置加载文字
        mLoadingTv.setText(mLoadingTitle);
        // <color name="transparent">#00FFFFFF</color>
        //设置透明
        getWindow().setBackgroundDrawableResource(R.color.transparent);
    }

    public LoadingDialog(Context context, String content, AnimationDrawable animationDrawable) {
        super(context);
        this.mLoadingTitle = content;
//        this.layout = layout;
        this.animationDrawable = animationDrawable;
        //点击外围取消
        setCanceledOnTouchOutside(false);

    }



}

