package com.besall.allbase.bluetooth.service.watchavs;

import static com.amazon.alexa.accessory.protocol.Accessories.Command.START_SPEECH;
import static com.besall.allbase.bluetooth.service.watchavs.WatchAvsConstants.OP_TOTA_ALEXA_AUDIO_DATA;
import static com.besall.allbase.bluetooth.service.watchavs.WatchAvsConstants.OP_TOTA_ALEXA_CMD_CTL;
import static com.besall.allbase.bluetooth.service.watchavs.WatchAvsConstants.OP_TOTA_ALEXA_CONNECT_STATUS;
import static com.besall.allbase.bluetooth.service.watchavs.WatchAvsConstants.OP_TOTA_ALEXA_TTS_DATA;
import static com.besall.allbase.bluetooth.service.watchavs.WatchAvsConstants.WATCH_AVS_PARSE_ERROR;
import static com.besall.allbase.bluetooth.service.watchavs.WatchAvsConstants.WATCH_AVS_RECEIVE_ALEXA_AUDIO_DATA;
import static com.besall.allbase.bluetooth.service.watchavs.WatchAvsConstants.WATCH_AVS_RECEIVE_ALEXA_CMD_CTL;

import android.util.Log;

import com.amazon.alexa.accessory.protocol.Accessories;
import com.amazon.alexa.accessory.protocol.Common;
import com.amazon.alexa.accessory.protocol.Speech;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.CmdInfo;
import com.google.protobuf.InvalidProtocolBufferException;

public class WatchAvsCMD {

    private static Accessories.ControlEnvelope mControlEnvelope;

    private static CmdInfo alexaConnectStatusCmd = new CmdInfo(OP_TOTA_ALEXA_CONNECT_STATUS, new byte[0]);
    private static byte[] alexaCmdCTLCmd = new CmdInfo(OP_TOTA_ALEXA_CMD_CTL, new byte[0]).toBytes();
    private static byte[] alexaTTSDataCmd = new CmdInfo(OP_TOTA_ALEXA_TTS_DATA, new byte[0]).toBytes();
    private static byte[] alexaAudioDataCmd = new CmdInfo(OP_TOTA_ALEXA_AUDIO_DATA, new byte[0]).toBytes();

    private static byte[] audioData = new byte[0];
    private static byte[] audioDataBuffer = new byte[0];

    public static byte[] getConnectStateCmd(boolean connect) {
        return new CmdInfo(OP_TOTA_ALEXA_CONNECT_STATUS, new byte[]{(byte) (connect ? 0x01 : 0x02)}).toBytes();
    }

    public static byte[] getResponseCmd(Accessories.Command command, Common.ErrorCode errorCode) {
        Accessories.Response response = Accessories.Response.newBuilder().setErrorCode(errorCode).build();
        Accessories.ControlEnvelope controlEnvelope = Accessories.ControlEnvelope.newBuilder().setCommand(command)
                .setResponse(response)
                .build();
        return new CmdInfo(OP_TOTA_ALEXA_CMD_CTL, controlEnvelope.toByteArray()).toBytes();
    }

    public static byte[] getStopSpeechCommandCmd(Accessories.Command command, Common.ErrorCode errorCode) {
        Speech.StopSpeech stopSpeech = Speech.StopSpeech.newBuilder()
                .setErrorCode(errorCode)
                .build();
        Accessories.ControlEnvelope controlEnvelope = Accessories.ControlEnvelope.newBuilder().setCommand(command)
                .setStopSpeech(stopSpeech)
                .build();
        return new CmdInfo(OP_TOTA_ALEXA_CMD_CTL, controlEnvelope.toByteArray()).toBytes();
    }

    public static byte[] getTransferDataCmd(byte[] data, short cmd) {
        return new CmdInfo(cmd, data).toBytes();
    }

    public static byte[] getAudioData() {
        return audioData;
    }

    public static Accessories.ControlEnvelope getmControlEnvelope() {
        return mControlEnvelope;
    }
    public static int receiveData(byte[] data) {
        mControlEnvelope = null;
        if (data.length < 4) {
            return 0;
        }
        if (data[0] == alexaAudioDataCmd[0] && data[1] == alexaAudioDataCmd[1]) {
            byte[] lBytes = new byte[4];
            System.arraycopy(data, 2, lBytes, 0, 2);
            int l = ArrayUtil.bytesToIntLittle(lBytes);
            byte[] curAudioData = new byte[l];
            System.arraycopy(data, 4, curAudioData, 0, l);
            audioDataBuffer = ArrayUtil.byteMerger(audioDataBuffer, curAudioData);
            if (audioDataBuffer.length > l * 3) {
                audioData = audioDataBuffer;
                audioDataBuffer = new byte[0];
                return WATCH_AVS_RECEIVE_ALEXA_AUDIO_DATA;
            }
            return 0;
        } else if (data[0] == alexaCmdCTLCmd[0] && data[1] == alexaCmdCTLCmd[1]) {
            audioDataBuffer = new byte[0];
            byte[] lBytes = new byte[4];
            System.arraycopy(data, 2, lBytes, 0, 2);
            int l = ArrayUtil.bytesToIntLittle(lBytes);
            byte[] realData = new byte[l];
            System.arraycopy(data, 4, realData, 0, l);
            try {
                mControlEnvelope = Accessories.ControlEnvelope.parseFrom(realData);

                Log.i("TAG", "controlEnvelope: ----" + mControlEnvelope.toString());
            } catch (InvalidProtocolBufferException e) {
                mControlEnvelope = null;
                throw new RuntimeException(e);
            }
            return mControlEnvelope == null ? WATCH_AVS_PARSE_ERROR : WATCH_AVS_RECEIVE_ALEXA_CMD_CTL;
        }
        return 0;
    }
}
