package com.besall.allbase.bluetooth.service.health.temperature;

import static com.besall.allbase.bluetooth.service.health.temperature.TemperatureConstants.OP_TOTA_RING_HEALTH_INFO;

import android.content.Context;

import com.bes.bessdk.utils.CmdInfo;
import com.besall.allbase.bluetooth.BluetoothConstants;

public class TemperatureCMD {
    public static byte[] HealthInfo(Context context, int type) {
//        boolean Protocol = (boolean) SPHelper.getPreference(context, RssiConstants.RSSI_PROTOCOL, RssiConstants.BES_RSSI_PROTOCOL_VALUE);
        byte flashType = (byte) BluetoothConstants.Connect.CMD_CONFIRM;
        byte value = (byte) 0x00;
        switch (type){
            case 0:
                value = (byte) 0x01;
                break;
            case 1:
                value = (byte) 0x02;
                break;
            case 2:
                value = (byte) 0x03;
                break;
        }
// new byte[]{flashType}
        CmdInfo flashreadcmdInfo = new CmdInfo(OP_TOTA_RING_HEALTH_INFO, null);
        return flashreadcmdInfo.toBytes();
    }
}
