package com.besall.allbase.bluetooth.service.smartvoice;

import com.bes.bessdk.utils.ArrayUtil;

import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_RECEIVE_FILE_PATH;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_RECEIVE_OTA_PATH;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_RECEIVE_WIFI_CONFIG;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_RECEIVE_WIFI_OPEN_WIFI_FAIL;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_WIFI_CMD;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_WIFI_FILE_PATH;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_WIFI_OPEN_WIFI_FAIL;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_WIFI_OPEN_WIFI_RSP;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_WIFI_OTA_PATH;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_WIFI_RESPONSE;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.HEADER_GETWORKPAT;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.HEADER_TOOPENWIFI;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.HEADER_WIFICONFIG;

import android.content.Context;
import android.util.Log;

public class SmartVoiceCMD {
    private String TAG = "BleWifiCMD";

}