/*
 * Copyright (c) 2017 Baidu, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.besall.allbase.common.utils;

import android.content.Context;


/**
 * BLe  保存MAC地址状态
 * <p>
 * <NAME_EMAIL> on 2017/6/17.
 */
public class BlePreferenceUtil extends PreferenceUtil {
    // 保存到的文件名字
    private static final String BLE_CONFIG = "ble_config";

    public static final String KEY_SAVE_PCM = "savePcm";
    public static final String PCM_SAVE_ENABLE = "pcmSaveEnable";
    public static final String PCM_SAVE_DISABLE = "pcmSaveDisable";

    /**
     * 保存音量或者静音的数据状态
     *
     * @param context 上下文
     * @param key     键
     * @param object  值
     */
    public static void put(Context context, String key, Object object) {
        put(context, BLE_CONFIG, key, object);
    }

    /**
     * 读取音量或者静音的数据状态
     *
     * @param context       上下文
     * @param key           键
     * @param defaultObject 默认值
     */
    public static Object get(Context context, String key, Object defaultObject) {
        return get(context, BLE_CONFIG, key, defaultObject);
    }
}
