package com.besall.allbase.view.activity.chipstoollevel4.EQ;

import android.app.AlertDialog;
import android.bluetooth.BluetoothDevice;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.text.Editable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextWatcher;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.SeekBar;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.connect.BTService;
import com.bes.bessdk.scan.BtHeleper;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.R;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.service.EQ.EQConstants;
import com.besall.allbase.common.utils.ActivityUtils;

import com.besall.allbase.common.utils.FileUtils;
import com.besall.allbase.view.base.BaseActivity;

import java.io.File;
import java.io.FileInputStream;
import java.io.ObjectInputStream;
import java.util.ArrayList;
import java.util.List;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_ERROR;
import static com.besall.allbase.bluetooth.BluetoothConstants.Scan.BES_SCAN_RESULT;
import static com.besall.allbase.common.Constants.CHIPS_FILE_PATH_RESULT;
import static com.besall.allbase.common.Constants.FILE_CODE;

/**
 * <AUTHOR>
 * @time $ $
 */
public class EQActivity extends BaseActivity<IEQActivity, EQPresenter> implements IEQActivity, BesServiceListener, View.OnClickListener, AdapterView.OnItemSelectedListener, RadioGroup.OnCheckedChangeListener {
    private static EQActivity instance;
    public String cur_title = "EQ";
    BluetoothDevice mDevice;
    HmDevice mHmDevice;
    BesServiceConfig mServiceConfig;

    private Button pick_device;
    private Button connect_device;
    private TextView device_address;
    private TextView device_name;

    private RadioGroup radiogroup_function;

    private TextView msgview;
    private View iir_eq_view_title;
    private RecyclerView recyclerview_content;
    private ArrayList<EQListBeans> eqList;
    private ArrayList<EQSeekbarBeans> eqDrcs;
    private ArrayList<EQSeekbarBeans> eqLimiters;

    private Button eq_function_btn;
    private Button preview;
    private Button audition;
    private Button eq_open_btn;
    private Button eq_writeflash_btn;
    private EditText gain_left_et;
    private EditText gain_right_et;
    private Spinner eq_model_spinner;
    private Spinner iir_type_spinner;
    private Button save_as;
    private Button load;

    private int recyclerParamsH;

    private String[] eqModelList;
    private String[] iirTypeList;

    @Override
    protected EQPresenter createPresenter() {
        return new EQPresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        mServiceConfig = new BesServiceConfig();
        eqModelList = new String[]{"Bass Boost", "Normal", "Treble Boose"};
        iirTypeList = new String[]{"dac_iir_eq", "sw_iir_eq", "hw_iir_eq", "iir_eq", "fir_eq", "drc", "limiter", "burn", "audio_burn", "anc_switch", "cmd", "ping"};
    }

    @Override
    protected int getContentViewId() {
        return R.layout.act_connect;
    }

    @Override
    protected void bindView() {
        pick_device = (Button)findViewById(R.id.pick_device);
        connect_device =(Button)findViewById(R.id.connect_device);
        device_address = (TextView) findViewById(R.id.device_address);
        device_name = (TextView) findViewById(R.id.device_name);
    }

    @Override
    protected void initView() {
        inittoolbar(cur_title);
        pick_device.setOnClickListener(instance);
        connect_device.setOnClickListener(instance);
        loadanimdrawable();

        initlayout();
    }

    public void initlayout() {
        setContentView(R.layout.activity_eq_pc);
        inittoolbar(cur_title);
        instance = this;

        mToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                backtoconnect();
            }
        });

        radiogroup_function = (RadioGroup)findViewById(R.id.radiogroup_function);
        radiogroup_function.setOnCheckedChangeListener(this);

        msgview = (TextView)findViewById(R.id.msgview);
        msgview.setBackgroundColor(instance.getResources().getColor(R.color.green));

        iir_eq_view_title = (View)findViewById(R.id.iir_eq_view_title);
        recyclerview_content = (RecyclerView)findViewById(R.id.recyclerview_content);
        recyclerview_content.setLayoutManager(new LinearLayoutManager(this));
        DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(this, DividerItemDecoration.VERTICAL);
        recyclerview_content.addItemDecoration(dividerItemDecoration);
        LinearLayout.LayoutParams recyclerParams = (LinearLayout.LayoutParams)recyclerview_content.getLayoutParams();
        recyclerParamsH = recyclerParams.height;

        eqList = new ArrayList<EQListBeans>();
        EQListBeans eqListBeans;
        for (int i = 0; i < 20; i++) {
            eqListBeans = new EQListBeans();
            eqListBeans.setEnableFilterType(1);
            eqList.add(eqListBeans);
        }
        reloadRecyclerViewData();

        eq_function_btn = (Button)findViewById(R.id.eq_function_btn);
        eq_function_btn.setOnClickListener(instance);

        preview = (Button)findViewById(R.id.preview);
        preview.setOnClickListener(instance);

        audition = (Button)findViewById(R.id.audition);
        audition.setOnClickListener(instance);

        eq_open_btn = (Button)findViewById(R.id.eq_open_btn);
        eq_open_btn.setOnClickListener(instance);

        eq_writeflash_btn = (Button)findViewById(R.id.eq_writeflash_btn);
        eq_writeflash_btn.setOnClickListener(instance);

        gain_left_et = (EditText)findViewById(R.id.gain_left_et);
        gain_right_et = (EditText)findViewById(R.id.gain_right_et);

        eq_model_spinner = (Spinner)findViewById(R.id.eq_model_spinner);
        ArrayAdapter<String> adapter = new ArrayAdapter<String>(instance, R.layout.simple_spinner_item);
        for (int i = 0; i < eqModelList.length; i++) {
            adapter.add(eqModelList[i]);
        }
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        eq_model_spinner.setAdapter(adapter);
        eq_model_spinner.setOnItemSelectedListener(instance);

        iir_type_spinner = (Spinner)findViewById(R.id.iir_type_spinner);
        ArrayAdapter<String> adapter2 = new ArrayAdapter<String>(instance, R.layout.simple_spinner_item);
        for (int i = 0; i < iirTypeList.length; i++) {
            adapter2.add(iirTypeList[i]);
        }
        adapter2.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        iir_type_spinner.setAdapter(adapter2);
        iir_type_spinner.setOnItemSelectedListener(instance);

        save_as = (Button)findViewById(R.id.save_as);
        save_as.setOnClickListener(instance);

        load = (Button)findViewById(R.id.load);
        load.setOnClickListener(instance);
    }

    private void resetRecycleViewHight(int height) {
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams)recyclerview_content.getLayoutParams();
        layoutParams.height = height == 0 ? recyclerParamsH : height;
        recyclerview_content.setLayoutParams(layoutParams);
    }

    private void reloadRecyclerViewData() {
        EQAdapter eqAdapter = null;
        switch (radiogroup_function.getCheckedRadioButtonId()) {
            case R.id.iir_eq:
                eqAdapter = new EQAdapter(eqList, null, instance);
                break;
            case R.id.drc:
                if (eqDrcs == null) {
                    String[] seekbar_title = new String[]{"Freq1", "Freq2", "Knee Width", "Look Ahead Time", "Attack Time", "Relesae Time", "Threshold", "Makeup Gain", "Ratio", "Scale Factor", "Attack Time", "Relesae Time", "Threshold", "Makeup Gain", "Ratio", "Scale Factor", "Attack Time", "Relesae Time", "Threshold", "Makeup Gain", "Ratio", "Scale Factor"};
                    String[] seekbar_min = new String[]{"50", "50", "0", "0", "1", "1", "-60", "-28", "1", "0.125", "1", "1", "-60", "-28", "1", "0.125", "1", "1", "-60", "-28", "1", "0.125"};
                    String[] seekbar_max = new String[]{"20000", "20000", "20", "10", "200", "3000", "0", "30", "100", "1.0", "200", "3000", "0", "30", "100", "1.0", "200", "3000", "0", "30", "100", "1.0"};
                    String[] seekbar_unit = new String[]{"", "", "dB", "ms", "ms", "ms", "dB", "dB", ":1", "", "ms", "ms", "dB", "dB", ":1", "", "ms", "ms", "dB", "dB", ":1", "", };
                    String[] seekbar_real = new String[]{"50", "50", "0", "0", "1", "1", "0", "0.0", "1", "0.125", "1", "1", "0", "0.0", "1", "0.125", "1", "1", "0", "0.0", "1", "0.125"};
                    int[] seekbar_progress = new int[]{0, 0, 0, 0, 0, 0, 60, 50, 0, 0, 0, 0, 60, 50, 0, 0, 0, 0, 60, 50, 0, 0};

                    String[] freq = new String[]{"50", "63", "79", "100", "126", "158", "200", "251", "316", "398", "501", "631", "794", "1000", "1300", "1600", "2000", "2500", "3200", "4000", "5000", "6300", "8000", "10000", "13000", "15800", "20000"};
                    String[] equal1 = new String[]{"1"};
                    String[] equal05 = new String[]{"0.5"};
                    String[] ratio = new String[]{"1", "2", "4", "8", "20", "100"};
                    String[] scaleFactor = new String[]{"0.125", "0.25", "0.5", "1.0"};
                    String[][] seekbar_interval = new String[][]{freq, freq, equal1, equal1, equal1, equal1, equal1, equal05, ratio, scaleFactor, equal1, equal1, equal1, equal05, ratio, scaleFactor, equal1, equal1, equal1, equal05, ratio, scaleFactor};

                    eqDrcs = new ArrayList<EQSeekbarBeans>();
                    EQSeekbarBeans eqSeekbarBeans;
                    for (int i = 0; i < seekbar_title.length; i++) {
                        eqSeekbarBeans = new EQSeekbarBeans();
                        if (i == 0 || i == 1) {
                            eqSeekbarBeans.setHasRadio(1);
                        } else if (i == 2 || i == 3) {
                            eqSeekbarBeans.setHasRadio(2);
                        }
                        eqSeekbarBeans.setSeekbar_title(seekbar_title[i]);
                        eqSeekbarBeans.setSeekbar_min(seekbar_min[i]);
                        eqSeekbarBeans.setSeekbar_max(seekbar_max[i]);
                        eqSeekbarBeans.setSeekbar_unit(seekbar_unit[i]);
                        eqSeekbarBeans.setSeekbar_real(seekbar_real[i]);
                        eqSeekbarBeans.setSeekbar_progress(seekbar_progress[i]);
                        eqSeekbarBeans.setSeekbar_interval(seekbar_interval[i]);
                        if (i > 9) {
                            eqSeekbarBeans.setCanEdit(false);
                        }
                        eqDrcs.add(eqSeekbarBeans);
                    }
                }
                eqAdapter = new EQAdapter(null, eqDrcs, instance);
                break;
            case R.id.limiter:
                if (eqLimiters == null) {
                    String[] seekbar_title = new String[]{"Look Ahead Time", "Knee Width", "Attack Time", "Release Time", "Threshold", "Makeup Gain"};
                    String[] seekbar_min = new String[]{"0", "0", "1", "1", "-60", "-28"};
                    String[] seekbar_max = new String[]{"10", "20", "200", "3000", "0", "30"};
                    String[] seekbar_unit = new String[]{"ms", "dB", "ms", "ms", "dB", "dB"};
                    String[] seekbar_real = new String[]{"0", "0", "1", "1", "0", "0.0"};
                    int[] seekbar_progress = new int[]{0, 0, 0, 0, 60, 50};

                    String[] equal1 = new String[]{"1"};
                    String[] equal05 = new String[]{"0.5"};
                    String[][] seekbar_interval = new String[][]{equal1, equal1, equal1, equal1, equal1, equal05};
                    eqLimiters = new ArrayList<EQSeekbarBeans>();
                    EQSeekbarBeans eqSeekbarBeans;
                    for (int i = 0; i < seekbar_title.length; i++) {
                        eqSeekbarBeans = new EQSeekbarBeans();
                        eqSeekbarBeans.setSeekbar_title(seekbar_title[i]);
                        eqSeekbarBeans.setSeekbar_min(seekbar_min[i]);
                        eqSeekbarBeans.setSeekbar_max(seekbar_max[i]);
                        eqSeekbarBeans.setSeekbar_unit(seekbar_unit[i]);
                        eqSeekbarBeans.setSeekbar_real(seekbar_real[i]);
                        eqSeekbarBeans.setSeekbar_progress(seekbar_progress[i]);
                        eqSeekbarBeans.setSeekbar_interval(seekbar_interval[i]);
                        eqLimiters.add(eqSeekbarBeans);
                    }
                }
                eqAdapter = new EQAdapter(null, eqLimiters, instance);
                break;
            default:
                break;
        }
        recyclerview_content.setAdapter(eqAdapter);
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == BluetoothConstants.Scan.REQUEST_CODE_SCAN) {
            onPickDevice(resultCode, data);
        } else if (requestCode == FILE_CODE) {
            if (resultCode == CHIPS_FILE_PATH_RESULT) {
                final String jsonfile = data.getStringExtra("getFilePath");
                Log.i(TAG, "onActivityResult: --------" + jsonfile);
                if (jsonfile.contains(EQConstants.EQ_MARK_IIREQ)) {
                    eqList = getLocalEQBeans(jsonfile);
                } else if (jsonfile.contains(EQConstants.EQ_MARK_DRC)) {
                    eqDrcs = getLocalEQDrcs(jsonfile);
                } else if (jsonfile.contains(EQConstants.EQ_MARK_LIMITER)) {
                    eqLimiters = getLocalEQDrcs(jsonfile);
                }
                reloadRecyclerViewData();
            }
        }
    }

    private void onPickDevice(int resultCode, Intent data) {
        if (resultCode == RESULT_OK) {
            mHmDevice = (HmDevice) data.getSerializableExtra(BES_SCAN_RESULT);
            mDevice = BtHeleper.getBluetoothAdapter(instance).getRemoteDevice(mHmDevice.getPreferredProtocol() == DeviceProtocol.PROTOCOL_BLE ? mHmDevice.getBleAddress() : mHmDevice.getDeviceMAC());

            Log.i(TAG, "onPickDevice: " + mDevice.getName());
            Log.i(TAG, "onPickDevice: " + mDevice.getAddress());
            device_address.setText(mDevice.getAddress());
            mServiceConfig.setDevice(mHmDevice);
            String name = mDevice.getName();
            SpannableString ss = new SpannableString(name);
            BesSdkConstants.BesConnectState state = BTService.getDeviceConnectState(instance, mServiceConfig);
            if (state == BesSdkConstants.BesConnectState.BES_CONNECT) {
                ss.setSpan(new ForegroundColorSpan(Color.rgb(103, 200, 77)), 0, name.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
            device_name.setText(ss);
        }
    }

    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (state == true) {
                    initlayout();
                    loadingDialog.dismiss();
                }
            }
        });
    }

    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {

    }

    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {

                if (msg == BES_CONNECT_ERROR) {
                    Log.i(TAG, "run: failed");
                    loadingDialog.dismiss();
                    ActivityUtils.showToast(R.string.connect_failed);
                }
            }
        });
    }

    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {

    }

    public void backtoconnect() {
        setContentView(R.layout.act_connect);
        bindView();
        initView();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_documents, menu);
        return true;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.pick_device:
                mPresenter.pickDecice(instance, BluetoothConstants.Scan.SCAN_SPP);
                connect_device.setVisibility(View.VISIBLE);
                break;
            case R.id.connect_device:
                loadinganim();
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            Log.i(TAG, "run: 1111");
                            Thread.sleep(6000);
                            Log.i(TAG, "run: 22");
                            loadingDialog.dismiss();
                            Log.i(TAG, "run: 1111" + loadingDialog);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }
                }).start();
                if (mHmDevice == null) {
                    Log.i(TAG, "onClick: failed");
                    loadingDialog.dismiss();
                    ActivityUtils.showToast(R.string.connect_failed);
                    return;
                }
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        Log.i(TAG, "run: 1");
                        mServiceConfig.setDevice(mHmDevice);
                        Log.i(TAG, "onPickDevice:1111 " + mDevice.getAddress());
                        mServiceConfig.setTotaConnect(true);
                        mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
                        mPresenter.connectDevice(mServiceConfig, instance, instance);
                    }
                });
                break;
            case R.id.eq_function_btn:
                if (recyclerview_content.getScrollState() != 0) {
                    return;
                }
                eq_function_btn.setSelected(!eq_function_btn.isSelected());
                if (eq_function_btn.isSelected()) {
                    eq_function_btn.setText("hide");
                    resetRecycleViewHight(500);
                } else {
                    eq_function_btn.setText("more");
                    resetRecycleViewHight(0);
                }

                break;
            case R.id.preview:
                if (!checkEqList()) {
                    ActivityUtils.showToast("please check params");
                    return;
                }

                break;
            case R.id.audition:
                if (!checkEqList()) {
                    ActivityUtils.showToast("please check params");
                    return;
                }

                mPresenter.eqSet(1, iir_type_spinner.getSelectedItem().toString(), Float.parseFloat(gain_left_et.getText().toString()), Float.parseFloat(gain_right_et.getText().toString()), eqList);

                break;
            case R.id.eq_open_btn:
                eq_open_btn.setSelected(!eq_open_btn.isSelected());
                if (eq_open_btn.isSelected()) {
                    eq_open_btn.setTextColor(instance.getResources().getColor(R.color.green));
                    eq_open_btn.setText("EQ ON/OFF(ON)");

                    mPresenter.eqSet(1, iir_type_spinner.getSelectedItem().toString(), Float.parseFloat(gain_left_et.getText().toString()), Float.parseFloat(gain_right_et.getText().toString()), eqList);
                } else {
                    eq_open_btn.setTextColor(eq_writeflash_btn.getCurrentTextColor());
                    eq_open_btn.setText("EQ ON/OFF(OFF)");

                    ArrayList<EQListBeans> eqListN = new ArrayList<EQListBeans>();
                    EQListBeans eqListBeans;
                    for (int i = 0; i < 20; i++) {
                        eqListBeans = new EQListBeans();
                        eqListBeans.setEnableFilterType(1);
                        eqListN.add(eqListBeans);
                    }
                    mPresenter.eqSet(1, iir_type_spinner.getSelectedItem().toString(), Float.parseFloat(gain_left_et.getText().toString()), Float.parseFloat(gain_right_et.getText().toString()), eqListN);
                }
                break;
            case R.id.eq_writeflash_btn:

                break;
            case R.id.save_as:
                final EditText inputServer = new EditText(this);
                switch (radiogroup_function.getCheckedRadioButtonId()) {
                    case R.id.iir_eq:
                        if (!checkEqList()) {
                            ActivityUtils.showToast("please check params");
                            return;
                        }
                        inputServer.setText(EQConstants.EQ_MARK_IIREQ);
                        break;
                    case R.id.drc:
                        inputServer.setText(EQConstants.EQ_MARK_DRC);
                        break;
                    case R.id.limiter:
                        inputServer.setText(EQConstants.EQ_MARK_LIMITER);
                        break;
                    default:
                        break;
                }
                AlertDialog.Builder builder = new AlertDialog.Builder(this);
                builder.setTitle("name").setIcon(android.R.drawable.ic_dialog_info).setView(inputServer)
                        .setNegativeButton("Cancel", null);
                builder.setPositiveButton("Ok", new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int which) {
                        saveToFile(inputServer.getText().toString());
                    }
                });
                builder.show();
                break;
            case R.id.load:
                mPresenter.importfile(instance);
                break;
            default:
                break;
        }

    }

    private void saveToFile(String name) {
        switch (radiogroup_function.getCheckedRadioButtonId()) {
            case R.id.iir_eq:
                FileUtils.writeArrayListTOFile(eqList, EQConstants.EQ_SAVE_DATA, name,"txt");
                break;
            case R.id.drc:
                FileUtils.writeArrayListTOFile(eqDrcs, EQConstants.EQ_SAVE_DATA, name,"txt");
                break;
            case R.id.limiter:
                FileUtils.writeArrayListTOFile(eqLimiters, EQConstants.EQ_SAVE_DATA, name,"txt");
                break;
            default:
                break;
        }
    }

    private boolean checkEqList() {
        boolean hasChecked = false;
        for (int i = 0; i < eqList.size(); i ++) {
            EQListBeans eqListBeans = eqList.get(i);
            if (eqListBeans.checked) {
                hasChecked = true;
                if (eqListBeans.getGain().length() == 0 || eqListBeans.getFreq().length() == 0 || eqListBeans.getQ().length() == 0) {
                    return false;
                }
            }
        }
        return hasChecked;
    }

    @Override
    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
        Log.i(TAG, "onItemSelected: --------" + position);
        TextView tv = (TextView)view;
        if (tv != null) {
            tv.setTextSize(12);
        }
    }

    @Override
    public void onNothingSelected(AdapterView<?> parent) {

    }

    @Override
    public void onCheckedChanged(RadioGroup group, int checkedId) {
        switch (group.getCheckedRadioButtonId()) {
            case R.id.iir_eq:
                iir_eq_view_title.setVisibility(View.VISIBLE);

                break;
            case R.id.drc:
                iir_eq_view_title.setVisibility(View.GONE);

                break;
            case R.id.limiter:
                iir_eq_view_title.setVisibility(View.GONE);

                break;
            default:
                break;
        }

        reloadRecyclerViewData();
    }

    public class EQAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> implements View.OnClickListener, AdapterView.OnItemSelectedListener, TextWatcher, View.OnFocusChangeListener, SeekBar.OnSeekBarChangeListener {

        protected final String TAG = getClass().getSimpleName();

        private List<EQListBeans> list;
        private List<EQSeekbarBeans> seekbars;
        private EQActivity context;
        private int curTag = 0;

        public EQAdapter(List<EQListBeans> list, List<EQSeekbarBeans> seekbars, EQActivity context) {
            this.list = list;
            this.seekbars = seekbars;
            this.context = context;
        }

        @Override
        public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            EQHolder eqHolder = null;
            switch (radiogroup_function.getCheckedRadioButtonId()) {
                case R.id.iir_eq:
                    eqHolder = new EQHolder(LayoutInflater.from(getApplicationContext()).inflate(R.layout.item_eq_iireq, null));//引入自定义列表项的资源文件
                    break;
                case R.id.drc:
                case R.id.limiter:
                    eqHolder = new EQHolder(LayoutInflater.from(getApplicationContext()).inflate(R.layout.item_eq_drc, null));//引入自定义列表项的资源文件
                    break;
                default:
                    break;
            }
            return eqHolder;
        }

        @Override
        public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
            EQHolder eqHolder = (EQHolder) holder;
            switch (radiogroup_function.getCheckedRadioButtonId()) {
                case R.id.iir_eq:
                    bindViewHolder_iireq(eqHolder, position);
                    break;
                case R.id.drc:
                case R.id.limiter:
                    bindViewHolder_dirc(eqHolder, position);
                    break;
                default:
                    break;
            }
        }

        private void bindViewHolder_iireq(EQHolder eqHolder, int position) {
            eqHolder.radioBtn.setChecked(list.get(position).checked);
            eqHolder.radioBtn.setTag(100 + position);
            eqHolder.radioBtn.setOnClickListener(this);

            //LOW_SHELF, PEAK, HIGH_SHELF, LOW_PASS, HIGH_PASS
            String[] strings = {"LOW_SHELF", "PEAK", "HIGH_SHELF", "LOW_PASS", "HIGH_PASS"};
            ArrayAdapter<String> adapter = new ArrayAdapter<String>(context, R.layout.simple_spinner_item);
            for (int i = 0; i < strings.length; i ++) {
                adapter.add(strings[i]);
            }
            adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);

            eqHolder.enableFilterType.setTag(200 + position);
            eqHolder.enableFilterType.setAdapter(adapter);
            eqHolder.enableFilterType.setOnItemSelectedListener(this);
            eqHolder.enableFilterType.setSelection(list.get(position).enableFilterType);

            eqHolder.gain_et.setText(list.get(position).getGain());
            eqHolder.gain_et.addTextChangedListener(this);
            eqHolder.gain_et.setOnFocusChangeListener(this);
            eqHolder.gain_et.setTag(300 + position);

            eqHolder.freq.setText(list.get(position).getFreq());
            eqHolder.freq.addTextChangedListener(this);
            eqHolder.freq.setOnFocusChangeListener(this);
            eqHolder.freq.setTag(400 + position);

            eqHolder.q.setText(list.get(position).getQ());
            eqHolder.q.addTextChangedListener(this);
            eqHolder.q.setOnFocusChangeListener(this);
            eqHolder.q.setTag(500 + position);
        }

        private void bindViewHolder_dirc(EQHolder eqHolder, int position) {
            if (seekbars.get(position).isHasRadio() == 1) {
                eqHolder.seekbar_radioview.setVisibility(View.VISIBLE);
                eqHolder.seekbar_radio.setVisibility(View.VISIBLE);
                if (seekbars.get(position).isRadioCheck()) {
                    eqHolder.seekbar_radio.setChecked(true);
                } else {
                    eqHolder.seekbar_radio.setChecked(false);
                }
                eqHolder.seekbar_radio.setTag(600 + position);
                eqHolder.seekbar_radio.setOnClickListener(this);
            } else if (seekbars.get(position).isHasRadio() == 2) {
                eqHolder.seekbar_radioview.setVisibility(View.INVISIBLE);
                eqHolder.seekbar_radio.setVisibility(View.INVISIBLE);
            } else {
                eqHolder.seekbar_radioview.setVisibility(View.GONE);
                eqHolder.seekbar_radio.setVisibility(View.GONE);
            }

            eqHolder.seekbar_title.setText(seekbars.get(position).getSeekbar_title());
            eqHolder.seekbar_min.setText(seekbars.get(position).getSeekbar_min() + seekbars.get(position).getSeekbar_unit());
            eqHolder.seekbar_real.setText(seekbars.get(position).getSeekbar_real());
            eqHolder.seekbar_real.setTag(700 + position);
            eqHolder.seekbar_max.setText(seekbars.get(position).getSeekbar_max() + seekbars.get(position).getSeekbar_unit());

            String[] strings = seekbars.get(position).getSeekbar_interval();
            if (strings.length > 1) {
                eqHolder.seekbar.setMax(strings.length - 1);
            } else {
                float max = Float.parseFloat(seekbars.get(position).getSeekbar_max());
                float min = Float.parseFloat(seekbars.get(position).getSeekbar_min());
                float interval = Float.parseFloat(seekbars.get(position).getSeekbar_interval()[0]);
                int count = (int)((max - min) / interval);
                eqHolder.seekbar.setMax(count);
            }
            eqHolder.seekbar.setTag(800 + position);
            eqHolder.seekbar.setProgress(seekbars.get(position).getSeekbar_progress());
            eqHolder.seekbar.setOnSeekBarChangeListener(this);

            if (seekbars.get(position).isCanEdit()) {
                eqHolder.seekbar_title.setTextColor(Color.BLACK);
                eqHolder.seekbar_min.setTextColor(Color.BLACK);
                eqHolder.seekbar_max.setTextColor(Color.BLACK);
                eqHolder.seekbar_real.setTextColor(Color.BLACK);
                eqHolder.seekbar.setEnabled(true);
            } else {
                eqHolder.seekbar_title.setTextColor(Color.GRAY);
                eqHolder.seekbar_min.setTextColor(Color.GRAY);
                eqHolder.seekbar_max.setTextColor(Color.GRAY);
                eqHolder.seekbar_real.setTextColor(Color.GRAY);
                eqHolder.seekbar.setEnabled(false);
            }

        }

        @Override
        public int getItemCount() {
            int count = 0;
            switch (radiogroup_function.getCheckedRadioButtonId()) {
                case R.id.iir_eq:
                    count = this.list.size();
                    break;
                case R.id.drc:
                case R.id.limiter:
                    count = this.seekbars.size();
                    break;
                default:
                    break;
            }
            return count;
        }

        @Override
        public void onClick(View v) {
            RadioButton radioButton = (RadioButton)v;
            switch (radiogroup_function.getCheckedRadioButtonId()) {
                case R.id.iir_eq:
                    radioButton.setChecked(!this.list.get((int)v.getTag() - 100).getChecked());
                    context.radioButtonClick((int)v.getTag() - 100);
                    break;
                case R.id.drc:
                case R.id.limiter:
                    radioButton.setChecked(!this.seekbars.get((int)v.getTag() - 600).isRadioCheck());
                    context.radioButtonClick((int)v.getTag() - 600);
                    break;
                default:
                    break;
            }
        }

        @Override
        public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
            TextView tv = (TextView)view;
            tv.setTextSize(10.0f);
            context.spinnerSelected((int)parent.getTag() - 200, position);
        }

        @Override
        public void onNothingSelected(AdapterView<?> parent) {
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            if (start == 0 && before == 0 && count == 0) {
                return;
            }
            context.editTextValueChanged(curTag, s + "");
        }

        @Override
        public void afterTextChanged(Editable s) {

        }

        @Override
        public void onFocusChange(View v, boolean hasFocus) {
            curTag = (int)v.getTag();
        }

        @Override
        public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
            if (!fromUser) return;

            int position = (int)seekBar.getTag() - 800;
            String[] strings = seekbars.get(position).getSeekbar_interval();
            TextView textView = seekBar.getRootView().findViewWithTag(position + 700);

            String real = "";
            if (strings.length > 1) {
                if (textView != null) {
                    real = strings[progress];
                }
            } else {
                float min = Float.parseFloat(seekbars.get(position).getSeekbar_min());
                float interval = Float.parseFloat(seekbars.get(position).getSeekbar_interval()[0]);
                if (textView != null) {
                    real = (min + interval * progress) + "";
                }
            }
            textView.setText(real);

            seekbarProgressChanged(position, progress, real);
        }

        @Override
        public void onStartTrackingTouch(SeekBar seekBar) {

        }

        @Override
        public void onStopTrackingTouch(SeekBar seekBar) {

        }

        class EQHolder extends RecyclerView.ViewHolder {

            RadioButton radioBtn;
            Spinner enableFilterType;
            EditText gain_et;
            EditText freq;
            EditText q;

            View seekbar_radioview;
            RadioButton seekbar_radio;
            TextView seekbar_title;
            SeekBar seekbar;
            TextView seekbar_min;
            TextView seekbar_real;
            TextView seekbar_max;

            public EQHolder(View itemView) {
                super(itemView);
                switch (radiogroup_function.getCheckedRadioButtonId()) {
                    case R.id.iir_eq:
                        radioBtn = (RadioButton)itemView.findViewById(R.id.radioBtn);
                        enableFilterType = (Spinner)itemView.findViewById(R.id.enableFilterType);
                        gain_et = (EditText)itemView.findViewById(R.id.gain_et);
                        freq = (EditText)itemView.findViewById(R.id.freq);
                        q = (EditText)itemView.findViewById(R.id.q);
                        break;
                    case R.id.drc:
                    case R.id.limiter:
                        seekbar_radioview = (View)itemView.findViewById(R.id.seekbar_radioview);
                        seekbar_radio = (RadioButton)itemView.findViewById(R.id.seekbar_radio);
                        seekbar_title = (TextView)itemView.findViewById(R.id.seekbar_title);
                        seekbar  = (SeekBar)itemView.findViewById(R.id.seekbar);
                        seekbar_min = (TextView)itemView.findViewById(R.id.seekbar_min);
                        seekbar_real = (TextView)itemView.findViewById(R.id.seekbar_real);
                        seekbar_max = (TextView)itemView.findViewById(R.id.seekbar_max);
                        break;
                    default:
                        break;
                }

            }
        }
    }

    public void radioButtonClick(int position) {
        switch (radiogroup_function.getCheckedRadioButtonId()) {
            case R.id.iir_eq:
                EQListBeans eqListBeans = eqList.get(position);
                eqListBeans.setChecked(!eqListBeans.getChecked());
                eqList.set(position, eqListBeans);
                break;
            case R.id.drc:
                EQSeekbarBeans eqSeekbarBeans = eqDrcs.get(position);
                eqSeekbarBeans.setRadioCheck(!eqSeekbarBeans.isRadioCheck());
                eqDrcs.set(position, eqSeekbarBeans);

                for (int i = 10; i < eqDrcs.size(); i ++) {
                    EQSeekbarBeans eqBA = eqDrcs.get(i);
                    eqBA.setCanEdit(false);
                    eqDrcs.set(i, eqBA);
                }
                int check = 0;
                for (int i = 0; i < 2; i ++) {
                    EQSeekbarBeans eqA = eqDrcs.get(i);
                    if (eqA.isRadioCheck()) {
                        check ++;
                    }
                }
                for (int i = 0; i < check; i ++) {
                    for (int j = 10 + i * 6; j < 10 + i * 6 + 6; j ++) {
                        EQSeekbarBeans eqBA = eqDrcs.get(j);
                        eqBA.setCanEdit(true);
                        eqDrcs.set(j, eqBA);
                    }
                }
                reloadRecyclerViewData();
                break;
            default:
                break;
        }
    }

    public void seekbarProgressChanged(int position, int progress, String real) {
        switch (radiogroup_function.getCheckedRadioButtonId()) {
            case R.id.drc:
                EQSeekbarBeans eqSeekbarBeans = eqDrcs.get(position);
                eqSeekbarBeans.setSeekbar_progress(progress);
                eqSeekbarBeans.setSeekbar_real(real);
                eqDrcs.set(position, eqSeekbarBeans);
               break;
            case R.id.limiter:
                EQSeekbarBeans eqSeekbarBeans2 = eqLimiters.get(position);
                eqSeekbarBeans2.setSeekbar_progress(progress);
                eqSeekbarBeans2.setSeekbar_real(real);
                eqDrcs.set(position, eqSeekbarBeans2);
                break;
            default:
                break;
        }
    }

    public void spinnerSelected(int position, int spinnerIndex) {
        EQListBeans eqListBeans = eqList.get(position);
        eqListBeans.enableFilterType = spinnerIndex;
        eqList.set(position, eqListBeans);
    }

    public void editTextValueChanged(int tag, String content) {
        if (recyclerview_content.getScrollState() != 0) {
            return;
        }
        int position = 0;
        int type = 100;
        if (tag > 499) {
            position = tag - 500;
            type = 2;
        } else if (tag > 399) {
            position = tag - 400;
            type = 1;
        } else if (tag > 299) {
            position = tag - 300;
            type = 0;
        } else {
            return;
        }
        EQListBeans eqListBeans = eqList.get(position);
        if (type == 0) {
            eqListBeans.setGain(content);
        } else if (type == 1) {
            eqListBeans.setFreq(content);
        } else if (type == 2) {
            eqListBeans.setQ(content);
        }
        eqList.set(position, eqListBeans);
    }

    public ArrayList<EQListBeans> getLocalEQBeans(String path) {
        ObjectInputStream objectInputStream = null;
        FileInputStream fileInputStream = null;
        ArrayList<EQListBeans> savedArrayList = new ArrayList<>();
        try {
            File file = new File(path);
            fileInputStream = new FileInputStream(file);
            objectInputStream = new ObjectInputStream(fileInputStream);
            savedArrayList = (ArrayList<EQListBeans>) objectInputStream.readObject();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return savedArrayList;
    }

    public ArrayList<EQSeekbarBeans> getLocalEQDrcs(String path) {
        ObjectInputStream objectInputStream = null;
        FileInputStream fileInputStream = null;
        ArrayList<EQSeekbarBeans> savedArrayList = new ArrayList<>();
        try {
            File file = new File(path);
            fileInputStream = new FileInputStream(file);
            objectInputStream = new ObjectInputStream(fileInputStream);
            savedArrayList = (ArrayList<EQSeekbarBeans>) objectInputStream.readObject();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return savedArrayList;
    }






}


