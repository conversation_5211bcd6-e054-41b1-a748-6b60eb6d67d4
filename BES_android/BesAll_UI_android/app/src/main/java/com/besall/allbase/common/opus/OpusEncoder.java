package com.besall.allbase.common.opus;

import android.util.Log;

/**
 *  Created by all0 on 2017/12/07.
 */

public class OpusEncoder {
    private native long init_voip_native(int fs, int channels);

    private native long init_audio_native(int fs, int channels);

    private native long init_restricted_low_delay_native(int fs, int channels);

    private native byte[] encode_native(long encoder, short[] pcm, int frameSize);

    private native void destory_native(long encoder);

    private long encoder;

    public void initVoip(int fs, int channels) {
        encoder = init_voip_native(fs, channels);
    }

    public void initAudio(int fs, int channels) {
        encoder = init_audio_native(fs, channels);
    }

    public void initRestrictedLowDelay(int fs, int channels) {
        encoder = init_restricted_low_delay_native(fs, channels);
    }

    public byte[] encode(short[] pcm, int frameSize) {
        return encode_native(encoder, pcm, frameSize);
    }

    public void destory() {
        destory_native(encoder);
    }
}
