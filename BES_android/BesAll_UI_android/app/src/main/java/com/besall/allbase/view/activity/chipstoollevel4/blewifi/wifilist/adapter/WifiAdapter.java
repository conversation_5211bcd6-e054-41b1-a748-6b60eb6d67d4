package com.besall.allbase.view.activity.chipstoollevel4.blewifi.wifilist.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import androidx.annotation.UiThread;
import com.besall.allbase.R;

import java.util.ArrayList;
import java.util.List;

public class WifiAdapter extends BaseAdapter {

    private List<String> mNames;
    private Context mContext;

    public WifiAdapter(Context context) {
        mNames = new ArrayList<String>();
        mContext= context;
    }

    @Override
    public int getCount() {
        return mNames.size();
    }

    @Override
    public Object getItem(int position) {
        return position;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        if (convertView == null) {
            convertView = LayoutInflater.from(mContext).inflate(R.layout.wifi_item, parent, false);
            holder = new WifiAdapter.ViewHolder();
            holder.mName = (TextView) convertView.findViewById(R.id.name);
            convertView.setTag(holder);
        } else {
            holder = (WifiAdapter.ViewHolder) convertView.getTag();
        }

        holder.mName.setText(mNames.get(position));
        return convertView;
    }

    @UiThread
    public void add(String name) {
        synchronized (mNames) {
            mNames.add(name);
            notifyDataSetChanged();
        }
    }
    public void clear() {
        synchronized (mNames) {
            mNames.clear();
            mNames = new ArrayList<String>();
            notifyDataSetChanged();
        }
    }

    private class ViewHolder {
        TextView mName;
    }
}
