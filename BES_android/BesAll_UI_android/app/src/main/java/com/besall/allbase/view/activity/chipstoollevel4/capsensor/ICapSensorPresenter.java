package com.besall.allbase.view.activity.chipstoollevel4.capsensor;

import android.content.Context;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;

public interface ICapSensorPresenter {

    void pickDecice(CapSensorActivity context, int scan);

    void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context);

    void getSensorData();

    String getChannelData();

    String getExtraParamData();
    void stopSensorData();

    void test();

    void selectfile(CapSensorActivity context, int file);

    void sendData(byte[] data);
}
