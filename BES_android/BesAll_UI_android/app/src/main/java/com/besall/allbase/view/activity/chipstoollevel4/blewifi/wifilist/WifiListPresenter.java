package com.besall.allbase.view.activity.chipstoollevel4.blewifi.wifilist;

import android.app.Activity;
import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bes.bessdk.scan.BesScanManager;
import com.bes.sdk.scan.ScanManager;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.common.manager.PermissionManager;
import com.besall.allbase.view.base.BasePresenter;

import java.util.Collection;
import java.util.Iterator;


public class WifiListPresenter extends BasePresenter<IWifiListActivity> implements IWifiListPresenter {

    private final String TAG = getClass().getSimpleName();




}
