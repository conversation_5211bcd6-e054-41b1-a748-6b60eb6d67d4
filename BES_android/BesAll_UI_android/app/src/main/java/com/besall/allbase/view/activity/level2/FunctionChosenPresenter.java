package com.besall.allbase.view.activity.level2;

import android.content.Intent;

import com.besall.allbase.common.utils.ActivityUtils;

import com.besall.allbase.view.activity.tools.SettingActivity.SettingActivity;
import com.besall.allbase.view.activity.tools.aboutus.AboutUsActivity;
import com.besall.allbase.view.base.BasePresenter;

/**
 * <AUTHOR>
 * @time $ $
 */
public class FunctionChosenPresenter extends BasePresenter<IFunctionChosenActivity> implements IFunctionChosenPresenter {

    @Override
    public void goToSettingActivity(FunctionChosenActivity activity) {
        ActivityUtils.gotoAct(new Intent(), activity, SettingActivity.class);
    }
}
