package com.besall.allbase.view.activity.chipstoollevel4.capsensor;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_SUCCESS;
import static com.besall.allbase.bluetooth.BluetoothConstants.Scan.BES_SCAN_RESULT;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.BES_CAPSENSOER_SPP_UUID;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.BES_CAPSENSOR_CUSTOMER_UUID_KEY;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.BES_CAPSENSOR_CUSTOMER_UUID_VALUE;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.BES_CAPSENSOR_DATA_MULTIPLE;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.BES_CAPSENSOR_DATA_MULTIPLE_VALUE;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.BES_CAPSENSOR_HAS_EXTRAS_PARAM_KEY;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.BES_CAPSENSOR_HAS_EXTRAS_PARAM_VALUE;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.BES_CAPSENSOR_SEND_CMD_KEY;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.BES_CAPSENSOR_SEND_CMD_VALUE;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.BES_CAPSENSOR_SHOW_TOUCH_EVENT;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.BES_CAPSENSOR_SHOW_TOUCH_EVENT_VALUE;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_CHART_COEFFICIENTS_DATA_KEY;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_COLORS;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_DATA_LOG;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAP_KEY_EVENT.CAP_KEY_EVENT_CLICK;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAP_KEY_EVENT.CAP_KEY_EVENT_CLICK_AND_LONGLONGPRESS;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAP_KEY_EVENT.CAP_KEY_EVENT_CLICK_AND_LONGPRESS;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAP_KEY_EVENT.CAP_KEY_EVENT_DOUBLECLICK;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAP_KEY_EVENT.CAP_KEY_EVENT_DOUBLECLICK_AND_LONGLONGPRESS;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAP_KEY_EVENT.CAP_KEY_EVENT_DOWN;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAP_KEY_EVENT.CAP_KEY_EVENT_DOWNSLIDE;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAP_KEY_EVENT.CAP_KEY_EVENT_EXTEND;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAP_KEY_EVENT.CAP_KEY_EVENT_LONGLONGLONGLONGPRESS;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAP_KEY_EVENT.CAP_KEY_EVENT_LONGLONGLONGPRESS;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAP_KEY_EVENT.CAP_KEY_EVENT_LONGLONGPRESS;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAP_KEY_EVENT.CAP_KEY_EVENT_LONGPRESS;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAP_KEY_EVENT.CAP_KEY_EVENT_NONE;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAP_KEY_EVENT.CAP_KEY_EVENT_OFF_EAR;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAP_KEY_EVENT.CAP_KEY_EVENT_ON_EAR;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAP_KEY_EVENT.CAP_KEY_EVENT_RAMPAGECLICK;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAP_KEY_EVENT.CAP_KEY_EVENT_SEVENCLICK;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAP_KEY_EVENT.CAP_KEY_EVENT_SIXCLICK;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAP_KEY_EVENT.CAP_KEY_EVENT_TRIPLECLICK;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAP_KEY_EVENT.CAP_KEY_EVENT_TRIPLECLICK_AND_LONGLONGPRESS;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAP_KEY_EVENT.CAP_KEY_EVENT_ULTRACLICK;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAP_KEY_EVENT.CAP_KEY_EVENT_UP;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAP_KEY_EVENT.CAP_KEY_EVENT_UPSLIDE;
import static com.besall.allbase.common.Constants.CHIPS_FILE_PATH_RESULT;
import static com.besall.allbase.common.Constants.FILE_CODE;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.graphics.Color;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.format.DateFormat;
import android.text.method.ScrollingMovementMethod;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.scan.BtHeleper;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.R;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.common.utils.ExcelUtils;
import com.besall.allbase.view.base.BaseActivity;
import com.github.mikephil.charting.charts.LineChart;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;
import com.suke.widget.SwitchButton;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class CapSensorActivity extends BaseActivity<ICapSensorActivity, CapSensorPresenter> implements ICapSensorActivity, BesServiceListener, View.OnClickListener, CompoundButton.OnCheckedChangeListener, SwitchButton.OnCheckedChangeListener {

    private final String TAG = getClass().getSimpleName();
    public final static String DIRNAME = "capsensor";

    private static CapSensorActivity instance;
    BluetoothDevice mDevice;
    HmDevice mHmDevice;
    BesServiceConfig mServiceConfig;
    public String cur_title = "CAP SENSOR";

    private boolean isTotaDevice = false;

    private TextView customer_uuid_tips;
    private EditText customer_uuid, et_data_multiple;

    private Button pick_device;
    private Button pick_device_tota;
    private Button pick_device_customer;
    private Button connect_device;
    private TextView device_address;
    private TextView device_name;

    private Button start;
    private Button stop;
    private Button continues;
    private EditText saveName;
    private com.suke.widget.SwitchButton capsensor_show_touch_switch;
    private TextView touch_event_text;
    private TextView ear_state_text;
    private CheckBox cn1, cn2, cn3, cn4, cn5, cn6, cn7, cn8;
    private CheckBox[] mCheckBoxs;
    private int[] mChannels = new int[]{0, 1, 2, 3, 4, 5, 6, 7};

    private TextView capsensor_data;

    private LineChart lineChart;

    private Button capsensor_send_btn;
    private EditText capsensor_send_text;

    private boolean shouldRefreshChannel = false;

    private UUID mServiceUUID = BES_CAPSENSOER_SPP_UUID;

    @Override
    protected CapSensorPresenter createPresenter() {
        return new CapSensorPresenter();
    }

    @Override
    protected void initBeforeSetContent() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.act_connect;
    }

    @Override
    protected void bindView() {
        customer_uuid_tips = (TextView) findViewById(R.id.customer_uuid_tips);
        customer_uuid = (EditText) findViewById(R.id.customer_uuid);
        pick_device = (Button)findViewById(R.id.pick_device);
        pick_device_tota = (Button) findViewById(R.id.pick_device_ble);
        pick_device_customer = (Button) findViewById(R.id.edit_cutomercmd);
        connect_device = (Button)findViewById(R.id.connect_device);
        device_address = (TextView) findViewById(R.id.device_address);
        device_name = (TextView) findViewById(R.id.device_name);

        done = (Button) findViewById(R.id.done);
        loginfo = (View) findViewById(R.id.loginfo);
    }

    @SuppressLint("ResourceAsColor")
    @Override
    protected void initView() {
        inittoolbar(cur_title);
        tv_title.setOnClickListener(instance);
        done.setOnClickListener(instance);

        customer_uuid_tips.setVisibility(View.VISIBLE);
        customer_uuid.setVisibility(View.VISIBLE);


        customer_uuid.setText((String) SPHelper.getPreference(instance, BES_CAPSENSOR_CUSTOMER_UUID_KEY, BES_CAPSENSOR_CUSTOMER_UUID_VALUE));
        pick_device.setOnClickListener(instance);
        pick_device_tota.setOnClickListener(instance);
        pick_device_tota.setVisibility(View.VISIBLE);
        pick_device_tota.setText(getString(R.string.change_device_tota_spp));
        connect_device.setOnClickListener(instance);
        pick_device_customer.setVisibility(View.VISIBLE);
        pick_device_customer.setText(getString(R.string.change_device_customer_spp));
        pick_device_customer.setOnClickListener(instance);
        loadanimdrawable();


//        initlayout();
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        return false;
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        return super.dispatchTouchEvent(ev);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data)
    {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == BluetoothConstants.Scan.REQUEST_CODE_SCAN) {
            onPickDevice(resultCode, data);
        } else if (requestCode == FILE_CODE) {
            onPickFile(resultCode, data);
        }
    }

    @SuppressLint("MissingPermission")
    private void onPickDevice(int resultCode, Intent data) {
        if (resultCode == RESULT_OK) {
            mHmDevice = (HmDevice) data.getSerializableExtra(BES_SCAN_RESULT);
            mDevice = BtHeleper.getBluetoothAdapter(instance).getRemoteDevice(mHmDevice.getPreferredProtocol() == DeviceProtocol.PROTOCOL_BLE ? mHmDevice.getBleAddress() : mHmDevice.getDeviceMAC());

            device_address.setText(mDevice.getAddress());
            device_name.setText("Current Device:" + mDevice.getName());
        }
    }

    private void onPickFile(int resultCode, Intent data) {
        if (lineChart == null) {
            return;
        }
        if (resultCode == CHIPS_FILE_PATH_RESULT) {
            if (connect_device != null) {
                initlayout();
            }

            resetLineChart();
            String filePath = data.getStringExtra("getFilePath");
            Log.i(TAG, "onPickFile: -----" + filePath);
            List<float[]> allData = ExcelUtils.getCapsensorData(filePath);
            float[] curCNs = allData.get(0);
            for (int i = 0; i < curCNs.length; i ++) {
                Log.i(TAG, "curCNs: -------" + curCNs[i]);
            }
            int length = 0;
            for (int i = 0; i < curCNs.length; i ++) {
                if (i > 0 && curCNs[i] == 0) {
                    continue;
                }
                length ++;
            }
            Log.i(TAG, "length: ------" + length);
            mChannels = new int[length];
            for (int i = 0; i < length; i ++) {
                mChannels[i] = (int)curCNs[i];
            }
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    for (int i = 0; i < mCheckBoxs.length; i ++) {
                        if (i < mChannels.length) {
                            mCheckBoxs[i].setVisibility(View.VISIBLE);
                            mCheckBoxs[i].setText(mChannels[i] + "");
                        } else {
                            mCheckBoxs[i].setVisibility(View.GONE);
                            mCheckBoxs[i].setChecked(false);
                        }
                    }
                    refreshChartView(new float[0], true);
                }
            });
            for (int i = 1; i < allData.size(); i ++) {
                refreshChartView(allData.get(i), false);
            }
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;
            case R.id.menu_file:
                mPresenter.selectfile(instance, FILE_CODE);
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.pick_device:
                isTotaDevice = false;
                mServiceUUID = BesSdkConstants.BES_SPP_CONNECT;
                mPresenter.pickDecice(instance, BluetoothConstants.Scan.SCAN_SPP);
                break;
            case R.id.pick_device_ble:
                isTotaDevice = true;
                mServiceUUID = BesSdkConstants.BES_SPP_CONNECT;
                mPresenter.pickDecice(instance, BluetoothConstants.Scan.SCAN_SPP);
                break;
            case R.id.edit_cutomercmd:
                if (customer_uuid.getText().length() != 0 && customer_uuid.getText().length() < 47) {
                    ActivityUtils.showToast(R.string.uuid_format_error);
                    return;
                }
                if (customer_uuid.getText().length() == 0) {
                    SPHelper.putPreference(instance, BES_CAPSENSOR_CUSTOMER_UUID_KEY, BES_CAPSENSOR_CUSTOMER_UUID_VALUE);
                    mServiceUUID = BES_CAPSENSOER_SPP_UUID;
                }
                if (customer_uuid.getText().length() != 0) {
                    String uuidStr = customer_uuid.getText().toString();
                    String[] uuidStrList = uuidStr.split(",");
                    Log.i(TAG, "onClick length: -------"  + uuidStrList.length);
                    if (uuidStrList.length != 16) {
                        ActivityUtils.showToast(R.string.uuid_format_error);
                        return;
                    }
                    String str = "";
                    for (int i = 0; i < uuidStrList.length; i ++) {
                        if (uuidStrList[15 - i].length() != 2)  {
                            ActivityUtils.showToast(R.string.uuid_format_error);
                            return;
                        }
                        str = str + uuidStrList[15 - i];
                        Log.i(TAG, "onClick: -------" + str);
                    }
                    StringBuilder stringBuilder = new StringBuilder(str);
                    stringBuilder.insert(8, "-");
                    stringBuilder.insert(8 + 5, "-");
                    stringBuilder.insert(8 + 5 + 5, "-");
                    stringBuilder.insert(8 + 5 + 5 + 5, "-");

                    mServiceUUID = UUID.fromString(stringBuilder.toString());
                    SPHelper.putPreference(instance, BES_CAPSENSOR_CUSTOMER_UUID_KEY, customer_uuid.getText().toString());
                }

                isTotaDevice = false;
                mPresenter.pickDecice(instance, BluetoothConstants.Scan.SCAN_SPP);
                break;
            case R.id.connect_device:
                loadinganim();
                if (mHmDevice == null) {
                    Log.i(TAG, "onClick: failed");
                    loadingDialog.dismiss();
                    ActivityUtils.showToast(R.string.connect_failed);
                    return;
                }
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        Log.i(TAG, "run: 1");
                        mServiceConfig = new BesServiceConfig();
                        mServiceConfig.setDevice(mHmDevice);
                        Log.i(TAG, "onPickDevice:1111 " + mDevice.getAddress());
                        mServiceConfig.setServiceUUID(mServiceUUID);
                        Log.i(TAG, "onPickDevice:1111 " + mDevice.getAddress());
                        if (isTotaDevice) {
                            Log.i(TAG, "run: -------isTotaDevice");
                            mServiceConfig.setTotaConnect(true);
                            boolean useTotaV2 = (boolean) SPHelper.getPreference(instance, BesSdkConstants.BES_TOTA_USE_TOTAV2, BesSdkConstants.BES_TOTA_USE_TOTAV2_VALUE);
                            mServiceConfig.setUseTotaV2(useTotaV2);
                        }
                        mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
                        mPresenter.connectDevice(mServiceConfig, instance, instance);
                    }
                });
                break;
            case R.id.start:
                if (et_data_multiple.getText().toString().equals("0")) {
                    ActivityUtils.showToast("倍数不能为0");
                    return;
                }
                SPHelper.putPreference(instance, BES_CAPSENSOR_DATA_MULTIPLE, Integer.parseInt(et_data_multiple.getText().toString()));
                resetLineChart();
                String[] titleStrs = new String[mChannels.length];
                for (int i = 0; i < mChannels.length; i ++) {
                    titleStrs[i] = "CN" + mChannels[i];
                }

                String sheetName = (mHmDevice.getPreferredProtocol() == DeviceProtocol.PROTOCOL_BLE ? mHmDevice.getBleAddress() : mHmDevice.getDeviceMAC()).replace(":", " ");
                ExcelUtils.createExcel(DIRNAME, saveName.getText().toString() + DateFormat.format("yyyy-MM-dd HH:mm:ss:sss", System.currentTimeMillis()), instance, titleStrs, sheetName, true);
                shouldRefreshChannel = true;
                mPresenter.getSensorData();
                break;
            case R.id.stop:
                mPresenter.stopSensorData();
//                drawing.setEnabled(true);
                ExcelUtils.saveData();
                break;
            case R.id.continues:
//                byte[] data = new byte[]{0x21, 0x63, 0x24, 0x00,   0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  0x01, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,   0x02, 0x00, 0x00, 0x00,   0x03, 0x00, 0x00, 0x00,   0x04, 0x00, 0x00, 0x00,  0x05, 0x00, 0x00, 0x00,  0x06, 0x00, 0x00, 0x00,};
//
//                CapSensorCMD.receiveData(instance, data, true);
//
//                onStateChangedMessage(CapSensorConstants.CAPSENSOR_DATA, "0\n" + "1\n", null);
//                mPresenter.test();
                break;
            case R.id.done:
                loginfo.setVisibility(View.GONE);
                break;
            case R.id.tv_title:
                loginfo.setVisibility(View.VISIBLE);
                break;
            case R.id.capsensor_send_btn:
                if (capsensor_send_text.getText().length() == 0) {
                    return;
                }
                SPHelper.putPreference(instance, BES_CAPSENSOR_SEND_CMD_KEY, capsensor_send_text.getText().toString());
                mPresenter.sendData(ArrayUtil.toBytes(capsensor_send_text.getText().toString()));
                break;
            default:
                break;
        }
    }

    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (state == true) {
                    loadingDialog.dismiss();
                    initlayout();
                }
            }
        });
    }

    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {

            }
        });
    }

    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (msg == CapSensorConstants.CAPSENSOR_DATA) {
//                    String[] dataStrs = msgStr.split("\n");
//                    String[] extraParam = mPresenter.getExtraParamData().split("\n");
                    String[] mData = msgStr.split("besmask");
                    String[] dataStrs = mData[0].split("\n");
                    String[] extraParam = mData[1].split("\n");
                    int earStateLocation = extraParam.length - 1 - (capsensor_show_touch_switch.isChecked() ? 1 : 0);
                    ear_state_text.setText(extraParam[earStateLocation].equals("0") ? "Off Ear" : "On Ear");
                    if (capsensor_show_touch_switch.isChecked()) {
                        Log.i(TAG, "valueOfrun: -------" + getCapKeyEvent(Integer.valueOf(extraParam[extraParam.length - 1])).getName());

                        touch_event_text.setText(getCapKeyEvent(Integer.valueOf(extraParam[extraParam.length - 1])).getName());
                    }
                    ExcelUtils.addCapsensorData(extraParam);
//                    Log.i(TAG, "onStateChangedMessage: ----------" + msgStr);
                    float[] floaty = new float[dataStrs.length];
                    for (int i = 0; i < dataStrs.length; i ++) {
                        floaty[i] = Float.valueOf(dataStrs[i]);
                    }
                    if (dataStrs.length > mChannels.length) {
                        addlog(msgStr);
                        return;
                    }
                    SpannableStringBuilder builder = new SpannableStringBuilder();
                    for (int i = 0; i < dataStrs.length; i ++) {
                        String content = "CN" + mChannels[i] + ":" + dataStrs[i] + "  " + (i == 3 ? "\n" : "");
                        SpannableString spannableString = new SpannableString(content);
                        spannableString.setSpan(new ForegroundColorSpan(CAPSENSOR_COLORS[i])
                                , content.indexOf("CN" + mChannels[i])
                                , content.indexOf(":")
                                , Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                        builder.append(spannableString);
                    }
                    capsensor_data.setText(builder);
                    refreshChartView(floaty, true);
                } else if (msg == CAPSENSOR_DATA_LOG) {
                    addlog("---------------");
                    addlog(msgStr);

                } else if (msg == BES_CONNECT_SUCCESS) {
                    loadingDialog.dismiss();
                    initlayout();
                }
            }
        });
    }

    private CapSensorConstants.CAP_KEY_EVENT getCapKeyEvent(int value) {
        switch (value) {
            case 0:
                return CAP_KEY_EVENT_NONE;
            case 1:
                return CAP_KEY_EVENT_DOWN;
            case 4:
                return CAP_KEY_EVENT_UP;
            case 6:
                return CAP_KEY_EVENT_LONGPRESS;
            case 7:
                return CAP_KEY_EVENT_LONGLONGPRESS;
            case 8:
                return CAP_KEY_EVENT_CLICK;
            case 9:
                return CAP_KEY_EVENT_DOUBLECLICK;
            case 10:
                return CAP_KEY_EVENT_TRIPLECLICK;
            case 11:
                return CAP_KEY_EVENT_ULTRACLICK;
            case 12:
                return CAP_KEY_EVENT_RAMPAGECLICK;
            case 21:
                return CAP_KEY_EVENT_UPSLIDE;
            case 22:
                return CAP_KEY_EVENT_DOWNSLIDE;
            case 23:
                return CAP_KEY_EVENT_ON_EAR;
            case 24:
                return CAP_KEY_EVENT_OFF_EAR;
            case 25:
                return CAP_KEY_EVENT_LONGLONGLONGPRESS;
            case 26:
                return CAP_KEY_EVENT_LONGLONGLONGLONGPRESS;
            case 40:
                return CAP_KEY_EVENT_EXTEND;
            case 41:
                return CAP_KEY_EVENT_SIXCLICK;
            case 42:
                return CAP_KEY_EVENT_SEVENCLICK;
            case 43:
                return CAP_KEY_EVENT_CLICK_AND_LONGPRESS;
            case 44:
                return CAP_KEY_EVENT_CLICK_AND_LONGLONGPRESS;
            case 45:
                return CAP_KEY_EVENT_DOUBLECLICK_AND_LONGLONGPRESS;
            case 46:
                return CAP_KEY_EVENT_TRIPLECLICK_AND_LONGLONGPRESS;

            default:
                return CAP_KEY_EVENT_NONE;
        }
    }

    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {

    }

    public void initlayout() {
        setContentView(R.layout.activity_capsensor);
        inittoolbar(cur_title);
        instance = this;

        inittoolbar(cur_title);
        mToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                backtoconnect();
            }
        });

        lineChart = (LineChart) findViewById(R.id.capsensor_chart);
        setLineChart();

        capsensor_show_touch_switch = (SwitchButton) findViewById(R.id.capsensor_show_touch_switch);
        capsensor_show_touch_switch.setOnCheckedChangeListener(instance);
        boolean shwoTouchEvent = (boolean) SPHelper.getPreference(instance, BES_CAPSENSOR_SHOW_TOUCH_EVENT, BES_CAPSENSOR_SHOW_TOUCH_EVENT_VALUE);
        capsensor_show_touch_switch.setChecked(shwoTouchEvent);

        touch_event_text = (TextView) findViewById(R.id.touch_event_text);
        if (shwoTouchEvent) {
            touch_event_text.setVisibility(View.VISIBLE);
        } else {
            touch_event_text.setVisibility(View.GONE);
        }

        ear_state_text = (TextView) findViewById(R.id.ear_state_text);

        start = (Button)findViewById(R.id.start);
        start.setOnClickListener(instance);
        stop = (Button)findViewById(R.id.stop);
        stop.setOnClickListener(instance);
        continues = (Button)findViewById(R.id.continues);
        continues.setOnClickListener(instance);
        saveName = (EditText) findViewById(R.id.saveName);

        cn1 = (CheckBox)findViewById(R.id.cn1);
        cn1.setOnCheckedChangeListener(instance);
        cn2 = (CheckBox)findViewById(R.id.cn2);
        cn2.setOnCheckedChangeListener(instance);
        cn3 = (CheckBox)findViewById(R.id.cn3);
        cn3.setOnCheckedChangeListener(instance);
        cn4 = (CheckBox)findViewById(R.id.cn4);
        cn4.setOnCheckedChangeListener(instance);
        cn5 = (CheckBox)findViewById(R.id.cn5);
        cn5.setOnCheckedChangeListener(instance);
        cn6 = (CheckBox)findViewById(R.id.cn6);
        cn6.setOnCheckedChangeListener(instance);
        cn7 = (CheckBox)findViewById(R.id.cn7);
        cn7.setOnCheckedChangeListener(instance);
        cn8 = (CheckBox)findViewById(R.id.cn8);
        cn8.setOnCheckedChangeListener(instance);
        mCheckBoxs = new CheckBox[]{cn1, cn2, cn3, cn4, cn5, cn6, cn7, cn8};

        capsensor_data = (TextView)findViewById(R.id.capsensor_data);

        capsensor_send_btn = (Button) findViewById(R.id.capsensor_send_btn);
        capsensor_send_btn.setOnClickListener(instance);
        capsensor_send_text = (EditText) findViewById(R.id.capsensor_send_text);
        String text = (String) SPHelper.getPreference(instance, BES_CAPSENSOR_SEND_CMD_KEY, BES_CAPSENSOR_SEND_CMD_VALUE);
        capsensor_send_text.setText(text);
        et_data_multiple = (EditText) findViewById(R.id.et_data_multiple);

        int curMultiple = (int) SPHelper.getPreference(instance, BES_CAPSENSOR_DATA_MULTIPLE, BES_CAPSENSOR_DATA_MULTIPLE_VALUE);
        et_data_multiple.setText(curMultiple + "");

        tv_title.setOnClickListener(instance);
        logV = (TextView) findViewById(R.id.logV);
        done = (Button) findViewById(R.id.done);
        scr_policy = (ScrollView)findViewById(R.id.scr_policy);
        loginfo = (View) findViewById(R.id.loginfo);
        logV.setMovementMethod(ScrollingMovementMethod.getInstance());
        done.setOnClickListener(instance);
        scr_policy.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                v.getParent().requestDisallowInterceptTouchEvent(true);
                return false;
            }
        });
    }

    private void refreshChannel() {
        String[] dataStrs = mPresenter.getChannelData().split("\n");

        boolean isHasExtraParam = (boolean) SPHelper.getPreference(instance, BES_CAPSENSOR_HAS_EXTRAS_PARAM_KEY, BES_CAPSENSOR_HAS_EXTRAS_PARAM_VALUE);
        String[] newTitleStrs = new String[1 + mChannels.length + (isHasExtraParam ? 6 : 0)];
        newTitleStrs[0] = "time";
        mChannels = new int[dataStrs.length];
        for (int i = 0; i < dataStrs.length; i ++) {
            mChannels[i] = Integer.valueOf(dataStrs[i]);
            newTitleStrs[1 + i] = "CN" + dataStrs[i];
        }
        if (isHasExtraParam) {
            String[] extraTitles = new String[]{"offset0", "offset1", "cur_offset0", "cur_offset1", "ear_state", "touch_event"};
            for (int i = 0; i < extraTitles.length; i ++) {
                newTitleStrs[1 + dataStrs.length + i] = extraTitles[i];
            }
        }

        ExcelUtils.refreshHeader(newTitleStrs);
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (int i = 0; i < mCheckBoxs.length; i ++) {
                    if (i < mChannels.length) {
                        mCheckBoxs[i].setVisibility(View.VISIBLE);
                        mCheckBoxs[i].setText(mChannels[i] + "");
                    } else {
                        mCheckBoxs[i].setVisibility(View.GONE);
                        mCheckBoxs[i].setChecked(false);
                    }
                }
                refreshChartView(new float[0], true);
            }
        });
    }
    private void refreshChartView(float[] data, boolean check) {
        if (data.length == 0) {
            for (int i = 0; i < 8; i ++) {
                l.removeDataSet(lineDataSetArr[i]);
            }
            for (int i = 0; i < mChannels.length; i ++) {
                if (mCheckBoxs[i].isChecked()) {
                    Log.i(TAG, "refreshChartView isChecked: ------" + i);
                    l.addDataSet(lineDataSetArr[mChannels[i]]);
                }
            }
            return;
        }
        addLineChartData(data, check);
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        refreshChartView(new float[0], true);
    }

    public void backtoconnect() {
        setContentView(R.layout.act_connect);
        bindView();
        initView();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_documents, menu);

        return true;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    int timeTap = 0;
    LineData l = new LineData();

    List<Entry> entries0;
    List<Entry> entries1;
    List<Entry> entries2;
    List<Entry> entries3;
    List<Entry> entries4;
    List<Entry> entries5;
    List<Entry> entries6;
    List<Entry> entries7;
    LineDataSet lineDataSet1;
    LineDataSet lineDataSet2;
    LineDataSet lineDataSet3;
    LineDataSet lineDataSet4;
    LineDataSet lineDataSet5;
    LineDataSet lineDataSet6;
    LineDataSet lineDataSet7;
    LineDataSet lineDataSet8;
    private int axisMinimum = 60000;
    private int axisMaxmum = 135067;

    private LineDataSet[] lineDataSetArr;
    private void setLineChart() {
        entries0 = new ArrayList<Entry>();
        entries1 = new ArrayList<Entry>();
        entries2 = new ArrayList<Entry>();
        entries3 = new ArrayList<Entry>();
        entries4 = new ArrayList<Entry>();
        entries5 = new ArrayList<Entry>();
        entries6 = new ArrayList<Entry>();
        entries7 = new ArrayList<Entry>();

        lineDataSet1 = new LineDataSet(entries0,"CN0");
        lineDataSet2 = new LineDataSet(entries1,"CN1");
        lineDataSet3 = new LineDataSet(entries2,"CN2");
        lineDataSet4 = new LineDataSet(entries3,"CN3");
        lineDataSet5 = new LineDataSet(entries4,"CN4");
        lineDataSet6 = new LineDataSet(entries5,"CN5");
        lineDataSet7 = new LineDataSet(entries6,"CN6");
        lineDataSet8 = new LineDataSet(entries7,"CN7");

        lineChart.setBackgroundColor(Color.WHITE);
        lineChart.setTouchEnabled(true);
        lineChart.setDragEnabled(true);
        lineChart.setScaleEnabled(true);
        lineChart.zoom(3f, 1, 0, 0);
        lineChart.getXAxis().setAxisMinimum(1f);
        lineChart.getDescription().setEnabled(false);
        lineChart.getXAxis().setDrawGridLines(false);
        lineChart.getXAxis().setLabelCount(10, true);
        lineChart.getAxisLeft().setLabelCount(15, true);
        lineChart.getAxisLeft().setDrawGridLines(false);
        lineChart.getAxisLeft().setAxisMinimum(axisMinimum);
        lineChart.getAxisLeft().setAxisMaximum(axisMaxmum);

        lineDataSet1.setColor(CAPSENSOR_COLORS[0]);
        lineDataSet1.setDrawCircles(false);
        lineDataSet1.setDrawValues(false);

        lineDataSet2.setColor(CAPSENSOR_COLORS[1]);
        lineDataSet2.setDrawCircles(false);
        lineDataSet2.setDrawValues(false);

        lineDataSet3.setColor(CAPSENSOR_COLORS[2]);
        lineDataSet3.setDrawCircles(false);
        lineDataSet3.setDrawValues(false);

        lineDataSet4.setColor(CAPSENSOR_COLORS[3]);
        lineDataSet4.setDrawCircles(false);
        lineDataSet4.setDrawValues(false);

        lineDataSet5.setColor(CAPSENSOR_COLORS[4]);
        lineDataSet5.setDrawCircles(false);
        lineDataSet5.setDrawValues(false);

        lineDataSet6.setColor(CAPSENSOR_COLORS[5]);
        lineDataSet6.setDrawCircles(false);
        lineDataSet6.setDrawValues(false);

        lineDataSet7.setColor(CAPSENSOR_COLORS[6]);
        lineDataSet7.setDrawCircles(false);
        lineDataSet7.setDrawValues(false);

        lineDataSet8.setColor(CAPSENSOR_COLORS[7]);
        lineDataSet8.setDrawCircles(false);
        lineDataSet8.setDrawValues(false);

        lineDataSetArr = new LineDataSet[]{lineDataSet1, lineDataSet2, lineDataSet3, lineDataSet4, lineDataSet5, lineDataSet6, lineDataSet7, lineDataSet8};
    }

    private void addLineChartData(float[] data, boolean check) {
        if (shouldRefreshChannel) {
            if (mPresenter.getChannelData().length() == 0) {
                return;
            }
            refreshChannel();
            shouldRefreshChannel = false;
        }
        if (check && mChannels.length != data.length) {
            return;
        }
        for (int i = 0; i < mChannels.length; i ++) {
            float f = data[i];
            if (f < axisMinimum) {
                axisMinimum = (int) f - 5000;
                lineChart.getAxisLeft().setAxisMinimum(axisMinimum);
            } else if (f > axisMaxmum) {
                axisMaxmum = (int) f + 5000;
                lineChart.getAxisLeft().setAxisMaximum(axisMaxmum);
            }
            lineDataSetArr[mChannels[i]].addEntry(new Entry(timeTap, f));
        }

        timeTap ++;
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                l.notifyDataChanged();
                lineChart.moveViewToX(l.getXMax());
                lineChart.setData(l);
            }
        });
    }
    
    private void resetLineChart() {
        timeTap = 0;
        axisMinimum = 60000;
        lineChart.clear();
        for (int i = 0; i < lineDataSetArr.length; i ++) {
            lineDataSetArr[i].clear();
        }
        setLineChart();
        l = new LineData();
        l.notifyDataChanged();
    }

    @Override
    public void onCheckedChanged(SwitchButton view, boolean isChecked) {
        SPHelper.putPreference(instance, BES_CAPSENSOR_SHOW_TOUCH_EVENT, isChecked);
        if (isChecked) {
            touch_event_text.setVisibility(View.VISIBLE);
        } else {
            touch_event_text.setVisibility(View.GONE);
        }
    }
}
