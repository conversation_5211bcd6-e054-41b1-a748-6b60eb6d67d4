package com.besall.allbase.view.activity.chipstoollevel4.logdump;

import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.AnimationDrawable;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.method.ScrollingMovementMethod;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.ScaleXSpan;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.connect.BTService;
import com.bes.bessdk.scan.BtHeleper;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.R;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.service.audiodump.AudioDumpCMD;
import com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants;
import com.besall.allbase.bluetooth.service.log_dump.LogDumpConstants;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.common.utils.CircleProgressView;
import com.besall.allbase.common.utils.FileUtils;
import com.besall.allbase.common.utils.LoadingDialog;
import com.besall.allbase.view.base.BaseActivity;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_ERROR;
import static com.besall.allbase.bluetooth.BluetoothConstants.Scan.BES_SCAN_RESULT;
import static com.besall.allbase.bluetooth.service.log_dump.LogDumpConstants.LOGDUMP_PROGRESS;
import static com.besall.allbase.common.Constants.FILE_CODE;

/**
 * <AUTHOR>
 * @time $ $
 */
public class LogDumpActivity extends BaseActivity<ILogDumpActivity, LogDumpPresenter> implements ILogDumpActivity, BesServiceListener, View.OnClickListener
{
    private static LogDumpActivity instance;
    public String cur_title = "LOG DUMP";
    BluetoothDevice mDevice;
    HmDevice mHmDevice;
    BesServiceConfig mServiceConfig;

//    private Button connect_spp;
    private Button log_read_start;
    private TextView current_log_pro;
    private TextView current_log_status;
    private TextView total_log_status;
    private TextView total_log_progress;
    private ProgressBar log_progress;
    private ImageView percent;
    private Button spp_stop;
    private Button pick_device;
    private Button connect_device;
    private TextView device_address;
    private TextView device_name;
    private CircleProgressView mTasksView;
    private int mTotalProgress = 100;
    private int mCurrentProgress = 0;
    public String text;


    @Override
    protected LogDumpPresenter createPresenter() {
        return new LogDumpPresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        //log
        SPHelper.putPreference(instance, BesSdkConstants.BES_SAVE_LOG_NAME, "LogDump");

        mServiceConfig = new BesServiceConfig();
        mServiceConfig.setServiceUUID(BesSdkConstants.BES_SPP_CONNECT);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.act_connect;
    }

    @Override
    protected void bindView() {
        pick_device = (Button)findViewById(R.id.pick_device);
        connect_device =(Button)findViewById(R.id.connect_device);
        device_address = (TextView) findViewById(R.id.device_address);
        device_name = (TextView) findViewById(R.id.device_name);
    }

    @Override
    protected void initView() {
        inittoolbar(cur_title);
        pick_device.setOnClickListener(instance);
        connect_device.setOnClickListener(instance);
        loadanimdrawable();

        logV = (TextView) findViewById(R.id.logV);
        done = (Button) findViewById(R.id.done);
        scr_policy = (ScrollView)findViewById(R.id.scr_policy);
        loginfo = (View) findViewById(R.id.loginfo);

        tv_title.setOnClickListener(instance);
        done.setOnClickListener(instance);

        logV.setMovementMethod(ScrollingMovementMethod.getInstance());
        scr_policy.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                v.getParent().requestDisallowInterceptTouchEvent(true);
                return false;
            }
        });
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data)
    {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == BluetoothConstants.Scan.REQUEST_CODE_SCAN) {
            onPickDevice(resultCode, data);
        }
    }

    private void onPickDevice(int resultCode, Intent data) {
        if (resultCode == RESULT_OK) {
            mHmDevice = (HmDevice) data.getSerializableExtra(BES_SCAN_RESULT);
            mDevice = BtHeleper.getBluetoothAdapter(instance).getRemoteDevice(mHmDevice.getPreferredProtocol() == DeviceProtocol.PROTOCOL_BLE ? mHmDevice.getBleAddress() : mHmDevice.getDeviceMAC());

            Log.i(TAG, "onPickDevice: " + mDevice.getName());
            Log.i(TAG, "onPickDevice: " + mDevice.getAddress());
            device_address.setText(mDevice.getAddress());
            mServiceConfig.setDevice(mHmDevice);
            String name = mDevice.getName();
            SpannableString ss = new SpannableString(name);
            BesSdkConstants.BesConnectState state = BTService.getDeviceConnectState(instance, mServiceConfig);
            if (state == BesSdkConstants.BesConnectState.BES_CONNECT) {
                ss.setSpan(new ForegroundColorSpan(Color.rgb(103, 200, 77)), 0, name.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
            device_name.setText(ss);
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;
            case R.id.menu_file:
                mPresenter.selectfile(instance,FILE_CODE);
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.pick_device:
                mPresenter.pickDecice(instance, BluetoothConstants.Scan.SCAN_SPP);
                connect_device.setVisibility(View.VISIBLE);
                break;
            case R.id.connect_device:
//                initlayout();
                loadinganim();
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            Log.i(TAG, "run: 1111");
                            Thread.sleep(6000);
                            Log.i(TAG, "run: 22");
                            loadingDialog.dismiss();
                            Log.i(TAG, "run: 1111" + loadingDialog);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }
                }).start();
                if (mHmDevice == null) {
                    Log.i(TAG, "onClick: failed");
                    loadingDialog.dismiss();
                    ActivityUtils.showToast(R.string.connect_failed);
                    return;
                }
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        Log.i(TAG, "run: 1");
                        mServiceConfig.setDevice(mHmDevice);
                        Log.i(TAG, "onPickDevice:1111 " + mDevice.getAddress());
                        mServiceConfig.setTotaConnect(true);
                        boolean useTotaV2 = (boolean) SPHelper.getPreference(instance, BesSdkConstants.BES_TOTA_USE_TOTAV2, BesSdkConstants.BES_TOTA_USE_TOTAV2_VALUE);
                        mServiceConfig.setUseTotaV2(useTotaV2);
                        mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
                        mPresenter.connectDevice(mServiceConfig, instance, instance);
                    }
                });
                break;
            case R.id.log_read_start:
                mPresenter.startReadLog();
                total_log_status.setVisibility(View.VISIBLE);
                current_log_status.setVisibility(View.VISIBLE);
                percent.setVisibility(View.VISIBLE);
                log_read_start.setVisibility(View.INVISIBLE);
//                new Thread(new Runnable() {
//                    @Override
//                    public void run() {
//                        while (mCurrentProgress < mTotalProgress) {
//                            mCurrentProgress += 1;
//                            mTasksView.setProgress(mCurrentProgress);
//                            log_progress.setProgress(mCurrentProgress);
//                            text = String.valueOf(mCurrentProgress);
//                            runOnUiThread(new Runnable() {
//                                @Override
//                                public void run() {
//                                    current_log_pro.setText(text);
//                                    total_log_progress.setText(text+"%");
//                                    if (mCurrentProgress == 100){
//                                        total_log_status.setText(R.string.downloadcomplete);
//                                        current_log_status.setText(R.string.downloadcomplete);
//                                    }
//                                }
//                            });
//                            try {
//                                Thread.sleep(100);
//                            } catch (Exception e) {
//                                e.printStackTrace();
//                            }
//                        }
//                    }
//                }).start();

                break;
            case R.id.spp_stop:
                mPresenter.stopSpp();
                mCurrentProgress = 0;
                break;
            case R.id.done:
                Log.i(TAG, "onClick: 112313132");
                loginfo.setVisibility(View.GONE);
                break;
            case R.id.tv_title:
                loginfo.setVisibility(View.VISIBLE);

                break;
            default:
                break;

        }

    }

    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (state == true) {
                    initlayout();
                    loadingDialog.dismiss();
                }
            }
        });
    }

    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {

    }

    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (msg == BES_CONNECT_ERROR) {
                    Log.i(TAG, "run: failed");
                    loadingDialog.dismiss();
                    ActivityUtils.showToast(R.string.connect_failed);
                }

                if (msg == BesSdkConstants.TOTA_LOG_INFO) {
                    addlog(msgStr);
                }
            }
        });

        Log.i(TAG, "onStateChangedMessage: ============" + msg + "------" + msgStr);
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (msg == LOGDUMP_PROGRESS) {
                    mTasksView.setProgress((int)Float.valueOf(msgStr).floatValue());
                    log_progress.setProgress((int)Float.valueOf(msgStr).floatValue());
                    total_log_progress.setText(msgStr + "%");
                    current_log_pro.setText(msgStr);
                    if ((int)Float.valueOf(msgStr).floatValue() == 100){
                        total_log_status.setText(R.string.downloadcomplete);
                        current_log_status.setText(R.string.downloadcomplete);
                    }
                }
            }
        });
    }

    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {

    }

    public void initlayout() {
        setContentView(R.layout.activity_logdump);
        inittoolbar(cur_title);
        instance = this;
        log_read_start = (Button) findViewById(R.id.log_read_start);
        log_read_start.setOnClickListener(instance);
        log_read_start.setVisibility(View.VISIBLE);
        current_log_pro =(TextView) findViewById(R.id.current_log_pro);
        total_log_status = (TextView) findViewById(R.id.total_log_status);
        current_log_status =(TextView) findViewById(R.id.current_log_status);
        total_log_progress = (TextView) findViewById(R.id.total_log_progress);
        mTasksView = (CircleProgressView) findViewById(R.id.tasks_view);
        percent = (ImageView) findViewById(R.id.percent);
        spp_stop = (Button)findViewById(R.id.spp_stop);
        spp_stop.setOnClickListener(instance);

        log_progress = (ProgressBar)findViewById(R.id.log_progress);
        log_progress.setDrawingCacheBackgroundColor(getColor(R.color.fff8921f));


        mToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                backtoconnect();
            }
        });

        tv_title.setOnClickListener(instance);
        logV = (TextView) findViewById(R.id.logV);
        done = (Button) findViewById(R.id.done);
        scr_policy = (ScrollView)findViewById(R.id.scr_policy);
        loginfo = (View) findViewById(R.id.loginfo);
        logV.setMovementMethod(ScrollingMovementMethod.getInstance());
        done.setOnClickListener(instance);
        scr_policy.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                v.getParent().requestDisallowInterceptTouchEvent(true);
                return false;
            }
        });
    }

    public void backtoconnect() {
        setContentView(R.layout.act_connect);
        bindView();
        initView();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_documents, menu);

        return true;
    }

    public void textsize(){
        Log.i(TAG, "textsize: " + text);

        Spannable textSpan = new SpannableStringBuilder(text);
        textSpan.setSpan(new AbsoluteSizeSpan(50,true), 0, text.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        (current_log_pro).setText(textSpan);

    }
//
//    public void loadinganim(){
//        loadanimationDrawable = new AnimationDrawable();
//        for (int i = 0; i <= 88; i++) {
//            int id = getResources().getIdentifier("loading" + i, "drawable", getPackageName());
//            Log.i(TAG, "onCreate:loading " + id);
//            Drawable drawable = getResources().getDrawable(id);
//            loadanimationDrawable.addFrame(drawable, 0);
//        }
//        if (loadingDialog == null) {
//            //创建
//            loadingDialog = new LoadingDialog(this, "正在加载", loadanimationDrawable);
//        }
//        //调用
//        loadingDialog.show();
////            loadanimationDrawable.stop();
//        loadanimationDrawable.start();
//    }

//    public class ProgressRunable implements Runnable {
//        @Override
//        public void run() {
//            while (mCurrentProgress < mTotalProgress) {
//                mCurrentProgress += 1;
//                mTasksView.setProgress(mCurrentProgress);
//                log_progress.setProgress(mCurrentProgress);
//                text = String.valueOf(mCurrentProgress)+"";
//                textsize();
//                try {
//                    Thread.sleep(100);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
//        }
//    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mPresenter.stopSpp();
    }
}
