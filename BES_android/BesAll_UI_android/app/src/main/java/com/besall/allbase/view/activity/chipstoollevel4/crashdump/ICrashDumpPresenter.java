package com.besall.allbase.view.activity.chipstoollevel4.crashdump;

import android.content.Context;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.besall.allbase.view.activity.chipstoollevel4.logdump.LogDumpActivity;

public interface ICrashDumpPresenter {

    void pickDecice(CrashDumpActivity context, int scan);

    void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context);

    void dumpstart();

    void stopSpp();

    void selectfile(CrashDumpActivity context, int file);
}
