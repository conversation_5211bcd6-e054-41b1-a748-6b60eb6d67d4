package com.besall.allbase.bluetooth.service.health.sports;

import android.content.Context;
import android.util.Log;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.service.base.BesBaseService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.sdk.message.BaseMessage;

public class SportsService extends BesBaseService {
    public SportsService(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        super(serviceConfig, listener, context);
        startConnect(serviceConfig);
    }

    @Override
    public void onDataReceived(BaseMessage deviceMessage) {
        super.onDataReceived(deviceMessage);
        Log.i(TAG, "onDataReceived: ---sports----");
        String retStep = SportsCMD.footStep((byte[]) deviceMessage.getMsgContent());
    }

    public void OPSportsInfo(int type){
        if (totauccess) {
            sendData(SportsCMD.SportsInfo(mContext, type), 0);
        } else {
            callBackErrorMessage(BesSdkConstants.BES_TOTA_ERROR);
        }
    }

}
