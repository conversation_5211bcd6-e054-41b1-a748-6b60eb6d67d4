package com.besall.allbase.view.activity.chipstoollevel4.capsensor;

import static com.besall.allbase.common.Constants.CHOOSE_FILE_FOLDER_INTENT_KEY;
import static com.besall.allbase.view.activity.chipstoollevel4.capsensor.CapSensorActivity.DIRNAME;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;
import com.besall.allbase.bluetooth.service.capsensor.CapSensorService;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.activity.tools.FileActivity.FilelistActivity;
import com.besall.allbase.view.base.BasePresenter;

public class CapSensorPresenter extends BasePresenter<ICapSensorActivity> implements ICapSensorPresenter {

    private CapSensorService capSensorService;

    @Override
    public void pickDecice(CapSensorActivity context, int scan) {
        Intent intent = new Intent();
        intent.putExtra(BluetoothConstants.Scan.BES_SCAN, scan);
        ActivityUtils.gotoActForResult(intent, BluetoothConstants.Scan.REQUEST_CODE_SCAN, context, ScanActivity.class);
    }

    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        capSensorService = new CapSensorService(serviceConfig, listener, context);

    }

    @Override
    public void getSensorData() {
        if (capSensorService != null)
        capSensorService.getSensorData();
    }

    @Override
    public String getChannelData() {
        if (capSensorService != null)
            return capSensorService.getChannelData();
        return "";
    }

    @Override
    public String getExtraParamData() {
        if (capSensorService != null)
            return capSensorService.getExtraParamData();
        return "";
    }

    @Override
    public void stopSensorData() {
        if (capSensorService != null)
        capSensorService.stopSensorData();
    }

    @Override
    public void test() {
        if (capSensorService != null)
            capSensorService.test();
    }

    @Override
    public void selectfile(CapSensorActivity context, int file) {
        Intent intent = new Intent();
        intent.putExtra(CHOOSE_FILE_FOLDER_INTENT_KEY,context.getExternalFilesDir("").getAbsolutePath() + "/" + DIRNAME);
        ActivityUtils.gotoActForResult(intent, file, context, FilelistActivity.class);
    }

    @Override
    public void sendData(byte[] data) {
        if (capSensorService != null)
            capSensorService.sendData(data);
    }

}
