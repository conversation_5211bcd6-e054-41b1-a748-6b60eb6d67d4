package com.besall.allbase.view.activity;

import static com.bes.bessdk.BesSdkConstants.BES_BLE_OTA_AUTO_TEST_BIN_NAME_MARK;
import static com.besall.allbase.bluetooth.service.commandset.CommandSetConstants.COMMAND_SET_RECEIVE_DATA;
import static com.besall.allbase.bluetooth.service.commandset.CommandSetConstants.sample_preset_45yo;
import static com.besall.allbase.bluetooth.service.commandset.CommandSetConstants.sample_preset_60yo;

import android.Manifest;
import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothLeAudio;
import android.bluetooth.BluetoothManager;
import android.bluetooth.BluetoothProfile;
import android.bluetooth.le.BluetoothLeScanner;
import android.bluetooth.le.ScanResult;
import android.bluetooth.le.ScanSettings;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.media.AudioManager;
import android.net.Uri;
import android.os.Build;
import android.telephony.TelephonyManager;
import android.text.format.DateFormat;
import android.util.Log;
import android.view.Menu;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.core.app.ActivityCompat;

import com.amazon.alexa.accessory.protocol.Accessories;
import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.scan.BtHeleper;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.SPHelper;
import com.bes.d3dsdk.ImageMagic;
import com.bes.sdk.message.BaseMessage;
import com.besall.allbase.R;
import com.besall.allbase.bluetooth.service.capsensor.CapSensorCMD;
import com.besall.allbase.bluetooth.service.commandset.CommandSetCMD;
import com.besall.allbase.bluetooth.service.commandset.CommandSetService;
import com.besall.allbase.bluetooth.service.customerdial.CustomerDialCMD;
import com.besall.allbase.common.Constants;
import com.besall.allbase.common.manager.PermissionManager;
import com.besall.allbase.common.utils.ExcelUtils;
import com.besall.allbase.common.utils.FileUtils;
import com.besall.allbase.view.activity.chipstoollevel4.AuracastAssistant.AuracastAssistantActivity;
import com.besall.allbase.view.activity.chipstoollevel4.SmartVoice.SmartVoiceActivity;
import com.besall.allbase.view.activity.chipstoollevel4.WatchAvs.WatchAvsActivity;
import com.besall.allbase.view.activity.chipstoollevel4.audiodump.AudioDumpActivity;
import com.besall.allbase.view.activity.chipstoollevel4.capsensor.CapSensorActivity;
import com.besall.allbase.common.utils.ActivityUtils;
//import com.besall.allbase.view.activity.chipstoollevel4.commandset.BesHTConnect;
import com.besall.allbase.view.activity.chipstoollevel4.commandset.CommandSetActivity;
import com.besall.allbase.view.activity.chipstoollevel4.customerdial.CustomerDialActivity;
import com.besall.allbase.view.activity.chipstoollevel4.health.HealthActivity;
import com.besall.allbase.view.activity.level2.FunctionChosenActivity;
import com.besall.allbase.view.activity.tools.confirmdialog.ConfirmDialog;
import com.besall.allbase.view.activity.tools.confirmdialog.ConfirmDialoglistener;
import com.besall.allbase.view.base.BaseActivity;
import com.google.protobuf.InvalidProtocolBufferException;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipFile;


public class HomeActivity extends BaseActivity<IHomeActivity, HomePresenter> implements IHomeActivity, View.OnClickListener {

    private final String TAG = getClass().getSimpleName();
    private final String AGREE_KEY = "Bes_Agree_Key";
    private SharedPreferences preferences;
    private SharedPreferences.Editor editor;

    private static HomeActivity instance;
    private Button testBtn;
    private View agree_view;
    private TextView agreeTV;
    private Button privacy_policy;

    private Button privacy_Link;
    private Button agree;
    private Button disagree;

    private TextView version_text;
    static List<Integer> flashAddressList_receive = new ArrayList<>();

    @Override
    protected HomePresenter createPresenter() {
        return new HomePresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        preferences = getSharedPreferences("AGREE_KEY", 0);
        editor = preferences.edit();
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_main;
    }

    @Override
    protected void bindView() {
        testBtn = (Button) findViewById(R.id.entry_btn);
        agree_view = (View) findViewById(R.id.agree_view);
        privacy_policy = (Button) findViewById(R.id.privacy_policy);
        agree = (Button) findViewById(R.id.agree);
        disagree = (Button) findViewById(R.id.disagree);
        agreeTV = (TextView) findViewById(R.id.agreeTV);
        version_text = (TextView) findViewById(R.id.version_text);
        privacy_Link = (Button) findViewById(R.id.privacy_link);
    }

    @SuppressLint("ResourceAsColor")
    @Override
    protected void initView() {
//        Intent intent = new Intent();
//        ActivityUtils.gotoAct(intent, instance, TestActivity.class);
        testBtn.setOnClickListener(instance);
        privacy_policy.setOnClickListener(instance);
        agree.setOnClickListener(instance);
        disagree.setOnClickListener(instance);
        privacy_Link.setOnClickListener(instance);
        agreeTV.setText(getString(R.string.agreement));

        version_text.setText(getString(R.string.appVersion) + ": " + getVersionName(instance));

        boolean show = preferences.getBoolean(AGREE_KEY, true);
        if (show) {
            agree_view.setVisibility(View.VISIBLE);
        } else {
            agree_view.setVisibility(View.GONE);
        }

        //init blueToothConnect
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            SPHelper.putPreference(instance, BesSdkConstants.BES_USE_NORMAL_CONNECT, false);
        } else {
            SPHelper.putPreference(instance, BesSdkConstants.BES_USE_NORMAL_CONNECT, true);
        }

        SPHelper.putPreference(instance, BesSdkConstants.BES_BLE_OTA_AUTO_TEST, false);

        //init bin path
        new FileUtils(instance).initBinFile();

//        try {
//            ZipFile zipFile = new ZipFile(zipFilePath);
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//        String path1 = "/storage/emulated/0/Android/data/com.bes.besall/files/ss/ss.png";
//        String path2 = "/storage/emulated/0/Android/data/com.bes.besall/files/ss/ss1.png";
//        String path3 = "/storage/emulated/0/Android/data/com.bes.besall/files/ss/ss2.png";
//
//        String outPath1 = "/storage/emulated/0/Android/data/com.bes.besall/files/ss/ssout1.png";
//        String outPath2 = "/storage/emulated/0/Android/data/com.bes.besall/files/ss/ssout2.png";
//        String outPath3 = "/storage/emulated/0/Android/data/com.bes.besall/files/ss/ssout3.png";
//
//        try {
//            new ImageMagic().MagickImageCommande("path1", "outPath1");
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//
//        try {
//            Thread.sleep(1000);
//            new ImageMagic().MagickImageCommande(path2, outPath2);
//        } catch (InterruptedException e) {
//            throw new RuntimeException(e);
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//        try {
//            Thread.sleep(1000);
//            new ImageMagic().MagickImageCommande(path3, outPath3);
//        } catch (InterruptedException e) {
//            throw new RuntimeException(e);
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }

//        String str = "08,0b,5a,14,0a,04,08,01,10,01,12,04,08,03,12,00,1a,02,08,01,20,01,28,01,";
//        byte[] data = ArrayUtil.toBytes(str);
//        try {
//            Accessories.ControlEnvelope controlEnvelope = Accessories.ControlEnvelope.parseFrom(data);
//            Log.i(TAG, "initView: ------" + controlEnvelope.toString());
//        } catch (InvalidProtocolBufferException e) {
//            throw new RuntimeException(e);
//        }

//        for (int i = 0; i < 300; i++) {
//            byte a = (byte) i;
//            byte[] b = new byte[]{a};
//            Log.i(TAG, "initView: ------" + ArrayUtil.toHex(b));
//        }
//        byte a = (byte) 0xfd;
//
//
//        byte curIndex = (byte) (a + 2);
//        byte[] b = new byte[]{curIndex};
//        Log.i(TAG, "initView: ------" + ArrayUtil.toHex(b));


//        ExcelUtils.createExcel("DIRNAME", "saveName" + DateFormat.format("yyyy-MM-dd HH:mm:ss:sss", System.currentTimeMillis()), instance, new String[]{"CN1", "CN2", "CN3"}, "sheetName", true);
//        ExcelUtils.refreshHeader(new String[]{"Time", "CN1", "CN2", "CN3"});


//        audioManager = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
//        telephonyManager = (TelephonyManager) getSystemService(Context.TELEPHONY_SERVICE);



    }

    private AudioManager audioManager;
    private TelephonyManager telephonyManager;

    public boolean isMusicPlaying() {
        return audioManager.isMusicActive();
    }

    public boolean isInCall() {
        // 检测通话状态
        return telephonyManager.getCallState() != TelephonyManager.CALL_STATE_IDLE;
    }

    public static String getVersionName(Context mContext) {
        String versionName = "";
        try {
            //获取软件版本号，对应AndroidManifest.xml下android:versionCode
            versionName = mContext.getPackageManager().
                    getPackageInfo(mContext.getPackageName(), 0).versionName;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return versionName;
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.entry_btn:
                ActivityUtils.gotoAct(new Intent(), instance, FunctionChosenActivity.class);
                break;
            case R.id.privacy_policy:
                agree_view.setVisibility(View.VISIBLE);
                break;
            case R.id.agree:
                agree_view.setVisibility(View.GONE);
                editor.putBoolean(AGREE_KEY, false);
                editor.commit();

                showConfirmDialog(getString(R.string.permissions_guide));
                break;
            case R.id.disagree:
                editor.putBoolean(AGREE_KEY, true);
                editor.commit();
                finish();
                break;
            case R.id.privacy_link:
                Intent intent = new Intent(Intent.ACTION_VIEW);
                Uri uri = Uri.parse("https://www.bestechnic.com/home/<USER>/newsdetail/id/8Home/Index/index/lan_type/2");
                intent.setData(uri);
                if (intent.resolveActivity(getPackageManager()) != null){
                    startActivity(intent);
                    Log.i(TAG, "onClick: ---" + intent.getData());
                } else {
                    ActivityUtils.showToast("no web view");
                }
                break;
            default:
                break;
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.main, menu);

        return true;
    }


    private void showConfirmDialog(String msg) {
        ConfirmDialog confirmDialog = new ConfirmDialog(instance, msg, new ConfirmDialoglistener() {
            @Override
            public void confirmYes() {
                PermissionManager.getInstance().requestPermissions(HomeActivity.this, null, PermissionManager.Permission.Storage.WRITE_EXTERNAL_STORAGE);
            }

            @Override
            public void confirmNo() {

            }
        });

        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                confirmDialog.show();
            }
        });
    }

}
