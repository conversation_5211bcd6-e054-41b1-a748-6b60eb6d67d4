package com.besall.allbase.common.utils;

import static com.besall.allbase.view.activity.chipstoollevel4.capsensor.CapSensorActivity.DIRNAME;

import android.content.Context;
import android.text.format.DateFormat;
import android.util.Log;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import io.reactivex.internal.util.BlockingIgnoringReceiver;
import jxl.Cell;
import jxl.NumberCell;
import jxl.Sheet;
import jxl.Workbook;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.Colour;
import jxl.read.biff.BiffException;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;

public class ExcelUtils {

    static String TAG = "ExcelUtils";
    static WritableWorkbook writeBook;
    static int curRow = 0;
    static String curRandomMark = "";
    static String curFilePath = "";

    static int maxExcelLength = 65000;
    static String[] headerData = new String[]{};

    static Context mContext;
    static String mSheetName = "";

    public ExcelUtils() {

    }

    public static List<float[]> getCapsensorData(String path) {
        File file = new File(path);
        if (!file.exists()) {
            return new ArrayList<>();
        }
        try {
            Workbook workbook = Workbook.getWorkbook(file);
            Sheet sheet = workbook.getSheet(0);
            List<float[]> data = new ArrayList<>();
            for (int i = 0; i < sheet.getRows(); i ++) {
                Cell[] labels = sheet.getRow(i);
                float[] rowData = new float[labels.length - 1];
                for (int j = 1; j < labels.length; j ++) {
                    if (i == 0) {
                        Cell cell = labels[j];
                        if (!cell.getContents().contains("CN")) {
                            continue;
                        }
                        rowData[j - 1] = Integer.valueOf(cell.getContents().substring(2, 3));
                    } else {
                        NumberCell numberCell = (NumberCell) labels[j];
                        rowData[j - 1] = (float) numberCell.getValue();
                    }
                }
                data.add(rowData);
            }
            return data;
        } catch (IOException | BiffException e) {
            e.printStackTrace();
        }
        return new ArrayList<>();
    }

    public static void addCapsensorData(String[] data) {
        if (writeBook == null) return;
        curRow ++;
        try {
            WritableSheet writeSheet = writeBook.getSheet(0);
            for (int i = 0; i < data.length; i ++) {
                if (i > 0) {
                    jxl.write.Number number = new jxl.write.Number(i, curRow, Integer.valueOf(data[i]), getHeader());
                    writeSheet.addCell(number);
                } else {
                    Label label = new Label(i, curRow, data[i], getHeader());
                    writeSheet.addCell(label);
                }
            }
        } catch (RowsExceededException e) {
            e.printStackTrace();
        } catch (WriteException e) {
            e.printStackTrace();
        }
        if (curRow + 1 >= maxExcelLength) {
            curRow = 0;
            saveData();

            createExcel("", "", mContext, new String[]{"aaa", "aaa"}, mSheetName, false);
            refreshHeader(null);
        }
    }

    public static void refreshHeader(String[] data) {
        if (writeBook == null) return;
        if (data == null) {
            data = headerData;
        } else {
            headerData = data;
        }
        try {
            WritableSheet writeSheet = writeBook.getSheet(0);
            for (int i = 0; i < data.length; i ++) {
                Label label = new Label(i, 0, data[i], getHeader());
                writeSheet.addCell(label);
            }
        } catch (RowsExceededException e) {
            e.printStackTrace();
        } catch (WriteException e) {
            e.printStackTrace();
        }
    }

    public static void saveData() {
        if (writeBook == null) return;
        try {
            writeBook.write();
            writeBook.close();
        } catch (IOException | WriteException e) {
            e.printStackTrace();
        }
        writeBook = null;

        File oldFile = new File(curFilePath + ".xls");
        File newFile = new File(curFilePath + "--" + DateFormat.format("HH:mm:ss:sss", System.currentTimeMillis()) + curRandomMark + ".xls");
        boolean rename = oldFile.renameTo(newFile);
        Log.i(TAG, "rename: -------" + rename);
    }

    public static boolean createExcel(String dirName, String fileName, Context context, String[] titles, String sheetName, boolean reset) {
        mContext = context;
        FileUtils fileUtils = new FileUtils(context);
        if (reset) {
            curRow = 0;
            curRandomMark = "-" + getRandomStr(6);
            curFilePath = fileUtils.getFilesDir(fileName, dirName);
            mSheetName = sheetName;
        }
        String path = curFilePath + ".xls";
        File file = new File(path);
        if (file.exists()) {
            file.delete();
        }
        try {
            writeBook = Workbook.createWorkbook(file);
            WritableSheet writeSheet = writeBook.createSheet(sheetName, 0);
            for (int i = 0; i < titles.length; i ++) {
                Label label = new Label(i, 0, titles[i], getHeader());
                writeSheet.addCell(label);
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            return false;
        } catch (IOException e) {
            e.printStackTrace();
        } catch (RowsExceededException e) {
            e.printStackTrace();
        } catch (WriteException e) {
            e.printStackTrace();
        }
        return true;
    }

    public static WritableCellFormat getHeader() {
        WritableFont font = new WritableFont(WritableFont.TIMES, 10,
                WritableFont.NO_BOLD);
        try {
            font.setColour(Colour.BLACK);
        } catch (WriteException e1) {
            e1.printStackTrace();
        }
        WritableCellFormat format = new WritableCellFormat(font);
        try {
            format.setAlignment(jxl.format.Alignment.CENTRE);
            format.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE);
            format.setBorder(Border.ALL, BorderLineStyle.THIN,
                    Colour.BLACK);
            format.setBackground(Colour.WHITE);
        } catch (WriteException e) {
            e.printStackTrace();
        }
        return format;
    }

    private static String getRandomStr(int length) {
        String modeStr = "0123456789abcdefghijklmnopqrstuvwxyz";
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int random = new Random().nextInt(modeStr.length());
            sb.append(modeStr.substring(random, random + 1));
        }

        return sb.toString();
    }

}
