package com.besall.allbase.view.activity.chipstoollevel4.EQ;

import java.io.Serializable;

public class EQSeekbarBeans implements Serializable {
    public int hasRadio = 0;
    public boolean radioCheck = false;
    public String seekbar_title = "";
    public String seekbar_min = "";
    public String seekbar_max = "";
    public String seekbar_unit = "";
    public String seekbar_real = "";
    public int seekbar_progress = 0;
    public String[] seekbar_interval = new String[]{};
    public boolean canEdit = true;

    public void setHasRadio(int hasRadio) {
        this.hasRadio = hasRadio;
    }

    public int isHasRadio() {
        return hasRadio;
    }

    public void setRadioCheck(boolean radioCheck) {
        this.radioCheck = radioCheck;
    }

    public boolean isRadioCheck() {
        return radioCheck;
    }

    public void setSeekbar_title(String seekbar_title) {
        this.seekbar_title = seekbar_title;
    }

    public String getSeekbar_title() {
        return seekbar_title;
    }

    public void setSeekbar_min(String seekbar_min) {
        this.seekbar_min = seekbar_min;
    }

    public String getSeekbar_min() {
        return seekbar_min;
    }

    public void setSeekbar_max(String seekbar_max) {
        this.seekbar_max = seekbar_max;
    }

    public String getSeekbar_max() {
        return seekbar_max;
    }

    public void setSeekbar_unit(String seekbar_unit) {
        this.seekbar_unit = seekbar_unit;
    }

    public String getSeekbar_unit() {
        return seekbar_unit;
    }

    public void setSeekbar_real(String seekbar_real) {
        this.seekbar_real = seekbar_real;
    }

    public String getSeekbar_real() {
        return seekbar_real;
    }

    public void setSeekbar_progress(int seekbar_progress) {
        this.seekbar_progress = seekbar_progress;
    }

    public int getSeekbar_progress() {
        return seekbar_progress;
    }

    public void setSeekbar_interval(String[] seekbar_interval) {
        this.seekbar_interval = seekbar_interval;
    }

    public String[] getSeekbar_interval() {
        return seekbar_interval;
    }

    public void setCanEdit(boolean canEdit) {
        this.canEdit = canEdit;
    }

    public boolean isCanEdit() {
        return canEdit;
    }
}
