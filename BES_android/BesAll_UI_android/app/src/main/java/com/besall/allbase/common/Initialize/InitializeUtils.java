package com.besall.allbase.common.Initialize;



import java.util.ArrayList;

/**
 * Created by fanyu on 2019-04-18
 */
public class InitializeUtils {

    private static ArrayList<Runnable> list = new ArrayList<>();

    public static void init() {
        createRunnable();// 初始化任务
        // 添加任务
        for (Runnable runnable : list) {
            ThreadManager.executeProxy(runnable);
        }
        list.clear();
    }

    private static void createRunnable() {

    }
}
