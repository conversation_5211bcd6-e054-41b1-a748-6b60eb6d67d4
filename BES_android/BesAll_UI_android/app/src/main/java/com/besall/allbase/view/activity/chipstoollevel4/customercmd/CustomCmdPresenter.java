package com.besall.allbase.view.activity.chipstoollevel4.customercmd;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;
import com.besall.allbase.bluetooth.service.customcmd.CustomCmdConstants;
import com.besall.allbase.bluetooth.service.customcmd.CustomCmdService;
import com.besall.allbase.bluetooth.service.rssi.RssiService;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.common.utils.SharedPreferencesUtils;
import com.besall.allbase.view.activity.tools.FileActivity.CustomCmdFilelistActivity;
import com.besall.allbase.view.activity.tools.FileActivity.FilelistActivity;
import com.besall.allbase.view.base.BasePresenter;

import java.io.File;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

import static com.besall.allbase.bluetooth.service.customcmd.CustomCmdConstants.FileCode;
import static com.besall.allbase.common.Constants.CHIPS_FILE_PATH_RESULT;
import static com.besall.allbase.common.Constants.FILE_CODE;

/**
 * <AUTHOR>
 * @time $ $
 */
class CustomCmdPresenter extends BasePresenter<ICustomCmdActivity> implements ICustomCmdPresenter {

    private CustomCmdService CustomCmdService;

    static String TAG = "CustomCmdPresenter";
    @Override
    public void pickDecice(CustomCmdActivity context, int scan) {
        Intent intent = new Intent();
        intent.putExtra(BluetoothConstants.Scan.BES_SCAN, scan);
        ActivityUtils.gotoActForResult(intent, BluetoothConstants.Scan.REQUEST_CODE_SCAN, context, ScanActivity.class);
    }

    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        CustomCmdService = new CustomCmdService(serviceConfig, listener, context);
    }

    @Override
    public void sendCustomCmd(String cmd, int i) {
        if (CustomCmdService != null) {
            CustomCmdService.sendCustomCmd(cmd, i);
        }
    }

    @Override
    public void sendCustomCommand(byte[] data) {
        if (CustomCmdService != null) {
            CustomCmdService.sendCustomCommand(data);
        }
    }

    @Override
    public void stopSpp() {
        if (CustomCmdService != null) {
            CustomCmdService.disconnected();
        }
    }

    @Override
    public void importfile(CustomCmdActivity context, int json) {
        Intent intent = new Intent();
        ActivityUtils.gotoActForResult(intent, FILE_CODE, context, FilelistActivity.class);
    }

    @Override
    public void selectfile(CustomCmdActivity context, int file) {
        Intent intent = new Intent();
        ActivityUtils.gotoActForResult(intent, FILE_CODE, context, CustomCmdFilelistActivity.class);
    }


}
