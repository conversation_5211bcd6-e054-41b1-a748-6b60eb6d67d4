package com.besall.allbase.view.activity.chipstoollevel4.AvsLwa.resize;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_SUCCESS;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_INTENT_SERVICECONFIG_KEY;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_IS_LWA_LINK_KEY;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LANGUAGE_DIDSELECT_KEY;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LANGUAGE_SELECT;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_GET_WHOLE_SETTING_DATA;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_LANGUAGE_SELECT;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_SET_ERROR;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_SET_SUCCESS;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.besall.allbase.R;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.activity.chipstoollevel4.AvsLwa.AvsLwaLanguageSelectActivity;
import com.besall.allbase.view.base.BaseActivity;
import com.suke.widget.SwitchButton;

import java.io.Serializable;
import java.util.ArrayList;

public class AvsLwaResizeActivity extends BaseActivity<IAvsLwaResizeActivity, AvsLwaResizePresenter> implements IAvsLwaResizeActivity, View.OnClickListener, BesServiceListener, SeekBar.OnSeekBarChangeListener, SwitchButton.OnCheckedChangeListener {

    public String cur_title_resize = "Device";
    public String cur_title_setting = "Setting";

    private static AvsLwaResizeActivity instance;
    BesServiceConfig mServiceConfig;
    private ArrayList<String> languages;

    private SeekBar seekbar_lighting;
    private SeekBar seekbar_speaker_volume;
    private SeekBar seekbar_alert_volume;
    private SeekBar seekbar_ux_led;
    private SwitchButton switchButton_start_lisening;
    private SwitchButton switchButton_end_lisening;
    private SwitchButton switchButton_do_not_disturb;
    private Button language_select_btn;
    private int curSeekBarProgress = 0;

    private TextView setting_device_sn;
    private TextView setting_software_version;
    private TextView setting_mac_address;
    private Button deregister_the_device;

    private String seekbar_lighting_value = "0",
            seekbar_speaker_volume_value = "0",
            seekbar_alert_volume_value = "0",
            seekbar_ux_led_value = "0",
            switchButton_start_lisening_value = "0",
            switchButton_end_lisening_value = "0",
            switchButton_do_not_disturb_value = "0",
            device_sn_value = "0",
            software_version_value = "0",
            device_mac_address = "0";

    @Override
    protected AvsLwaResizePresenter createPresenter() {
        return new AvsLwaResizePresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        languages = new ArrayList<>();
        languages.add("English");

        SPHelper.putPreference(instance, AVS_IS_LWA_LINK_KEY, true);
        mServiceConfig = (BesServiceConfig)getIntent().getSerializableExtra(AVS_INTENT_SERVICECONFIG_KEY);
        if (mServiceConfig != null) {
            mPresenter.connectDevice(mServiceConfig, instance, instance);
        }
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_avslwa_resize;
    }

    @Override
    protected void bindView() {
       bindBaseUI();
    }

    @SuppressLint("ResourceAsColor")
    @Override
    protected void initView() {
       initBaseUI();
        loadanimdrawable();
        loadinganim(getString(R.string.setting_tips));
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    protected void onResume() {
        super.onResume();
        SPHelper.putPreference(instance, AVS_IS_LWA_LINK_KEY, true);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                if (tv_title.getText().equals(cur_title_resize)) {
                    finish();
                    return false;
                }
                initializeBaseUI();
                break;
            case R.id.menu_setting:
                initializeSettingUI();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    public boolean onCreateOptionsMenu(Menu menu) {
        if (tv_title.getText().equals(cur_title_resize)) {
            getMenuInflater().inflate(R.menu.menu_setting, menu);
        } else {
            getMenuInflater().inflate(R.menu.main, menu);
        }
        return true;
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.done:
                loginfo.setVisibility(View.GONE);
                break;
            case R.id.tv_title:
                loginfo.setVisibility(View.VISIBLE);
                break;

            case R.id.language_select_btn:
                Intent intent = new Intent();
                intent.putStringArrayListExtra(AVS_LANGUAGE_SELECT, languages);
                ActivityUtils.gotoActForResult(intent, AVS_LWA_LANGUAGE_SELECT, instance, AvsLwaLanguageSelectActivity.class);
                break;
            case R.id.deregister_the_device:
                mPresenter.sendCurSettingData(11, 0);

                break;
            default:
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == AVS_LWA_LANGUAGE_SELECT) {
            if (resultCode == RESULT_OK) {
                Log.i(TAG, "onActivityResult: ------" + data.getIntExtra(AVS_LANGUAGE_DIDSELECT_KEY, 0));

            }
        }

    }

    private void initializeBaseUI() {
        setContentView(R.layout.activity_avslwa_resize);
        bindBaseUI();
        initBaseUI();
    }

    private void bindBaseUI() {
        done = (Button) findViewById(R.id.done);
        loginfo = (View) findViewById(R.id.loginfo);
        logV = (TextView) findViewById(R.id.logV);

        seekbar_lighting = (SeekBar) findViewById(R.id.seekbar_lighting);
        seekbar_speaker_volume = (SeekBar) findViewById(R.id.seekbar_speaker_volume);
        seekbar_alert_volume = (SeekBar) findViewById(R.id.seekbar_alert_volume);
        seekbar_ux_led = (SeekBar) findViewById(R.id.seekbar_ux_led);

        switchButton_start_lisening = (SwitchButton) findViewById(R.id.switchButton_start_lisening);
        switchButton_end_lisening = (SwitchButton) findViewById(R.id.switchButton_end_lisening);
        switchButton_do_not_disturb = (SwitchButton) findViewById(R.id.switchButton_do_not_disturb);

        language_select_btn = (Button) findViewById(R.id.language_select_btn);

    }

    private void initBaseUI() {
        inittoolbar(cur_title_resize);
        tv_title.setOnClickListener(instance);
        done.setOnClickListener(instance);
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);

        seekbar_lighting.setOnSeekBarChangeListener(instance);
        seekbar_lighting.setProgress(Integer.valueOf(seekbar_lighting_value));
        seekbar_speaker_volume.setOnSeekBarChangeListener(instance);
        seekbar_speaker_volume.setProgress(Integer.valueOf(seekbar_speaker_volume_value));
        seekbar_alert_volume.setOnSeekBarChangeListener(instance);
        seekbar_alert_volume.setProgress(Integer.valueOf(seekbar_alert_volume_value));
        seekbar_ux_led.setOnSeekBarChangeListener(instance);
        seekbar_ux_led.setProgress(Integer.valueOf(seekbar_ux_led_value));

        switchButton_start_lisening.setChecked(Integer.valueOf(switchButton_start_lisening_value) == 0 ? false : true);
        switchButton_start_lisening.setOnCheckedChangeListener(instance);
        switchButton_end_lisening.setChecked(Integer.valueOf(switchButton_end_lisening_value) == 0 ? false : true);
        switchButton_end_lisening.setOnCheckedChangeListener(instance);
        switchButton_do_not_disturb.setChecked(Integer.valueOf(switchButton_do_not_disturb_value) == 0 ? false : true);
        switchButton_do_not_disturb.setOnCheckedChangeListener(instance);
        language_select_btn.setOnClickListener(instance);
    }

    private void initializeSettingUI() {
        setContentView(R.layout.activity_avslwa_setting);
        bindSettingUI();
        initSettingUI();
    }

    private void bindSettingUI() {
        setting_device_sn = (TextView) findViewById(R.id.setting_device_sn);
        setting_software_version = (TextView) findViewById(R.id.setting_software_version);
        setting_mac_address = (TextView) findViewById(R.id.setting_mac_address);
        deregister_the_device = (Button) findViewById(R.id.deregister_the_device);
    }

    private void initSettingUI() {
        inittoolbar(cur_title_setting);
        tv_title.setOnClickListener(instance);
        done.setOnClickListener(instance);
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);

        deregister_the_device.setOnClickListener(instance);

        setting_device_sn.setText(device_sn_value);
        setting_software_version.setText(software_version_value);
        setting_mac_address.setText(device_mac_address);
    }

    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {

    }

    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {
        Log.i(TAG, "onErrorMessage: -----------+++++");

    }

    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {
        Log.i(TAG, "onStateChangedMessage: -----------+++++" + msgStr);
        Log.i(TAG, "onStateChangedMessage: -----------+++++" + msg);

        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (msg == BES_CONNECT_SUCCESS) {
                    Log.i(TAG, "onStateChangedMessage: -getWholeSettingData");
//                    mPresenter.getWholeSettingData();

                }
                else if (msg == AVS_LWA_GET_WHOLE_SETTING_DATA) {
                    Log.i(TAG, "AVS_LWA_GET_WHOLE_SETTING_DATA: -------" + msgStr);
                    if (msgStr.length() == 0) {
                        return;
                    }
                    String[] strs = msgStr.split(" ");
                    for (int i = 0; i < strs.length; i ++) {
                        String data = strs[i];
                        String type = (i + 1) + "";
                        String value = data.substring(type.length(), data.length());
                        Log.i(TAG, "run data: ----" + data);
                        Log.i(TAG, "run value: ----" + value);
                        refreshUI(type, value);
                    }
                    loadingDialog.dismiss();
                }
                else if (msg == AVS_LWA_SET_ERROR) {
                    showToast(R.string.setting_language_fail);
                    refreshUI(mPresenter.getCurType(), mPresenter.getCurValue());
                }
                else if (msg == AVS_LWA_SET_SUCCESS) {
                    if (mPresenter.getCurType().equals("11")) {
                        mPresenter.disconnect();
                        Intent intent1 = new Intent();
                        intent1.putExtra(AVS_INTENT_SERVICECONFIG_KEY, (Serializable) mServiceConfig);
                        setResult(RESULT_OK, intent1);
                        finish();
                        return;
                    }

                    showToast(R.string.setting_success);
                    refreshUI(mPresenter.getCurType(), mPresenter.getCurValue());
                }
            }
        });
    }

    private void refreshUI(String type,  String valueStr) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (type.equals("9")) {
                    device_sn_value = valueStr;
                    if (setting_device_sn != null)
                        setting_device_sn.setText(valueStr);
                    return;
                } else if (type.equals("10")) {
                    software_version_value = valueStr;
                    if (setting_device_sn != null)
                        setting_software_version.setText(valueStr);
                    return;
                } else if (type.equals("12")) {
                    device_mac_address = valueStr;
                    if (setting_mac_address != null)
                        setting_mac_address.setText(valueStr);
                    return;
                }
                int value = Integer.valueOf(valueStr);
                if (type.equals("1")) {


                } else if (type.equals("2")) {
                    seekbar_lighting_value = valueStr;
                    if (seekbar_lighting != null)
                        seekbar_lighting.setProgress(value);
                } else if (type.equals("3")) {
                    seekbar_speaker_volume_value = valueStr;
                    if (seekbar_speaker_volume != null)
                        seekbar_speaker_volume.setProgress(value);
                } else if (type.equals("4")) {
                    seekbar_alert_volume_value = valueStr;
                    if (seekbar_alert_volume != null)
                        seekbar_alert_volume.setProgress(value);
                } else if (type.equals("5")) {
                    seekbar_ux_led_value = valueStr;
                    if (seekbar_ux_led != null)
                        seekbar_ux_led.setProgress(value);
                } else if (type.equals("6")) {
                    switchButton_start_lisening_value = valueStr;
                    if (switchButton_start_lisening != null)
                        switchButton_start_lisening.setChecked(value == 0 ? false : true);
                } else if (type.equals("7")) {
                    switchButton_end_lisening_value = valueStr;
                    if (switchButton_end_lisening != null)
                        switchButton_end_lisening.setChecked(value == 0 ? false : true);
                } else if (type.equals("8")) {
                    switchButton_do_not_disturb_value = valueStr;
                    if (switchButton_do_not_disturb != null)
                        switchButton_do_not_disturb.setChecked(value == 0 ? false : true);
                }
            }
        });
    }

    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {
        Log.i(TAG, "onSuccessMessage: -------------++++++");
    }

    @Override
    public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
        curSeekBarProgress = progress;
    }

    @Override
    public void onStartTrackingTouch(SeekBar seekBar) {
    }

    @Override
    public void onStopTrackingTouch(SeekBar seekBar) {
        if (seekBar.equals(seekbar_lighting)) {
            mPresenter.sendCurSettingData(2, curSeekBarProgress);
        } else if (seekBar.equals(seekbar_speaker_volume)) {
            mPresenter.sendCurSettingData(3, curSeekBarProgress);
        } else if (seekBar.equals(seekbar_alert_volume)) {
            mPresenter.sendCurSettingData(4, curSeekBarProgress);
        } else if (seekBar.equals(seekbar_ux_led)) {
            mPresenter.sendCurSettingData(5, curSeekBarProgress);
        }
    }

    protected void showToast(int msg) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (instance != null) {
                    Toast.makeText(instance, msg, Toast.LENGTH_SHORT).show();
                }
            }
        });
    }

    @Override
    public void onCheckedChanged(SwitchButton view, boolean isChecked) {
        if (view.equals(switchButton_start_lisening)) {
            mPresenter.sendCurSettingData(6, isChecked ? 1 : 0);
        } else if (view.equals(switchButton_end_lisening)) {
            mPresenter.sendCurSettingData(7, isChecked ? 1 : 0);
        } else if (view.equals(switchButton_do_not_disturb)) {
            mPresenter.sendCurSettingData(8, isChecked ? 1 : 0);
        }
    }
}
