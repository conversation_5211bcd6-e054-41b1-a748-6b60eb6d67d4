package com.besall.allbase.view.activity.chipstoollevel4.health.temperature;

import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Color;
import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;

import androidx.annotation.NonNull;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.R;
import com.besall.allbase.view.activity.chipstoollevel4.rssi.RssiChartBean;
import com.besall.allbase.view.base.BaseActivity;
import com.github.mikephil.charting.charts.LineChart;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;

import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

public class TemperatureActivity extends BaseActivity<ITemperatureActivity, TemperaturePresenter> implements ITemperatureActivity, View.OnClickListener , BesServiceListener {

    private LineChart lineChart;
    private static TemperatureActivity instance;
    public Button temperature_start;
    List<RssiChartBean> rssiChartBeans = new ArrayList<>();
    BesServiceConfig mServiceConfig;
    HmDevice mHmDevice;

    public String address = null;
    @Override
    protected TemperaturePresenter createPresenter() {
        return new TemperaturePresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        mServiceConfig = new BesServiceConfig();
    }

    @Override
    protected int getContentViewId() {
        return R.layout.act_temperature;
    }

    @Override
    protected void bindView() {
        Intent intent = getIntent();
        address = intent.getStringExtra("device");
        Log.i(TAG, "bindView: " + address);
        mHmDevice = new HmDevice();
        mHmDevice.setBleAddress(address);
        mHmDevice.setPreferredProtocol(DeviceProtocol.PROTOCOL_BLE);
        mHmDevice.setDeviceName(intent.getStringExtra("name"));
        Log.i(TAG, "bindView: " + mHmDevice);
        inittoolbar("Temperature");
        temperature_start = (Button)findViewById(R.id.temperature_start);
        temperature_start.setOnClickListener(instance);
        lineChart = (LineChart) findViewById(R.id.temperatureChart);

        setServiceConfig();
        if (address != null){
            mPresenter.connectDevice(mServiceConfig, instance, instance);
        }
        setLineChart();

    }

    public void setServiceConfig(){
        mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_BLE);
        mServiceConfig.setServiceUUID(BesSdkConstants.BES_TOTA_SERVICE_OTA_UUID);
        mServiceConfig.setCharacteristicsUUID(BesSdkConstants.BES_TOTA_CHARACTERISTI_OTA_UUID);
        mServiceConfig.setDescriptorUUID(BesSdkConstants.BES_TOTA_DESCRIPTOR_OTA_UUID);
        mServiceConfig.setDevice(mHmDevice);
        mServiceConfig.setTotaConnect(true);
        boolean useTotaV2 = (boolean) SPHelper.getPreference(instance, BesSdkConstants.BES_TOTA_USE_TOTAV2, BesSdkConstants.BES_TOTA_USE_TOTAV2_VALUE);
        mServiceConfig.setUseTotaV2(useTotaV2);

//        mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
//        mServiceConfig.setServiceUUID(BesSdkConstants.BES_SPP_CONNECT);
    }
    public void setLineChart(){
        Log.i(TAG, "setLineChart: -----");
        lineChart.setBackgroundColor(Color.WHITE);
        lineChart.getDescription().setEnabled(true);
        lineChart.setTouchEnabled(true);
        lineChart.setDragEnabled(true);
        lineChart.setScaleEnabled(true);
        lineChart.setPinchZoom(true);
        lineChart.zoom(3f,0,0,0);
        lineChart.getXAxis().setAxisMinimum(1f);
        lineChart.getXAxis().setLabelCount(9,true);
        

//        lineChart.getXAxis().setGranularityEnabled(true);
        lineChart.animateXY(1000,100);
//        lineChart.getViewPortHandler().setMaximumScaleX(20f);
//        lineChart.getViewPortHandler().setMaximumScaleY(20f);
        lineDataSet1.setColor(Color.RED);
        lineDataSet1.setCircleColor(Color.RED);

        lineDataSet2.setColor(Color.YELLOW);
        lineDataSet2.setCircleColor(Color.YELLOW);

        lineDataSet3.setColor(Color.BLUE);
        lineDataSet3.setCircleColor(Color.BLUE);

        lineDataSet4.setColor(Color.GREEN);
        lineDataSet4.setCircleColor(Color.GREEN);
        lineData.addDataSet(lineDataSet1);
        lineData.addDataSet(lineDataSet2);
        lineData.addDataSet(lineDataSet3);
        lineData.addDataSet(lineDataSet4);
    }

    @Override
    protected void initView() {
//        setYAxis();
        timer.schedule(timerTask,0,100);

//        new Thread(new Runnable() {
//            @Override
//            public void run() {
//                try {
//                    Thread.sleep(100);
//                    Log.i(TAG, "run: -------------");
//                    setData();
//
//                } catch (InterruptedException e) {
//                    throw new RuntimeException(e);
//                }
//            }
//        }).start();

    }

    private Handler handler = new Handler() {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            if (msg.what == 1){
                testData();
            }
        }
    };

    Timer timer = new Timer();
    TimerTask timerTask = new TimerTask() {
        @Override
        public void run() {
            Message message = new Message();
            message.what = 1;
            handler.sendMessage(message);
        }
    };
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_setting, menu);

        return true;
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;
            case R.id.menu_setting:
                clearD();
//                mPresenter.goToSettingActivity(instance);
//                float highX = lineChart.getHighestVisibleX();
//                setRequestedOrientation(isPort()? ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE :ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    public boolean isPort() {
        return getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT;
    }
    public void setYAxis(){
        lineChart.getAxisRight().setEnabled(false);
        YAxis yAxis = lineChart.getAxisLeft();
        yAxis.setLabelCount(7,true);
//        yAxis.setAxisMaximum(19f);
    }
    public void setData(){
        float datas[] = {14f,15f,16f,17f,16f,16f};
        float data[] = new float[100];
        float data2[] = new float[100];

        for (int i = 0; i < 100; i++) {
            data[i] = (float) (Math.random());
            data2[i] = (float) (Math.random() + 4);
            Log.i(TAG, "setData: 111111111111111");
        }
        //在MPAndroidChart一般都是通过List<Entry>对象来装数据的
        List<Entry> entries = new ArrayList<Entry>();
        List<Entry> entries2 = new ArrayList<Entry>();
        List<Entry> entries3 = new ArrayList<Entry>();
        List<Entry> entries4 = new ArrayList<Entry>();
        //循环取出数据
        for(int i = 0; i < data.length; i++){
            entries.add(new Entry(i,data[i]));
            entries2.add(new Entry(i,data2[i]));
            entries3.add(new Entry(i,data[i]+10));
            entries4.add(new Entry(i,data[i]+6));
        }
        //一个LineDataSet对象就是一条曲线
        LineDataSet lineDataSet = new LineDataSet(entries,"第1条数据");
        LineDataSet lineDataSet2 = new LineDataSet(entries2,"第2条数据");
        LineDataSet lineDataSet3 = new LineDataSet(entries3,"第3条数据");
        LineDataSet lineDataSet4 = new LineDataSet(entries4,"第4条数据");
        lineDataSet2.setColor(Color.YELLOW);
        lineDataSet3.setColor(Color.RED);
        lineDataSet4.setColor(Color.GREEN);

        lineDataSet2.setCircleColor(Color.YELLOW);
        lineDataSet3.setCircleColor(Color.RED);
        lineDataSet4.setCircleColor(Color.GREEN);
        //LineData才是正真给LineChart的数据
        LineData lineData = new LineData(lineDataSet,lineDataSet2,lineDataSet3,lineDataSet4);
        lineChart.moveViewToX(lineData.getXMax());
        lineChart.setData(lineData);
    }

    List<Entry> leftEntries = new ArrayList<Entry>();
    List<Entry> leftTwsEntries = new ArrayList<Entry>();
    List<Entry> rightEntries = new ArrayList<Entry>();
    List<Entry> rightTwsEntries = new ArrayList<Entry>();
    //一个LineDataSet对象就是一条曲线
    LineDataSet lineDataSet1 = new LineDataSet(leftEntries,"Left Rssi");
    LineDataSet lineDataSet2 = new LineDataSet(leftTwsEntries,"Left TWS");
    LineDataSet lineDataSet3 = new LineDataSet(rightEntries,"Right Rssi");
    LineDataSet lineDataSet4 = new LineDataSet(rightTwsEntries,"Right TWS");
    LineData lineData = new LineData();
    int i = 0;
    public void testData(){


//        RssiChartBean rssiChartBean = new RssiChartBean();
//        rssiChartBean.setLeftRssi((float) Math.random()*60  );
//        rssiChartBean.setLeftTWS((float) Math.random()*10);
//        rssiChartBean.setRightRssi((float) Math.random()*100 - 100);
//        rssiChartBean.setRightTWS((float) Math.random()*10 - 50);
//        rssiChartBeans.add(rssiChartBean);

        //在MPAndroidChart一般都是通过List<Entry>对象来装数据的


        //循环取出数据
//        for(int i = 0; i < rssiChartBeans.size(); i++){
//            leftEntries.add(new Entry(i, rssiChartBeans.get(i).getLeftRssi()));
//            leftTwsEntries.add(new Entry(i, rssiChartBeans.get(i).getLeftTWS()));
//            rightEntries.add(new Entry(i, rssiChartBeans.get(i).getRightRssi()));
//            rightTwsEntries.add(new Entry(i, rssiChartBeans.get(i).getRightTWS()));
//        }



        lineDataSet1.addEntry(new Entry(i, (float) Math.random()*60));
        lineDataSet2.addEntry(new Entry(i, (float) Math.random()*2));
        lineDataSet3.addEntry(new Entry(i, (float) Math.random()*100 - 50));
        lineDataSet4.addEntry(new Entry(i, (float) Math.random()*10 - 50));
        i++;


        //LineData才是正真给LineChart的数据

        lineData.notifyDataChanged();
        lineChart.moveViewToX(lineData.getXMax());
        lineChart.setData(lineData);


    }
    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    public void clearD(){
        i = 0;
        lineChart.clear();
        lineDataSet1.clear();
        lineDataSet2.clear();
        lineDataSet3.clear();
        lineDataSet4.clear();
        lineData.clearValues();
        lineDataSet1 = new LineDataSet(leftEntries,"Left Rssi");
        lineDataSet2 = new LineDataSet(leftTwsEntries,"Left TWS");
        lineDataSet3 = new LineDataSet(rightEntries,"Right Rssi");
        lineDataSet4 = new LineDataSet(rightTwsEntries,"Right TWS");
        lineData = new LineData();
        timerTask.cancel();
        timer.cancel();

    }

    public void restart(){
        timerTask = new TimerTask() {
            @Override
            public void run() {
                Message message = new Message();
                message.what = 1;
                handler.sendMessage(message);
            }
        };
        timer = new Timer();
        timer.schedule(timerTask,0,1000);

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.temperature_start:
                if (address != null){
                    mPresenter.sendHealth();
                }
//                Log.i(TAG, "onClick: temperature_start");
//                setLineChart();
//                restart();
                break;
        }
    }

    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {

    }

    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {

    }

    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {

    }

    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {

    }
}
