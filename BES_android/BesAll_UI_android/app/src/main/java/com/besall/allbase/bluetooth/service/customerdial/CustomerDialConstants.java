package com.besall.allbase.bluetooth.service.customerdial;

import android.annotation.SuppressLint;

public class CustomerDialConstants {
    public static final short                        OP_TOTA_GET_SCREEN_PARAMETER_CMD =  (short)0x2000;
    public static final short                        OP_TOTA_GET_SCREEN_PARAMETER_ACK =  (short)0x2001;
    public static final short                           OP_TOTA_GET_FLASH_ADDRESS_CMD =  (short)0x2002;
    public static final short                               OP_TOTA_FLASH_ADDRESS_CMD =  (short)0x2003;
    public static final short                             OP_TOTA_WATCH_SEND_DATA_CMD =  (short)0x2006;
    public static final short                            OP_TOTA_WRITE_FLASH_DATA_CMD =  (short)0x6200;
    public static final short                         OP_TOTA_WRITE_FLASH_WHOLE_CHECK =  (short)0x2004;
    public static final short                                OP_TOTA_RECEIVE_DATA_ACK =  (short)0x6000;
    public static final short                  OP_TOTA_WRITE_FLASH_WHOLE_CHECK_RESULT =  (short)0x2005;
    public static final short                                OP_TOTA_EXCHANGE_MTU_CMD =  (short)0x6301;
    public static final short                              OP_TOTA_RECEIVE_DATA_ERROR =  (short)0x62FE;
    public static final short                              OP_TOTA_CONFIRM_DATA_ERROR =  (short)0x62FD;
    public static final short                                 OP_TOTA_LAST_FLASH_ADDR =  (short)0x62FC;


    public static final String                      CUSTOMER_DIAL_CROP_PHOTO_PATH_KEY = "CUSTOMER_DIAL_CROP_PHOTO_PATH_KEY";
    public static final String                          CUSTOMER_DIAL_CHOOSE_DIAL_KEY = "CUSTOMER_DIAL_CHOOSE_DIAL_KEY";
    public static final String                      CUSTOMER_DIAL_CHOOSE_DFU_FILE_KEY = "CUSTOMER_DIAL_CHOOSE_DFU_FILE_KEY";
    public static final String                                   CUSTOMER_DIAL_FOLDER = "CustomerDial/Photo";
    public static final String                             CUSTOMER_DIAL_FOLDER_CACHE = "CustomerDial/Cache";
    public static final String                             CUSTOMER_DIAL_FOLDER_LOG = "CustomerDial/log";
    public static final String                  CUSTOMER_DIAL_FOLDER_CACHE_PHOTO_NAME = "dialphoto.png";
    public static final String                      CUSTOMER_DIAL_CROP_PHOTO_FORM_KEY = "CUSTOMER_DIAL_CROP_PHOTO_FORM_KEY";
    public static final int                       CUSTOMER_DIAL_CROP_PHOTO_FORM_VALUE = 2;//1 circle 2 square
    public static final String                    CUSTOMER_DIAL_CROP_PHOTO_LENGTH_KEY = "CUSTOMER_DIAL_CROP_PHOTO_LENGTH_KEY";
    public static final int                     CUSTOMER_DIAL_CROP_PHOTO_LENGTH_VALUE = 380;
    public static final String                     CUSTOMER_DIAL_CROP_PHOTO_WIDTH_KEY = "CUSTOMER_DIAL_CROP_PHOTO_WIDTH_KEY";
    public static final int                      CUSTOMER_DIAL_CROP_PHOTO_WIDTH_VALUE = 320;

    public static final int                                   REQUEST_CODE_CROP_PHOTO = 0x00000200;
    public static final int                                  REQUEST_CODE_CHOOSE_DIAL = 0x00000201;
    public static final int                              REQUEST_CODE_CHOOSE_DFU_FILE = 0x00000202;

    public static final int               CUSTOMER_DIAL_GET_FLASH_ADDRESS_RESULT_FAIL = 0x00000300;
    public static final int                    CUSTOMER_DIAL_GET_FLASH_ADDRESS_RESULT = 0x00000301;
    public static final int                          CUSTOMER_DIAL_RECEIVE_ACK_RESULT = 0x00000302;
    public static final int                              CUSTOMER_DIAL_SEND_DATA_OVER = 0x00000303;
    public static final int                 OP_TOTA_WRITE_FLASH_WHOLE_CHECK_RESULT_OK = 0x00000304;
    public static final int               OP_TOTA_WRITE_FLASH_WHOLE_CHECK_RESULT_FAIL = 0x00000305;
    public static final int                               OP_TOTA_WRITE_FLASH_PROCESS = 0x00000306;
    public static final int                                    CUSTOMER_DIAL_LOG_INFO = 0x00000308;
    public static final int                        CUSTOMER_DIAL_GET_SCREEN_PARAMETER = 0x00000309;
    public static final int                                     CUSTOMER_DIAL_GET_MTU = 0x00000400;
    public static final int                          CUSTOMER_DIAL_RECEIVE_DATA_ERROR = 0x00000401;
    public static final int                     CUSTOMER_DIAL_RECEIVE_LSAT_FLASH_ADDR = 0x00000402;
    public static final int                             CUSTOMER_DIAL_RECEIVE_ALL_ACK = 0x00000403;


    //handle_msg
    public static final int                               MSG_CUSTOMER_DIAL_SEND_DATA = 0x00000500;
    public static final int                        MSG_CUSTOMER_DIAL_GET_MTU_TIME_OUT = 0x00000501;
    public static final int                 MSG_CUSTOMER_DIAL_GET_FLASH_ADDR_TIME_OUT = 0x00000502;
    public static final int                        MSG_CUSTOMER_DIAL_WAIT_ACK_TIMEOUT = 0x00000503;
    public static final int                       MSG_CUSTOMER_DIAL_WAIT_OVER_TIMEOUT = 0x00000504;
    public static final int              MSG_CUSTOMER_DIAL_WAIT_CONFIRM_ERROR_TIMEOUT = 0x00000505;

    public static final boolean                                CUSTOMER_DIAL_USE_TEST = false;//模拟测试
    public static final String                         CUSTOMER_DIAL_TEST_ADDRESS_KEY = "CUSTOMER_DIAL_TEST_ADDRESS_KEY";

    @SuppressLint({"ResourceAsColor", "NewApi"})
    public static final                                   String[] CUSTOM_DIAL_COLORS = new String[]{
            "#ffc00000",
            "#ffc00060",
            "#ffc000ff",
            "#ffc020a0",
            "#ffc040a0",
            "#ffc080a0",
            "#ffc0c0a0",
            "#ffff00a0",
            "#ffff60a0",
            "#ff0000a0",
            "#ffffffa0",
            "#ffff80a0",
            "#ff006000",
            "#ff006080",
            "#ff0060ff",
            "#ff00a060",
            "#ff00a0c0",
            "#ff00ffff",
            "#ff804040",
            "#ff80a060",
            "#ff80a0a0",
            "#ff80a0ff",
            "#ffa02000",
            "#ffa02060",
            "#ffa020a0",
            "#ffa020ff",
            "#ffa04040",
            "#ffa04080",
            "#ffa060c0",
            "#ffa060ff",
            "#ffa080a0",
            "#ffa0a0a0",

            "#00000000"};



}
