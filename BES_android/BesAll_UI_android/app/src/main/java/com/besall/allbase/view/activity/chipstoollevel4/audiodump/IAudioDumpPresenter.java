package com.besall.allbase.view.activity.chipstoollevel4.audiodump;

import android.content.Context;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;

/**
 * <AUTHOR>
 * @time $ $
 */
interface IAudioDumpPresenter {

    String findSppChannel(Context context);

    void openSppRxThread();

    void closeSppRxThread();

    void startRecord(String streamStartText);

    void stopRecord();

    void audioDumpSetInsertData(byte data);
    void player(String path, int type);

    void selectfile(AudioDumpActivity context, int file);
}
