package com.besall.allbase.view.activity.chipstoollevel4.rssi;

public class RssiChartBean {
    public float leftRssi;
    public float leftTWS;
    public float rightRssi;
    public float rightTWS;

    public void setLeftRssi(float leftRssi) {
        this.leftRssi = leftRssi;
    }

    public void setLeftTWS(float leftTWS) {
        this.leftTWS = leftTWS;
    }

    public void setRightRssi(float rightRssi) {
        this.rightRssi = rightRssi;
    }

    public void setRightTWS(float rightTWS) {
        this.rightTWS = rightTWS;
    }

    public float getLeftRssi() {
        return leftRssi;
    }

    public float getLeftTWS() {
        return leftTWS;
    }

    public float getRightRssi() {
        return rightRssi;
    }

    public float getRightTWS() {
        return rightTWS;
    }
}
