package com.besall.allbase.common.manager;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.SPHelper;

import org.apache.commons.io.IOUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.concurrent.TimeUnit;

import javax.mail.BodyPart;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMultipart;
import javax.mail.util.ByteArrayDataSource;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.ConnectionPool;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;
import okio.BufferedSink;
import okio.BufferedSource;
import okio.ByteString;

public class AlexaHttpManager {

    public interface AlexaHttpListener {
        void onAlexaStateChange(int state, Object data);
    }

    public static String ALEXA_ACCESS_TOKEN_RESPONSE_KEY = "ALEXA_ACCESS_TOKEN_RESPONSE_KEY";
    private static String ALEXA_CLIENT_ID_KEY = "ALEXA_CLIENT_ID_KEY";
    private static String ALEXA_PRODUCT_ID_KEY = "ALEXA_PRODUCT_ID_KEY";
    private static String ALEXA_DSN_KEY = "ALEXA_DSN_KEY";

    public static int ALEXA_STATE_NO_LOGIN = 0;
    public static int ALEXA_STATE_NO_NETWORK = 1;
    public static int ALEXA_STATE_LOGIN_REFRESH_TOKEN = 2;
    public static int ALEXA_STATE_LOGIN_SUCCESS = 3;
    public static int ALEXA_STATE_SPEECH_RECOGNIZER = 4;
    public static int ALEXA_STATE_RECEIVE_DIRECTIVES_SPEAK = 6;
    public static int ALEXA_STATE_SPEECH_START = 7;
    public static int ALEXA_STATE_RECEIVE_TEXT = 8;
    public static int ALEXA_STATE_NETWORK_RECONNECT = 9;
    public static int ALEXA_STATE_NETWORK_DISCONNECT = 10;

    public int curAlexaState = ALEXA_STATE_NO_LOGIN;

    public final String TAG = getClass().getSimpleName();
    private static volatile AlexaHttpManager mHttpManager;
    private static Context mContext;
    private static AlexaHttpListener mListener;

    private byte[] audioData = new byte[0];
    private int sendAudioDataLen = -1;

    OkHttpClient mClient;
    String alexaUri = "https://api.amazon.com/auth/O2/token";
    int REQUEST_FOR_TOKEN = 0;
    int REQUEST_FOR_REFRESH_TOKEN = 1;
    String mMessageId = "aa";
    String mDialogRequestId = "mDialogRequestId";
    String mStartOfSpeechTimestamp = "mStartOfSpeechTimestamp";

    private String CODE_VERIFIER = "dBjftJeZ4CVP-mB92K27uhbUJU1p1r_wW1gFWFOEjXk";

    public static AlexaHttpManager getHttpManager(Context context, AlexaHttpListener listener) {
        if (mHttpManager == null) {
            synchronized (AlexaHttpManager.class) {
                if (mHttpManager == null) {
                    mHttpManager = new AlexaHttpManager();
                }
            }
        }
        if (context != null) {
            if (mContext == null) {
                mContext = context;
                initReceiver();
            }
            mContext = context;
        }
        if (listener != null) {
            mListener = listener;
        }
        mHttpManager.checkCurAlexaState();
        return mHttpManager;
    }

    private static void initReceiver() {
        Log.i("TAG", "initReceiver: -----");
        IntentFilter filter = new IntentFilter();
        filter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
        filter.addAction(ConnectivityManager.EXTRA_NETWORK);
        mContext.registerReceiver(new MyBroadcastReceiver(), filter);
    }

    private static class MyBroadcastReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction().equals(ConnectivityManager.CONNECTIVITY_ACTION)) {
                NetworkInfo info = intent.getParcelableExtra(ConnectivityManager.EXTRA_NETWORK_INFO);
                if (info != null) {
                    if (NetworkInfo.State.CONNECTED == info.getState() && info.isAvailable()) {
                        if (mListener != null) {
                            mListener.onAlexaStateChange(ALEXA_STATE_NETWORK_RECONNECT, new byte[0]);
                        }
                        mHttpManager.checkCurAlexaState();
                    } else {
                        if (mListener != null) {
                            mListener.onAlexaStateChange(ALEXA_STATE_NETWORK_DISCONNECT, new byte[0]);
                        }
                    }
                }
            }
        }
    }

    public int checkCurAlexaState() {
        ConnectivityManager connectivityManager
                = (ConnectivityManager) mContext.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
        if (activeNetworkInfo == null || !activeNetworkInfo.isConnected()) {
            curAlexaState = ALEXA_STATE_NO_NETWORK;
            return curAlexaState;
        }
        String token = getCurAccess_token();
        if (token.length() == 0 && curAlexaState != ALEXA_STATE_LOGIN_REFRESH_TOKEN) {
            curAlexaState = ALEXA_STATE_NO_LOGIN;
        } else if (token.length() > 0) {
            curAlexaState = ALEXA_STATE_LOGIN_SUCCESS;
            createDownChannelStream();
        }
        return curAlexaState;
    }

    public static void saveInformation(String clientId, String productId, String dsn) {
        if (clientId.length() > 0) {
            SPHelper.putPreference(mContext, ALEXA_CLIENT_ID_KEY, clientId);
        }
        if (productId.length() > 0) {
            SPHelper.putPreference(mContext, ALEXA_PRODUCT_ID_KEY, productId);
        }
        if (dsn.length() > 0) {
            SPHelper.putPreference(mContext, ALEXA_DSN_KEY, dsn);
        }
    }

    public void ready() {
        Log.i(TAG, "ready: ----------");
        sendAudioDataLen = -1;
    }

    public void stopRecording() {
        isRecording = false;
//        if (mListener != null) {
//            mListener.onAlexaStateChange(ALEXA_STATE_RECEIVE_DIRECTIVES_SPEAK, new byte[0]);
//        }
    }

    public void addData(byte[] data) {
        Log.i(TAG, "addData: ------------");
        audioData = ArrayUtil.byteMerger(audioData, data);
        if (sendAudioDataLen == -1) {
            if (mListener != null) {
                mListener.onAlexaStateChange(ALEXA_STATE_SPEECH_RECOGNIZER, new byte[0]);
            }
            sendAudioDataLen = 0;
            isRecording = true;
            mMessageId = "BesMessagetId" + System.currentTimeMillis();
            mDialogRequestId = "BesDialogRequestId" + System.currentTimeMillis();
            mStartOfSpeechTimestamp = "mStartOfSpeechTimestamp" + System.currentTimeMillis();
//            new Thread(new Runnable() {
//                @Override
//                public void run() {
                    setSendAlexaSpeechAudio();
//                }
//            }).start();
        } else if (sendAudioDataLen == -2) {

            return;
        }
    }

    private int calculateVolume(byte[] buffer) {
        short[] audioData = new short[buffer.length / 2];
        ByteBuffer.wrap(buffer).order(ByteOrder.LITTLE_ENDIAN).asShortBuffer().get(audioData);
        double sum = 0.0;
        for (int i = 0; i < audioData.length; i ++) {
            sum += (audioData[i] * audioData[i]);
        }
        double mean = sum / audioData.length;
        double volume = 10 * Math.log10(mean);

        return (int) volume;
    }

    public void requestTokenData(String authorizationCode, String redirectUri, String clientId) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("grant_type", "authorization_code");
            jsonObject.put("code", authorizationCode);
            jsonObject.put("redirect_uri", redirectUri);
            jsonObject.put("client_id", clientId);
            jsonObject.put("code_verifier", CODE_VERIFIER);
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }

        requestPost(jsonObject, alexaUri, REQUEST_FOR_TOKEN);
    }

    public void requestPost(JSONObject jsonObject, String url, int task) {
        Log.i(TAG, "requestPost: -----------");
        OkHttpClient okHttpClient = new OkHttpClient();
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), jsonObject.toString());
        Request request = new Request.Builder().url(url).post(requestBody).build();
        okHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(@NonNull Call call, @NonNull IOException e) {
                Log.i(TAG, "onFailure: ----" + e.toString());
                doRequestTask(task, null);
            }

            @Override
            public void onResponse(@NonNull Call call, @NonNull Response response) throws IOException {
                if (response.isSuccessful()) {
                    try {
                        JSONObject jsonObjectResult = new JSONObject(response.body().string());
                        if (task == REQUEST_FOR_TOKEN) {
                            jsonObjectResult.put("client_id", jsonObject.getString("client_id"));
                            jsonObjectResult.put("redirect_uri", jsonObject.getString("redirect_uri"));
                        }
                        doRequestTask(task, jsonObjectResult);
                    } catch (JSONException e) {
                        throw new RuntimeException(e);
                    }
                } else {
                    Log.i(TAG, "onResponse: ----" + "fail");
                    doRequestTask(task, null);
                }
            }
        });
    }

    private void doRequestTask(int task, JSONObject jsonObject) {
        Log.i(TAG, "doRequestTask: -------" + task);
        if (jsonObject != null) {
            Log.i(TAG, "doRequestTask: -------" + jsonObject.toString());
        }
        if (task == REQUEST_FOR_TOKEN && jsonObject != null) {
            try {
                jsonObject.put("time", (System.currentTimeMillis() + "").substring(0, 10));
                SPHelper.putPreference(mContext, ALEXA_ACCESS_TOKEN_RESPONSE_KEY, jsonObject.toString());
                if (curAlexaState != ALEXA_STATE_LOGIN_SUCCESS && mListener != null) {
                    createDownChannelStream();
                    mListener.onAlexaStateChange(ALEXA_STATE_LOGIN_SUCCESS, new byte[0]);
                }
                curAlexaState = ALEXA_STATE_LOGIN_SUCCESS;
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
        } else if (task == REQUEST_FOR_REFRESH_TOKEN && jsonObject != null) {
            String lastResponse = (String) SPHelper.getPreference(mContext, ALEXA_ACCESS_TOKEN_RESPONSE_KEY, "");
            try {
                JSONObject lastJsonObject = new JSONObject(lastResponse);
                jsonObject.put("time", (System.currentTimeMillis() + "").substring(0, 10));
                jsonObject.put("client_id", lastJsonObject.getString("client_id"));
                jsonObject.put("redirect_uri", lastJsonObject.getString("redirect_uri"));
                SPHelper.putPreference(mContext, ALEXA_ACCESS_TOKEN_RESPONSE_KEY, jsonObject.toString());
                if (curAlexaState != ALEXA_STATE_LOGIN_SUCCESS && mListener != null) {
                    createDownChannelStream();
                    mListener.onAlexaStateChange(ALEXA_STATE_LOGIN_SUCCESS, new byte[0]);
                }
                curAlexaState = ALEXA_STATE_LOGIN_SUCCESS;
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
        }
    }

    static String AUTHORIZATION = "Authorization";
    static String BEARER = "Bearer ";
    static String CONTENT_TYPE = "content-type";
    static String STREAM_TYPE = "application/octet-stream";
    static String JSON_TYPE = "application/json; charset=UTF-8";
    static String DISPOSITION = "Content-Disposition";
    static String META_DATA = "form-data; name=\"metadata\"";
    static String BOUNDARY_VALUE = "--89uuhhJq0M2Yijnuhu904c09o--";
    static String BOUNDARY = "boundary";
    static String MULTI_DATA = "multipart/form-data";

    static String AVS_URL = "https://avs-alexa-eu.amazon.com";
//    static String AVS_URL = "https://bob-dispatch-prod-na.amazon.com";

    static String AVS_DIRECTIVES_URL = AVS_URL + "/v20160207/directives";
    static String AVS_EVENTS_URL = AVS_URL + "/v20160207/events";
    static String AVS_PING = AVS_URL + "/ping";
    private static final long CONNECTION_POOL_TIMEOUT_MILLISECONDS = 60 * 60 * 1000;

    public void createDownChannelStream() {
        if (mClient != null) return;

        mMessageId = "BesMessagetId" + System.currentTimeMillis();
        mDialogRequestId = "BesDialogRequestId" + System.currentTimeMillis();

        ConnectionPool connectionPool = new ConnectionPool(5,
                CONNECTION_POOL_TIMEOUT_MILLISECONDS, TimeUnit.MILLISECONDS);
        mClient = new OkHttpClient.Builder()
                .connectTimeout(5, TimeUnit.SECONDS)
                .writeTimeout(10, TimeUnit.SECONDS)
                .readTimeout(10, TimeUnit.SECONDS)
                .connectionPool(connectionPool)
                .build();
        Request request = new Request.Builder()
                .get()
                .addHeader(AUTHORIZATION, BEARER + getCurAccess_token())
                .addHeader(CONTENT_TYPE, MULTI_DATA)
                .addHeader(BOUNDARY, BOUNDARY_VALUE)
                .url(AVS_DIRECTIVES_URL)
                .build();

        mClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(@NonNull Call call, @NonNull IOException e) {
                Log.i(TAG, "createDownChannelStream onFailure: -------------");
            }

            @Override
            public void onResponse(@NonNull Call call, @NonNull Response response) throws IOException {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        verifyGateway();
                    }
                }).start();

                BufferedSource bufferedSource = response.body().source();
                String line = bufferedSource.readUtf8Line();
                Log.i(TAG, "onResponse line--------" + line);
                Log.i(TAG, "createDownChannelStream--------" + response.code());

                boolean checkJson = isJSONValid(line);
                if (checkJson) {
                    try {
                        JSONObject objectDirective = new JSONObject(line);
                        Log.i(TAG, "createDownChannelStream: ------" + objectDirective.toString());

                    } catch (JSONException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        });
    }

    public boolean isJSONValid(String test) {
        try {
            new JSONObject(test);
        } catch (JSONException ex) {
            try {
                new JSONArray(test);
            } catch (JSONException ex1) {
                return false;
            }
        }
        return true;
    }

    private void setPingAlexa() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                Request requestPing = new Request.Builder()
                        .get()
                        .addHeader(AUTHORIZATION, BEARER + getCurAccess_token())
                        .addHeader(CONTENT_TYPE, MULTI_DATA)
                        .url(AVS_PING)
                        .build();
                Call pingCall = mClient.newCall(requestPing);
                Response response2 = null;
                try {
                    response2 = pingCall.execute();
                    boolean isPingCallOpen = response2.isSuccessful();
                    Log.i(TAG, "setPingAlexa: --------" + isPingCallOpen);
                    if (isPingCallOpen) {
                        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                setPingAlexa();
                            }
                        }, 4 * 60 * 1000);
                    }
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }).start();
    }

    private void speechConfirmationChanged() {
        RequestBody requestBody = RequestBody.create(MediaType.parse(JSON_TYPE), String.valueOf(getSpeechConfirmationChangedJson()));
        MultipartBody.Builder multiBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("metadata", "metadata", requestBody);
        Request requestSyncState = new Request.Builder()
                .url(AVS_EVENTS_URL)
                .post(multiBody.build())
                .addHeader(AUTHORIZATION, BEARER + getCurAccess_token())
                .addHeader(CONTENT_TYPE, MULTI_DATA)
                .addHeader(BOUNDARY, BOUNDARY_VALUE)
                .build();

        Response response = null;
        try {
            response = mClient.newCall(requestSyncState).execute();
            boolean isSuccess = response.isSuccessful();
            Log.i(TAG, "speechConfirmationChanged: ------" + isSuccess);
            Log.i(TAG, "speechConfirmationChanged: ------" + response.code());
            Log.i(TAG, "speechConfirmationChanged: ------" + response.headers().toString());

            InputStream inputStreamObject = response.body().byteStream();
            BufferedReader streamReader = new BufferedReader(new InputStreamReader(inputStreamObject, "UTF-8"));
            StringBuilder responseStrBuilder = new StringBuilder();
            String inputStr;
            while ((inputStr = streamReader.readLine()) != null)
                responseStrBuilder.append(inputStr);

            Log.i(TAG, "speechConfirmationChanged.toString: ------" + responseStrBuilder.toString());

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private void verifyGateway() {
        Log.i(TAG, "verifyGateway: --------");
        RequestBody requestBody = RequestBody.create(MediaType.parse(JSON_TYPE), String.valueOf(getVerifyGatewayJson()));
        MultipartBody.Builder multiBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("metadata", "metadata", requestBody);
        Request requestSyncState = new Request.Builder()
                .url(AVS_EVENTS_URL)
                .post(multiBody.build())
                .addHeader(AUTHORIZATION, BEARER + getCurAccess_token())
                .addHeader(CONTENT_TYPE, MULTI_DATA)
                .addHeader(BOUNDARY, BOUNDARY_VALUE)
                .build();

        Response responseVerifyGateway = null;
        try {
            responseVerifyGateway = mClient.newCall(requestSyncState).execute();
            boolean isOk = responseVerifyGateway.isSuccessful();
            if (isOk && responseVerifyGateway.code() == 200) {
                ByteArrayDataSource ds = new ByteArrayDataSource(responseVerifyGateway.body().byteStream(), MULTI_DATA);
                responseVerifyGateway.body().close();
                MimeMultipart multipart = null;
                multipart = new MimeMultipart(ds);
                for (int i = 0; i < multipart.getCount(); i++) {
                    BodyPart bodypart = multipart.getBodyPart(i);
                    String contentType = bodypart.getContentType();
                    Log.i(TAG, "contentType: --------" + contentType);
                    if (contentType.equals(JSON_TYPE)) {
                        InputStream inputStreamObject = bodypart.getInputStream();
                        BufferedReader streamReader = new BufferedReader(new InputStreamReader(inputStreamObject, "UTF-8"));
                        StringBuilder responseStrBuilder = new StringBuilder();
                        String inputStr;
                        while ((inputStr = streamReader.readLine()) != null)
                            responseStrBuilder.append(inputStr);

                        Log.i(TAG, "responseStrBuilder: --------" + responseStrBuilder.toString());
                        JSONObject jsonObject = new JSONObject(responseStrBuilder.toString());
                        JSONObject directive = (JSONObject) jsonObject.get("directive");
                        JSONObject header = (JSONObject) directive.get("header");
                        JSONObject payload = (JSONObject) directive.get("payload");
                        String namespace = header.getString("namespace");
                        String name = header.getString("name");
                        if (namespace.equals("Alexa.ApiGateway") && name.equals("SetGateway")) {
                            AVS_URL = payload.getString("gateway");
                            AVS_DIRECTIVES_URL = AVS_URL + "/v20160207/directives";
                            AVS_EVENTS_URL = AVS_URL + "/v20160207/events";
                        }
                    }
                }
            }
            Log.i(TAG, "AVS_EVENTS_URL: ---------" + AVS_EVENTS_URL);
            addOrUpdateReport();
        } catch (IOException | MessagingException | JSONException e) {
            throw new RuntimeException(e);
        }
    }

    private void addOrUpdateReport() {
        Log.i(TAG, "addOrUpdateReport: --------");
        RequestBody requestBody = RequestBody.create(MediaType.parse(JSON_TYPE), String.valueOf(getAddOrUpdateReportJson()));
        MultipartBody.Builder multiBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("metadata", "metadata", requestBody);
        Request requestSyncState = new Request.Builder()
                .url(AVS_EVENTS_URL)
                .post(multiBody.build())
                .addHeader(AUTHORIZATION, BEARER + getCurAccess_token())
                .addHeader(CONTENT_TYPE, MULTI_DATA)
                .addHeader(BOUNDARY, BOUNDARY_VALUE)
                .build();

        Response responseAddOrUpdateReport = null;
        try {
            responseAddOrUpdateReport = mClient.newCall(requestSyncState).execute();
            boolean isOk = responseAddOrUpdateReport.isSuccessful();
            responseAddOrUpdateReport.close();
//            Log.i(TAG, "addOrUpdateReport: ------" + isOk);
//            Log.i(TAG, "addOrUpdateReport: ------" + responseAddOrUpdateReport.code());
//            Log.i(TAG, "addOrUpdateReport: ---------->" + responseAddOrUpdateReport.body().toString());
//            InputStream inputStreamObject = responseAddOrUpdateReport.body().byteStream();
//            BufferedReader streamReader = new BufferedReader(new InputStreamReader(inputStreamObject, "UTF-8"));
//            StringBuilder responseStrBuilder = new StringBuilder();
//            String inputStr;
//            while ((inputStr = streamReader.readLine()) != null)
//                responseStrBuilder.append(inputStr);
//
//            Log.i(TAG, "addOrUpdateReport.toString: ------" + responseStrBuilder.toString());

            setAlexaSyncState();
//            setSendAlexaSpeechAudio();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    private void setAlexaSyncState() {
        RequestBody requestBody = RequestBody.create(MediaType.parse(JSON_TYPE), String.valueOf(getSyncStateJson()));
        MultipartBody.Builder multiBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("metadata", "metadata", requestBody);
        Request requestSyncState = new Request.Builder()
                .url(AVS_EVENTS_URL)
                .post(multiBody.build())
                .addHeader(AUTHORIZATION, BEARER + getCurAccess_token())
                .addHeader(CONTENT_TYPE, MULTI_DATA)
                .addHeader(BOUNDARY, BOUNDARY_VALUE)
                .build();

        Response responseSyncState = null;
        try {
            responseSyncState = mClient.newCall(requestSyncState).execute();
            boolean isSyncState = responseSyncState.isSuccessful();
            Log.i(TAG, "isSyncState: ------" + isSyncState);
            Log.i(TAG, "isSyncState: ------" + responseSyncState.code());

            InputStream inputStreamObject = responseSyncState.body().byteStream();
            BufferedReader streamReader = new BufferedReader(new InputStreamReader(inputStreamObject, "UTF-8"));
            StringBuilder responseStrBuilder = new StringBuilder();
            String inputStr;
            while ((inputStr = streamReader.readLine()) != null)
                responseStrBuilder.append(inputStr);

            Log.i(TAG, "setAlexaSyncState.toString: ------" + responseStrBuilder.toString());

            if (isSyncState) {
                setPingAlexa();
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public void setSendAlexaSpeechAudio() {
        RequestBody requestBody = RequestBody.create(MediaType.parse(JSON_TYPE), String.valueOf(getSendSpeechAudioJson()));
        RequestBody multiBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("metadata", "metadata", requestBody)
                .addFormDataPart("audio", STREAM_TYPE, streamRequestBody)
                .build();
        Request requestAudio = new Request.Builder()
                .url(AVS_EVENTS_URL)
                .addHeader(AUTHORIZATION, BEARER + getCurAccess_token())
                .addHeader(CONTENT_TYPE, MULTI_DATA)
                .addHeader(BOUNDARY, BOUNDARY_VALUE)
                .post(multiBody)
                .build();
        mClient.newCall(requestAudio).enqueue(new Callback() {
            @Override
            public void onFailure(@NonNull Call call, @NonNull IOException e) {
                isRecording = false;
                Log.i(TAG, "setSendAlexaSpeechAudio--------onFailure");
            }

            @Override
            public void onResponse(@NonNull Call call, @NonNull Response response) throws IOException {
                isRecording = false;
                if (response.isSuccessful()) {
                    Log.i(TAG, "setSendAlexaSpeechAudio: --------success" + response.code());
                    if (response.code() == 200) {
                        ByteArrayDataSource ds = new ByteArrayDataSource(response.body().byteStream(), MULTI_DATA);
                        response.body().close();

                        MimeMultipart multipart = null;
                        try {
                            multipart = new MimeMultipart(ds);
                            for (int i = 0; i < multipart.getCount(); i++) {

                                BodyPart bodypart = multipart.getBodyPart(i);
                                String contentType = bodypart.getContentType();
                                Log.i(TAG, "contentType: --------" + contentType);

                                if (contentType.equals(JSON_TYPE)) {
                                    InputStream inputStreamObject = bodypart.getInputStream();
                                    BufferedReader streamReader = new BufferedReader(new InputStreamReader(inputStreamObject, "UTF-8"));
                                    StringBuilder responseStrBuilder = new StringBuilder();
                                    String inputStr;
                                    String token = null;
                                    String namespace = null;
                                    String name = null;
                                    String scheduledTime = null;
                                    String type = null;
                                    while ((inputStr = streamReader.readLine()) != null)
                                        responseStrBuilder.append(inputStr);

                                    Log.i(TAG, "responseStrBuilder: --------" + responseStrBuilder.toString());
                                    JSONObject dhanraj = new JSONObject(responseStrBuilder.toString());
                                    JSONObject directive = (JSONObject) dhanraj.get("directive");
                                    JSONObject header = (JSONObject) directive.get("header");

                                    JSONObject payload = (JSONObject) directive.get("payload");
                                    namespace = header.getString("namespace");
                                    name = header.getString("name");
                                    if (name.equals("StopCapture") && namespace.equals("SpeechRecognizer")) {
//                                        stopListening();
                                    } else if (name.equals("ExpectSpeech") && namespace.equals("SpeechRecognizer")) {
//                                        long timeoutInMilliseconds = payload.getLong("timeoutInMilliseconds");
//                                        Log.d("timeoutmsec", String.valueOf((timeoutInMilliseconds)));
//                                        checkforExpectSpeech = true;
                                    } else if (name.equals("Speak") && namespace.equals("SpeechSynthesizer")) {
//                                        Log.d("token", "SpeakDirective");
                                        if (payload.toString().contains("caption")) {
                                            audioData = new byte[0];
                                            sendAudioDataLen = -2;

                                            JSONObject caption = (JSONObject) payload.get("caption");
                                            String captionType = caption.getString("type");
                                            if (captionType.contains("WEBVTT")) {
                                                String captionContent = caption.getString("content");
                                                String[] contentSet = captionContent.split("\n");
                                                String contentMsgStr = "";
                                                for (int j = 0; j < contentSet.length; j ++) {
                                                    String curContentStr = contentSet[j];
                                                    if (curContentStr.contains(" --> ") || curContentStr.equals("WEBVTT") || curContentStr.length() < 3) {
                                                        continue;
                                                    }
                                                    contentMsgStr = contentMsgStr + curContentStr;
                                                }
                                                if (mListener != null) {
                                                    mListener.onAlexaStateChange(ALEXA_STATE_RECEIVE_TEXT, contentMsgStr);
                                                }
                                            }
                                        } else {
                                            new Thread(new Runnable() {
                                                @Override
                                                public void run() {
                                                    setSendAlexaSpeechAudio();
                                                }
                                            }).start();
//                                            if (mListener != null) {
//                                                mListener.onAlexaStateChange(ALEXA_STATE_RECEIVE_TEXT, "Didn't hear what was said");
//                                            }
                                        }
                                        if (payload.getString("token") != null) {
                                            token = payload.getString("token");
                                            Log.i(TAG, "onResponse: token------" + token);
                                            String url = payload.getString("url");
                                            Log.i(TAG, "onResponse: url------" + url);

//                                            tokenfrompayload = token;
                                        }
                                    }

                                } else if (contentType.equals(STREAM_TYPE)) {
                                    Log.i(TAG, "onResponse: --------" + STREAM_TYPE);
                                    if (mListener != null) {
                                        mListener.onAlexaStateChange(ALEXA_STATE_RECEIVE_DIRECTIVES_SPEAK, new byte[0]);
                                    }
                                    byte[] b = IOUtils.toByteArray(bodypart.getInputStream());



                                    String path = mContext.getExternalFilesDir("").getAbsolutePath() + "/" + "alexa_response.pcm";

                                    File mTemp = new File(path);
                                    mTemp.delete();
                                    mTemp = new File(path);
                                    FileOutputStream fos = new FileOutputStream(mTemp);
                                    fos.write(b);
                                    fos.close();

                                    if (mListener != null) {
                                        mListener.onAlexaStateChange(ALEXA_STATE_SPEECH_START, b);
                                    }
                                    Log.i(TAG, "onResponse: ----" + b.length);
                                }
                            }
                        } catch (MessagingException | JSONException e) {
                            throw new RuntimeException(e);
                        }
                    }
                }
            }
        });
    }

    private void setAlexaSpeechStartedEvent() {
        RequestBody requestBody = RequestBody.create(MediaType.parse(JSON_TYPE), String.valueOf(getAlexaSpeechStartedEventJson()));
        RequestBody multiBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("metadata", "metadata", requestBody)
                .build();
        Request requestStarted = new Request.Builder()
                .url(AVS_EVENTS_URL)
                .addHeader(AUTHORIZATION, BEARER + getCurAccess_token())
                .addHeader(CONTENT_TYPE, MULTI_DATA)
                .addHeader(BOUNDARY, BOUNDARY_VALUE)
                .post(multiBody)
                .build();
        mClient.newCall(requestStarted).enqueue(new Callback() {
            @Override
            public void onResponse(@NonNull Call call, @NonNull Response response) throws IOException {
                if (response.isSuccessful()) {
                    setSendAlexaSpeechAudio();
                    Log.i(TAG, "setAlexaSpeechStartedEvent: ------success");
                } else {
                    Log.i(TAG, "setAlexaSpeechStartedEvent: ------" + response.code());
                    InputStream inputStreamObject = response.body().byteStream();
                    BufferedReader streamReader = new BufferedReader(new InputStreamReader(inputStreamObject, "UTF-8"));
                    StringBuilder responseStrBuilder = new StringBuilder();
                    String inputStr;
                    while ((inputStr = (streamReader.readLine())) != null)
                        responseStrBuilder.append(inputStr);

                    Log.i(TAG, "onResponse: ------" + responseStrBuilder.toString());

                }
            }

            @Override
            public void onFailure(@NonNull Call call, @NonNull IOException e) {
                Log.i(TAG, "setAlexaSpeechStartedEvent: ------onFailure");

            }
        });
    }

    private void setAlexaSpeechFinishedEvent() {
        RequestBody requestBody = RequestBody.create(MediaType.parse(JSON_TYPE), String.valueOf(getAlexaSpeechFinishedEventJson()));
        RequestBody multiBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("metadata", "metadata", requestBody)
                .build();
        Request requestStarted = new Request.Builder()
                .url(AVS_EVENTS_URL)
                .addHeader(AUTHORIZATION, BEARER + getCurAccess_token())
                .addHeader(CONTENT_TYPE, MULTI_DATA)
                .addHeader(BOUNDARY, BOUNDARY_VALUE)
                .post(multiBody)
                .build();
        mClient.newCall(requestStarted).enqueue(new Callback() {
            @Override
            public void onResponse(@NonNull Call call, @NonNull Response response) throws IOException {
                if (response.isSuccessful()) {
                    setSendAlexaSpeechAudio();
                    Log.i(TAG, "setAlexaSpeechFinishedEvent: ------success");
                } else {
                    Log.i(TAG, "setAlexaSpeechFinishedEvent: ------" + response.code());
                    InputStream inputStreamObject = response.body().byteStream();
                    BufferedReader streamReader = new BufferedReader(new InputStreamReader(inputStreamObject, "UTF-8"));
                    StringBuilder responseStrBuilder = new StringBuilder();
                    String inputStr;
                    while ((inputStr = (streamReader.readLine())) != null)
                        responseStrBuilder.append(inputStr);

                    Log.i(TAG, "onResponse: ------" + responseStrBuilder.toString());

                }
            }

            @Override
            public void onFailure(@NonNull Call call, @NonNull IOException e) {
                Log.i(TAG, "setAlexaSpeechStartedEvent: ------onFailure");

            }
        });
    }

    private JSONObject getSpeechConfirmationChangedJson() {
        JSONObject eventHeader = createJsonObject(new String[]{"namespace", "name", "messageId"}, new Object[]{"SpeechRecognizer", "SpeechConfirmationChanged", "mMessageId4434"});
        JSONObject eventPayload = createJsonObject(new String[]{"speechConfirmation"}, new Object[]{"NONE"});
        JSONObject event = createJsonObject(new String[]{"header", "payload"}, new Object[]{eventHeader, eventPayload});

        JSONObject jsonObject = createJsonObject(new String[]{"event"}, new Object[]{event});
        return jsonObject;
    }

    private JSONObject getVerifyGatewayJson() {
        JSONObject eventHeader = createJsonObject(new String[]{"namespace", "name", "messageId"}, new Object[]{"Alexa.ApiGateway", "VerifyGateway", "BesVerifyGateway" + System.currentTimeMillis()});
        JSONObject directive = createJsonObject(new String[]{"header", "payload"}, new Object[]{eventHeader, new JSONObject()});

        JSONObject jsonObject = createJsonObject(new String[]{"event"}, new Object[]{directive});
        return jsonObject;
    }

    private JSONObject getAddOrUpdateReportJson() {
        JSONObject eventHeader = createJsonObject(new String[]{"namespace", "name", "payloadVersion", "messageId", "eventCorrelationToken"}, new Object[]{"Alexa.Discovery", "AddOrUpdateReport", "3", "getAddOrUpdateReportJson" + System.currentTimeMillis(), mMessageId});

        JSONObject eventPayloadScope = createJsonObject(new String[]{"type", "token"}, new Object[]{"BearerToken", getCurAccess_token()});
        JSONArray eventPayloadEndpointsJsonArr = new JSONArray();

        String CLIENT_ID = (String) SPHelper.getPreference(mContext, ALEXA_CLIENT_ID_KEY, "");
        String PRODUCT_ID = (String) SPHelper.getPreference(mContext, ALEXA_PRODUCT_ID_KEY, "");
        String DSN = (String) SPHelper.getPreference(mContext, ALEXA_DSN_KEY, "");
        String endpointId = CLIENT_ID + "::" + PRODUCT_ID + "::" + DSN;
        JSONObject registrationObject = createJsonObject(new String[]{"productId", "deviceSerialNumber"}, new Object[]{PRODUCT_ID, DSN});
        String manufacturerName = "Silent Voice Assistants";
        String description = "Interact with voice assistants without having to talk to them.";
        String friendlyName = "Silent Alexa";
        JSONArray displayCategoriesArr = new JSONArray();
        displayCategoriesArr.put("COMPUTER");
        displayCategoriesArr.put("LAPTOP");
        displayCategoriesArr.put("TABLET");
        JSONArray capabilitiesArr = new JSONArray();
        JSONObject capabilities = createJsonObject(new String[]{"type", "interface", "version"}, new Object[]{"AlexaInterface", "SpeechSynthesizer", "1.3"});
        capabilitiesArr.put(capabilities);
        JSONObject capabilities1 = createJsonObject(new String[]{"type", "interface", "version"}, new Object[]{"AlexaInterface", "Alexa.ApiGateway", "1.0"});
        capabilitiesArr.put(capabilities1);
        JSONObject capabilities2 = createJsonObject(new String[]{"type", "interface", "version"}, new Object[]{"AlexaInterface", "SpeechRecognizer", "2.3"});
        capabilitiesArr.put(capabilities2);
        JSONObject capabilities3 = createJsonObject(new String[]{"type", "interface", "version"}, new Object[]{"AlexaInterface", "System", "2.1"});
        capabilitiesArr.put(capabilities3);

        JSONArray connectionsArr = new JSONArray();
        JSONObject connections = createJsonObject(new String[]{"type", "value"}, new Object[]{"UNKNOWN", DSN});
        connectionsArr.put(connections);

        JSONObject eventPayloadEndpointsJsonObject = createJsonObject(new String[]{"endpointId", "registration", "manufacturerName", "description", "friendlyName", "displayCategories", "capabilities", "connections"}, new Object[]{endpointId, registrationObject, manufacturerName, description, friendlyName, displayCategoriesArr, capabilitiesArr, connectionsArr});
        eventPayloadEndpointsJsonArr.put(eventPayloadEndpointsJsonObject);

        JSONObject eventPayload = createJsonObject(new String[]{"scope", "endpoints"}, new Object[]{eventPayloadScope, eventPayloadEndpointsJsonArr});
        JSONObject event = createJsonObject(new String[]{"header", "payload"}, new Object[]{eventHeader, eventPayload});

        JSONObject jsonObject = createJsonObject(new String[]{"event"}, new Object[]{event});
        return jsonObject;
    }

    private JSONObject getSyncStateJson() {
        JSONObject contextHeader0 = createJsonObject(new String[]{"namespace", "name"}, new Object[]{"AudioPlayer", "PlaybackState"});
        JSONObject contextPayload0 = createJsonObject(new String[]{"token", "offsetInMilliseconds", "playerActivity"}, new Object[]{"", 0, "IDLE"});
        JSONObject context0 = createJsonObject(new String[]{"header", "payload"}, new Object[]{contextHeader0, contextPayload0});

        JSONObject contextHeader1 = createJsonObject(new String[]{"namespace", "name"}, new Object[]{"Alerts", "AlertsState"});
        JSONObject contextPayload1 = createJsonObject(new String[]{"activeAlerts", "allAlerts"}, new Object[]{new JSONArray(), new JSONArray()});
        JSONObject context1 = createJsonObject(new String[]{"header", "payload"}, new Object[]{contextHeader1, contextPayload1});

        JSONObject contextHeader2 = createJsonObject(new String[]{"namespace", "name"}, new Object[]{"Speaker", "VolumeState"});
        JSONObject contextPayload2 = createJsonObject(new String[]{"muted", "volume"}, new Object[]{false, "100"});
        JSONObject context2 = createJsonObject(new String[]{"header", "payload"}, new Object[]{contextHeader2, contextPayload2});

        JSONObject contextHeader3 = createJsonObject(new String[]{"namespace", "name"}, new Object[]{"SpeechSynthesizer", "SpeechState"});
        JSONObject contextPayload3 = createJsonObject(new String[]{"offsetInMilliseconds", "playerActivity", "token"}, new Object[]{0, "FINISHED", ""});
        JSONObject context3 = createJsonObject(new String[]{"header", "payload"}, new Object[]{contextHeader3, contextPayload3});

        JSONObject contextHeader4 = createJsonObject(new String[]{"namespace", "name"}, new Object[]{"SpeechRecognizer", "Recognize"});
        JSONObject context4 = createJsonObject(new String[]{"header", "payload"}, new Object[]{contextHeader4, new JSONObject()});

        JSONArray context = new JSONArray();
//        context.put("Alerts.AlertsState");
//        context.put("AudioPlayer.PlaybackState");
//        context.put("Speaker.VolumeState");
//        context.put("SpeechSynthesizer.SpeechState");
//        context.put("SpeechRecognizer.RecognizerState");
//        context.put(context0);
//        context.put(context1);
        context.put(context2);
        context.put(context3);
        context.put(context4);

        JSONObject eventHeader = createJsonObject(new String[]{"namespace", "name", "messageId"}, new Object[]{"System", "SynchronizeState", mMessageId});
        JSONObject eventPayload = new JSONObject();
        JSONObject event = createJsonObject(new String[]{"header", "payload"}, new Object[]{eventHeader, eventPayload});

        JSONObject jsonObject = createJsonObject(new String[]{"context", "event"}, new Object[]{context, event});
        return jsonObject;
    }

    private JSONObject getSendSpeechAudioJson() {
        JSONArray contextArr = new JSONArray();

//        JSONObject contextHeader0 = createJsonObject(new String[]{"namespace", "name"}, new Object[]{"AudioPlayer", "PlaybackState"});
//        JSONObject contextPayload0 = createJsonObject(new String[]{"token", "offsetInMilliseconds", "playerActivity"}, new Object[]{getCurAccess_token(), 0, ""});
//        JSONObject context0 = createJsonObject(new String[]{"header", "payload"}, new Object[]{contextHeader0, contextPayload0});
//        contextArr.put(context0);

//        JSONObject contextHeader1 = createJsonObject(new String[]{"namespace", "name"}, new Object[]{"SpeechRecognizer", "RecognizerState"});
//        JSONObject contextPayload1 = createJsonObject(new String[]{"token", "offsetInMilliseconds", "playerActivity"}, new Object[]{getCurAccess_token(), 0, ""});
//        JSONObject context1 = createJsonObject(new String[]{"header", "payload"}, new Object[]{contextHeader1, new JSONObject()});
//        contextArr.put(context1);

        JSONObject contextHeader2 = createJsonObject(new String[]{"namespace", "name"}, new Object[]{"Notifications", "IndicatorState"});
        JSONObject contextPayload2 = createJsonObject(new String[]{"isEnabled", "isVisualIndicatorPersisted"}, new Object[]{true, true});
        JSONObject context2 = createJsonObject(new String[]{"header", "payload"}, new Object[]{contextHeader2, contextPayload2});
        contextArr.put(context2);

        JSONObject contextHeader3 = createJsonObject(new String[]{"namespace", "name"}, new Object[]{"Speaker", "VolumeState"});
        JSONObject contextPayload3 = createJsonObject(new String[]{"volume", "muted"}, new Object[]{100, false});
        JSONObject context3 = createJsonObject(new String[]{"header", "payload"}, new Object[]{contextHeader3, contextPayload3});
        contextArr.put(context3);

//        JSONObject contextHeader4 = createJsonObject(new String[]{"namespace", "name"}, new Object[]{"SpeechSynthesizer", "SpeechState"});
//        JSONObject contextPayload4 = createJsonObject(new String[]{"token", "offsetInMilliseconds", "playerActivity"}, new Object[]{getCurAccess_token(), 0, "FINISHED"});
//        JSONObject context4 = createJsonObject(new String[]{"header", "payload"}, new Object[]{contextHeader4, contextPayload4});
//        contextArr.put(context4);

        JSONObject eventHeader = createJsonObject(new String[]{"namespace", "name", "messageId", "dialogRequestId"}, new Object[]{"SpeechRecognizer", "Recognize", "SendSpeechAudio" + System.currentTimeMillis(), mDialogRequestId});
        JSONObject eventPayload = createJsonObject(new String[]{"format", "profile", "startOfSpeechTimestamp"}, new Object[]{"AUDIO_L16_RATE_16000_CHANNELS_1", "NEAR_FIELD", mStartOfSpeechTimestamp});//AUDIO_L16_RATE_16000_CHANNELS_1 OPUS
        JSONObject event = createJsonObject(new String[]{"header", "payload", "type", "interface", "version"}, new Object[]{eventHeader, eventPayload, "AlexaInterface", "SpeechRecognizer", "2.3"});
        JSONObject jsonObject = createJsonObject(new String[]{"context", "event"}, new Object[]{contextArr, event});
        return jsonObject;
    }

    private JSONObject getAlexaSpeechStartedEventJson() {
        JSONObject eventHeader = createJsonObject(new String[]{"namespace", "name", "messageId"}, new Object[]{"SpeechSynthesizer", "SpeechStarted", mMessageId});
        JSONObject eventPayload = createJsonObject(new String[]{"token"}, new Object[]{getCurAccess_token()});
        JSONObject event = createJsonObject(new String[]{"header", "payload"}, new Object[]{eventHeader, eventPayload});

        JSONObject jsonObject = createJsonObject(new String[]{"event"}, new Object[]{event});
        return jsonObject;
    }

    private JSONObject getAlexaSpeechFinishedEventJson() {
        JSONObject eventHeader = createJsonObject(new String[]{"namespace", "name", "messageId"}, new Object[]{"SpeechSynthesizer", "SpeechFinished", mMessageId});
        JSONObject eventPayload = createJsonObject(new String[]{"token"}, new Object[]{""});
        JSONObject event = createJsonObject(new String[]{"header", "payload"}, new Object[]{eventHeader, eventPayload});

        JSONObject jsonObject = createJsonObject(new String[]{"event"}, new Object[]{event});
        return jsonObject;
    }

    private JSONArray getContextJsonObject() {
        JSONArray jsonArray = new JSONArray();
        JSONObject capabilities = createJsonObject(new String[]{"type", "interface", "version"}, new Object[]{"AlexaInterface", "SpeechSynthesizer", "1.3"});

        return jsonArray;
    }

    private JSONObject createJsonObject(String[] key, Object[] value) {
        JSONObject jsonObject = new JSONObject();
        if (key.length != value.length)
            return null;
        for (int i = 0; i < key.length; i ++) {
            try {
                jsonObject.put(key[i], value[i]);
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
        }
        return jsonObject;
    }

    private boolean isRecording = false;
    private RequestBody streamRequestBody = new RequestBody() {
        @Override
        public void writeTo(@NonNull BufferedSink bufferedSink) throws IOException {
            while (isRecording) {
                if (sendAudioDataLen < audioData.length) {
                    bufferedSink.write(getCurStreamData());
                }
            }
        }

        @Nullable
        @Override
        public MediaType contentType() {
            return MediaType.parse(STREAM_TYPE);
        }
    };

    private byte[] getCurStreamData() {
        int curL = audioData.length;
        int l = curL - sendAudioDataLen;
        byte[] d = new byte[l];
        System.arraycopy(audioData, sendAudioDataLen, d, 0, l);
        sendAudioDataLen = curL;

//        int curL = 320 * 10;
//        if (sendAudioDataLen + curL > audioData.length) {
//            curL = audioData.length - sendAudioDataLen;
//        }
//        byte[] d = new byte[curL];
//        System.arraycopy(audioData, sendAudioDataLen, d, 0, curL);
//        sendAudioDataLen += curL;
        return d;
    }

    public String getCurAccess_token() {
        ConnectivityManager connectivityManager
                = (ConnectivityManager) mContext.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
        if (activeNetworkInfo == null || !activeNetworkInfo.isConnected()) {
            curAlexaState = ALEXA_STATE_NO_NETWORK;
            if (mListener != null) {
                mListener.onAlexaStateChange(curAlexaState, new byte[0]);
            }
            return "";
        }
        String curTime = (System.currentTimeMillis() + "").substring(0, 10);
        String response = (String) SPHelper.getPreference(mContext, ALEXA_ACCESS_TOKEN_RESPONSE_KEY, "");

        if (response.length() > 0) {
            try {
                JSONObject jsonObjectResult = new JSONObject(response);
                String time = jsonObjectResult.getString("time");
                double diff = Integer.valueOf(curTime) - Integer.valueOf(time);
                if (diff < 60 * 50) {
                    curAlexaState = ALEXA_STATE_LOGIN_SUCCESS;
                    return jsonObjectResult.getString("access_token");
                }
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put("grant_type", "refresh_token");
                    jsonObject.put("refresh_token", jsonObjectResult.getString("refresh_token"));
                    jsonObject.put("client_id", jsonObjectResult.getString("client_id"));
                } catch (JSONException e) {
                    throw new RuntimeException(e);
                }
                if (diff < 60 * 59) {
                    curAlexaState = ALEXA_STATE_LOGIN_SUCCESS;
                    mHttpManager.requestPost(jsonObject, alexaUri, REQUEST_FOR_REFRESH_TOKEN);
                } else {
                    curAlexaState = ALEXA_STATE_LOGIN_REFRESH_TOKEN;
                    if (mListener != null) {
                        mListener.onAlexaStateChange(curAlexaState, new byte[0]);
                    }
                    mHttpManager.requestPost(jsonObject, alexaUri, REQUEST_FOR_REFRESH_TOKEN);
                }
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
        }
        return "";
    }
}
