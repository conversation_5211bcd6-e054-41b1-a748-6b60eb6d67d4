package com.besall.allbase.view.activity.chipstoollevel4.rssiextend;

import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.graphics.Color;
import android.text.method.ScrollingMovementMethod;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.scan.BtHeleper;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.R;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.service.rssi.RssiConstants;
import com.besall.allbase.bluetooth.service.rssiextend.RssiExtendConstants;
import com.besall.allbase.view.base.BaseActivity;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_ERROR;
import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_SUCCESS;
import static com.besall.allbase.bluetooth.BluetoothConstants.Scan.BES_SCAN_RESULT;
import static com.besall.allbase.bluetooth.service.rssi.RssiConstants.RSSI_START;

/**
 * <AUTHOR>
 * @time $ $
 */
public class RssiExtendActivity extends BaseActivity<IRssiExtendActivity, RssiExtendPresenter> implements IRssiExtendActivity, BesServiceListener, View.OnClickListener, View.OnFocusChangeListener {
    private static RssiExtendActivity instance;

    BluetoothDevice mDevice;
    HmDevice mHmDevice;
    BesServiceConfig mServiceConfig;

    private Button connect_spp;
    private Button rssi_read_start;
    private Button rssi_read_stop;
    private Button spp_stop;

    private EditText interval;
    private int intervel_num =1;
    private String RssiData[] = new String[29];


    private TextView left_tws_min_rssi;
    private TextView left_tws_max_rssi;
    private TextView left_tws_rssi;
    private TextView left_tws_agc;
    private TextView left_call_min_rssi;
    private TextView left_call_max_rssi;
    private TextView left_call_rssi;
    private TextView left_call_agc;
    private TextView right_tws_min_rssi;
    private TextView right_tws_max_rssi;
    private TextView right_tws_rssi;
    private TextView right_tws_agc;
    private TextView right_call_min_rssi;
    private TextView right_call_max_rssi;
    private TextView right_call_rssi;
    private TextView right_call_agc;
    private TextView left_packet_loss_rate;
    private TextView right_packet_loss_rate;
    private TextView rssi_extra_info;




    @Override
    protected RssiExtendPresenter createPresenter() {
        return new RssiExtendPresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        SPHelper.putPreference(instance, BesSdkConstants.BES_SAVE_LOG_NAME, "RSSI EXTENDED");

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_rssiextend;
    }

    @Override
    protected void bindView() {
        connect_spp = (Button)findViewById(R.id.connect_spp);
        rssi_read_start = (Button)findViewById(R.id.rssi_read_start);
        rssi_read_stop = (Button)findViewById(R.id.rssi_read_stop);
        spp_stop = (Button)findViewById(R.id.spp_stop);


        left_tws_min_rssi = (TextView)findViewById(R.id.left_tws_min_rssi);
        left_tws_max_rssi = (TextView)findViewById(R.id.left_tws_max_rssi);
        left_tws_rssi = (TextView)findViewById(R.id.left_tws_rssi);
        left_tws_agc = (TextView)findViewById(R.id.left_tws_agc);

        left_call_min_rssi = (TextView)findViewById(R.id.left_call_min_rssi);
        left_call_max_rssi = (TextView)findViewById(R.id.left_call_max_rssi);
        left_call_rssi = (TextView)findViewById(R.id.left_call_rssi);
        left_call_agc = (TextView)findViewById(R.id.left_call_agc);
        right_call_agc = (TextView)findViewById(R.id.right_call_agc);
        right_call_rssi = (TextView)findViewById(R.id.right_call_rssi);
        right_call_min_rssi = (TextView)findViewById(R.id.right_call_min_rssi);
        right_call_max_rssi = (TextView)findViewById(R.id.right_call_max_rssi);
        right_tws_min_rssi = (TextView)findViewById(R.id.right_tws_min_rssi);
        right_tws_max_rssi = (TextView)findViewById(R.id.right_tws_max_rssi);
        right_tws_rssi = (TextView)findViewById(R.id.right_tws_rssi);
        right_tws_agc = (TextView)findViewById(R.id.right_tws_agc);
        right_packet_loss_rate = (TextView) findViewById(R.id.right_packet_loss_rate);
        left_packet_loss_rate = (TextView) findViewById(R.id.left_packet_loss_rate);
        rssi_extra_info = (TextView)findViewById(R.id.rssi_extra_info);

        interval = (EditText)findViewById(R.id.interval);
        tv_title = (TextView) findViewById(R.id.tv_title);
        mToolbar = (Toolbar) findViewById(R.id.toolbar);

        logV = (TextView) findViewById(R.id.logV);
//        logV.setMovementMethod(ScrollingMovementMethod.getInstance());
    }

    @Override
    protected void initView() {
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        String titleStr = "RSSI EXTENDED";
        getSupportActionBar().setTitle(titleStr);

        connect_spp.setOnClickListener(instance);
        rssi_read_start.setOnClickListener(instance);
        rssi_read_stop.setOnClickListener(instance);
        spp_stop.setOnClickListener(instance);
        interval.setOnFocusChangeListener(instance);

        tv_title.setText("RSSI EXTENDED");
        mToolbar.setTitle("");

        tv_title.setOnClickListener(instance);

        getSupportActionBar().setHomeButtonEnabled(true);
        logV.setMovementMethod(ScrollingMovementMethod.getInstance());
        done.setOnClickListener(instance);
        scr_policy.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                v.getParent().requestDisallowInterceptTouchEvent(true);
                return false;
            }
        });
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data)
    {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == BluetoothConstants.Scan.REQUEST_CODE_SCAN) {
            onPickDevice(resultCode, data);
        }
    }

    private void onPickDevice(int resultCode, Intent data)
    {
        if (resultCode == RESULT_OK) {
            mHmDevice = (HmDevice) data.getSerializableExtra(BES_SCAN_RESULT);
            mDevice = BtHeleper.getBluetoothAdapter(instance).getRemoteDevice(mHmDevice.getPreferredProtocol() == DeviceProtocol.PROTOCOL_BLE ? mHmDevice.getBleAddress() : mHmDevice.getDeviceMAC());

            Log.i(TAG, "onPickDevice: " + mDevice.getName());
            Log.i(TAG, "onPickDevice: " + mDevice.getAddress());
            mServiceConfig = new BesServiceConfig();
            mServiceConfig.setServiceUUID(BesSdkConstants.BES_SPP_CONNECT);
            mServiceConfig.setDevice(mHmDevice);
            boolean totaEncryption = (boolean) SPHelper.getPreference(instance, BesSdkConstants.BES_TOTA_ENCRYPTION_KEY, BesSdkConstants.BES_TOTA_ENCRYPTION_VALUE);
            mServiceConfig.setTotaConnect(totaEncryption);
            boolean useTotaV2 = (boolean) SPHelper.getPreference(instance, BesSdkConstants.BES_TOTA_USE_TOTAV2, BesSdkConstants.BES_TOTA_USE_TOTAV2_VALUE);
            mServiceConfig.setUseTotaV2(useTotaV2);
            mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
            mPresenter.connectDevice(mServiceConfig, instance, instance);
        }
    }

    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_more, menu);

        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                mPresenter.stopReadRssi();
                mPresenter.stopSpp();
                finish();
                break;
            case R.id.is_usev2totaprotocol:
                Log.i(TAG, "onOptionsItemSelected: checked");
                SPHelper.putPreference(instance, RssiExtendConstants.RSSI_PROTOCOL, 0);
                rssi_read_start.setText("RSSI " + "(6306)");
                break;
            case R.id.is_useOprotocol:
                SPHelper.putPreference(instance, RssiExtendConstants.RSSI_PROTOCOL, 1);
                rssi_read_start.setText("RSSI " + "(6309)");
                break;
            case R.id.is_useoldprotocol:
                SPHelper.putPreference(instance, RssiExtendConstants.RSSI_PROTOCOL, 2);
                rssi_read_start.setText("RSSI " + "(900b)");
                break;

        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.connect_spp:
                mPresenter.pickDecice(instance, BluetoothConstants.Scan.SCAN_SPP);
                break;
            case R.id.rssi_read_start:
                if(interval.getText().toString()== null||interval.getText().toString().trim()==""||interval.getText().toString().trim()==" ")
                {
                    interval.setText("1");
                }
                else {
                    if(Integer.valueOf(interval.getText().toString())>10||Integer.valueOf(interval.getText().toString())<0) {
                        interval.setText("1");
                    }
                    else {
                        intervel_num = Integer.parseInt(interval.getText().toString());
                    }
                }
                String rssi = interval.getText().toString();
                mPresenter.startReadRssi(rssi, instance);
                break;
            case R.id.rssi_read_stop:
                mPresenter.stopReadRssi();

                break;
            case R.id.spp_stop:
                mPresenter.stopSpp();
                reSetdata();
                break;
            case R.id.done:
                Log.i(TAG, "onClick: 112313132");
                loginfo.setVisibility(View.GONE);
                break;
            case R.id.tv_title:
                loginfo.setVisibility(View.VISIBLE);
                break;
            default:
                break;

        }

    }

    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (state == false) {
                    connect_spp.setText("  tota error");
                }
            }
        });
    }

    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {

    }

    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {
        Log.i(TAG, "onStateChangedMessage: +" + msgStr);
        if(msgStr =="error"){
            return;
        }
        if (msg == BesSdkConstants.TOTA_LOG_INFO) {
            addlog(msgStr);
        }

        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if(msg == RSSI_START){
                    RssiData = msgStr.split(";");
                    int flag = 0;
                    left_call_agc.setText(RssiData[0].replace(":"," "));
                    left_call_rssi.setText(RssiData[1].replace(":"," "));
                    left_call_min_rssi.setText(RssiData[3].replace(":"," "));
                    left_call_max_rssi.setText(RssiData[2].replace(":"," "));
                    left_tws_agc.setText(RssiData[4].replace(":"," "));
                    left_tws_rssi.setText(RssiData[5].replace(":"," "));
                    left_tws_min_rssi.setText(RssiData[7].replace(":"," "));
                    left_tws_max_rssi.setText(RssiData[6].replace(":"," "));
                    left_packet_loss_rate.setText(RssiData[8].replace(":"," "));

                    right_call_agc.setText(RssiData[9].replace(":"," "));
                    right_call_rssi.setText(RssiData[10].replace(":"," "));
                    right_call_min_rssi.setText(RssiData[12].replace(":"," "));
                    right_call_max_rssi.setText(RssiData[11].replace(":"," "));
                    right_tws_agc.setText(RssiData[13].replace(":"," "));
                    right_tws_rssi.setText(RssiData[14].replace(":"," "));
                    right_tws_min_rssi.setText(RssiData[16].replace(":"," "));
                    right_tws_max_rssi.setText(RssiData[15].replace(":"," "));
                    right_packet_loss_rate.setText(RssiData[17].replace(":"," "));
                    flag = Integer.parseInt(RssiData[18].replace("flag:","").toString());
                    rssi_extra_info.setText(RssiData[19]);

                    if(flag == 1)
                    {
                        left_call_agc.setTextColor(Color.BLACK);
                        left_call_rssi.setTextColor(Color.BLACK);
                        left_call_min_rssi.setTextColor(Color.BLACK);
                        left_call_max_rssi.setTextColor(Color.BLACK);
                        left_tws_agc.setTextColor(Color.BLACK);
                        left_tws_rssi.setTextColor(Color.BLACK);
                        left_tws_min_rssi.setTextColor(Color.BLACK);
                        left_tws_max_rssi.setTextColor(Color.BLACK);
                        left_packet_loss_rate.setTextColor(Color.BLACK);
                        right_call_agc.setTextColor(Color.BLUE);
                        right_call_rssi.setTextColor(Color.BLUE);
                        right_call_min_rssi.setTextColor(Color.BLUE);
                        right_call_max_rssi.setTextColor(Color.BLUE);
                        right_tws_agc.setTextColor(Color.BLUE);
                        right_tws_rssi.setTextColor(Color.BLUE);
                        right_tws_min_rssi.setTextColor(Color.BLUE);
                        right_tws_max_rssi.setTextColor(Color.BLUE);
                        right_packet_loss_rate.setTextColor(Color.BLUE);
                    }
                    else
                    {
                        left_call_agc.setTextColor(Color.BLUE);
                        left_call_rssi.setTextColor(Color.BLUE);
                        left_call_min_rssi.setTextColor(Color.BLUE);
                        left_call_max_rssi.setTextColor(Color.BLUE);
                        left_tws_agc.setTextColor(Color.BLUE);
                        left_tws_rssi.setTextColor(Color.BLUE);
                        left_tws_min_rssi.setTextColor(Color.BLUE);
                        left_tws_max_rssi.setTextColor(Color.BLUE);
                        left_packet_loss_rate.setTextColor(Color.BLUE);
                        right_call_agc.setTextColor(Color.BLACK);
                        right_call_rssi.setTextColor(Color.BLACK);
                        right_call_min_rssi.setTextColor(Color.BLACK);
                        right_call_max_rssi.setTextColor(Color.BLACK);
                        right_tws_agc.setTextColor(Color.BLACK);
                        right_tws_rssi.setTextColor(Color.BLACK);
                        right_tws_min_rssi.setTextColor(Color.BLACK);
                        right_tws_max_rssi.setTextColor(Color.BLACK);
                        right_packet_loss_rate.setTextColor(Color.BLACK);
                    }

                } else if (msg == BES_CONNECT_SUCCESS) {
                    connect_spp.setText("  spp connect");
                } else if (msg == BES_CONNECT_ERROR) {
                    connect_spp.setText("  Click To Connect");
                }
            }
        });
    }

    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {

    }


    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        if (hasFocus == true) {
            return;
        }
        else
        {
            int num = Integer.parseInt(interval.getText().toString());
            if (num < 1) {
                interval.setText("1");
            }
            else if(num>10)
            {
                interval.setText("10");
            }
            intervel_num =  Integer.parseInt(interval.getText().toString());
        }
    }

    public void reSetdata() {

        left_call_min_rssi.setText("MIN_RSSI_0");
        left_call_agc.setText("AGC_0");
        left_call_max_rssi.setText("MAX_RSSI_0");
        left_call_rssi.setText("RSSI_0");
        left_tws_agc.setText("AGC_0");
        left_tws_rssi.setText("RSSI_0");
        left_tws_min_rssi.setText("MIN_RSSI_0");
        left_tws_max_rssi.setText("MAX_RSSI_0");
        right_call_min_rssi.setText("MIN_RSSI_0");
        right_call_agc.setText("AGC_0");
        right_tws_agc.setText("AGC_0");
        right_tws_min_rssi.setText("MIN_RSSI_0");
        right_tws_max_rssi.setText("MAX_RSSI_0");
        right_call_max_rssi.setText("MAX_RSSI_0");
        right_call_rssi.setText("RSSI_0");
        right_tws_rssi.setText("RSSI_0");
        right_packet_loss_rate.setText("PACKET LOSS RATE(%):0" );
        left_packet_loss_rate.setText("PACKET LOSS RATE(%):0" );
        rssi_extra_info.setText("RSSI EXTRA INFO:--");

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mPresenter.stopSpp();
    }


}
