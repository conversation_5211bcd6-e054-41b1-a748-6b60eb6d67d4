package com.besall.allbase.view.activity.chipstoollevel4.SmartVoice;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_SUCCESS;
import static com.bes.bessdk.BesSdkConstants.BesConnectState.BES_CONNECT;
import static com.besall.allbase.bluetooth.BluetoothConstants.Scan.BES_SCAN_RESULT;
import static com.besall.allbase.bluetooth.BluetoothConstants.Scan.SCAN_BLE;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.graphics.Color;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.connect.BleConnector;
import com.bes.bessdk.scan.BtHeleper;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.R;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.view.base.BaseActivity;

public class SmartVoiceActivity extends BaseActivity<ISmartVoiceActivity, SmartVoicePresenter> implements ISmartVoiceActivity, View.OnClickListener, BesServiceListener {

    private static SmartVoiceActivity instance;

    BluetoothDevice mDevice;
    HmDevice mHmDevice;
    BesServiceConfig mServiceConfig;

    private Button pickDevice, connect_device;
    private TextView ble_name;


    @Override
    protected SmartVoicePresenter createPresenter() {
        return new SmartVoicePresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        //log
        SPHelper.putPreference(instance, BesSdkConstants.BES_SAVE_LOG_NAME, "SmartVoice");

        mServiceConfig = new BesServiceConfig();
        mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_BLE);
        mServiceConfig.setServiceUUID(BesSdkConstants.BES_OTA_SERVICE_OTA_UUID);
        mServiceConfig.setCharacteristicsUUID(BesSdkConstants.BES_OTA_CHARACTERISTIC_OTA_UUID);
        mServiceConfig.setDescriptorUUID(BesSdkConstants.BES_OTA_DESCRIPTOR_OTA_UUID);
        mServiceConfig.setTotaConnect(false);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.act_smartvoice;
    }

    @Override
    protected void bindView() {
        pickDevice = (Button)findViewById(R.id.pick_device);
        connect_device = (Button)findViewById(R.id.connect_device);
        ble_name = (TextView) findViewById(R.id.ble_name);
    }

    @SuppressLint("ResourceAsColor")
    @Override
    protected void initView() {
        String titleStr = "SmartVoice";
        tv_title.setText(titleStr);
        mToolbar.setTitle("");
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);

        pickDevice.setOnClickListener(instance);
        connect_device.setOnClickListener(instance);
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.pick_device:
                mPresenter.pickDecice(instance, SCAN_BLE);
                break;
            case R.id.connect_device:
                mPresenter.connectDevice(mServiceConfig, instance, instance);
                break;
            default:
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == BluetoothConstants.Scan.REQUEST_CODE_SCAN) {
            onPickDevice(resultCode, data);
        }
    }

    private void onPickDevice(int resultCode, Intent data) {
        if (resultCode == RESULT_OK) {
            mHmDevice = (HmDevice) data.getSerializableExtra(BES_SCAN_RESULT);
            mDevice = BtHeleper.getBluetoothAdapter(instance).getRemoteDevice(mHmDevice.getPreferredProtocol() == DeviceProtocol.PROTOCOL_BLE ? mHmDevice.getBleAddress() : mHmDevice.getDeviceMAC());

            mServiceConfig.setDevice(mHmDevice);
            BesSdkConstants.BesConnectState state = BleConnector.getsConnector(instance, null, null).getDeviceConnectState(mServiceConfig);
            if (state == BES_CONNECT) {
                ble_name.setTextColor(instance.getResources().getColor(R.color.green));
                mPresenter.connectDevice(mServiceConfig, instance, instance);
            } else {
                ble_name.setTextColor(Color.DKGRAY);
            }
            ble_name.setText(mDevice.getName());
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }


    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {

    }

    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                ble_name.setTextColor(Color.RED);
            }
        });
    }

    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (msg == BES_CONNECT_SUCCESS) {
                    ble_name.setTextColor(getResources().getColor(R.color.green));
                }

            }
        });
    }

    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {

    }
}
