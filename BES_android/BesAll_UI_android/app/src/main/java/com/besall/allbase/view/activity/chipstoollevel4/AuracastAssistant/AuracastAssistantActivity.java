package com.besall.allbase.view.activity.chipstoollevel4.AuracastAssistant;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_ERROR;
import static com.besall.allbase.bluetooth.BluetoothConstants.Scan.BES_SCAN_RESULT;
import static com.besall.allbase.bluetooth.BluetoothConstants.Scan.SCAN_BLE;
import static com.besall.allbase.bluetooth.BluetoothConstants.Scan.SCAN_SPP;
import static com.besall.allbase.bluetooth.service.auracastassistant.AuracastAssistantConstants.AURACAST_ASSISTANT_RECEIVE_DATA;
import static com.besall.allbase.bluetooth.service.auracastassistant.AuracastAssistantConstants.AURACAST_ASSISTANT_RECEIVE_NUMBER_OF_BIS;
import static com.besall.allbase.bluetooth.service.auracastassistant.AuracastAssistantConstants.AURACAST_ASSISTANT_RECEIVE_SAMPLING_RATE;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;

import androidx.annotation.UiThread;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.scan.BtHeleper;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.R;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.activity.tools.confirmdialog.BisCodeDialog;
import com.besall.allbase.view.activity.tools.confirmdialog.BisCodeDialoglistener;
import com.besall.allbase.view.activity.tools.confirmdialog.ConfirmDialog;
import com.besall.allbase.view.activity.tools.confirmdialog.ConfirmDialoglistener;
import com.besall.allbase.view.base.BaseActivity;
import com.google.zxing.integration.android.IntentIntegrator;
import com.google.zxing.integration.android.IntentResult;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time $ $
 */
public class AuracastAssistantActivity extends BaseActivity<IAuracastAssistantActivity, AuracastAssistantPresenter> implements IAuracastAssistantActivity, BesServiceListener, View.OnClickListener, IAuracastAssistantPresenter.BisScanListener, AdapterView.OnItemClickListener {
    private static AuracastAssistantActivity instance;

    public String cur_title = "Auracast Assistant";

    BluetoothDevice mDevice;
    HmDevice mHmDevice;
    BesServiceConfig mServiceConfig;

    private Button pick_device, pick_spp_device;
    private Button connect_device;
    private TextView device_address;
    private TextView device_name;

    TextView scan_QR_result;
    Button scan_complete, refresh, scan_QR;
    ListView bis_devices;

    BisDeviceAdapter adapter;

    public String QRresult = "";

    private String HISTORY_DEVICE_LIST_KEY = "HISTORY_DEVICE_LIST_KEY";

    @Override
    protected AuracastAssistantPresenter createPresenter() {
        return new AuracastAssistantPresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        mServiceConfig = new BesServiceConfig();

//        mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
//        mServiceConfig.setServiceUUID(BesSdkConstants.BES_SPP_CONNECT);
        mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_BLE);
        mServiceConfig.setServiceUUID(BesSdkConstants.BES_TOTA_SERVICE_OTA_UUID);
        mServiceConfig.setCharacteristicsUUID(BesSdkConstants.BES_TOTA_CHARACTERISTI_OTA_UUID);
        mServiceConfig.setDescriptorUUID(BesSdkConstants.BES_TOTA_DESCRIPTOR_OTA_UUID);
        mServiceConfig.setUSER_FLAG(0);
        mServiceConfig.setTotaConnect(true);
        mServiceConfig.setUseTotaV2(true);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.act_connect;
    }

    @Override
    protected void bindView() {
        mBindView();
    }

    private void mBindView() {
        pick_device = (Button)findViewById(R.id.pick_device_ble);
        pick_spp_device = (Button)findViewById(R.id.pick_device);
        connect_device = (Button)findViewById(R.id.connect_device);
        device_address = (TextView) findViewById(R.id.device_address);
        device_name = (TextView) findViewById(R.id.device_name);
    }

    @Override
    protected void initView() {
        mInitView();
    }

    private void mInitView() {
        inittoolbar(cur_title);
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);
        loadanimdrawable();

        pick_spp_device.setVisibility(View.GONE);
        pick_device.setVisibility(View.VISIBLE);
        pick_device.setOnClickListener(instance);
        connect_device.setOnClickListener(instance);
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mPresenter.stopScanBisAdv();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode != RESULT_OK) {
            return;
        }
        if (requestCode == BluetoothConstants.Scan.REQUEST_CODE_SCAN) {
            onPickDevice(resultCode, data);
        }

        IntentResult result = IntentIntegrator.parseActivityResult(requestCode, resultCode, data);
        if (result != null) {
            if (result.getContents() == null) {
                ActivityUtils.showToast("扫描取消");
            } else {
                QRresult = result.getContents();
                // 将扫描结果显示在界面上或进行其他处理
                scan_QR_result.setText(QRresult);
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        mPresenter.sendQRData(ArrayUtil.toBytes(ArrayUtil.str2HexStr(QRresult)));
                    }
                });

            }
        }
    }

    @SuppressLint("MissingPermission")
    private void onPickDevice(int resultCode, Intent data) {
        if (resultCode == RESULT_OK) {
            mHmDevice = (HmDevice) data.getSerializableExtra(BES_SCAN_RESULT);
            mDevice = BtHeleper.getBluetoothAdapter(instance).getRemoteDevice(mHmDevice.getPreferredProtocol() == DeviceProtocol.PROTOCOL_BLE ? mHmDevice.getBleAddress() : mHmDevice.getDeviceMAC());

            device_address.setText(mDevice.getAddress());
            device_name.setText(mDevice.getName() == null ? "null" : mDevice.getName());
        }
    }

    @Override
    public void onBackPressed() {
//        super.onBackPressed();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                if (tv_title.getText().equals(cur_title)) {
                   resetUI();

                } else {
                    initLayout(false);
                }
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    private void resetUI() {
        resetDataList();

        mPresenter.disconnect();
        setContentView(R.layout.act_connect);

        mBindView();
        mInitView();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.pick_device_ble:
                mPresenter.pickDecice(instance, SCAN_BLE);
//                mPresenter.startScanBisAdv(instance, instance);

                break;
            case R.id.connect_device:
//                initLayout(true);
                if (mHmDevice == null) {
                    return;
                }
                loadinganim();
                mServiceConfig.setDevice(mHmDevice);
                mPresenter.connectDevice(mServiceConfig, instance, instance);
                break;
            case R.id.refresh:
                resetDataList();

                mPresenter.startScanBisAdv(instance, instance);
                break;
            case R.id.scan_complete:
                mPresenter.stopScanBisAdv();
                break;
            case R.id.leave_btn:
                Log.i(TAG, "leave_btn onClick: --------");
                showComfirmDialog();
                break;
            case R.id.scan_QR:
                scanQR();


                break;
            default:
                break;
        }
    }

    private void resetDataList() {
        mPresenter.stopScanBisAdv();
        deviceList = new ArrayList<>();
        nameList = new ArrayList<>();
        dataList = new ArrayList<>();
        statusList = new ArrayList<>();
        bisPlayList = new ArrayList<>();
        bisInfoList = new ArrayList<>();
        selectIndex = -1;
    }
    public void scanQR() {
        IntentIntegrator intentIntegrator = new IntentIntegrator(instance);
        intentIntegrator.setOrientationLocked(true);
        intentIntegrator.setBeepEnabled(false);
        intentIntegrator.initiateScan();
    }



    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {
        Log.i(TAG, "onErrorMessage: ------------");

    }

    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (msg == BES_CONNECT_ERROR) {
                    loadingDialog.dismiss();

                    resetDataList();

                    setContentView(R.layout.act_connect);

                    mBindView();
                    mInitView();
                } else if (msg == AURACAST_ASSISTANT_RECEIVE_SAMPLING_RATE) {
                    if (selectIndex == -1) {
                        return;
                    }
                    bisInfoList.remove(selectIndex);
                    bisInfoList.add(selectIndex, true);
                    statusList.remove(selectIndex);
                    statusList.add(selectIndex, "Joined");
                    bisPlayList.remove(selectIndex);
                    bisPlayList.add(selectIndex, true);
                    adapter.notifyDataSetChanged();

                    sampling_rate_str = msgStr;
                    if (sampling_rate != null) {
                        sampling_rate.setText(sampling_rate_str);
                    }
                } else if (msg == AURACAST_ASSISTANT_RECEIVE_NUMBER_OF_BIS) {
                    number_of_channel_str = msgStr + (msgStr.equals("1") ? "(MONO)" : "(Left,Right)");
                    if (number_of_channel != null) {
                        number_of_channel.setText(number_of_channel_str);
                    }
                }
            }
        });
    }

    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {
        Log.i(TAG, "onSuccessMessage: ------------");

    }

    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                loadingDialog.dismiss();

                if (state == true) {
                    initLayout(true);
                } else {

                }
            }
        });
    }

    public void initLayout(boolean needScan) {
        setContentView(R.layout.activity_auracastassistant);
        instance = this;
        inittoolbar(cur_title);
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);

        refresh = (Button) findViewById(R.id.refresh);
        scan_complete = (Button) findViewById(R.id.scan_complete);
        bis_devices = (ListView) findViewById(R.id.bis_devices);
        scan_QR = (Button) findViewById(R.id.scan_QR);
        scan_QR_result = (TextView) findViewById(R.id.scan_QR_result);
        refresh.setOnClickListener(instance);
        scan_complete.setOnClickListener(instance);
        scan_QR.setOnClickListener(instance);
        adapter = new BisDeviceAdapter();
        bis_devices.setOnItemClickListener(instance);
        bis_devices.setAdapter(adapter);

        if (needScan) {
            mPresenter.startScanBisAdv(instance, instance);
        } else {
            adapter.notifyDataSetChanged();
        }
    }

    private List<BluetoothDevice> deviceList = new ArrayList<>();
    private List<String> nameList = new ArrayList<>();
    private List<byte[]> dataList = new ArrayList<>();
    private List<String> bitStrList = new ArrayList<>();
    private List<String> statusList = new ArrayList<>();
    private List<Boolean> bisPlayList = new ArrayList<>();
    private List<Boolean> bisInfoList = new ArrayList<>();

    private int selectIndex = -1;

    @Override
    public void onBisScanResult(BluetoothDevice device, String name, String bitStr, byte[] data) {
        if (!deviceList.contains(device)) {
            String historyDeviceList = (String) SPHelper.getPreference(instance, HISTORY_DEVICE_LIST_KEY, "");
            if (historyDeviceList.contains(name)) {
                deviceList.add(0, device);
                nameList.add(0, name);
                dataList.add(0, data);
                bitStrList.add(0, bitStr);
                statusList.add(0, "");
                bisPlayList.add(0, false);
                bisInfoList.add(0, false);
            } else {
                deviceList.add(device);
                nameList.add(name);
                dataList.add(data);
                bitStrList.add(bitStr);
                statusList.add("");
                bisPlayList.add(false);
                bisInfoList.add(false);
            }
            adapter.notifyDataSetChanged();
        }
    }

    @SuppressLint("MissingPermission")
    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        mPresenter.stopScanBisAdv();

        if (position == selectIndex) {
            return;
        }
        Log.i(TAG, "onItemClick selectIndex0: ----->" + selectIndex);
        if (selectIndex != -1) {
            mPresenter.sendStopData(dataList.get(selectIndex));
            bisInfoList.remove(selectIndex);
            bisInfoList.add(selectIndex, false);
            bisPlayList.remove(selectIndex);
            bisPlayList.add(selectIndex, false);
            statusList.remove(selectIndex);
            statusList.add(selectIndex, "");
            selectIndex = -1;
            initLayout(false);
        }
        String historyDeviceList = (String) SPHelper.getPreference(instance, HISTORY_DEVICE_LIST_KEY, "");
        if (!historyDeviceList.contains(nameList.get(position))) {
            historyDeviceList = historyDeviceList + "<-->" + nameList.get(position);
            SPHelper.putPreference(instance, HISTORY_DEVICE_LIST_KEY, historyDeviceList);
        }
        adapter.addSelectState(position);
        infoViewTitle = nameList.get(selectIndex);
        byte[] data = dataList.get(selectIndex);
        broadcast_id_str = "0x" + ArrayUtil.toHex(new byte[]{data[0], data[1], data[2]}).replace(",", "");

        String bitStr = bitStrList.get(selectIndex);
        if (bitStr.substring(bitStr.length() - 1, bitStr.length()).equals("1")) {
            adapter.notifyDataSetChanged();
            showBisCodeDialog("Please Enter Your Broadcast Code");
            return;
        }
        mPresenter.sendJoinData(dataList.get(selectIndex), new byte[0]);

        adapter.notifyDataSetChanged();
    }

    public class BisDeviceAdapter extends BaseAdapter implements View.OnClickListener {

        @Override
        public void onClick(View v) {
            Log.i(TAG, "bis_btn_info: ---BisDeviceAdapter onClick: -------------" + v.getTag());
            if ((int)(v.getTag()) > 19999) {
                mPresenter.sendStopData(dataList.get(selectIndex));
                bisInfoList.remove(selectIndex);
                bisInfoList.add(selectIndex, false);
                bisPlayList.remove(selectIndex);
                bisPlayList.add(selectIndex, false);
                statusList.remove(selectIndex);
                statusList.add(selectIndex, "");
                selectIndex = -1;
                adapter.notifyDataSetChanged();
            }
            else if ((int)(v.getTag()) < 10000) {
                initInfoLayoutView();
            } else {
                Log.i(TAG, "bis_btn_info: ---BisDeviceAdapter onClick: -------------" + v.getTag());
                mPresenter.refreshJoinState();
            }
        }

        @Override
        public int getCount() {
            return deviceList.size();
        }

        @Override
        public Object getItem(int position) {
            return position;
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @SuppressLint("MissingPermission")
        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            ViewHolder holder;
            if (convertView == null) {
                convertView = LayoutInflater.from(instance).inflate(R.layout.bis_device_item, parent, false);
                holder = new ViewHolder();
                holder.mName = (TextView) convertView.findViewById(R.id.name);
                holder.mStatus = (TextView) convertView.findViewById(R.id.status);
                holder.join_mark_text = (TextView) convertView.findViewById(R.id.join_mark_text);
                holder.join_mark_button = (Button) convertView.findViewById(R.id.join_mark_button);

                holder.bis_icon_need_code = (ImageView) convertView.findViewById(R.id.bis_icon_need_code);
                holder.bis_icon_sound = (ImageView) convertView.findViewById(R.id.bis_icon_sound);
                holder.bis_btn_info = (ImageView) convertView.findViewById(R.id.bis_btn_info);
                holder.bis_icon_exit= (ImageView) convertView.findViewById(R.id.bis_icon_exit);

                convertView.setTag(holder);
            } else {
                holder = (ViewHolder) convertView.getTag();
            }
            holder.bis_icon_need_code.setVisibility(View.GONE);
            holder.bis_icon_sound.setVisibility(bisPlayList.get(position) ? View.VISIBLE : View.GONE);
            holder.bis_btn_info.setVisibility(bisInfoList.get(position) ? View.VISIBLE : View.GONE);

            holder.mName.setText(nameList.get(position));
            holder.mStatus.setText(statusList.get(position));

            if (selectIndex == position && statusList.get(position).length() == 0) {
                holder.join_mark_text.setVisibility(View.VISIBLE);
//                holder.join_mark_button.setVisibility(View.VISIBLE);
//                holder.join_mark_button.setTag(10000 + position);
//                holder.join_mark_button.setOnClickListener(adapter);
                holder.join_mark_button.setVisibility(View.GONE);
            } else {
                holder.join_mark_text.setVisibility(View.GONE);
                holder.join_mark_button.setVisibility(View.GONE);
            }
            if (selectIndex == position) {
                holder.bis_icon_exit.setVisibility(View.VISIBLE);
                holder.bis_icon_exit.setTag(20000 + position);
                holder.bis_icon_exit.setOnClickListener(adapter);

            } else {
                holder.bis_icon_exit.setVisibility(View.GONE);
            }

            holder.bis_btn_info.setTag(100 + position);
            holder.bis_btn_info.setOnClickListener(adapter);

            String bitStr = bitStrList.get(position);
            if (bitStr.substring(bitStr.length() - 1, bitStr.length()).equals("1")) {
                holder.bis_icon_need_code.setVisibility(View.VISIBLE);
            }
            return convertView;
        }

        @UiThread
        public void addSelectState(int index) {
            selectIndex = index;
        }

        private class ViewHolder {
            TextView mName;
            TextView mStatus;
            TextView join_mark_text;
            Button join_mark_button;

            ImageView bis_icon_need_code;
            ImageView bis_icon_sound;
            ImageView bis_btn_info;
            ImageView bis_icon_exit;
        }
    }

    private void showBisCodeDialog(String msg) {
        BisCodeDialog bisCodeDialog = new BisCodeDialog(instance, msg, new BisCodeDialoglistener() {
            @Override
            public void confirmYesWithText(BisCodeDialog obj, String psw) {
                Log.i(TAG, "confirmYesWithText: --------" + psw);
//                while (psw.length() < 16) {
//                    psw = psw + "0";
//                }

                byte[] pswBytes = new byte[16];
                byte[] data = ArrayUtil.toBytes(ArrayUtil.str2HexStr(psw));
                System.arraycopy(data, 0, pswBytes, 0, data.length);
                mPresenter.sendJoinData(dataList.get(selectIndex), pswBytes);
                obj.dismiss();
            }

            @Override
            public void confirmNo() {
                bisInfoList.remove(selectIndex);
                bisInfoList.add(selectIndex, false);
                bisPlayList.remove(selectIndex);
                bisPlayList.add(selectIndex, false);
                selectIndex = -1;
                initLayout(false);
            }
        });
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                bisCodeDialog.show();
            }
        });
    }

    private void showComfirmDialog() {
        ConfirmDialog confirmDialog = new ConfirmDialog(instance, "Do you want to leave this source?", new ConfirmDialoglistener() {
            @Override
            public void confirmYes() {
                mPresenter.sendStopData(dataList.get(selectIndex));
                bisInfoList.remove(selectIndex);
                bisInfoList.add(selectIndex, false);
                bisPlayList.remove(selectIndex);
                bisPlayList.add(selectIndex, false);
                statusList.remove(selectIndex);
                statusList.add(selectIndex, "");
                selectIndex = -1;
                initLayout(false);
            }

            @Override
            public void confirmNo() {

            }
        });
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                confirmDialog.show();
            }
        });
    }

    private TextView broadcast_id;
    private TextView number_of_channel;
    private TextView sampling_rate;
    private Button leave_btn;

    private String infoViewTitle = "";
    private String broadcast_id_str = "";
    private String number_of_channel_str = "";
    private String sampling_rate_str = "";

    private void initInfoLayoutView() {
        setContentView(R.layout.activity_auracastassistantinfo);
        instance = this;

//        inittoolbar(nameList.get(selectIndex));
        inittoolbar(infoViewTitle);
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);

        broadcast_id = (TextView) findViewById(R.id.broadcast_id);
        number_of_channel = (TextView) findViewById(R.id.number_of_channel);
        sampling_rate = (TextView) findViewById(R.id.sampling_rate);
        leave_btn = (Button) findViewById(R.id.leave_btn);
        leave_btn.setOnClickListener(instance);

        broadcast_id.setText(broadcast_id_str);
        number_of_channel.setText(number_of_channel_str);
        sampling_rate.setText(sampling_rate_str);
    }
}
