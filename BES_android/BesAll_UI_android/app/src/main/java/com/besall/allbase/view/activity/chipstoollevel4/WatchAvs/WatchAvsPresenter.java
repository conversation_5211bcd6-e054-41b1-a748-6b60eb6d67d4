package com.besall.allbase.view.activity.chipstoollevel4.WatchAvs;

import android.Manifest;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.media.AudioFormat;
import android.media.AudioManager;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.util.Log;

import androidx.core.app.ActivityCompat;

import com.amazon.alexa.accessory.protocol.Accessories;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;
import com.besall.allbase.bluetooth.service.watchavs.WatchAvsService;
import com.besall.allbase.common.manager.AlexaHttpManager;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.common.utils.FileUtils;
import com.besall.allbase.view.base.BasePresenter;

import java.util.Map;


public class WatchAvsPresenter extends BasePresenter<IWatchAvsActivity> implements IWatchAvsPresenter {
    static String TAG = "WatchAvsPresenter";
    private WatchAvsService watchAvsService;

    @Override
    public void pickDecice(WatchAvsActivity context, int scan) {
        Intent intent = new Intent();
        intent.putExtra(BluetoothConstants.Scan.BES_SCAN, scan);
        ActivityUtils.gotoActForResult(intent, BluetoothConstants.Scan.REQUEST_CODE_SCAN, context, ScanActivity.class);
    }

    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        watchAvsService = new WatchAvsService(serviceConfig, listener, context);
    }

    @Override
    public Map<String, String> getProductSid() {
        return null;
    }

    @Override
    public void startRecord(Activity activity, Context context, recordStateListener listener, AlexaHttpManager manager) {
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(activity, new String[]{Manifest.permission.RECORD_AUDIO}, 1);
            return;
        }

        openSco(context, listener, manager);
    }

    @Override
    public void stopRecord(Context context) {
        stopSco(context);
    }

    AudioManager mAudioManager;

    int frequency = 16000;
    boolean isPlaying = false;
    int micReceiveDtaCount = 0;
    Thread micThread;
    private void openSco(Context context, recordStateListener listener, AlexaHttpManager manager) {
        micReceiveDtaCount = 0;
        mAudioManager = (AudioManager)context.getSystemService(Context.AUDIO_SERVICE);
        int channelConfiguration = AudioFormat.CHANNEL_IN_MONO;
        int audioEncoding = AudioFormat.ENCODING_PCM_16BIT;
        int bufferSize = AudioRecord.getMinBufferSize(frequency, channelConfiguration, audioEncoding);

        Log.i(TAG, "openSco bufferSize: ------------" + bufferSize);
        if (!mAudioManager.isBluetoothScoAvailableOffCall()) {
            listener.onRecordStateChanged(11, "The system does not support Bluetooth recording");
            return;
        }
        mAudioManager.setBluetoothScoOn(true);
        mAudioManager.startBluetoothSco();
        context.registerReceiver(new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                int state = intent.getIntExtra(AudioManager.EXTRA_SCO_AUDIO_STATE, -1);
                Log.i(TAG, "state: --------" + state);
                if (AudioManager.SCO_AUDIO_STATE_CONNECTED == state) {
                    Log.i(TAG, "onReceive: ----------打开SCO");
                    mAudioManager.setBluetoothScoOn(true);  //打开SCO
                    AudioRecord audioRecord = new AudioRecord(MediaRecorder.AudioSource.MIC, frequency, channelConfiguration, audioEncoding, bufferSize);
                    Log.i(TAG, "audioRecord.getState(): ----" + audioRecord.getState());
                    if (audioRecord.getState() == 1) {
                        audioRecord.startRecording();
                        isPlaying = true;
                        micThread = new Thread(new Runnable() {
                            @Override
                            public void run() {
                                while (isPlaying) {
                                    byte[] buffer = new byte[bufferSize];
                                    int readBytes = audioRecord.read(buffer, 0, bufferSize);
                                    if (readBytes > 10) {
                                        manager.addData(buffer);
//                                        FileUtils.writeTOfileAndActiveClear("aaaaa", ArrayUtil.toHex(buffer));
                                    }
                                }
                                audioRecord.stop();
                            }
                        });
                        micThread.start();
                    }
                    context.unregisterReceiver(this);
                } else {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    mAudioManager.startBluetoothSco();
                }
            }
        }, new IntentFilter(AudioManager.ACTION_SCO_AUDIO_STATE_CHANGED));
    }

    void stopSco(Context context) {
        Log.i(TAG, "stopSco: --------");
        isPlaying = false;
        if (mAudioManager != null) {
            mAudioManager.stopBluetoothSco();
            mAudioManager.setBluetoothScoOn(false);
        }
        new Thread(new Runnable() {
            @Override
            public void run() {
                mAudioManager = (AudioManager)context.getSystemService(Context.AUDIO_SERVICE);
                mAudioManager.stopBluetoothSco();
                mAudioManager.setBluetoothScoOn(false);
            }
        }).start();
    }
}
