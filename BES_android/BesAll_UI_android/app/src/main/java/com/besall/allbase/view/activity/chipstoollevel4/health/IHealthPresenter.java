package com.besall.allbase.view.activity.chipstoollevel4.health;


import android.content.Context;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;

/**
 * <AUTHOR>
 * @time $ $
 */
public interface IHealthPresenter {

    void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context);

    void goToSettingActivity(HealthActivity activity);

    void pickDecice(HealthActivity context, int scan);

}
