package com.besall.allbase.view.activity.chipstoollevel4.ota;

import static com.bes.bessdk.BesSdkConstants.BES_BLE_OTA_AUTO_TEST;
import static com.bes.bessdk.BesSdkConstants.BES_BLE_OTA_AUTO_TEST_VALUE;
import static com.bes.sdk.utils.DeviceProtocol.PROTOCOL_BLE;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.bes.bessdk.service.BesOTAConstants;
import com.bes.bessdk.service.BesOtaService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.ota.OTADfuInfo;
import com.bes.sdk.ota.OTATask;
import com.bes.sdk.ota.RemoteOTAConfig;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.activity.tools.SettingActivity.SettingActivity;
import com.besall.allbase.view.base.BasePresenter;

import java.util.ArrayList;

public class OtaUIPresenter extends BasePresenter<IOtaUIActivity> implements IOtaUIPresenter {
    ArrayList<String> mFilePaths;
    ArrayList<OTATask> mOtaTasks = new ArrayList<>();

    BesOtaService besOtaServiceTest;

    boolean isDiffUpgrade = false;
    @Override
    public void pickDecice(OtaUIActivity context, int scan) {
        Intent intent = new Intent();
        intent.putExtra(BluetoothConstants.Scan.BES_SCAN, scan);
        intent.putExtra(BluetoothConstants.Scan.BES_SCAN_IS_MULTIPLE_DEVICES, true);
        ActivityUtils.gotoActForResult(intent, BluetoothConstants.Scan.REQUEST_CODE_SCAN, context, ScanActivity.class);
    }

    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context, int index) {
        boolean bleOtaAutoTest = (boolean) SPHelper.getPreference(context, BES_BLE_OTA_AUTO_TEST, BES_BLE_OTA_AUTO_TEST_VALUE);
        if (bleOtaAutoTest) {
            mOtaTasks = new ArrayList<>();
        }
        BesOtaService besOtaService = new BesOtaService(serviceConfig, listener, context);
        besOtaServiceTest = besOtaService;
        OTATask otaTask = besOtaService;
        mOtaTasks.add(index, otaTask);
    }

    @Override
    public void onPickOtaFile(String path, int index) {
        if (mFilePaths == null) {
            mFilePaths = new ArrayList<>();
        }
        if (mFilePaths.size() > index) {
            mFilePaths.remove(index);
        }
        mFilePaths.add(index, path);
    }

    @Override
    public void startOta(OtaUIActivity context, int breakpoint, int index) {
        if (mOtaTasks.get(index) == null || mFilePaths.get(index) == null) return;
        RemoteOTAConfig config = new RemoteOTAConfig();
        if (isDiffUpgrade) {
            config.setLocalPath(mFilePaths.get(0));
            config.setLocalPathMaster(mFilePaths.get(1));
        } else {
            config.setLocalPath(mFilePaths.get(index));
        }
        mOtaTasks.get(index).setOtaConfig(config);
        OTADfuInfo otaDfuInfo = new OTADfuInfo("001", breakpoint);
        mOtaTasks.get(index).startDataTransfer(otaDfuInfo, context);
    }

    @Override
    public void stopOta() {
        for (int i = 0; i < mOtaTasks.size(); i ++) {
            OTATask otaTask = mOtaTasks.get(i);
            if (otaTask != null) {
                otaTask.stopDataTransfer();
                otaTask = null;
            }
        }
    }

    @Override
    public void goToSettingActivity(OtaUIActivity activity) {
        ActivityUtils.gotoAct(new Intent(), activity, SettingActivity.class);
    }

    @Override
    public void setIsDiffUpgrade() {
        isDiffUpgrade = true;
    }

    @Override
    public boolean sendTestData(byte[] data) {
        return besOtaServiceTest.sendTestDsata(data);
    }
}
