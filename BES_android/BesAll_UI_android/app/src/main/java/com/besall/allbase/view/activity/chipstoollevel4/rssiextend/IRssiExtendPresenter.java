package com.besall.allbase.view.activity.chipstoollevel4.rssiextend;

import android.content.Context;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;

/**
 * <AUTHOR>
 * @time $ $
 */
interface IRssiExtendPresenter {
    void pickDecice(RssiExtendActivity context, int scan);

    void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context);

    void startReadRssi(String rssi, Context context);

    void stopReadRssi();

    void stopSpp();

    void initTimer(Context context);


}
