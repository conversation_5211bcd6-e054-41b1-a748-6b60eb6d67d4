package com.besall.allbase.view.activity.chipstoollevel4.rssiextend;

import android.content.Context;
import android.content.Intent;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;
import com.besall.allbase.bluetooth.service.rssi.RssiService;
import com.besall.allbase.bluetooth.service.rssiextend.RssiExtendService;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.base.BasePresenter;

import java.util.Timer;
import java.util.TimerTask;

/**
 * <AUTHOR>
 * @time $ $
 */
class RssiExtendPresenter extends BasePresenter<IRssiExtendActivity> implements IRssiExtendPresenter {

    private RssiExtendService RssiExtendService;
    private Timer timer;                  //创建一个定时器对象
    private TimerTask task;
    private int intervel_num =1;

    @Override
    public void pickDecice(RssiExtendActivity context, int scan) {
        Intent intent = new Intent();
        intent.putExtra(BluetoothConstants.Scan.BES_SCAN, scan);
        ActivityUtils.gotoActForResult(intent, BluetoothConstants.Scan.REQUEST_CODE_SCAN, context, ScanActivity.class);
    }

    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        RssiExtendService = new RssiExtendService(serviceConfig, listener, context);
    }



    @Override
    public void startReadRssi(String rssi, Context context) {
        if (RssiExtendService != null) {
            if (timer!=null) {
                timer.cancel();
                timer = null;
            }
            if (timer==null) {
                initTimer(context);
            }
            intervel_num = Integer.parseInt(rssi);
            timer.schedule(task,0,intervel_num * 1000);
        }
    }

    @Override
    public void stopReadRssi() {
        if (timer != null) {
            timer.cancel();
            timer = null;
        }

    }

    @Override
    public void stopSpp() {
        if (RssiExtendService != null) {
            RssiExtendService.disconnected();
        }
    }



    @Override
    public void initTimer(Context context) {

        timer = new Timer();
        task = new TimerTask()        //创建定时器任务对象，必须实现run方法，在该方法中定义用户任务
        {

            @Override

            public void run()

            {
                RssiExtendService.RssiReadStart(context);
            }

        };
    }



}
