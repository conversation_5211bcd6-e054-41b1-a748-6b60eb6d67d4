package com.besall.allbase.common.opus;

import android.util.Log;

/**
 *  Created by all0 on 2017/12/07.
 */

public class OpusDecoder {
    private String TAG = "OpusDecoder";
    private static long decoder;

    static {
        System.loadLibrary("besOpus");
    }

    private static native long init_native( int framePeriod , int singleFrameInputSize , int totalFrameInputSize , int pcmOutputSize);

    private static native int getOpusSingleFrameDecoderIntLen_native();
    private static native int getOpusFrameTotalFrameDecoderIntLen_native();

    private native short[] decode_native(long decoder, byte[] decodeDataIn);

    private native void destory_native(long decoder);

    public static void init( int framePeriod , int singleFrameInputSize , int totalFrameInputSize , int pcmOutputSize) {
        decoder = init_native(framePeriod, singleFrameInputSize , totalFrameInputSize , pcmOutputSize );
        Log.e("OPUS ", "decoder = "+ decoder);
    }

    /**
     * 根据 HuyaDecoder.h 配置的opus值返回设置解码输入的字节数/帧
     * @return
     */
    public static int getOpusSingleFrameDecoderIntLen(){
        return getOpusSingleFrameDecoderIntLen_native();
    }
    public static int getOpusTotalFrameDecoderIntLen(){
        return getOpusFrameTotalFrameDecoderIntLen_native();
    }

    public byte[] decode(byte[] decodeDataIn) {
        if(decoder == 0){
            init(0 , 0 , 0 , 0); // zero using the defaul setting
        }
        if(decodeDataIn == null || decodeDataIn.length != getOpusSingleFrameDecoderIntLen()){
            return  null ;
        }
        short[] shorts =  decode_native(decoder, decodeDataIn);
        if(shorts != null){
            return  OpusUtils.Shorts2Bytes(shorts);
        }
        return null;
    }

    public byte[] decodeAll(byte[] totalDataIn){
        if(totalDataIn == null || totalDataIn.length%getOpusSingleFrameDecoderIntLen() != 0){
            return  null ;
        }
        byte[] totalDatas = null;
        for (int i = 0 ; i < totalDataIn.length/getOpusSingleFrameDecoderIntLen();i++){
            byte[] frame = new byte[getOpusSingleFrameDecoderIntLen()];
            System.arraycopy(totalDataIn , i*getOpusSingleFrameDecoderIntLen() , frame,0 ,getOpusSingleFrameDecoderIntLen());
            if(frame != null){
                byte[] shorts2Bytes = decode(frame);
                if(shorts2Bytes != null){
                    if(totalDatas == null){
                        totalDatas = shorts2Bytes;
                    }else{
                        int newLen = totalDatas.length + shorts2Bytes.length ;
//                        Log.e(TAG , "newLen LEN = "+ newLen);
                        byte[] newDatas = new byte[newLen];
                        System.arraycopy(totalDatas , 0 , newDatas, 0 , totalDatas.length );
                        System.arraycopy(shorts2Bytes , 0 , newDatas, totalDatas.length , shorts2Bytes.length );
                        totalDatas = newDatas ;
                    }
                }
            }
        }
        if(totalDatas != null){
//            Log.e(TAG , "TOTAL DECODE OUT  = "+ totalDatas.length);
        }
        return totalDatas;
    }

    public void descory() {
        destory_native(decoder);
    }
}
