package com.besall.allbase.view.activity.chipstoollevel4.EQ;

import java.io.Serializable;

public class EQListBeans implements Serializable {
    public boolean checked = false;
    public int enableFilterType = 1;
    public String gain = "";
    public String freq = "";
    public String q = "";

    public void setChecked(boolean checked) {
        this.checked = checked;
    }

    public boolean getChecked() {
        return checked;
    }

    public void setEnableFilterType(int enableFilterType) {
        this.enableFilterType = enableFilterType;
    }

    public int getEnableFilterType() {
        return enableFilterType;
    }

    public void setGain(String gain) {
        this.gain = gain;
    }

    public String getGain() {
        return gain;
    }

    public void setFreq(String freq) {
        this.freq = freq;
    }

    public String getFreq() {
        return freq;
    }

    public void setQ(String q) {
        this.q = q;
    }

    public String getQ() {
        return q;
    }
}

