package com.besall.allbase.bluetooth.service.checkcrc;


import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_ERROR;
import static com.besall.allbase.bluetooth.service.checkcrc.CheckCrcConstants.OP_TOTA_CHECK_CRC_DATA;

import android.content.Context;

import com.bes.bessdk.service.base.BesBaseService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.message.BaseMessage;
import com.bes.sdk.utils.DeviceProtocol;

public class CheckCrcService extends BesBaseService {

    public CheckCrcService(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        super(serviceConfig, listener, context);
        startConnect(serviceConfig);
    }

    @Override
    public void onStatusChanged(HmDevice device, int status, DeviceProtocol protocol) {
        super.onStatusChanged(device, status, protocol);
        if (status == BES_CONNECT_ERROR) {
            callBackStateChangedMessage(BES_CONNECT_ERROR,"error");
        }
    }

    @Override
    public void onDataReceived(BaseMessage deviceMessage) {
        super.onDataReceived(deviceMessage);
        if (mConfig.getTotaConnect() && !totauccess) {
            return;
        }
        callBackStateChangedMessage(OP_TOTA_CHECK_CRC_DATA, ArrayUtil.toHex((byte[]) deviceMessage.getMsgContent()));

        String result = CheckCrcCMD.receiveData((byte[]) deviceMessage.getMsgContent());
        if (result.length() > 0) {
            callBackStateChangedMessage(OP_TOTA_CHECK_CRC_DATA, result);
        }
    }

    public void getCrc() {
        sendData(CheckCrcCMD.getCrcCMD());
    }

    public void factoryReset() {
        sendData(CheckCrcCMD.getFactoryReset());
    }
}
