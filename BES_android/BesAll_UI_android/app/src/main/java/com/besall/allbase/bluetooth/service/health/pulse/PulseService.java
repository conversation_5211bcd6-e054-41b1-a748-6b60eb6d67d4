package com.besall.allbase.bluetooth.service.health.pulse;

import android.content.Context;
import android.util.Log;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.service.base.BesBaseService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.sdk.message.BaseMessage;
import com.besall.allbase.bluetooth.service.health.sports.SportsCMD;

public class PulseService extends BesBaseService {
    public PulseService(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        super(serviceConfig, listener, context);
        startConnect(serviceConfig);
    }

    public void sendRequest(){
        if (totauccess) {
            sendData(PulseCMD.PulseRequest(mContext,0), 0);
        } else {
            callBackErrorMessage(BesSdkConstants.BES_TOTA_ERROR);
        }
    }

    @Override
    public void onDataReceived(BaseMessage deviceMessage) {
        super.onDataReceived(deviceMessage);
        Log.i(TAG, "onDataReceived: ---blood----");
        String retStep = SportsCMD.footStep((byte[]) deviceMessage.getMsgContent());
    }
}
