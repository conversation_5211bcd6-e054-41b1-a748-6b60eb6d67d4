package com.besall.allbase.view.activity.chipstoollevel4.commandset;

import android.Manifest;
import android.annotation.SuppressLint;
import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.media.MediaCodecInfo;
import android.media.MediaCodecList;
import android.media.MediaPlayer;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.method.ScrollingMovementMethod;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.ScrollView;
import android.widget.SeekBar;
import android.widget.TextView;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.connect.BTService;
import com.bes.bessdk.scan.BtHeleper;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.R;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.common.Constants;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.common.utils.FileUtils;
import com.besall.allbase.view.activity.chipstoollevel4.ota.OtaUIActivity;
import com.besall.allbase.view.base.BaseActivity;
import com.suke.widget.SwitchButton;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_ERROR;
import static com.besall.allbase.bluetooth.BluetoothConstants.Scan.BES_SCAN_RESULT;
import static com.besall.allbase.bluetooth.service.commandset.CommandSetConstants.*;

import androidx.core.app.ActivityCompat;

import java.io.Serializable;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.ArrayList;
import java.util.Timer;
import java.util.TimerTask;

/**
 * <AUTHOR>
 * @time $ $
 */
public class CommandSetActivity extends BaseActivity<ICommandSetActivity, CommandSetPresenter> implements ICommandSetActivity, BesServiceListener, View.OnClickListener, MyScrollViewListener, RadioGroup.OnCheckedChangeListener, SwitchButton.OnCheckedChangeListener, ICommandSetPresenter.CheckMicStateListener, SeekBar.OnSeekBarChangeListener {
    private static CommandSetActivity instance;
    public String cur_title = "COMMAND SET";
    BluetoothDevice mDevice;
    HmDevice mHmDevice;
    BesServiceConfig mServiceConfig;
    private Button pick_device;
    private Button pick_device_ble;
    private Button connect_device;
//    private Button connect_ota_service_device;

    private TextView device_address;
    private TextView device_name;
    private com.suke.widget.SwitchButton switchButton_multiPoint;

    private View line_wear_detect;
    private com.suke.widget.SwitchButton switchButton_in_ear_detection_all;
    private com.suke.widget.SwitchButton switchButton_in_ear_detection_left;
    private com.suke.widget.SwitchButton switchButton_in_ear_detection_right;
    private com.suke.widget.SwitchButton switchButton_wd_prompt;

    private TextView text_tws_connect_state;

    private TextView text_master_ear;
    private TextView text_slave_ear;
    private TextView text_in_ear_detection_left;
    private TextView text_in_ear_detection_right;
    private boolean isReceiveSwitchButtonChange = false;
    private boolean isMultipointSwitchButtonChange = false;

    private boolean isArtificialShutdownAll = false;

    private byte curEarbudsClickTimes = BUTTON_SETTINGS_CONTROL_CLICK;
    private byte curEarbudsClickType = BUTTON_SETTINGS_CONTROL_LAST_MUSIC;
    private RadioGroup earbuds_click_func_0;
    private RadioGroup earbuds_click_func_1;
    private RadioGroup earbuds_click_func_2;
    private RadioGroup earbuds_click_func_3;
    private RadioButton earbuds_click_func_disable;

    private Button factory_reset_cmd_set;
    private Button play;
    private Button pause;
    private Button next;
    private Button prev;
    private Button button_get_left_battery;
    private TextView text_left_battery;
    private Button button_get_right_battery;
    private TextView text_right_battery;

    private Button button_get_box_battery;
    private TextView text_box_battery;

    private Button button_get_codec_type;
    private TextView text_codec_type;
    private Button fit_test;
    private com.suke.widget.SwitchButton switchButton_fit_test;
    private TextView fit_text;
    private Button eq_test;
    private EditText eq_text;
    private EditText command_set_receive_data;

    private RadioGroup eq_basetype;
    private byte curEqBaseType = 0x00;
    private Button disconnect;
//    private MyScrollView scrollview_0;
//    private MyScrollView scrollview_1;
//    private MyScrollView scrollview_2;
//    private MyScrollView scrollview_3;
//    private MyScrollView scrollview_4;
//    private MyScrollView scrollview_5;
//    private MyScrollView scrollview_6;
//    private MyScrollView scrollview_7;
//    private MyScrollView scrollview_8;
//    private MyScrollView scrollview_9;

    private Button get_bt_state, start_ota_btn;
    private TextView bt_state_text;
    private Button get_spp_state;
    private TextView spp_state_text;
    private Button check_mic_state;
    private TextView mic_state_text;
    private Button check_left_speaker;
    private Button check_right_speaker;

    private TextView button_state_1, button_state_2, button_state_3, button_state_4, button_state_5, button_state_6, button_state_7, button_state_8;
    private Button earbuds_click_left, earbuds_click_right, earbuds_double_click_left, earbuds_double_click_right, earbuds_triple_click_left, earbuds_triple_click_right, earbuds_long_press_left, earbuds_long_press_right;
    private TextView command_set_current_product_model, command_set_current_version;
    private RadioGroup eq_switch_type;
//    private RadioGroup dolby_switch_type;
    private Button dolby_turn_off;
    private Button dolby_type_natual;
    private Button dolby_type_movie;

    private TextView dolby_switch_title;
    private int curDolbyType = 0;//0close 1Natual 2Movie

    private LinearLayout eq_bgview;
    private LinearLayout linear_dolby_state;
    private LinearLayout linear_bes_spatial_state;
    private LinearLayout linear_mimi_state;
    private LinearLayout linear_ceva_state, linear_head_tracking_pro;
    private Button head_tracking_off, head_tracking_on, ceva_recentre, ceva_fast_recentre, ceva_slow_recentre, btn_get_head_tracking, btn_get_auto_center_mode, btn_get_imu_orientation, head_tracking_off_pro, head_tracking_on_pro;

    private TextView tv_get_head_tracking_result, tv_get_auto_center_mode_result, tv_get_imu_orientation_result;
    private LinearLayout linear_anc_state;
    private RadioButton regulate_anc_speakthru;
    private TextView text_anc_speakthru;

    private RadioGroup bes_spatial_switch_type;
    private RadioGroup mimi_switch_type;
    private RadioGroup ceva_switch_type;
    private TextView text_anc_off, text_anc_default;
    private RadioButton regulate_anc_off, regulate_anc_default;

    private RadioGroup regulate_anc_type, anc_new_type;
    private LinearLayout linear_anc_new_state;

    private LinearLayout wear_bg1, wear_bg2, wear_bg3, wear_bg4;
    private Button get_tech_level;
    private TextView text_mimi_switch_preset;

    private TextView tech_level_text;
    private TextView text_mimi_switch_intensity;
    private SeekBar seekbar_mimi_switch_preset;
    private SeekBar seekbar_mimi_switch_intensity;

    private TextView text_eq_60, text_eq_120, text_eq_250, text_eq_750, text_eq_2k, text_eq_5k, text_eq_15k;
    private SeekBar seekbar_eq_60, seekbar_eq_120, seekbar_eq_250, seekbar_eq_750, seekbar_eq_2k, seekbar_eq_5k, seekbar_eq_15k;

    private LinearLayout seekbar_eq_bg, button_setting_bg, linear_led_onoff_state, linear_spatial_audio_state, linear_share_mode_onoff, linear_get_codec_type;
    private RadioGroup led_switch_type, led_switch_type_2, spatial_audio_switch_type, spatial_audio_switch_type_2, share_mode_switch_type;

    private int curSeekbarProgress;

    private boolean isReceiveButtonCmd = false;

    private Timer getSppStatusTimer;
    private TimerTask getSppStatusTimerTask;

    private boolean hasGetSppStatus = false;
    private String curModelName = "";
    private boolean isReceiveRoleSwitch = false;

    private ArrayList<String> speakBoxModels = new ArrayList<>();
    private LinearLayout linear_multiPoint_bg, linear_function_btn;
    private boolean openSpa = false;
    private LinearLayout linear_game_mode;
    private RadioGroup game_mode_type;
    private LinearLayout linear_volume;
    private Button btn_volume;
    private SeekBar seekbar_volume;
    private TextView tv_volume, tv_volume_cur, tv_click_func_disable;
    private RadioGroup eq_basetype2;
    private LinearLayout seekbar_eq_2_bg;
    private SeekBar seekbar_eq_2_32, seekbar_eq_2_64, seekbar_eq_2_125, seekbar_eq_2_250, seekbar_eq_2_500, seekbar_eq_2_1k, seekbar_eq_2_2k, seekbar_eq_2_4k, seekbar_eq_2_8k, seekbar_eq_2_16k;
    private TextView text_eq_2_32, text_eq_2_64, text_eq_2_125, text_eq_2_250, text_eq_2_500, text_eq_2_1k, text_eq_2_2k, text_eq_2_4k, text_eq_2_8k, text_eq_2_16k;

    private LinearLayout linear_touch_onoff, linear_disable_swipe;
    private com.suke.widget.SwitchButton switchButton_touch_onoff, switchButton_disable_swipe_left, switchButton_disable_swipe_right;


    @Override
    protected CommandSetPresenter createPresenter() {
        return new CommandSetPresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        speakBoxModels.add(COMMAND_SET_PRODUCT_MODEL_H50);
        speakBoxModels.add(COMMAND_SET_PRODUCT_MODEL_H40);
        speakBoxModels.add(COMMAND_SET_PRODUCT_MODEL_H20);
        speakBoxModels.add(COMMAND_SET_PRODUCT_MODEL_H10);
        speakBoxModels.add(COMMAND_SET_PRODUCT_MODEL_P1);
        speakBoxModels.add(COMMAND_SET_PRODUCT_MODEL_BES_BOARD);

        mServiceConfig = new BesServiceConfig();
        //log
        SPHelper.putPreference(instance, BesSdkConstants.BES_SAVE_LOG_NAME, "CommandSet");
        btHasConnect = false;
    }

    @Override
    protected int getContentViewId() {
        return R.layout.act_connect;
    }

    @Override
    protected void bindView() {
        pick_device = (Button)findViewById(R.id.pick_device);
        pick_device_ble = (Button)findViewById(R.id.pick_device_ble);
        connect_device =(Button)findViewById(R.id.connect_device);
//        connect_ota_service_device =(Button)findViewById(R.id.edit_cutomercmd);
        device_address = (TextView) findViewById(R.id.device_address);
        device_name = (TextView) findViewById(R.id.device_name);

        logV = (TextView) findViewById(R.id.logV);
        done = (Button) findViewById(R.id.done);
        loginfo = (View) findViewById(R.id.loginfo);

        loadanimdrawable();
    }

    @Override
    protected void initView() {
        inittoolbar(cur_title);
        pick_device.setOnClickListener(instance);
        pick_device_ble.setOnClickListener(instance);
        pick_device_ble.setVisibility(View.VISIBLE);
        connect_device.setOnClickListener(instance);
//        connect_ota_service_device.setOnClickListener(instance);
//        connect_ota_service_device.setVisibility(View.VISIBLE);
//        connect_ota_service_device.setText("connect ota service");

        scr_policy = (ScrollView)findViewById(R.id.scr_policy);

        tv_title.setOnClickListener(instance);
        done.setOnClickListener(instance);

        logV.setMovementMethod(ScrollingMovementMethod.getInstance());
        scr_policy.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                v.getParent().requestDisallowInterceptTouchEvent(true);
                return false;
            }
        });

        if (ActivityCompat.checkSelfPermission(instance, Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(instance, new String[]{Manifest.permission.RECORD_AUDIO}, 1);
            return;
        }

//        initlayout();
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == BluetoothConstants.Scan.REQUEST_CODE_SCAN) {
            onPickDevice(resultCode, data);
        }
    }

    private void onPickDevice(int resultCode, Intent data) {
        if (resultCode == RESULT_OK) {
            mHmDevice = (HmDevice) data.getSerializableExtra(BES_SCAN_RESULT);
            mDevice = BtHeleper.getBluetoothAdapter(instance).getRemoteDevice(mHmDevice.getPreferredProtocol() == DeviceProtocol.PROTOCOL_BLE ? mHmDevice.getBleAddress() : mHmDevice.getDeviceMAC());

            Log.i(TAG, "onPickDevice: " + mDevice.getName());
            Log.i(TAG, "onPickDevice: " + mDevice.getAddress());
            device_address.setText(mDevice.getAddress());
            mServiceConfig.setDevice(mHmDevice);
            String name = mDevice.getName();
            SpannableString ss = new SpannableString(name);
            BesSdkConstants.BesConnectState state = BTService.getDeviceConnectState(instance, mServiceConfig);
            Log.i(TAG, "onPickDevice: -------" + state);
            if (state == BesSdkConstants.BesConnectState.BES_CONNECT) {
                ss.setSpan(new ForegroundColorSpan(Color.rgb(103, 200, 77)), 0, name.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
            device_name.setText(ss);
        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        mPresenter.stopSpp();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                mPresenter.stopSpp();
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.pick_device:
                mPresenter.pickDecice(instance, BluetoothConstants.Scan.SCAN_SPP);
                connect_device.setVisibility(View.VISIBLE);
                mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
                break;
            case R.id.pick_device_ble:
//                initlayout();
                mPresenter.pickDecice(instance, BluetoothConstants.Scan.SCAN_BLE);
                connect_device.setVisibility(View.VISIBLE);
                mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_BLE);
                break;
            case R.id.connect_device:
                //use ota service
                loadinganim();
                if (mHmDevice == null) {
                    loadingDialog.dismiss();
                    ActivityUtils.showToast(R.string.pleaseSelectDevice);
                    return;
                }
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        Log.i(TAG, "run: 1");
                        mServiceConfig.setDevice(mHmDevice);
                        Log.i(TAG, "onPickDevice:1111 " + mDevice.getAddress());
                        if (mServiceConfig.getDeviceProtocol() == DeviceProtocol.PROTOCOL_BLE) {
                            mServiceConfig.setServiceUUID(BesSdkConstants.BES_OTA_SERVICE_OTA_UUID);
                            mServiceConfig.setCharacteristicsUUID(BesSdkConstants.BES_OTA_CHARACTERISTIC_OTA_UUID);
                            mServiceConfig.setDescriptorUUID(BesSdkConstants.BES_OTA_DESCRIPTOR_OTA_UUID);
                        } else {
                            mServiceConfig.setServiceUUID(BesSdkConstants.BES_OTA_SERVICE_OTA_UUID);
                        }
                        mServiceConfig.setIsUserOtaService(true);
                        mServiceConfig.setTotaConnect(true);
                        boolean useTotaV2 = (boolean) SPHelper.getPreference(instance, BesSdkConstants.BES_TOTA_USE_TOTAV2, BesSdkConstants.BES_TOTA_USE_TOTAV2_VALUE);
                        mServiceConfig.setUseTotaV2(useTotaV2);
                        mPresenter.connectDevice(mServiceConfig, instance, instance);
                    }
                });

                break;
//            case R.id.connect_device:
//                //use TOTA service
////                initlayout();
//                loadinganim();
//                if (mHmDevice == null) {
//                    loadingDialog.dismiss();
//                    ActivityUtils.showToast(R.string.connect_failed);
//                    return;
//                }
//                runOnUiThread(new Runnable() {
//                    @Override
//                    public void run() {
//                        Log.i(TAG, "run: 1");
//                        mServiceConfig.setDevice(mHmDevice);
//                        Log.i(TAG, "onPickDevice:1111 " + mDevice.getAddress());
//                        if (mServiceConfig.getDeviceProtocol() == DeviceProtocol.PROTOCOL_BLE) {
//                            mServiceConfig.setServiceUUID(BesSdkConstants.BES_TOTA_SERVICE_OTA_UUID);
//                            mServiceConfig.setCharacteristicsUUID(BesSdkConstants.BES_TOTA_CHARACTERISTI_OTA_UUID);
//                            mServiceConfig.setDescriptorUUID(BesSdkConstants.BES_TOTA_DESCRIPTOR_OTA_UUID);
//                        } else {
//                            mServiceConfig.setServiceUUID(BesSdkConstants.BES_SPP_CONNECT);
//                        }
//                        mServiceConfig.setIsUserOtaService(false);
//                        mServiceConfig.setTotaConnect(true);
//                        boolean useTotaV2 = (boolean) SPHelper.getPreference(instance, BesSdkConstants.BES_TOTA_USE_TOTAV2, BesSdkConstants.BES_TOTA_USE_TOTAV2_VALUE);
//                        mServiceConfig.setUseTotaV2(useTotaV2);
//                        mPresenter.connectDevice(mServiceConfig, instance, instance);
//                    }
//                });
//                break;
            case R.id.earbuds_click_left:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BUTTON_SETTINGS_CONTROL, true, BUTTON_SETTINGS_CONTROL_CLICK_LEFT, BUTTON_SETTINGS_CONTROL_CLICK, curEarbudsClickType);

                getButtonState();
                break;
            case R.id.earbuds_click_right:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BUTTON_SETTINGS_CONTROL, true, BUTTON_SETTINGS_CONTROL_CLICK_RIGHT, BUTTON_SETTINGS_CONTROL_CLICK, curEarbudsClickType);

                getButtonState();
                break;
            case R.id.earbuds_double_click_left:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BUTTON_SETTINGS_CONTROL, true, BUTTON_SETTINGS_CONTROL_CLICK_LEFT, BUTTON_SETTINGS_CONTROL_DOUBLE_CLICK, curEarbudsClickType);

                getButtonState();
                break;
            case R.id.earbuds_double_click_right:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BUTTON_SETTINGS_CONTROL, true, BUTTON_SETTINGS_CONTROL_CLICK_RIGHT, BUTTON_SETTINGS_CONTROL_DOUBLE_CLICK, curEarbudsClickType);

                getButtonState();
                break;
            case R.id.earbuds_triple_click_left:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BUTTON_SETTINGS_CONTROL, true, BUTTON_SETTINGS_CONTROL_CLICK_LEFT, BUTTON_SETTINGS_CONTROL_TRIPLE_CLICK, curEarbudsClickType);

                getButtonState();
                break;
            case R.id.earbuds_triple_click_right:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BUTTON_SETTINGS_CONTROL, true, BUTTON_SETTINGS_CONTROL_CLICK_RIGHT, BUTTON_SETTINGS_CONTROL_TRIPLE_CLICK, curEarbudsClickType);

                getButtonState();
                break;
            case R.id.earbuds_long_press_left:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BUTTON_SETTINGS_CONTROL, true, BUTTON_SETTINGS_CONTROL_CLICK_LEFT, BUTTON_SETTINGS_CONTROL_LONG_PRESS, curEarbudsClickType);

                getButtonState();
                break;
            case R.id.earbuds_long_press_right:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BUTTON_SETTINGS_CONTROL, true, BUTTON_SETTINGS_CONTROL_CLICK_RIGHT, BUTTON_SETTINGS_CONTROL_LONG_PRESS, curEarbudsClickType);

                getButtonState();
                break;
            case R.id.factory_reset_cmd_set:
                mPresenter.sendTestData(COMMAND_SET_TYPE_FACTORY_RESET, false, (byte) 0x00);
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            Thread.sleep(1000);
                            mPresenter.stopSpp();
                        } catch (InterruptedException e) {
                            throw new RuntimeException(e);
                        }
                    }
                }).start();
                break;
            case R.id.play:
                mPresenter.sendTestData(COMMAND_SET_TYPE_MUSIC_PLAY_BACK, true, MUSIC_PLAY_BACK_PLAY);
                break;
            case R.id.pause:
                mPresenter.sendTestData(COMMAND_SET_TYPE_MUSIC_PLAY_BACK, true, MUSIC_PLAY_BACK_PAUSE);
                break;
            case R.id.next:
                mPresenter.sendTestData(COMMAND_SET_TYPE_MUSIC_PLAY_BACK, true, MUSIC_PLAY_BACK_NEXT);
                break;
            case R.id.prev:
                mPresenter.sendTestData(COMMAND_SET_TYPE_MUSIC_PLAY_BACK, true, MUSIC_PLAY_BACK_PREV);
                break;
            case R.id.button_get_left_battery:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BATTREY_PERCENTAGE, true, BATTREY_PERCENTAGE_LEFT);
                break;
            case R.id.button_get_right_battery:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BATTREY_PERCENTAGE, true, BATTREY_PERCENTAGE_RIGHT);
                break;
            case R.id.button_get_box_battery:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BATTREY_PERCENTAGE, true, BATTREY_PERCENTAGE_BOX);
                break;
            case R.id.button_get_codec_type:
                mPresenter.sendTestData(APP_TOTA_CODEC_TYPE, true, SWITCH_STATUS_GET);
                break;
//            case R.id.fit_test:
//                if (switchButton_fit_test.isChecked()) {
//                    fit_test.setClickable(false);
//                    fit_test.setText("Is testing");
//                }
//                mPresenter.sendTestData(COMMAND_SET_TYPE_EARBUD_FIT_TEST, true, switchButton_fit_test.isChecked () ? (byte) 0x01 : (byte) 0x00);
//                break;
            case R.id.eq_test:
//                if (eq_text.getText().length() > 1) {
//                    byte eqData = ArrayUtil.toBytes(eq_text.getText().toString())[0];
//                    mPresenter.sendTestData(COMMAND_SET_TYPE_EQ, eqData);
//                }
                mPresenter.sendTestData(COMMAND_SET_TYPE_EQ_SWITCH, true,  SWITCH_STATUS_SET_PARAM, curEqBaseType);
                break;
            case R.id.disconnect:
                mPresenter.stopSpp();
//                onStateChangedMessage(COMMAND_SET_RECEIVE_CURRENT_PRODUCT_MODEL, COMMAND_SET_PRODUCT_MODEL_255ARC, null);
//                onStateChangedMessage(COMMAND_SET_RECEIVE_BUTTON_SETTINGS_CONTROL, "01,01,0a,01", null);

                break;
            case R.id.start_ota_btn:
                if (curModelName.length() == 0) {
                    ActivityUtils.showToast("please check Product Model");
                    return;
                }
                if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IE2A) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IE2B) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_255ARCPLUS) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_255ARC) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IE_Gaming) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_D49) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_KB20) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_H40) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_H20) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_H10) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_BES_BOARD)) {
                    start_ota_btn.setEnabled(false);
                    mPresenter.sendTestData(COMMAND_SET_TYPE_START_OTA, false, (byte) 0x00);
                } else {
                    Intent intent5 = new Intent();
                    BesServiceConfig serviceConfig5 = new BesServiceConfig();
                    serviceConfig5.setDeviceProtocol(mServiceConfig.getDeviceProtocol());
                    if (mServiceConfig.getDeviceProtocol() == DeviceProtocol.PROTOCOL_SPP) {
                        serviceConfig5.setServiceUUID(BesSdkConstants.BES_OTA_SERVICE_OTA_UUID);
                    } else {
                        serviceConfig5.setServiceUUID(BesSdkConstants.BES_OTA_SERVICE_OTA_UUID);
                        serviceConfig5.setCharacteristicsUUID(BesSdkConstants.BES_OTA_CHARACTERISTIC_OTA_UUID);
                        serviceConfig5.setDescriptorUUID(BesSdkConstants.BES_OTA_DESCRIPTOR_OTA_UUID);
                    }
                    serviceConfig5.setUSER_FLAG(1);
                    serviceConfig5.setTotaConnect(false);
                    serviceConfig5.setUseTotaV2(false);
                    serviceConfig5.setDevice(mHmDevice);
                    if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IM2_DOLBY) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IH6PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_NEBULA) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_TOUCH_HEADPHONE) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9ANC)) {
                        serviceConfig5.setCurUser(3);
                    } else {
                        serviceConfig5.setCurUser(1);
                    }
                    serviceConfig5.setImageSideSelection(0);
                    if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_TOUCH_HEADPHONE) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_255ARC) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_R2)) {
                        serviceConfig5.setImageSideSelection(1);
                    }
                    intent5.putExtra(Constants.OTA_SERVICE_CONFIG, (Serializable) serviceConfig5);
                    ActivityUtils.gotoAct(intent5, instance, OtaUIActivity.class);
                }
                break;
            case R.id.get_bt_state:
                refreshBtState(mPresenter.getBtState());
                break;
            case R.id.get_spp_state:
//                startTimer();
//                mPresenter.sendTestData(COMMAND_SET_TYPE_GET_SPP_CONNECT_STATUS, false, (byte) 0x00);
                break;
            case R.id.check_left_speaker:
                refreshBtState(mPresenter.getBtState());
                if (bt_state_text.getCurrentTextColor() != getColor(R.color.green)) {
                    ActivityUtils.showToast("please check bt state");
                    return;
                }
                refreshSpeakerButtonState(false, 0);
                mPresenter.startPlayVideoWithType(instance, (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_255ARC) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_KB20) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_TOUCH_HEADPHONE) || speakBoxModels.contains(curModelName)) ? 2 : 0, new MediaPlayer.OnCompletionListener() {
                    @Override
                    public void onCompletion(MediaPlayer mp) {
                        Log.i(TAG, "onCompletion: -----left");
                        refreshSpeakerButtonState(true, 0);
                    }
                });
                break;
            case R.id.check_right_speaker:
                refreshBtState(mPresenter.getBtState());
                if (bt_state_text.getCurrentTextColor() != getColor(R.color.green)) {
                    ActivityUtils.showToast("please check bt state");
                    return;
                }
                refreshSpeakerButtonState(false, 1);
                mPresenter.startPlayVideoWithType(instance, 1, new MediaPlayer.OnCompletionListener() {
                    @Override
                    public void onCompletion(MediaPlayer mp) {
                        Log.i(TAG, "onCompletion: -----right");
                        refreshSpeakerButtonState(true, 1);
                    }
                });
                break;
            case R.id.check_mic_state:
                refreshBtState(mPresenter.getBtState());
                if (bt_state_text.getCurrentTextColor() != getColor(R.color.green)) {
                    ActivityUtils.showToast("please check bt state");
                    return;
                }
                refreshSpeakerButtonState(false, 2);

                mPresenter.checkMicState(instance, instance, instance);
                break;
            case R.id.dolby_turn_off:
                if (curDolbyType == 0) {
                    return;
                }
                curDolbyType = 0;
                mPresenter.sendTestData(COMMAND_SET_TYPE_DOLBY_SWITCH, true, SWITCH_STATUS_SET, SWITCH_STATUS_OFF);
                refreshDolbyButtonUI();
                break;
            case R.id.dolby_type_natual:
                if (curDolbyType == 1) {
                    return;
//                    curDolbyType = 0;
//                    mPresenter.sendTestData(COMMAND_SET_TYPE_DOLBY_SWITCH, true, SWITCH_STATUS_SET_PARAM, DOLBY_TYPE_NO_STATE);
                }
                curDolbyType = 1;
                mPresenter.sendTestData(COMMAND_SET_TYPE_DOLBY_SWITCH, true, SWITCH_STATUS_SET_PARAM, DOLBY_TYPE_NATUAL);
                refreshDolbyButtonUI();
                break;
            case R.id.dolby_type_movie:
                if (curDolbyType == 2) {
                    return;
//                    curDolbyType = 0;
//                    mPresenter.sendTestData(COMMAND_SET_TYPE_DOLBY_SWITCH, true, SWITCH_STATUS_SET_PARAM, DOLBY_TYPE_NO_STATE);
                }
                curDolbyType = 2;
                mPresenter.sendTestData(COMMAND_SET_TYPE_DOLBY_SWITCH, true, SWITCH_STATUS_SET_PARAM, DOLBY_TYPE_MOVIE);
                refreshDolbyButtonUI();
                break;
            case R.id.get_tech_level:
                mPresenter.sendTestData(COMMAND_SET_TYPE_MIMI_SWITCH, true, SWITCH_STATUS_SET_PARAM_GET_TECH_LEVEL);
                break;
            case R.id.done:
                loginfo.setVisibility(View.GONE);
                break;
            case R.id.tv_title:
                loginfo.setVisibility(View.VISIBLE);
                break;
            case R.id.head_tracking_off:
                mPresenter.sendTestData(APP_TOTA_HEAD_TRACKING_OFF_CMD, true, (byte) 0x00);
                break;
            case R.id.head_tracking_on:
                mPresenter.sendTestData(APP_TOTA_HEAD_TRACKING_ON_CMD, true, (byte) 0x00);
                break;
            case R.id.head_tracking_off_pro:
                mPresenter.sendTestData(APP_TOTA_HEAD_TRACKING_OFF_CMD, true, (byte) 0x00);
                break;
            case R.id.head_tracking_on_pro:
                mPresenter.sendTestData(APP_TOTA_HEAD_TRACKING_ON_CMD, true, (byte) 0x00);
                break;
            case R.id.ceva_recentre:
                mPresenter.sendTestData(APP_TOTA_TARE_CMD, true, (byte) 0x00);
                break;
            case R.id.ceva_slow_recentre:
                mPresenter.sendTestData(APP_TOTA_AUTOCENTER_SLOW_CMD, true, (byte) 0x00);
                break;
            case R.id.ceva_fast_recentre:
                mPresenter.sendTestData(APP_TOTA_AUTOCENTER_FAST_CMD, true, (byte) 0x00);
                break;
            case R.id.btn_get_head_tracking:
                mPresenter.sendTestData(APP_TOTA_GET_HEAD_TRACKING_STATE_CMD, true, (byte) 0x00);
                break;
            case R.id.btn_get_auto_center_mode:
                mPresenter.sendTestData(APP_TOTA_GET_AUTO_CENTER_MODE_CMD, true, (byte) 0x00);
                break;
            case R.id.btn_get_imu_orientation:
                mPresenter.sendTestData(APP_TOTA_GET_IMU_ORIENTATION_CMD, true, (byte) 0x00);
                break;
            case R.id.btn_volume:
                mPresenter.sendTestData(APP_TOTA_VOLUME, true, APP_TOTA_GET);
                break;
            default:
                break;
        }
    }

    private void startTimer() {
        refreshBtState(mPresenter.getBtState());
//        runOnUiThread(new Runnable() {
//            @Override
//            public void run() {
//                mPresenter.sendTestData(COMMAND_SET_TYPE_GET_SPP_CONNECT_STATUS, false, (byte) 0x00);
//            }
//        });
//        hasGetSppStatus = false;
        if (getSppStatusTimer != null) {
            getSppStatusTimer.cancel();
            getSppStatusTimer = null;
        }
        getSppStatusTimer = new Timer();
        getSppStatusTimerTask = new TimerTask()
        {
            @Override
            public void run()
            {
//                Log.i(TAG, "getSppStatusTimer: -------" + hasGetSppStatus);
//                if (!hasGetSppStatus) {
//                    refreshSppState(0);
//                }
                getSppStatusTimer.cancel();
                getSppStatusTimer = null;
                startTimer();
            }
        };

        getSppStatusTimer.schedule(getSppStatusTimerTask, 2000);
    }

private boolean isInBackground = false;
    @Override
    protected void onPause() {
        super.onPause();
        if (getSppStatusTimer != null) {
            getSppStatusTimer.cancel();
            getSppStatusTimer = null;
        }
        isInBackground = true;
        new Thread(new Runnable() {
            @Override
            public void run() {
                while (isInBackground) {
                    try {
                        Thread.sleep(1000);

                        refreshBtState(mPresenter.getBtState());
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        }).start();
    }

    @Override
    protected void onResume() {
        super.onResume();
        startTimer();

        isInBackground = false;
    }

    private boolean btHasConnect = false;
    private void refreshBtState(int state) {
        if (bt_state_text == null) {
            return;
        }
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (state > 0) {
                    btHasConnect = true;
                    bt_state_text.setText("Connect");
                    bt_state_text.setTextColor(getColor(R.color.green));
                } else {
                    bt_state_text.setText("disconnect");
                    bt_state_text.setTextColor(getColor(R.color.fff06e6e));
                    if (btHasConnect) {
                        btHasConnect = false;
                        mPresenter.stopSpp();
                    }
                }
            }
        });
    }

    private void refreshSppState(int state) {
        if (spp_state_text == null) {
            return;
        }
        if (state == 1) {
            spp_state_text.setText("Connect");
            spp_state_text.setTextColor(getColor(R.color.green));
        } else {
            spp_state_text.setText("disconnect");
            spp_state_text.setTextColor(getColor(R.color.fff06e6e));
        }
    }

    private void refreshSpeakerButtonState(boolean enable, int type) {
        check_left_speaker.setEnabled(enable);
        check_right_speaker.setEnabled(enable);
        check_mic_state.setEnabled(enable);
        if (enable) {
            if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_255ARC) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_KB20) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_R2) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_TOUCH_HEADPHONE) || speakBoxModels.contains(curModelName)) {
                check_left_speaker.setText("Check Speaker");
            } else {
                check_left_speaker.setText("Check Left Speaker");
            }
            check_right_speaker.setText("Check Right Speaker");

            check_mic_state.setText("Check MIC State");
            check_mic_state.setTextColor(getColor(R.color.white));
            check_mic_state.setEnabled(true);
        } else {
            if (type == 0) {
                check_left_speaker.setText("Playing...");
            } else if (type == 1) {
                check_right_speaker.setText("Playing...");
            } else {
                check_mic_state.setText("Please speak...");
                check_mic_state.setTextColor(getColor(R.color.ffff5d5d));

                mic_state_text.setTextColor(getColor(R.color.black));
                mic_state_text.setText("Detection progress：" + 0 + "%");
            }
        }
    }

    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {

        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (state == true) {
                    if (isReceiveRoleSwitch) {
                        isReceiveRoleSwitch = false;

                        getUIState();

                        return;
                    }
                    initlayout();
                    loadingDialog.dismiss();

                    getButtonState();
                    if (mServiceConfig.getDeviceProtocol() == DeviceProtocol.PROTOCOL_SPP) {
                        refreshSppState(1);
                    }
                } else {
                    if (isReceiveRoleSwitch) {
                        isReceiveRoleSwitch = false;
                        return;
                    }
                }
            }
        });
    }

    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {

    }

    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {
        Log.i(TAG, "onStateChangedMessage: +" + msgStr);
        FileUtils.writeTOfileAndActiveClear("CommandSet", "onStateChangedMessage------->" + msg + "<--->msgStr-->" + msgStr);
        runOnUiThread(new Runnable() {
            @SuppressLint("SetTextI18n")
            @Override
            public void run() {
                if (msg == BES_CONNECT_ERROR) {
                    if (isReceiveRoleSwitch) {
                        return;
                    }
                    Log.i(TAG, "run: failed");
                    loadingDialog.dismiss();
                    ActivityUtils.showToast(R.string.connect_failed);
                    if (mServiceConfig.getDeviceProtocol() == DeviceProtocol.PROTOCOL_SPP) {
                        refreshSppState(0);
                    }
                } else if (msg == COMMAND_SET_RECEIVE_MULTIPOINT_STATE) {
                    isMultipointSwitchButtonChange = true;
                    byte[] data = ArrayUtil.toBytes(msgStr);
                    if (data[0] == MULTIPOINT_OPEN) {
                        switchButton_multiPoint.setChecked(true);
                    } else {
                        switchButton_multiPoint.setChecked(false);
                    }
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                Thread.sleep(500);
                                isMultipointSwitchButtonChange = false;
                            } catch (InterruptedException e) {
                                throw new RuntimeException(e);
                            }
                        }
                    }).start();
                } else if (msg == COMMAND_SET_RECEIVE_BUTTON_SETTINGS_CONTROL) {
                    String text = "set";
                    byte[] data = ArrayUtil.toBytes(msgStr);
                    if (data[0] == BUTTON_SETTINGS_CONTROL_CLICK_LEFT) {
                        text = text + (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_TOUCH_HEADPHONE) ? " Touch" : "left ear");
                    } else {
                        text = text + (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_TOUCH_HEADPHONE) ? " MFB" : " right ear");
                    }
                    if (data[3] == BUTTON_SETTINGS_CONTROL_RESULT_NO_CONNECT) {
                        text = text + ":Earphone not connected";
                    } else {
                        if (data[1] == BUTTON_SETTINGS_CONTROL_CLICK) {
                            text = text + " click";
                        } else if (data[1] == BUTTON_SETTINGS_CONTROL_DOUBLE_CLICK) {
                            text = text + " double click";
                        } else if (data[1] == BUTTON_SETTINGS_CONTROL_TRIPLE_CLICK) {
                            text = text + " triple click";
                        } else if (data[1] == BUTTON_SETTINGS_CONTROL_LONG_PRESS) {
                            text = text + " long press";
                        } else if (data[1] == BUTTON_SETTINGS_CONTROL_SWIPE) {
                            text = text + " swipe";
                        }
                        if (data[2] == BUTTON_SETTINGS_CONTROL_LAST_MUSIC) {
                            text = text + " last music";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_NEXT_MUSIC) {
                            text = text + " next music";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_AMBIENT_MUSIC) {
                            text = text + " ambient music";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_PHONE_CALL_BACK) {
                            text = text + " call back";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_VOLUME_ADD) {
                            text = text + " volume ++";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_VOLUME_LOSE) {
                            text = text + " volume --";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_PLAY_MUSIC) {
                            text = text + " play music";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_STOP_MUSIC) {
                            text = text + " stop music";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_ASSISTANT) {
                            text = text + " assistant";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_PLAY_PAUSE_MUSIC) {
                            text = text + " play pause music";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_GAME_MODE) {
                            text = text + " game mode";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_ALGO) {
                            text = text + " ALGO";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_SPEAKTHRU) {
                            text = text + " SpeakThru";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_DISABLE) {
                            text = text + " Disable";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_SWIPE_DISABLE) {
                            text = text + " Disable";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_SWIPE_ENABLE) {
                            text = text + " Enable";
                        }
                        if (data[3] == BUTTON_SETTINGS_CONTROL_RESULT_SUCCESS) {
                            text = text + ":SUCCESS";
                        } else {
                            text = text + ":FAIL";
                        }
                    }
                    ActivityUtils.showToast(text);
                } else if (msg == COMMAND_SET_RECEIVE_DATA) {
                    command_set_receive_data.setText(msgStr);
//                    addlog("---------------");
//                    addlog(msgStr);

                    FileUtils.writeTOfileAndActiveClear("CommandSet", msgStr);
                } else if (msg == COMMAND_SET_RECEIVE_BATTERY_LEFT) {
                    if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_255ARC)) {
                        text_left_battery.setText((Integer.parseInt(msgStr) - 10) + "");
                    } else {
                        text_left_battery.setText(msgStr);
                    }
                } else if (msg == COMMAND_SET_RECEIVE_BATTERY_RIGHT) {
                    if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_255ARC)) {
                        text_right_battery.setText((Integer.parseInt(msgStr) - 10) + "");
                    } else {
                        text_right_battery.setText(msgStr);
                    }
                } else if (msg == COMMAND_SET_RECEIVE_BATTERY_BOX) {
                    if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_255ARC)) {
                        text_box_battery.setText((Integer.parseInt(msgStr) - 10) + "");
                    } else {
                        text_box_battery.setText(msgStr);
                    }
                } else if (msg == COMMAND_SET_RECEIVE_IS_FIT) {
                    fit_test.setClickable(true);
                    fit_test.setText("Earbud Fit Test");
                    fit_text.setText(msgStr);
                } else if (msg == COMMAND_SET_RECEIVE_WD_PROMPT_RESULT) {
                    byte[] data = ArrayUtil.toBytes(msgStr);
                    isReceiveSwitchButtonChange = true;
                    if (data[0] == IN_EAR_DETECTION_OPEN) {
                        switchButton_wd_prompt.setChecked(true);
                    } else {
                        switchButton_wd_prompt.setChecked(false);
                    }
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                Thread.sleep(200);
                                isReceiveSwitchButtonChange = false;

                            } catch (InterruptedException e) {
                                throw new RuntimeException(e);
                            }
                        }
                    }).start();
                }
                else if (msg == COMMAND_SET_RECEIVE_IN_EAR_DETECTION_RESULT) {
                    if (msgStr.equals(COMMAND_SET_PRODUCT_MODEL_E53B) || msgStr.equals(COMMAND_SET_PRODUCT_MODEL_K7PRO) || msgStr.equals(COMMAND_SET_PRODUCT_MODEL_K6PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9CHATGPT) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9ANC) || msgStr.equals(COMMAND_SET_PRODUCT_MODEL_NIRVANAZPRO) || msgStr.equals(COMMAND_SET_PRODUCT_MODEL_NIRVANA_ULTRON)) {
                      return;
                    }
                    Log.i(TAG, "run: ------msg == COMMAND_SET_RECEIVE_IN_EAR_DETECTION_RESULT");
                    if (isArtificialShutdownAll) {
                        return;
                    }
                    isReceiveSwitchButtonChange = true;
                    byte[] data = ArrayUtil.toBytes(msgStr);
                    if (data[0] == IN_EAR_DETECTION_ALL) {
                        refreshInEarDetectionUI(true, data[1] == IN_EAR_DETECTION_CLOSE ? isClosed : (data[2] == IN_EAR_DETECTION_RESULT_OUT_EAR ? outEar : inEar));
                        refreshInEarDetectionUI(false, data[3] == IN_EAR_DETECTION_CLOSE ? isClosed : (data[4] == IN_EAR_DETECTION_RESULT_OUT_EAR ? outEar : inEar));
                        if (data[1] == IN_EAR_DETECTION_OPEN && data[3] == IN_EAR_DETECTION_OPEN) {
                            switchButton_in_ear_detection_all.setChecked(true);
                        } else {
                            switchButton_in_ear_detection_all.setChecked(false);
                        }
                    } else {
                        refreshInEarDetectionUI(data[0] == IN_EAR_DETECTION_LEFT_EAR ? true : false, data[1] == IN_EAR_DETECTION_CLOSE ? isClosed : (data[2] == IN_EAR_DETECTION_RESULT_OUT_EAR ? outEar : inEar));
                        boolean otherState = data[0] == IN_EAR_DETECTION_LEFT_EAR ? switchButton_in_ear_detection_right.isChecked() : switchButton_in_ear_detection_left.isChecked();
                        if (data[1] == IN_EAR_DETECTION_OPEN && otherState) {
                            switchButton_in_ear_detection_all.setChecked(true);
                        } else {
                            switchButton_in_ear_detection_all.setChecked(false);
                        }
                    }
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                Thread.sleep(500);
                                isReceiveSwitchButtonChange = false;

                            } catch (InterruptedException e) {
                                throw new RuntimeException(e);
                            }
                        }
                    }).start();
                } else if (msg == COMMAND_SET_RECEIVE_TWS_AND_MASTER_STATE) {
                    byte[] data = ArrayUtil.toBytes(msgStr);
                    if (data[0] == (byte) 0x00) {
                        text_tws_connect_state.setText("disconnect");
                    } else if (data[0] == (byte) 0x01) {
                        text_tws_connect_state.setText("connect");
                    }
                    if (data[1] == (byte) 0x01) {
                        text_master_ear.setText("Left");
                        text_slave_ear.setText("Right");
                    } else if (data[1] == (byte) 0x02) {
                        text_master_ear.setText("Right");
                        text_slave_ear.setText("Left");
                    }
                    if (data[0] == (byte) 0x00) {
                        if (data[1] == (byte) 0x01) {
                            text_in_ear_detection_right.setText(outEar);
                            text_in_ear_detection_right.setTextColor(getColor(R.color.black));
                        } else if (data[1] == (byte) 0x02) {
                            text_in_ear_detection_left.setText(outEar);
                            text_in_ear_detection_left.setTextColor(getColor(R.color.black));
                        }
                    }
                } else if (msg == COMMAND_SET_RECEIVE_SPP_CONNECT_STATUS) {
//                    hasGetSppStatus = true;
//                    refreshSppState(Integer.valueOf(msgStr));
                } else if (msg == COMMAND_SET_RECEIVE_BUTTON_STATUS) {
                    String[] curState = new String[]{"no functon", "Last Music", "Next Music", "ANC", "Call back", "Volume+", "Volume-", "Play music", "Stop music", "Wake up voice assistant", "play/pause", "game mode", "ALGO", "Speakthru"};
                    byte[] data = ArrayUtil.toBytes(msgStr);
                    Log.i(TAG, "COMMAND_SET_RECEIVE_BUTTON_STATUS: -------------" + data.length);
                    Log.i(TAG, "COMMAND_SET_RECEIVE_BUTTON_STATUS: -------------" + msgStr);
                    if (data[0] == GET_BUTTON_STATE_LEFT_CLICK && data[1] != (byte) 0xff) {
                        button_state_1.setText(curState[data[1]]);
                    }
                    if (data[2] == GET_BUTTON_STATE_RIGHT_CLICK && data[3] != (byte) 0xff) {
                        button_state_2.setText(curState[data[3]]);
                    }
                    if (data[4] == GET_BUTTON_STATE_LEFT_DOUBLE_CLICK && data[5] != (byte) 0xff) {
                        button_state_3.setText(curState[data[5]]);
                    }
                    if (data[6] == GET_BUTTON_STATE_RIGHT_DOUBLE_CLICK && data[7] != (byte) 0xff) {
                        button_state_4.setText(curState[data[7]]);
                    }
                    if (data[8] == GET_BUTTON_STATE_LEFT_TRIPLE_CLICK && data[9] != (byte) 0xff) {
                        int s = data[9];
//                        if (s > 0 && s < curState.length) {
                            button_state_5.setText(curState[s]);
//                        }
                    } if (data[10] == GET_BUTTON_STATE_RIGHT_TRIPLE_CLICK && data[11] != (byte) 0xff) {
                        int s = data[11];
//                        if (s > 0 && s < curState.length) {
                            button_state_6.setText(curState[s]);
//                        }
                    } if (data[12] == GET_BUTTON_STATE_LEFT_LONG_PRESS && data[13] != (byte) 0xff) {
                        int s = data[13];
//                        if (s > 0 && s < curState.length) {
                            button_state_7.setText(curState[s]);
//                        }
                    } if (data[14] == GET_BUTTON_STATE_RIGHT_LONG_PRESS && data[15] != (byte) 0xff) {
                        int s = data[15];
//                        if (s > 0 && s < curState.length) {
                            button_state_8.setText(curState[s]);
//                        }
                    }
                    if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_AD271) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_D49) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_J1) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_1053) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_ADBLUE)) {
                        isReceiveButtonCmd = true;
                        if (data[16] == GET_BUTTON_STATE_LEFT_SWIPE) {
                            if (data[17] == BUTTON_SETTINGS_CONTROL_SWIPE_ENABLE) {
                                switchButton_disable_swipe_left.setChecked(false);
                            } else {
                                switchButton_disable_swipe_left.setChecked(true);
                            }
                        }
                        if (data[18] == GET_BUTTON_STATE_RIGHT_SWIPE) {
                            if (data[19] == BUTTON_SETTINGS_CONTROL_SWIPE_ENABLE) {
                                switchButton_disable_swipe_right.setChecked(false);
                            } else {
                                switchButton_disable_swipe_right.setChecked(true);
                            }
                        }
                        isReceiveButtonCmd = false;
                    }
                } else if (msg == COMMAND_SET_RECEIVE_CURRENT_PRODUCT_MODEL) {
                    curModelName = msgStr;
                    command_set_current_product_model.setText("PRODUCT MODEL:" + curModelName);
                    if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IE2A) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_D49)) {
                        if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IE2A)) {
                            linear_bes_spatial_state.setVisibility(View.VISIBLE);
                            getSwitchState(COMMAND_SET_TYPE_BES_SPATIAL_SWITCH);

                            linear_mimi_state.setVisibility(View.VISIBLE);
                            getSwitchState(COMMAND_SET_TYPE_MIMI_SWITCH);
                        }
                    } else if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IM2_DOLBY) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_R1)) {
                        linear_dolby_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_DOLBY_SWITCH);

                        linear_mimi_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_MIMI_SWITCH);
                    } else if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IH5) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_R2)) {
                        linear_bes_spatial_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_BES_SPATIAL_SWITCH);

                        linear_mimi_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_MIMI_SWITCH);

                        linear_anc_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_REGULATE_ANC);

                    } else if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IH6)) {
                        linear_ceva_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_CEAV_SWITCH);

                        linear_mimi_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_MIMI_SWITCH);

                        linear_anc_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_REGULATE_ANC);

                        regulate_anc_speakthru.setVisibility(View.VISIBLE);
                        text_anc_speakthru.setVisibility(View.VISIBLE);
                    } else if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_255ARCPLUS) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_255ARC) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_KB20) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_TOUCH_HEADPHONE)) {
                        if (!curModelName.equals(COMMAND_SET_PRODUCT_MODEL_TOUCH_HEADPHONE)) {
                            linear_bes_spatial_state.setVisibility(View.VISIBLE);
                            getSwitchState(COMMAND_SET_TYPE_BES_SPATIAL_SWITCH);
                        } else {
                            linear_touch_onoff.setVisibility(View.VISIBLE);
                            getSwitchState(APP_TOTA_TOUCH_ONOFF);
                        }

                        linear_mimi_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_MIMI_SWITCH);

                        if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_255ARC) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_KB20) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_TOUCH_HEADPHONE)) {
                            if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_TOUCH_HEADPHONE)) {
                                linear_dolby_state.setVisibility(View.VISIBLE);
                                getSwitchState(COMMAND_SET_TYPE_DOLBY_SWITCH);
                            }
                        }
                    } else if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_B2) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IM2PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IM2LDAC)) {
                        linear_mimi_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_MIMI_SWITCH);

                        if (!curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IM2LDAC)) {
                            linear_bes_spatial_state.setVisibility(View.VISIBLE);
                            getSwitchState(COMMAND_SET_TYPE_BES_SPATIAL_SWITCH);
                        }
                    } else if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_ANAVRIN) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_NEBULA) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_ZENITH) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IH6PRO)) {
                        linear_dolby_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_DOLBY_SWITCH);

                        linear_mimi_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_MIMI_SWITCH);

                        linear_anc_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_REGULATE_ANC);

                        if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IH6PRO)) {
                            linear_head_tracking_pro.setVisibility(View.VISIBLE);
                        }
                    } else if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IE_Gaming)) {
                        linear_dolby_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_DOLBY_SWITCH);

                        linear_mimi_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_MIMI_SWITCH);

                        linear_ceva_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_CEAV_SWITCH);
                    } else if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_E53B) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_K7PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_K6PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9CHATGPT) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9ANC) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_NIRVANAZPRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_NIRVANA_ULTRON)) {
                        if (!msgStr.equals(COMMAND_SET_PRODUCT_MODEL_I9) && !msgStr.equals(COMMAND_SET_PRODUCT_MODEL_I9PRO) && !msgStr.equals(COMMAND_SET_PRODUCT_MODEL_I9ANC)) {
                            if (!msgStr.equals(COMMAND_SET_PRODUCT_MODEL_I9CHATGPT)) {
                                linear_mimi_state.setVisibility(View.VISIBLE);
                                getSwitchState(COMMAND_SET_TYPE_MIMI_SWITCH);
                            }
                            linear_bes_spatial_state.setVisibility(View.VISIBLE);
                            getSwitchState(COMMAND_SET_TYPE_BES_SPATIAL_SWITCH);
                        }

                        if (!msgStr.equals(COMMAND_SET_PRODUCT_MODEL_K6PRO) && !msgStr.equals(COMMAND_SET_PRODUCT_MODEL_I9) && !msgStr.equals(COMMAND_SET_PRODUCT_MODEL_I9ANC)) {
                            linear_anc_state.setVisibility(View.VISIBLE);
                            getSwitchState(COMMAND_SET_TYPE_REGULATE_ANC);
                        }

                        if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_K7PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_K6PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9CHATGPT) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9ANC)) {
                            seekbar_eq_bg.setVisibility(View.GONE);

                            linear_game_mode.setVisibility(View.VISIBLE);
                            getStateFF(APP_TOTA_GAME);

                            linear_volume.setVisibility(View.VISIBLE);
                            getStateFF(APP_TOTA_VOLUME);

                            eq_basetype.setVisibility(View.GONE);
                            eq_basetype2.setVisibility(View.VISIBLE);

                            seekbar_eq_2_bg.setVisibility(View.VISIBLE);
                        }

                        if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9ANC)) {
                            linear_dolby_state.setVisibility(View.VISIBLE);
                            getSwitchState(COMMAND_SET_TYPE_DOLBY_SWITCH);
                        }
                        if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9ANC)) {
                            linear_anc_state.setVisibility(View.VISIBLE);
                            getSwitchState(COMMAND_SET_TYPE_REGULATE_ANC);
                        }
                    } else if (speakBoxModels.contains(curModelName)) {
                        //speak box
                        setSingleUI();
                        seekbar_eq_bg.setVisibility(View.GONE);
                        eq_bgview.setVisibility(View.GONE);
                        button_setting_bg.setVisibility(View.GONE);

                        linear_led_onoff_state.setVisibility(View.VISIBLE);
                        linear_spatial_audio_state.setVisibility(View.VISIBLE);
                        if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_H50)) {
                            spatial_audio_switch_type_2.setVisibility(View.VISIBLE);
                        } else {
                            spatial_audio_switch_type.setVisibility(View.VISIBLE);
                        }
                        linear_share_mode_onoff.setVisibility(View.VISIBLE);
                        linear_multiPoint_bg.setVisibility(View.GONE);
                        linear_function_btn.setVisibility(View.GONE);
//                        factory_reset_cmd_set.setVisibility(View.GONE);
                        linear_get_codec_type.setVisibility(View.GONE);

                        if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_H50)) {
                            led_switch_type.setVisibility(View.GONE);
                            led_switch_type_2.setVisibility(View.VISIBLE);
                        }

                        getSpeakBoxState();
                    } else if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IE2B)) {
                        linear_bes_spatial_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_BES_SPATIAL_SWITCH);
                    }
                    if (msgStr.equals(COMMAND_SET_PRODUCT_MODEL_K7PRO) || msgStr.equals(COMMAND_SET_PRODUCT_MODEL_K6PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9CHATGPT) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9ANC) || msgStr.equals(COMMAND_SET_PRODUCT_MODEL_255ARC) || msgStr.equals(COMMAND_SET_PRODUCT_MODEL_TOUCH_HEADPHONE) || msgStr.equals(COMMAND_SET_PRODUCT_MODEL_R2)) {
                        setSingleUI();
                    }
                    if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IH6PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_NIRVANAZPRO)) {
                        text_anc_default.setVisibility(View.GONE);
                        regulate_anc_default.setVisibility(View.GONE);
                        text_anc_off.setVisibility(View.VISIBLE);
                        regulate_anc_off.setVisibility(View.VISIBLE);
                        linear_anc_new_state.setVisibility(View.VISIBLE);
                    }
                    if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_AD271) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_D49) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_J1) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_1053) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_ADBLUE)) {
                        line_wear_detect.setVisibility(View.GONE);
                        wear_bg1.setVisibility(View.GONE);
                        wear_bg2.setVisibility(View.GONE);
                        wear_bg3.setVisibility(View.GONE);
                        wear_bg4.setVisibility(View.GONE);

                        tv_click_func_disable.setVisibility(View.VISIBLE);
                        earbuds_click_func_disable.setVisibility(View.VISIBLE);
                        linear_disable_swipe.setVisibility(View.VISIBLE);
                    }
                } else if (msg == COMMAND_SET_RECEIVE_REGULATE_ANC) {
                    isReceiveButtonCmd = true;
                    byte[] ancState = ArrayUtil.toBytes(msgStr);
                    if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IH6PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_NIRVANAZPRO)) {
                        if (ancState[0] == REGULATE_ANC_TYPE_ANC) {
                            linear_anc_new_state.setVisibility(View.VISIBLE);
                            regulate_anc_type.check(R.id.regulate_anc_anc);
                            anc_new_type.check(R.id.anc_new_type_adapt);
                        } else if (ancState[0] == REGULATE_ANC_TYPE_AMNBITNE) {
                            linear_anc_new_state.setVisibility(View.GONE);
                            regulate_anc_type.check(R.id.regulate_anc_ambient);
                        } else if (ancState[0] == REGULATE_ANC_TYPE_DEFAULT) {
                            linear_anc_new_state.setVisibility(View.GONE);
                            regulate_anc_type.check(R.id.regulate_anc_off);
                        } else if (ancState[0] == REGULATE_ANC_TYPE_LOW) {
                            linear_anc_new_state.setVisibility(View.VISIBLE);
                            regulate_anc_type.check(R.id.regulate_anc_anc);
                            anc_new_type.check(R.id.anc_new_type_low);
                        } else if (ancState[0] == REGULATE_ANC_TYPE_MID) {
                            linear_anc_new_state.setVisibility(View.VISIBLE);
                            regulate_anc_type.check(R.id.regulate_anc_anc);
                            anc_new_type.check(R.id.anc_new_type_mid);
                        } else if (ancState[0] == REGULATE_ANC_TYPE_HIGH) {
                            linear_anc_new_state.setVisibility(View.VISIBLE);
                            regulate_anc_type.check(R.id.regulate_anc_anc);
                            anc_new_type.check(R.id.anc_new_type_high);
                        }
                    } else {
                        if (ancState[0] == REGULATE_ANC_TYPE_ANC) {
                            regulate_anc_type.check(R.id.regulate_anc_anc);
                        } else if (ancState[0] == REGULATE_ANC_TYPE_AMNBITNE) {
                            regulate_anc_type.check(R.id.regulate_anc_ambient);
                        } else if (ancState[0] == REGULATE_ANC_TYPE_DEFAULT) {
                            regulate_anc_type.check(R.id.regulate_anc_default);
                        } else if (ancState[0] == REGULATE_ANC_TYPE_SPEAKTHRU) {
                            regulate_anc_type.check(R.id.regulate_anc_speakthru);
                        }
                    }
                    isReceiveButtonCmd = false;
                } else if (msg == COMMAND_SET_RECEIVE_EQ_SWITCH) {
                    isReceiveButtonCmd = true;
                    byte[] eqState = ArrayUtil.toBytes(msgStr);
                    if (eqState[0] == SWITCH_STATUS_OPEN) {
                        if (openSpa && linear_bes_spatial_state.getVisibility() == View.VISIBLE && bes_spatial_switch_type.getCheckedRadioButtonId() == R.id.bes_spatial_switch_open) {
                            openSpa = false;
                            isReceiveButtonCmd = false;
                            mPresenter.sendTestData(COMMAND_SET_TYPE_EQ_SWITCH, true, SWITCH_STATUS_SET, SWITCH_STATUS_OFF);
                            return;
                        }
                        eq_switch_type.check(R.id.eq_switch_open);

                        seekbar_eq_60.setProgress(5);
                        seekbar_eq_120.setProgress(5);
                        seekbar_eq_250.setProgress(5);
                        seekbar_eq_750.setProgress(5);
                        seekbar_eq_2k.setProgress(5);
                        seekbar_eq_5k.setProgress(5);
                        seekbar_eq_15k.setProgress(5);
                    } else if (eqState[0] == SWITCH_STATUS_OFF) {
                        eq_switch_type.check(R.id.eq_switch_off);
                    }
                    isReceiveButtonCmd = false;
                    refreshEqButtonStateUI();
                    if (eqState[1] == (byte) 0x00) {
                        if (eqState[0] == SWITCH_STATUS_OFF) {
                            seekbar_eq_60.setProgress(5);
                            seekbar_eq_120.setProgress(5);
                            seekbar_eq_250.setProgress(5);
                            seekbar_eq_750.setProgress(5);
                            seekbar_eq_2k.setProgress(5);
                            seekbar_eq_5k.setProgress(5);
                            seekbar_eq_15k.setProgress(5);
                        }
                        return;
                    } else if (eqState[1] == (byte) 0xff) {
                        seekbar_eq_60.setProgress(eqState[2]);
                        seekbar_eq_120.setProgress(eqState[3]);
                        seekbar_eq_250.setProgress(eqState[4]);
                        seekbar_eq_750.setProgress(eqState[5]);
                        seekbar_eq_2k.setProgress(eqState[6]);
                        seekbar_eq_5k.setProgress(eqState[7]);
                        seekbar_eq_15k.setProgress(eqState[8]);
                        return;
                    }
                    isReceiveButtonCmd = true;
//                    if (eqState[1] == EQ_TYPE_DEFAULT) {
//                        eq_basetype.check(R.id.eq_basetype_default);
//                    } else
                    if (eqState[1] == EQ_TYPE_POP) {
                        eq_basetype.check(R.id.eq_basetype_pop);
                    } else if (eqState[1] == EQ_TYPE_ROCK) {
                        eq_basetype.check(R.id.eq_basetype_rock);
                    } else if (eqState[1] == EQ_TYPE_JAZZ) {
                        eq_basetype.check(R.id.eq_basetype_jazz);
                    } else if (eqState[1] == EQ_TYPE_CLASSIC) {
                        eq_basetype.check(R.id.eq_basetype_classic);
                    } else if (eqState[1] == EQ_TYPE_COUNTRY) {
                        eq_basetype.check(R.id.eq_basetype_country);
                    }
                    isReceiveButtonCmd = false;
                } else if (msg == COMMAND_SET_RECEIVE_DOLBY_SWITCH) {
                    byte[] dolbyState = ArrayUtil.toBytes(msgStr);
//                    if (dolbyState[0] == SWITCH_STATUS_OPEN) {
//                        dolby_switch_type.check(R.id.dolby_switch_open);
//                        dolby_switch_title.setText("Dolby State: ON");
//                        dolby_switch_title.setTextColor(getColor(R.color.green));
//                    } else if (dolbyState[0] == SWITCH_STATUS_OFF) {
//                        dolby_switch_type.check(R.id.dolby_switch_off);
//                        dolby_switch_title.setText("Dolby State: OFF");
//                        dolby_switch_title.setTextColor(getColor(R.color.fff06e6e));
//                    }
                    if (dolbyState[0] == SWITCH_STATUS_OFF) {
                        curDolbyType = 0;
                        refreshDolbyButtonUI();
                        return;
                    }
                    if (dolbyState.length > 1) {
                        curDolbyType = dolbyState[1];
                        if (curDolbyType == 0) {
                            curDolbyType = 3;
                        }
                        refreshDolbyButtonUI();
                    }
                } else if (msg == COMMAND_SET_RECEIVE_BES_SPATIAL_SWITCH) {
                    isReceiveButtonCmd = true;
                    byte[] state = ArrayUtil.toBytes(msgStr);
                    if (state[0] == SWITCH_STATUS_OPEN) {
                        bes_spatial_switch_type.check(R.id.bes_spatial_switch_open);
                    } else if (state[0] == SWITCH_STATUS_OFF) {
                        bes_spatial_switch_type.check(R.id.bes_spatial_switch_off);
                    }
                    isReceiveButtonCmd = false;
                } else if (msg == COMMAND_SET_RECEIVE_MIMI_SWITCH) {
                    isReceiveButtonCmd = true;
                    byte[] state = ArrayUtil.toBytes(msgStr);
                    if (state[0] == SWITCH_STATUS_OPEN) {
                        mimi_switch_type.check(R.id.mimi_switch_open);
                    } else if (state[0] == SWITCH_STATUS_OFF) {
                        mimi_switch_type.check(R.id.mimi_switch_off);
                    }
                    isReceiveButtonCmd = false;
                } else if (msg == COMMAND_SET_RECEIVE_MIMI_TECH_LEVEL) {
                    tech_level_text.setText(msgStr);
                } else if (msg == COMMAND_SET_RECEIVE_MIMI_INTENSITY) {
                    float result = (float) ((Integer.valueOf(msgStr)) * 0.01);
                    if (result < 0) {
                        result = 0;
                    }
                    seekbar_mimi_switch_intensity.setProgress(Integer.valueOf(msgStr));
                    text_mimi_switch_intensity.setText("intensity: " + result + "");
                } else if (msg == COMMAND_SET_RECEIVE_MIMI_SET_PRESENT_RESULT) {
                    byte[] state = ArrayUtil.toBytes(msgStr);
                    if (state[0] == MIMI_SET_PRESENT_SUCCESS) {
                        ActivityUtils.showToast("MIMI set preset success");
                    } else {
                        ActivityUtils.showToast("MIMI set preset fail");
                    }
                }
                else if (msg == COMMAND_SET_RECEIVE_CEVA_SWITCH) {
                    isReceiveButtonCmd = true;
                    byte[] state = ArrayUtil.toBytes(msgStr);
                    if (state[0] == SWITCH_STATUS_OPEN) {
                        ceva_switch_type.check(R.id.ceva_switch_open);
                    } else if (state[0] == SWITCH_STATUS_OFF) {
                        ceva_switch_type.check(R.id.ceva_switch_off);
                    }
//                    else if (state[0] == SWITCH_STATUS_NEW) {
//                        ceva_switch_type.check(R.id.ceva_switch_off);
//                        ActivityUtils.showToast("CEVA cannot be opened with one ear");
//                    }
                    isReceiveButtonCmd = false;
                } else if (msg == COMMAND_SET_RECEIVE_VERSION_CRC) {
                    command_set_current_version.setText(msgStr);
                } else if (msg == COMMAND_SET_RECEIVE_START_OTA) {
                    mPresenter.stopSpp();
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                    Intent intent0 = new Intent();
                    BesServiceConfig serviceConfig0 = new BesServiceConfig();
                    serviceConfig0.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
                    serviceConfig0.setServiceUUID(BesSdkConstants.BES_OTA_SERVICE_OTA_UUID_OLD);
                    serviceConfig0.setUSER_FLAG(-1);
                    serviceConfig0.setDevice(mHmDevice);
                    intent0.putExtra(Constants.OTA_SERVICE_CONFIG, (Serializable) serviceConfig0);
                    ActivityUtils.gotoAct(intent0, instance, OtaUIActivity.class);
                } else if (msg == COMMAND_SET_RECEIVE_SWITCH_ROLE) {
                    text_tws_connect_state.setText("disconnect");
                    isReceiveRoleSwitch = true;
                } else if (msg == COMMAND_SET_RECEIVE_LED_ONOFF_RESULT) {
                    isReceiveButtonCmd = true;
                    byte[] state = ArrayUtil.toBytes(msgStr);
                    if (state[0] == SWITCH_STATUS_OPEN_0) {
                        led_switch_type_2.check(R.id.led_switch_2_open_0);
                    } else if (state[0] == SWITCH_STATUS_OPEN) {
                        if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_H50)) {
                            led_switch_type_2.check(R.id.led_switch_2_open_1);
                        } else {
                            led_switch_type.check(R.id.led_switch_open);
                        }
                    } else if (state[0] == SWITCH_STATUS_OFF) {
                        if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_H50)) {
                            led_switch_type_2.check(R.id.led_switch_2_off);
                        } else {
                            led_switch_type.check(R.id.led_switch_off);
                        }
                    } else if (state[0] == SWITCH_STATUS_NEW) {
                        if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_H50)) {
                            led_switch_type_2.check(R.id.led_switch_2_flash);
                        } else {
                            led_switch_type.check(R.id.led_switch_flash);
                        }
                    }
                    isReceiveButtonCmd = false;
                } else if (msg == COMMAND_SET_RECEIVE_SPATIAL_AUDIO_RESULT) {
                    isReceiveButtonCmd = true;
                    byte[] state = ArrayUtil.toBytes(msgStr);
                    if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_H50)) {
                        if (state[0] == SWITCH_STATUS_OPEN_0) {
                            spatial_audio_switch_type_2.check(R.id.spatial_audio_switch_spa);
                        } else if (state[0] == SWITCH_STATUS_OPEN) {
                            spatial_audio_switch_type_2.check(R.id.spatial_audio_switch_360);
                        } else if (state[0] == SWITCH_STATUS_OFF) {
                            spatial_audio_switch_type_2.check(R.id.spatial_audio_switch_off_2);
                        }
                    } else  {
                        if (state[0] == SWITCH_STATUS_OPEN) {
                            spatial_audio_switch_type.check(R.id.spatial_audio_switch_open);
                        } else if (state[0] == SWITCH_STATUS_OFF) {
                            spatial_audio_switch_type.check(R.id.spatial_audio_switch_off);
                        }
                    }
                    isReceiveButtonCmd = false;
                } else if (msg == COMMAND_SET_RECEIVE_HEAD_TRACKING_STATE_RESULT) {
                    byte[] state = ArrayUtil.toBytes(msgStr);
                    if (state[0] == HEAD_TRACKING_STATE_OFF) {
                        tv_get_head_tracking_result.setText("OFF");
                    } else if (state[0] == HEAD_TRACKING_STATE_ON) {
                        tv_get_head_tracking_result.setText("ON");
                    }
                } else if (msg == COMMAND_SET_RECEIVE_AUTO_CENTER_MODE_RESULT) {
                    byte[] state = ArrayUtil.toBytes(msgStr);
                    if (state[0] == AUTO_CENTER_MODE_SLOW) {
                        tv_get_auto_center_mode_result.setText("Slow");
                    } else if (state[0] == AUTO_CENTER_MODE_FAST) {
                        tv_get_auto_center_mode_result.setText("Fast");
                    } else if (state[0] == AUTO_CENTER_MODE_STATIONARY) {
                        tv_get_auto_center_mode_result.setText("Stationary");
                    }
                } else if (msg == COMMAND_SET_RECEIVE_SHARE_MODE_RESULT) {
                    isReceiveButtonCmd = true;
                    byte[] state = ArrayUtil.toBytes(msgStr);
                    if (state[0] == SWITCH_STATUS_OPEN) {
                        share_mode_switch_type.check(R.id.share_mode_switch_open);
                    } else if (state[0] == SWITCH_STATUS_OFF) {
                        share_mode_switch_type.check(R.id.share_mode_switch_off);
                    }
                    isReceiveButtonCmd = false;
                } else if (msg == COMMAND_SET_RECEIVE_IMU_ORIENTATION_RESULT) {
                    byte[] imuData = ArrayUtil.toBytes(msgStr);
                    ByteBuffer bb = ByteBuffer.wrap(imuData);
                    bb.order(ByteOrder.LITTLE_ENDIAN);
                    float q14 = 1.0f / (1 << 14);
                    float w = bb.getShort(0) * q14;
                    float x = bb.getShort(2) * q14;
                    float y = bb.getShort(4) * q14;
                    float z = bb.getShort(6) * q14;
                    float rad2deg = (float) (180F/Math.PI);
                    float[] angle = getOrientation(w, y, x, -z);
                    float yaw = angle[0] * rad2deg;
                    float pitch = angle[1] * rad2deg;
                    float roll = angle[2] * rad2deg;
                    tv_get_imu_orientation_result.setText("yaw:" + yaw + "  pitch:" + pitch + "  roll:" + roll);
                } else if (msg == COMMAND_SET_RECEIVE_CODEC_TYPE_RESULT) {
                    byte[] state = ArrayUtil.toBytes(msgStr);
                    if (state[0] == (byte) 0xBD) {
                        text_codec_type.setText("No type was obtained");
                        return;
                    }
                    if (state[1] == (byte) 0x04 || state[2] == (byte) 0x04) {
                        linear_mimi_state.setVisibility(View.GONE);
                    } else {
                        if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_NIRVANAZPRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IM2_DOLBY) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_R1) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IH5) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_R2) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IH6) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_255ARCPLUS) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_255ARC) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_KB20) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_TOUCH_HEADPHONE) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_B2) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IM2PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IM2LDAC) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_ANAVRIN) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_NEBULA) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_ZENITH) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IH6PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IE_Gaming)) {
                            linear_mimi_state.setVisibility(View.VISIBLE);
                        }
                    }
                    String msg ="A:" + getCodecType(state[0]) + "    " + getCodecType(state[1]);
                    if (state[2] != (byte) 0xBD) {
                        msg = msg + "\n" + "B:" + getCodecType(state[0]) + "    " + getCodecType(state[2]);
                    }
                    text_codec_type.setText(msg);
                } else if (msg == COMMAND_SET_RECEIVE_GAME_MODE_RESULT) {
                    byte[] state = ArrayUtil.toBytes(msgStr);
                    isReceiveButtonCmd = true;
                    if (state[0] == SWITCH_STATUS_OPEN_0) {
                        game_mode_type.check(R.id.game_mode_close);
                    } else if (state[0] == SWITCH_STATUS_OPEN) {
                        game_mode_type.check(R.id.game_mode_open);
                    }
                    isReceiveButtonCmd = false;
                } else if (msg == COMMAND_SET_RECEIVE_VOLUME_RESULT) {
                    byte[] state = ArrayUtil.toBytes(msgStr);
                    tv_volume.setText("A:" + state[0] + "\n" + "B:" + state[1]);
                    seekbar_volume.setProgress(state[0]);
                    tv_volume_cur.setText("" + state[0]);
                } else if (msg == COMMAND_SET_RECEIVE_TOUCH_ONOFF_RESULT) {
                    byte[] state = ArrayUtil.toBytes(msgStr);
                    if (state[0] == SWITCH_STATUS_OPEN_0) {
                        switchButton_touch_onoff.setChecked(false);
                    } else if (state[0] == SWITCH_STATUS_OPEN) {
                        switchButton_touch_onoff.setChecked(true);
                    }
                }
            }
        });
    }

    private String getCodecType(byte data) {
        //只支持AAC，就是0x01，只SBC就是0x02，LDAC=0x04,三个都支持就是0x07
        //AAC+SBC = 0x03
        if (data == 0x01) {
            return "AAC";
        } else if (data == 0x02) {
            return "SBC";
        } else if (data == 0x04) {
            return "LDAC";
        } else if (data == 0x03) {
            return "AAC/SBC";
        }  else if (data == 0x05) {
            return "AAC/LDAC";
        } else if (data == 0x06) {
            return "SBC/LDAC";
        } else if (data == 0x07) {
            return "AAC/SBC/LDAC";
        }
        return "";
    }

    private float[] getOrientation(float w, float x, float y, float z) {
        float yaw = (float) Math.atan2((2f * (w * z + x * y)), (1f - 2f * (y * y + z * z)));
        float pitch = (float) Math.asin(2f * (w * y - x * z));
        float roll = (float) Math.atan2((2f * (w * x + y * z)), (1f - 2f * (x * x + y * y)));
        return new float[]{yaw, pitch, roll};
    }

    private void getSpeakBoxState() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        mPresenter.sendTestData(COMMAND_SET_TYPE_LED_ONOFF, true, SWITCH_STATUS_GET);
                    }
                });

                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }

                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        mPresenter.sendTestData(COMMAND_SET_TYPE_SPATIAL_AUDIO_ONOFF, true, SWITCH_STATUS_GET);
                    }
                });

                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }

                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        mPresenter.sendTestData(COMMAND_SET_TYPE_SHARE_MODE_ONOFF, true, SWITCH_STATUS_GET);
                    }
                });

                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }

                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        mPresenter.sendTestData(COMMAND_SET_TYPE_BATTREY_PERCENTAGE, true, BATTREY_PERCENTAGE_LEFT);
                    }
                });
            }
        }).start();
    }

    private void setSingleUI() {
        check_left_speaker.setText("Check Speaker");
        check_right_speaker.setVisibility(View.INVISIBLE);

        if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_TOUCH_HEADPHONE)) {
            earbuds_click_left.setText("Click Touch");
            earbuds_click_right.setText("Click MFB");

            earbuds_double_click_left.setText("Double Click Touch");
            earbuds_double_click_right.setText("Double Click MFB");

            earbuds_triple_click_left.setText("Triple Click Touch");
            earbuds_triple_click_right.setText("Triple Click MFB");

            earbuds_long_press_left.setText("Long Press Touch");
            earbuds_long_press_right.setText("Long Press MFB");

        } else {
            earbuds_click_left.setText("Click Earbud");
            earbuds_click_right.setVisibility(View.INVISIBLE);
            button_state_2.setVisibility(View.INVISIBLE);

            earbuds_double_click_left.setText("Double Click Earbud");
            earbuds_double_click_right.setVisibility(View.INVISIBLE);
            button_state_4.setVisibility(View.INVISIBLE);

            earbuds_triple_click_left.setText("Triple Click Earbud");
            earbuds_triple_click_right.setVisibility(View.INVISIBLE);
            button_state_6.setVisibility(View.INVISIBLE);

            earbuds_long_press_left.setText("Long Press Earbud");
            earbuds_long_press_right.setVisibility(View.INVISIBLE);
            button_state_8.setVisibility(View.INVISIBLE);
        }

        line_wear_detect.setVisibility(View.GONE);
        wear_bg1.setVisibility(View.GONE);
        wear_bg2.setVisibility(View.GONE);
        wear_bg3.setVisibility(View.GONE);
        wear_bg4.setVisibility(View.GONE);

        button_get_left_battery.setText("GET Battery");
        button_get_right_battery.setVisibility(View.GONE);
        text_right_battery.setVisibility(View.GONE);
        button_get_box_battery.setVisibility(View.GONE);
        text_box_battery.setVisibility(View.GONE);
    }

    private void getStateFF(byte type) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    Thread.sleep(50);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mPresenter.sendTestData(type, true, APP_TOTA_GET);
                        }
                    });
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }).start();
    }
    private void getSwitchState(byte type) {
        if (type == COMMAND_SET_TYPE_DOLBY_SWITCH) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    boolean dlbDolbyDevice = false;
                    for(int i = 0; i < MediaCodecList.getCodecCount(); i ++) {
                        MediaCodecInfo info = MediaCodecList.getCodecInfoAt(i);
                        if (info.isEncoder()) {
                            continue;
                        }
                        for (String type : info.getSupportedTypes()) {
                            if ((type.equals("audio/ac4")) || (type.equals("audio/eac3"))) {
                                dlbDolbyDevice = true;
                            }
                        }
                    }
                    ActivityUtils.showToast("DAX:" + (dlbDolbyDevice ? "YES" : "NO"));
                    mPresenter.sendTestData(APP_TOTA_DAX_PHONE, true, (dlbDolbyDevice ? DAX_PHONE_TRUE : DAX_PHONE_FALSE));
                }
            });
        }
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    Thread.sleep(50);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mPresenter.sendTestData(type, true, SWITCH_STATUS_GET);
                        }
                    });
                    if (type == COMMAND_SET_TYPE_MIMI_SWITCH) {
                        Thread.sleep(200);
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mPresenter.sendTestData(type, true, SWITCH_STATUS_SET_PARAM_2);
                            }
                        });
                    }
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }).start();
    }

    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {

    }

    public void initlayout() {
        setContentView(R.layout.activity_commandset);
        inittoolbar(cur_title);

        logV = (TextView) findViewById(R.id.logV);
        done = (Button) findViewById(R.id.done);
        loginfo = (View) findViewById(R.id.loginfo);
        tv_title.setOnClickListener(instance);
        done.setOnClickListener(instance);

        earbuds_click_func_0 = (RadioGroup) findViewById(R.id.earbuds_click_func_0);
        earbuds_click_func_1 = (RadioGroup) findViewById(R.id.earbuds_click_func_1);
        earbuds_click_func_2 = (RadioGroup) findViewById(R.id.earbuds_click_func_2);
        earbuds_click_func_3 = (RadioGroup) findViewById(R.id.earbuds_click_func_3);

        switchButton_disable_swipe_left = (com.suke.widget.SwitchButton) findViewById(R.id.switchButton_disable_swipe_left);
        switchButton_disable_swipe_right = (com.suke.widget.SwitchButton) findViewById(R.id.switchButton_disable_swipe_right);
        switchButton_disable_swipe_left.setOnCheckedChangeListener(instance);
        switchButton_disable_swipe_right.setOnCheckedChangeListener(instance);

        linear_disable_swipe = (LinearLayout) findViewById(R.id.linear_disable_swipe);
        earbuds_click_left = (Button) findViewById(R.id.earbuds_click_left);
        earbuds_click_right = (Button) findViewById(R.id.earbuds_click_right);
        earbuds_double_click_left = (Button) findViewById(R.id.earbuds_double_click_left);
        earbuds_double_click_right = (Button) findViewById(R.id.earbuds_double_click_right);
        earbuds_triple_click_left = (Button) findViewById(R.id.earbuds_triple_click_left);
        earbuds_triple_click_right = (Button) findViewById(R.id.earbuds_triple_click_right);
        earbuds_long_press_left = (Button) findViewById(R.id.earbuds_long_press_left);
        earbuds_long_press_right = (Button) findViewById(R.id.earbuds_long_press_right);
        tv_click_func_disable = (TextView) findViewById(R.id.tv_click_func_disable);
        earbuds_click_func_disable = (RadioButton) findViewById(R.id.earbuds_click_func_disable);

        factory_reset_cmd_set = (Button) findViewById(R.id.factory_reset_cmd_set);
        play = (Button) findViewById(R.id.play);
        pause = (Button) findViewById(R.id.pause);
        next = (Button) findViewById(R.id.next);
        prev = (Button) findViewById(R.id.prev);
        button_get_left_battery = (Button) findViewById(R.id.button_get_left_battery);
        text_left_battery = (TextView) findViewById(R.id.text_left_battery);
        button_get_right_battery = (Button) findViewById(R.id.button_get_right_battery);
        text_right_battery = (TextView) findViewById(R.id.text_right_battery);
        button_get_box_battery = (Button) findViewById(R.id.button_get_box_battery);
        text_box_battery = (TextView) findViewById(R.id.text_box_battery);

        linear_get_codec_type = (LinearLayout) findViewById(R.id.linear_get_codec_type);
        button_get_codec_type = (Button) findViewById(R.id.button_get_codec_type);
        text_codec_type = (TextView) findViewById(R.id.text_codec_type);
//        fit_test = (Button) findViewById(R.id.fit_test);
//        switchButton_fit_test = (com.suke.widget.SwitchButton) findViewById(R.id.switchButton_fit_test);
//        fit_text = (TextView) findViewById(R.id.fit_text);
        eq_test = (Button) findViewById(R.id.eq_test);
        eq_text = (EditText) findViewById(R.id.eq_text);
        command_set_receive_data = (EditText) findViewById(R.id.command_set_receive_data);
        eq_basetype = (RadioGroup) findViewById(R.id.eq_basetype);
        command_set_current_product_model = (TextView) findViewById(R.id.command_set_current_product_model);
        command_set_current_version = (TextView) findViewById(R.id.command_set_current_version);
        regulate_anc_type = (RadioGroup) findViewById(R.id.regulate_anc_type);
        eq_switch_type = (RadioGroup) findViewById(R.id.eq_switch_type);
//        dolby_switch_type = (RadioGroup) findViewById(R.id.dolby_switch_type);
        bes_spatial_switch_type = (RadioGroup) findViewById(R.id.bes_spatial_switch_type);
        mimi_switch_type = (RadioGroup) findViewById(R.id.mimi_switch_type);
        ceva_switch_type = (RadioGroup) findViewById(R.id.ceva_switch_type);

        wear_bg1 = (LinearLayout) findViewById(R.id.wear_bg1);
        wear_bg2 = (LinearLayout) findViewById(R.id.wear_bg2);
        wear_bg3 = (LinearLayout) findViewById(R.id.wear_bg3);
        wear_bg4 = (LinearLayout) findViewById(R.id.wear_bg4);

        disconnect = (Button) findViewById(R.id.disconnect);
//        scrollview_0 = (MyScrollView) findViewById(R.id.scrollview_0);
//        scrollview_0.addInterface(instance, 0);
//        scrollview_1 = (MyScrollView) findViewById(R.id.scrollview_1);
//        scrollview_1.addInterface(instance, 1);
//        scrollview_2 = (MyScrollView) findViewById(R.id.scrollview_2);
//        scrollview_2.addInterface(instance, 2);
//        scrollview_3 = (MyScrollView) findViewById(R.id.scrollview_3);
//        scrollview_3.addInterface(instance, 3);
//        scrollview_4 = (MyScrollView) findViewById(R.id.scrollview_4);
//        scrollview_4.addInterface(instance, 4);
//        scrollview_5 = (MyScrollView) findViewById(R.id.scrollview_5);
//        scrollview_5.addInterface(instance, 5);
//        scrollview_6 = (MyScrollView) findViewById(R.id.scrollview_6);
//        scrollview_6.addInterface(instance, 6);
//        scrollview_7 = (MyScrollView) findViewById(R.id.scrollview_7);
//        scrollview_7.addInterface(instance, 7);
//        scrollview_8 = (MyScrollView) findViewById(R.id.scrollview_8);
//        scrollview_8.addInterface(instance, 8);
//        scrollview_9 = (MyScrollView) findViewById(R.id.scrollview_9);
//        scrollview_9.addInterface(instance, 9);

//        switchButton_fit_test.setChecked(true);
//        switchButton_fit_test.setOnCheckedChangeListener(instance);
        earbuds_click_func_0.setOnCheckedChangeListener(instance);
        earbuds_click_func_1.setOnCheckedChangeListener(instance);
        earbuds_click_func_2.setOnCheckedChangeListener(instance);
        earbuds_click_func_3.setOnCheckedChangeListener(instance);

        factory_reset_cmd_set.setOnClickListener(instance);
        earbuds_click_left.setOnClickListener(instance);
        earbuds_click_right.setOnClickListener(instance);
        earbuds_double_click_left.setOnClickListener(instance);
        earbuds_double_click_right.setOnClickListener(instance);
        earbuds_triple_click_left.setOnClickListener(instance);
        earbuds_triple_click_right.setOnClickListener(instance);
        earbuds_long_press_left.setOnClickListener(instance);
        earbuds_long_press_right.setOnClickListener(instance);

        play.setOnClickListener(instance);
        pause.setOnClickListener(instance);
        next.setOnClickListener(instance);
        prev.setOnClickListener(instance);
        button_get_left_battery.setOnClickListener(instance);
        button_get_right_battery.setOnClickListener(instance);
        button_get_codec_type.setOnClickListener(instance);
        button_get_box_battery.setOnClickListener(instance);

//        fit_test.setOnClickListener(instance);
        eq_test.setOnClickListener(instance);
        eq_basetype.setOnCheckedChangeListener(instance);
        regulate_anc_type.setOnCheckedChangeListener(instance);
        eq_switch_type.setOnCheckedChangeListener(instance);
//        dolby_switch_type.setOnCheckedChangeListener(instance);
        bes_spatial_switch_type.setOnCheckedChangeListener(instance);
        mimi_switch_type.setOnCheckedChangeListener(instance);
        ceva_switch_type.setOnCheckedChangeListener(instance);

        disconnect.setOnClickListener(instance);

        start_ota_btn = (Button) findViewById(R.id.start_ota_btn);
        start_ota_btn.setOnClickListener(instance);
        get_bt_state = (Button) findViewById(R.id.get_bt_state);
        get_bt_state.setOnClickListener(instance);
        bt_state_text = (TextView) findViewById(R.id.bt_state_text);
        refreshBtState(mPresenter.getBtState());

        get_spp_state = (Button) findViewById(R.id.get_spp_state);
        get_spp_state.setOnClickListener(instance);
        spp_state_text = (TextView) findViewById(R.id.spp_state_text);
        mPresenter.sendTestData(COMMAND_SET_TYPE_GET_SPP_CONNECT_STATUS, false, (byte) 0x00);

        check_mic_state = (Button) findViewById(R.id.check_mic_state);
        check_mic_state.setOnClickListener(instance);
        mic_state_text = (TextView) findViewById(R.id.mic_state_text);

        check_left_speaker = (Button) findViewById(R.id.check_left_speaker);
        check_left_speaker.setOnClickListener(instance);
        check_right_speaker = (Button) findViewById(R.id.check_right_speaker);
        check_right_speaker.setOnClickListener(instance);

        line_wear_detect = (View) findViewById(R.id.line_wear_detect);
        switchButton_multiPoint = (com.suke.widget.SwitchButton) findViewById(R.id.switchButton_multiPoint);
        switchButton_in_ear_detection_all = (com.suke.widget.SwitchButton) findViewById(R.id.switchButton_in_ear_detection_all);
        switchButton_in_ear_detection_left = (com.suke.widget.SwitchButton) findViewById(R.id.switchButton_in_ear_detection_left);
        switchButton_in_ear_detection_right = (com.suke.widget.SwitchButton) findViewById(R.id.switchButton_in_ear_detection_right);
        switchButton_touch_onoff = (com.suke.widget.SwitchButton) findViewById(R.id.switchButton_touch_onoff);
        switchButton_wd_prompt = (com.suke.widget.SwitchButton) findViewById(R.id.switchButton_wd_prompt);
        switchButton_multiPoint.setOnCheckedChangeListener(instance);
        switchButton_in_ear_detection_all.setOnCheckedChangeListener(instance);
        switchButton_in_ear_detection_left.setOnCheckedChangeListener(instance);
        switchButton_in_ear_detection_right.setOnCheckedChangeListener(instance);
        switchButton_wd_prompt.setOnCheckedChangeListener(instance);
        switchButton_touch_onoff.setOnCheckedChangeListener(instance);

        text_tws_connect_state = (TextView) findViewById(R.id.text_tws_connect_state);
        text_master_ear = (TextView) findViewById(R.id.text_master_ear);
        text_slave_ear = (TextView) findViewById(R.id.text_slave_ear);

        text_in_ear_detection_left = (TextView) findViewById(R.id.text_in_ear_detection_left);
        text_in_ear_detection_right = (TextView) findViewById(R.id.text_in_ear_detection_right);


        button_state_1 = (TextView) findViewById(R.id.button_state_1);
        button_state_2 = (TextView) findViewById(R.id.button_state_2);
        button_state_3 = (TextView) findViewById(R.id.button_state_3);
        button_state_4 = (TextView) findViewById(R.id.button_state_4);
        button_state_5 = (TextView) findViewById(R.id.button_state_5);
        button_state_6 = (TextView) findViewById(R.id.button_state_6);
        button_state_7 = (TextView) findViewById(R.id.button_state_7);
        button_state_8 = (TextView) findViewById(R.id.button_state_8);

        dolby_switch_title = (TextView) findViewById(R.id.dolby_switch_title);
        dolby_turn_off = (Button) findViewById(R.id.dolby_turn_off);
        dolby_turn_off.setOnClickListener(instance);
        dolby_type_natual = (Button) findViewById(R.id.dolby_type_natual);
        dolby_type_natual.setOnClickListener(instance);
        dolby_type_movie = (Button) findViewById(R.id.dolby_type_movie);
        dolby_type_movie.setOnClickListener(instance);

        linear_touch_onoff = (LinearLayout) findViewById(R.id.linear_touch_onoff);

        eq_bgview = (LinearLayout) findViewById(R.id.eq_bgview);
        linear_dolby_state = (LinearLayout) findViewById(R.id.linear_dolby_state);
        linear_bes_spatial_state = (LinearLayout) findViewById(R.id.linear_bes_spatial_state);
        linear_mimi_state = (LinearLayout) findViewById(R.id.linear_mimi_state);
        linear_ceva_state = (LinearLayout) findViewById(R.id.linear_ceva_state);
        head_tracking_on = (Button) findViewById(R.id.head_tracking_on);
        head_tracking_on.setOnClickListener(instance);
        head_tracking_off = (Button) findViewById(R.id.head_tracking_off);
        head_tracking_off.setOnClickListener(instance);
        ceva_recentre = (Button) findViewById(R.id.ceva_recentre);
        ceva_recentre.setOnClickListener(instance);
        ceva_fast_recentre = (Button) findViewById(R.id.ceva_fast_recentre);
        ceva_fast_recentre.setOnClickListener(instance);
        ceva_slow_recentre = (Button) findViewById(R.id.ceva_slow_recentre);
        ceva_slow_recentre.setOnClickListener(instance);

        linear_head_tracking_pro = (LinearLayout) findViewById(R.id.linear_head_tracking_pro);
        head_tracking_on_pro = (Button) findViewById(R.id.head_tracking_on_pro);
        head_tracking_on_pro.setOnClickListener(instance);
        head_tracking_off_pro = (Button) findViewById(R.id.head_tracking_off_pro);
        head_tracking_off_pro.setOnClickListener(instance);

        linear_multiPoint_bg = (LinearLayout) findViewById(R.id.linear_multiPoint_bg);
        linear_function_btn = (LinearLayout) findViewById(R.id.linear_function_btn);

        btn_get_head_tracking = (Button) findViewById(R.id.btn_get_head_tracking);
        btn_get_head_tracking.setOnClickListener(instance);
        btn_get_auto_center_mode = (Button) findViewById(R.id.btn_get_auto_center_mode);
        btn_get_auto_center_mode.setOnClickListener(instance);
        btn_get_imu_orientation = (Button) findViewById(R.id.btn_get_imu_orientation);
        btn_get_imu_orientation.setOnClickListener(instance);

        tv_get_head_tracking_result = (TextView) findViewById(R.id.tv_get_head_tracking_result);
        tv_get_auto_center_mode_result = (TextView) findViewById(R.id.tv_get_auto_center_mode_result);
        tv_get_imu_orientation_result = (TextView) findViewById(R.id.tv_get_imu_orientation_result);

        linear_led_onoff_state = (LinearLayout) findViewById(R.id.linear_led_onoff_state);
        led_switch_type = (RadioGroup) findViewById(R.id.led_switch_type);
        led_switch_type.setOnCheckedChangeListener(instance);
        led_switch_type_2 = (RadioGroup) findViewById(R.id.led_switch_type_2);
        led_switch_type_2.setOnCheckedChangeListener(instance);

        linear_spatial_audio_state = (LinearLayout) findViewById(R.id.linear_spatial_audio_state);
        spatial_audio_switch_type = (RadioGroup) findViewById(R.id.spatial_audio_switch_type);
        spatial_audio_switch_type.setOnCheckedChangeListener(instance);
        spatial_audio_switch_type_2 = (RadioGroup) findViewById(R.id.spatial_audio_switch_type_2);
        spatial_audio_switch_type_2.setOnCheckedChangeListener(instance);

        linear_share_mode_onoff = (LinearLayout) findViewById(R.id.linear_share_mode_onoff);
        share_mode_switch_type = (RadioGroup) findViewById(R.id.share_mode_switch_type);
        share_mode_switch_type.setOnCheckedChangeListener(instance);

        linear_anc_state = (LinearLayout) findViewById(R.id.linear_anc_state);
        regulate_anc_speakthru = (RadioButton) findViewById(R.id.regulate_anc_speakthru);
        text_anc_speakthru = (TextView) findViewById(R.id.text_anc_speakthru);
        regulate_anc_off = (RadioButton) findViewById(R.id.regulate_anc_off);
        regulate_anc_default = (RadioButton) findViewById(R.id.regulate_anc_default);
        text_anc_off = (TextView) findViewById(R.id.text_anc_off);
        text_anc_default = (TextView) findViewById(R.id.text_anc_default);
        linear_anc_new_state = (LinearLayout) findViewById(R.id.linear_anc_new_state);
        anc_new_type = (RadioGroup) findViewById(R.id.anc_new_type);
        anc_new_type.setOnCheckedChangeListener(instance);

        get_tech_level = (Button) findViewById(R.id.get_tech_level);
        get_tech_level.setOnClickListener(instance);
        tech_level_text = (TextView) findViewById(R.id.tech_level_text);
        text_mimi_switch_preset = (TextView) findViewById(R.id.text_mimi_switch_preset);
        text_mimi_switch_intensity = (TextView) findViewById(R.id.text_mimi_switch_intensity);
        seekbar_mimi_switch_preset = (SeekBar) findViewById(R.id.seekbar_mimi_switch_preset);
        seekbar_mimi_switch_intensity = (SeekBar) findViewById(R.id.seekbar_mimi_switch_intensity);
        seekbar_mimi_switch_preset.setOnSeekBarChangeListener(instance);
        seekbar_mimi_switch_intensity.setOnSeekBarChangeListener(instance);

        text_eq_60 = (TextView) findViewById(R.id.text_eq_60);
        text_eq_120 = (TextView) findViewById(R.id.text_eq_120);
        text_eq_250 = (TextView) findViewById(R.id.text_eq_250);
        text_eq_750 = (TextView) findViewById(R.id.text_eq_750);
        text_eq_2k = (TextView) findViewById(R.id.text_eq_2k);
        text_eq_5k = (TextView) findViewById(R.id.text_eq_5k);
        text_eq_15k = (TextView) findViewById(R.id.text_eq_15k);

        button_setting_bg = (LinearLayout) findViewById(R.id.button_setting_bg);
        seekbar_eq_bg = (LinearLayout) findViewById(R.id.seekbar_eq_bg);
        seekbar_eq_60 = (SeekBar) findViewById(R.id.seekbar_eq_60);
        seekbar_eq_60.setOnSeekBarChangeListener(instance);
        seekbar_eq_120 = (SeekBar) findViewById(R.id.seekbar_eq_120);
        seekbar_eq_120.setOnSeekBarChangeListener(instance);
        seekbar_eq_250 = (SeekBar) findViewById(R.id.seekbar_eq_250);
        seekbar_eq_250.setOnSeekBarChangeListener(instance);
        seekbar_eq_750 = (SeekBar) findViewById(R.id.seekbar_eq_750);
        seekbar_eq_750.setOnSeekBarChangeListener(instance);
        seekbar_eq_2k = (SeekBar) findViewById(R.id.seekbar_eq_2k);
        seekbar_eq_2k.setOnSeekBarChangeListener(instance);
        seekbar_eq_5k = (SeekBar) findViewById(R.id.seekbar_eq_5k);
        seekbar_eq_5k.setOnSeekBarChangeListener(instance);
        seekbar_eq_15k = (SeekBar) findViewById(R.id.seekbar_eq_15k);
        seekbar_eq_15k.setOnSeekBarChangeListener(instance);

        linear_game_mode = (LinearLayout) findViewById(R.id.linear_game_mode);
        game_mode_type = (RadioGroup) findViewById(R.id.game_mode_type);
        game_mode_type.setOnCheckedChangeListener(instance);

        linear_volume = (LinearLayout) findViewById(R.id.linear_volume);
        btn_volume = (Button) findViewById(R.id.btn_volume);
        btn_volume.setOnClickListener(instance);
        seekbar_volume = (SeekBar) findViewById(R.id.seekbar_volume);
        seekbar_volume.setOnSeekBarChangeListener(instance);
        tv_volume = (TextView) findViewById(R.id.tv_volume);
        tv_volume_cur = (TextView) findViewById(R.id.tv_volume_cur);
        eq_basetype2 = (RadioGroup) findViewById(R.id.eq_basetype2);
        eq_basetype2.setOnCheckedChangeListener(instance);

        seekbar_eq_2_bg = (LinearLayout) findViewById(R.id.seekbar_eq_2_bg);
        seekbar_eq_2_32 = (SeekBar) findViewById(R.id.seekbar_eq_2_32);
        seekbar_eq_2_32.setOnSeekBarChangeListener(instance);
        seekbar_eq_2_64 = (SeekBar) findViewById(R.id.seekbar_eq_2_64);
        seekbar_eq_2_64.setOnSeekBarChangeListener(instance);
        seekbar_eq_2_125 = (SeekBar) findViewById(R.id.seekbar_eq_2_125);
        seekbar_eq_2_125.setOnSeekBarChangeListener(instance);
        seekbar_eq_2_250 = (SeekBar) findViewById(R.id.seekbar_eq_2_250);
        seekbar_eq_2_250.setOnSeekBarChangeListener(instance);
        seekbar_eq_2_500 = (SeekBar) findViewById(R.id.seekbar_eq_2_500);
        seekbar_eq_2_500.setOnSeekBarChangeListener(instance);
        seekbar_eq_2_1k = (SeekBar) findViewById(R.id.seekbar_eq_2_1k);
        seekbar_eq_2_1k.setOnSeekBarChangeListener(instance);
        seekbar_eq_2_2k = (SeekBar) findViewById(R.id.seekbar_eq_2_2k);
        seekbar_eq_2_2k.setOnSeekBarChangeListener(instance);
        seekbar_eq_2_4k = (SeekBar) findViewById(R.id.seekbar_eq_2_4k);
        seekbar_eq_2_4k.setOnSeekBarChangeListener(instance);
        seekbar_eq_2_8k = (SeekBar) findViewById(R.id.seekbar_eq_2_8k);
        seekbar_eq_2_8k.setOnSeekBarChangeListener(instance);
        seekbar_eq_2_16k = (SeekBar) findViewById(R.id.seekbar_eq_2_16k);
        seekbar_eq_2_16k.setOnSeekBarChangeListener(instance);

        text_eq_2_32 = (TextView) findViewById(R.id.text_eq_2_32);
        text_eq_2_64 = (TextView) findViewById(R.id.text_eq_2_64);
        text_eq_2_125 = (TextView) findViewById(R.id.text_eq_2_125);
        text_eq_2_250 = (TextView) findViewById(R.id.text_eq_2_250);
        text_eq_2_500 = (TextView) findViewById(R.id.text_eq_2_500);
        text_eq_2_1k = (TextView) findViewById(R.id.text_eq_2_1k);
        text_eq_2_2k = (TextView) findViewById(R.id.text_eq_2_2k);
        text_eq_2_4k = (TextView) findViewById(R.id.text_eq_2_4k);
        text_eq_2_8k = (TextView) findViewById(R.id.text_eq_2_8k);
        text_eq_2_16k = (TextView) findViewById(R.id.text_eq_2_16k);



        FileUtils.writeTOfileAndActiveClear("CommandSet", "CommandSet initView");

        new Thread(new Runnable() {
            @Override
            public void run() {

                try {
                    Thread.sleep(500);
                    getButtonState();

                    getCurProductModel();

                    getUIState();

                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }).start();
    }

    private void getUIState() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {

                    Thread.sleep(200);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mPresenter.sendTestData(COMMAND_SET_TYPE_MULTIPOINT, true, MULTIPOINT_SET_GET);
                        }
                    });

                    Thread.sleep(100);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mPresenter.sendTestData(COMMAND_SET_TYPE_IN_EAR_DETECTION, true, IN_EAR_DETECTION_WD_PROMTP);
                        }
                    });

                    Thread.sleep(100);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mPresenter.sendGetVersionCrcData();
                        }
                    });
//                    Thread.sleep(100);
//                    runOnUiThread(new Runnable() {
//                        @Override
//                        public void run() {
//                            mPresenter.sendTestData(COMMAND_SET_TYPE_MULTIPOINT, true, MULTIPOINT_SET_GET);
//                        }
//                    });
                    Thread.sleep(100);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mPresenter.sendTestData(COMMAND_SET_TYPE_TWS_STATUS, false, (byte)0x00);
                        }
                    });
                    Thread.sleep(100);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mPresenter.sendTestData(COMMAND_SET_TYPE_GET_BUTTON_STATE, false, (byte)0x00);
                        }
                    });
                    Thread.sleep(100);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mPresenter.sendTestData(COMMAND_SET_TYPE_EQ_SWITCH, true, SWITCH_STATUS_GET);
                        }
                    });

                    Thread.sleep(100);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mPresenter.sendTestData(COMMAND_SET_TYPE_IN_EAR_DETECTION, true, IN_EAR_DETECTION_ALL);
                        }
                    });

                    Thread.sleep(100);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mPresenter.sendTestData(COMMAND_SET_TYPE_EQ_SWITCH, true, SWITCH_STATUS_SET_PARAM_3);
                        }
                    });

                    Thread.sleep(100);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mPresenter.sendTestData(APP_TOTA_CODEC_TYPE, true, SWITCH_STATUS_GET);
                        }
                    });
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }).start();
    }

    private void getCurProductModel() {
        Log.i(TAG, "getCurProductModel: ------------");
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mPresenter.sendTestData(COMMAND_SET_TYPE_GET_CURRENT_PRODUCT_MODEL, false, (byte)0x00);
                        }
                    });

                    Thread.sleep(3000);
                    if (curModelName.length() == 0) {
                        getCurProductModel();
                    }
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }).start();
    }

    private void getButtonState() {
        try {
            Thread.sleep(100);
            mPresenter.sendTestData(COMMAND_SET_TYPE_GET_BUTTON_STATE, false, (byte)0x00);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
//        mPresenter.stopSpp();
    }

    @Override
    public void scrollViewDidScroll(int scrollY, int index) {

    }

    @Override
    public void onCheckedChanged(RadioGroup group, int checkedId) {
        Log.i(TAG, "onCheckedChanged: --------aaaaa");
        if (isReceiveButtonCmd) {
            return;
        }
        Log.i(TAG, "onCheckedChanged: --------" + checkedId);
        if (group == earbuds_click_func_0 || group == earbuds_click_func_1 || group == earbuds_click_func_2 || group == earbuds_click_func_3) {
            earbuds_click_func_0.check(checkedId);
            earbuds_click_func_1.check(checkedId);
            earbuds_click_func_2.check(checkedId);
            earbuds_click_func_3.check(checkedId);
        }
        switch (checkedId) {
//            case R.id.eq_basetype_default:
//                curEqBaseType = EQ_TYPE_DEFAULT;
//                break;
            case R.id.eq_basetype_pop:
                curEqBaseType = EQ_TYPE_POP;
                break;
            case R.id.eq_basetype_rock:
                curEqBaseType = EQ_TYPE_ROCK;
                break;
            case R.id.eq_basetype_jazz:
                curEqBaseType = EQ_TYPE_JAZZ;
                break;
            case R.id.eq_basetype_classic:
                curEqBaseType = EQ_TYPE_CLASSIC;
                break;
            case R.id.eq_basetype_country:
                curEqBaseType = EQ_TYPE_COUNTRY;
                break;
            case R.id.earbuds_click_func_last_music:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_LAST_MUSIC;
                break;
            case R.id.earbuds_click_func_next_music:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_NEXT_MUSIC;
                break;
            case R.id.earbuds_click_func_ambient_music:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_AMBIENT_MUSIC;
                break;
            case R.id.earbuds_click_func_volume_add:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_VOLUME_ADD;
                break;
            case R.id.earbuds_click_func_volume_lose:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_VOLUME_LOSE;
                break;
            case R.id.earbuds_click_func_call_back:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_PHONE_CALL_BACK;
                break;
            case R.id.earbuds_click_func_stop_music:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_STOP_MUSIC;
                break;
            case R.id.earbuds_click_func_play_music:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_PLAY_MUSIC;
                break;
            case R.id.earbuds_click_func_assistant:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_ASSISTANT;
                break;
            case R.id.earbuds_click_func_play_pause_music:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_PLAY_PAUSE_MUSIC;
                break;
            case R.id.earbuds_click_func_game_mode:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_GAME_MODE;
                break;
            case R.id.earbuds_click_func_algo:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_ALGO;
                break;
            case R.id.earbuds_click_func_speakthru:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_SPEAKTHRU;
                break;
            case R.id.earbuds_click_func_disable:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_DISABLE;
                break;
            case R.id.regulate_anc_anc:
                Log.i(TAG, "onCheckedChanged: --------regulate_anc_anc");
                if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IH6PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_NIRVANAZPRO)) {
                    mPresenter.sendTestData(COMMAND_SET_TYPE_REGULATE_ANC, true, SWITCH_STATUS_SET, REGULATE_ANC_TYPE_HIGH);
                    linear_anc_new_state.setVisibility(View.VISIBLE);
                    return;
                }

                mPresenter.sendTestData(COMMAND_SET_TYPE_REGULATE_ANC, true, SWITCH_STATUS_SET, REGULATE_ANC_TYPE_ANC);
                break;
            case R.id.regulate_anc_ambient:
                Log.i(TAG, "onCheckedChanged: --------regulate_anc_ambient");
                if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IH6PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_NIRVANAZPRO)) {
                    linear_anc_new_state.setVisibility(View.GONE);
                }
                mPresenter.sendTestData(COMMAND_SET_TYPE_REGULATE_ANC, true, SWITCH_STATUS_SET, REGULATE_ANC_TYPE_AMNBITNE);

                break;
            case R.id.regulate_anc_default:
                Log.i(TAG, "onCheckedChanged: --------regulate_anc_default");

                mPresenter.sendTestData(COMMAND_SET_TYPE_REGULATE_ANC, true, SWITCH_STATUS_SET, REGULATE_ANC_TYPE_DEFAULT);

                break;
            case R.id.regulate_anc_speakthru:
                Log.i(TAG, "onCheckedChanged: --------regulate_anc_speakthru");
                mPresenter.sendTestData(COMMAND_SET_TYPE_REGULATE_ANC, true, SWITCH_STATUS_SET, REGULATE_ANC_TYPE_SPEAKTHRU);
                break;
            case R.id.regulate_anc_off:
                if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IH6PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_NIRVANAZPRO)) {
                    linear_anc_new_state.setVisibility(View.GONE);
                }
                Log.i(TAG, "onCheckedChanged: --------regulate_anc_off");
                mPresenter.sendTestData(COMMAND_SET_TYPE_REGULATE_ANC, true, SWITCH_STATUS_SET, REGULATE_ANC_TYPE_DEFAULT);
                break;
            case R.id.anc_new_type_adapt:
                Log.i(TAG, "onCheckedChanged: --------regulate_anc_adapt");
                mPresenter.sendTestData(COMMAND_SET_TYPE_REGULATE_ANC, true, SWITCH_STATUS_SET, REGULATE_ANC_TYPE_ANC);
                break;
            case R.id.anc_new_type_low:
                Log.i(TAG, "onCheckedChanged: --------regulate_anc_low");
                mPresenter.sendTestData(COMMAND_SET_TYPE_REGULATE_ANC, true, SWITCH_STATUS_SET, REGULATE_ANC_TYPE_LOW);
                break;
            case R.id.anc_new_type_mid:
                Log.i(TAG, "onCheckedChanged: --------regulate_anc_mid");
                mPresenter.sendTestData(COMMAND_SET_TYPE_REGULATE_ANC, true, SWITCH_STATUS_SET, REGULATE_ANC_TYPE_MID);
                break;
            case R.id.anc_new_type_high:
                Log.i(TAG, "onCheckedChanged: --------regulate_anc_high");
                mPresenter.sendTestData(COMMAND_SET_TYPE_REGULATE_ANC, true, SWITCH_STATUS_SET, REGULATE_ANC_TYPE_HIGH);
                break;
            case R.id.eq_switch_open:
                openSpa = false;
                mPresenter.sendTestData(COMMAND_SET_TYPE_EQ_SWITCH, true, SWITCH_STATUS_SET, SWITCH_STATUS_OPEN);
                refreshEqButtonStateUI();
                break;
            case R.id.eq_switch_off:
                mPresenter.sendTestData(COMMAND_SET_TYPE_EQ_SWITCH, true, SWITCH_STATUS_SET, SWITCH_STATUS_OFF);
                refreshEqButtonStateUI();
                break;
//            case R.id.dolby_switch_open:
//                mPresenter.sendTestData(COMMAND_SET_TYPE_DOLBY_SWITCH, true, SWITCH_STATUS_SET, SWITCH_STATUS_OPEN);
//                break;
//            case R.id.dolby_switch_off:
//                mPresenter.sendTestData(COMMAND_SET_TYPE_DOLBY_SWITCH, true, SWITCH_STATUS_SET, SWITCH_STATUS_OFF);
//                break;
            case R.id.bes_spatial_switch_open:
                openSpa = true;
                mPresenter.sendTestData(COMMAND_SET_TYPE_BES_SPATIAL_SWITCH, true, SWITCH_STATUS_SET, SWITCH_STATUS_OPEN);
                break;
            case R.id.bes_spatial_switch_off:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BES_SPATIAL_SWITCH, true, SWITCH_STATUS_SET, SWITCH_STATUS_OFF);
                break;
            case R.id.mimi_switch_open:
                mPresenter.sendTestData(COMMAND_SET_TYPE_MIMI_SWITCH, true, SWITCH_STATUS_SET, SWITCH_STATUS_OPEN);
                break;
            case R.id.mimi_switch_off:
                mPresenter.sendTestData(COMMAND_SET_TYPE_MIMI_SWITCH, true, SWITCH_STATUS_SET, SWITCH_STATUS_OFF);
                break;
//            case R.id.mimi_switch_preset_18:
//                mPresenter.sendTestData(COMMAND_SET_TYPE_MIMI_SWITCH, true, SWITCH_STATUS_SET_PARAM, (byte)1);
//                break;
//            case R.id.mimi_switch_preset_45:
//                mPresenter.sendTestData(COMMAND_SET_TYPE_MIMI_SWITCH, true, SWITCH_STATUS_SET_PARAM, (byte)2);
//                break;
//            case R.id.mimi_switch_preset_60:
//                mPresenter.sendTestData(COMMAND_SET_TYPE_MIMI_SWITCH, true, SWITCH_STATUS_SET_PARAM, (byte)3);
//                break;
//            case R.id.mimi_switch_intensity_0:
//                mPresenter.sendTestData(COMMAND_SET_TYPE_MIMI_SWITCH, true, SWITCH_STATUS_SET_PARAM_2, (byte)1);
//                break;
//            case R.id.mimi_switch_intensity_0_5:
//                mPresenter.sendTestData(COMMAND_SET_TYPE_MIMI_SWITCH, true, SWITCH_STATUS_SET_PARAM_2, (byte)2);
//                break;
//            case R.id.mimi_switch_intensity_1_0:
//                mPresenter.sendTestData(COMMAND_SET_TYPE_MIMI_SWITCH, true, SWITCH_STATUS_SET_PARAM_2, (byte)3);
//                break;
            case R.id.ceva_switch_open:
                mPresenter.sendTestData(COMMAND_SET_TYPE_CEAV_SWITCH, true, SWITCH_STATUS_SET, SWITCH_STATUS_OPEN);
                break;
            case R.id.ceva_switch_off:
                mPresenter.sendTestData(COMMAND_SET_TYPE_CEAV_SWITCH, true, SWITCH_STATUS_SET, SWITCH_STATUS_OFF);
                break;
            case R.id.led_switch_open:
                mPresenter.sendTestData(COMMAND_SET_TYPE_LED_ONOFF, true, SWITCH_STATUS_SET, SWITCH_STATUS_OPEN);
                break;
            case R.id.led_switch_off:
                mPresenter.sendTestData(COMMAND_SET_TYPE_LED_ONOFF, true, SWITCH_STATUS_SET, SWITCH_STATUS_OFF);
                break;
            case R.id.led_switch_flash:
                mPresenter.sendTestData(COMMAND_SET_TYPE_LED_ONOFF, true, SWITCH_STATUS_SET, SWITCH_STATUS_NEW);
                break;
            case R.id.led_switch_2_open_0:
                mPresenter.sendTestData(COMMAND_SET_TYPE_LED_ONOFF, true, SWITCH_STATUS_SET, SWITCH_STATUS_OPEN_0);
                break;
            case R.id.led_switch_2_open_1:
                mPresenter.sendTestData(COMMAND_SET_TYPE_LED_ONOFF, true, SWITCH_STATUS_SET, SWITCH_STATUS_OPEN);
                break;
            case R.id.led_switch_2_off:
                mPresenter.sendTestData(COMMAND_SET_TYPE_LED_ONOFF, true, SWITCH_STATUS_SET, SWITCH_STATUS_OFF);
                break;
            case R.id.led_switch_2_flash:
                mPresenter.sendTestData(COMMAND_SET_TYPE_LED_ONOFF, true, SWITCH_STATUS_SET, SWITCH_STATUS_NEW);
                break;
            case R.id.spatial_audio_switch_open:
                mPresenter.sendTestData(COMMAND_SET_TYPE_SPATIAL_AUDIO_ONOFF, true, SWITCH_STATUS_SET, SWITCH_STATUS_OPEN);
                break;
            case R.id.spatial_audio_switch_off:
                mPresenter.sendTestData(COMMAND_SET_TYPE_SPATIAL_AUDIO_ONOFF, true, SWITCH_STATUS_SET, SWITCH_STATUS_OFF);
                break;
            case R.id.spatial_audio_switch_spa:
                mPresenter.sendTestData(COMMAND_SET_TYPE_SPATIAL_AUDIO_ONOFF, true, SWITCH_STATUS_SET, SWITCH_STATUS_OPEN_0);
                break;
            case R.id.spatial_audio_switch_360:
                mPresenter.sendTestData(COMMAND_SET_TYPE_SPATIAL_AUDIO_ONOFF, true, SWITCH_STATUS_SET, SWITCH_STATUS_OPEN);
                break;
            case R.id.spatial_audio_switch_off_2:
                mPresenter.sendTestData(COMMAND_SET_TYPE_SPATIAL_AUDIO_ONOFF, true, SWITCH_STATUS_SET, SWITCH_STATUS_OFF);
                break;
            case R.id.share_mode_switch_open:
                mPresenter.sendTestData(COMMAND_SET_TYPE_SHARE_MODE_ONOFF, true, SWITCH_STATUS_SET, SWITCH_STATUS_OPEN);
                break;
            case R.id.share_mode_switch_off:
                mPresenter.sendTestData(COMMAND_SET_TYPE_SHARE_MODE_ONOFF, true, SWITCH_STATUS_SET, SWITCH_STATUS_OFF);
                break;
            case R.id.game_mode_close:
                mPresenter.sendTestData(APP_TOTA_GAME, true, SWITCH_STATUS_OPEN_0);
                break;
            case R.id.game_mode_open:
                mPresenter.sendTestData(APP_TOTA_GAME, true, SWITCH_STATUS_OPEN);
                break;
            case R.id.eq_basetype2_bass_boost:
                curEqBaseType = EQ_TYPE_2_BASS_BOOST;
                break;
            case R.id.eq_basetype2_classic:
                curEqBaseType = EQ_TYPE_2_CLASSIC;
                break;
            case R.id.eq_basetype2_hip_hop:
                curEqBaseType = EQ_TYPE_2_HIP_HOP;
                break;
            case R.id.eq_basetype2_jazz:
                curEqBaseType = EQ_TYPE_2_JAZZ;
                break;
            case R.id.eq_basetype2_podcast:
                curEqBaseType = EQ_TYPE_2_PODCAST;
                break;
            case R.id.eq_basetype2_rock:
                curEqBaseType = EQ_TYPE_2_ROCK;
                break;
            case R.id.eq_basetype2_pop:
                curEqBaseType = EQ_TYPE_2_POP;
                break;
            default:
                break;

        }
    }

    private String beCloseing = "Be closing...";
    private String beOpening = "Be opening...";
    private String inEar = "Be in the ear";
    private String outEar = "Not in the ear";
    private String isClosed = "In-ear testing is turned off";
    @Override
    public void onCheckedChanged(SwitchButton view, boolean isChecked) {
        Log.i(TAG, "onCheckedChanged: ----isReceiveSwitchButtonChange--" + isReceiveSwitchButtonChange);
        if (view == switchButton_multiPoint) {
            if (isMultipointSwitchButtonChange) {
                return;
            }
            Log.i(TAG, "onCheckedChanged: ----switchButton_multiPoint");
            mPresenter.sendTestData(COMMAND_SET_TYPE_MULTIPOINT, true, MULTIPOINT_SET_GET, isChecked ? MULTIPOINT_OPEN : MULTIPOINT_CLOSE);
            return;
        }

        if (isReceiveSwitchButtonChange) {
            return;
        }
        if (view == switchButton_fit_test) {
            if (isChecked) {
                fit_test.setClickable(true);
                fit_test.setText("Earbud Fit Test");

            } else {
                fit_test.setClickable(true);
                fit_test.setText("Close Fit Test");
            }
        } else if (view == switchButton_in_ear_detection_all) {
            refreshInEarDetectionUI(true, isChecked ? beOpening : isClosed);
            refreshInEarDetectionUI(false, isChecked ? beOpening : isClosed);
            mPresenter.sendTestData(COMMAND_SET_TYPE_IN_EAR_DETECTION, true, IN_EAR_DETECTION_ALL, isChecked ? IN_EAR_DETECTION_OPEN : IN_EAR_DETECTION_CLOSE);

            isArtificialShutdownAll = isChecked ? false : true;
        } else if (view == switchButton_in_ear_detection_left) {
            refreshInEarDetectionUI(true, isChecked ? beOpening : beCloseing);
            mPresenter.sendTestData(COMMAND_SET_TYPE_IN_EAR_DETECTION, true, IN_EAR_DETECTION_LEFT_EAR, isChecked ? IN_EAR_DETECTION_OPEN : IN_EAR_DETECTION_CLOSE);
            if (isChecked) {
                isArtificialShutdownAll = false;
            }
        } else if (view == switchButton_in_ear_detection_right) {
            refreshInEarDetectionUI(false, isChecked ? beOpening : beCloseing);
            mPresenter.sendTestData(COMMAND_SET_TYPE_IN_EAR_DETECTION, true, IN_EAR_DETECTION_RIGHT_EAR, isChecked ? IN_EAR_DETECTION_OPEN : IN_EAR_DETECTION_CLOSE);
            if (isChecked) {
                isArtificialShutdownAll = false;
            }
        } else if (view == switchButton_wd_prompt) {
            mPresenter.sendTestData(COMMAND_SET_TYPE_IN_EAR_DETECTION, true, IN_EAR_DETECTION_WD_PROMTP, isChecked ? IN_EAR_DETECTION_OPEN : IN_EAR_DETECTION_CLOSE);
        } else if (view == switchButton_touch_onoff) {
            mPresenter.sendTestData(APP_TOTA_TOUCH_ONOFF, true, SWITCH_STATUS_SET_0, isChecked ? SWITCH_STATUS_OPEN : SWITCH_STATUS_OPEN_0);
        } else if (view == switchButton_disable_swipe_left) {
            mPresenter.sendTestData(COMMAND_SET_TYPE_BUTTON_SETTINGS_CONTROL, true, BUTTON_SETTINGS_CONTROL_CLICK_LEFT, BUTTON_SETTINGS_CONTROL_SWIPE, isChecked ? BUTTON_SETTINGS_CONTROL_SWIPE_DISABLE : BUTTON_SETTINGS_CONTROL_SWIPE_ENABLE);
        } else if (view == switchButton_disable_swipe_right) {
            mPresenter.sendTestData(COMMAND_SET_TYPE_BUTTON_SETTINGS_CONTROL, true, BUTTON_SETTINGS_CONTROL_CLICK_RIGHT, BUTTON_SETTINGS_CONTROL_SWIPE, isChecked ? BUTTON_SETTINGS_CONTROL_SWIPE_DISABLE : BUTTON_SETTINGS_CONTROL_SWIPE_ENABLE);
        }
    }

    private void refreshInEarDetectionUI(boolean isLeft, String text) {
        Log.i(TAG, "refreshInEarDetectionUI: ------" + isLeft + "----" + text);
        TextView textView = isLeft ? text_in_ear_detection_left : text_in_ear_detection_right;
        SwitchButton switchButton = isLeft ? switchButton_in_ear_detection_left : switchButton_in_ear_detection_right;
        textView.setText(text);
        textView.setTextColor(getColor(R.color.activityTextMainClor));
        if (text.equals(inEar)) {
            switchButton.setChecked(true);
            textView.setTextColor(getColor(R.color.green));
        } else if (text.equals(outEar)) {
            switchButton.setChecked(true);
            textView.setTextColor(getColor(R.color.fff06e6e));
        } else if (text.equals(beOpening) || text.equals(beCloseing)) {
            textView.setTextColor(getColor(R.color.black));
        }
        if (text.equals(isClosed)) {
            switchButton.setChecked(false);
        }
    }

    private void refreshDolbyButtonUI() {
        addlog("refreshDolbyButtonUI----" + curDolbyType);
        dolby_turn_off.setBackground(getDrawable(R.drawable.ota_button_bg_press));
        dolby_type_natual.setBackground(getDrawable(R.drawable.ota_button_bg_press));
        dolby_type_movie.setBackground(getDrawable(R.drawable.ota_button_bg_press));
        if (curDolbyType == 0) {
            dolby_turn_off.setBackground(getDrawable(R.drawable.ota_click));
        } else if (curDolbyType == 1) {
            dolby_type_natual.setBackground(getDrawable(R.drawable.ota_click));
        } else if (curDolbyType == 2) {
            dolby_type_movie.setBackground(getDrawable(R.drawable.ota_click));
        } else if (curDolbyType == 3) {
            dolby_type_natual.setBackground(getDrawable(R.drawable.ota_click));
            mPresenter.sendTestData(COMMAND_SET_TYPE_DOLBY_SWITCH, true, SWITCH_STATUS_SET_PARAM, DOLBY_TYPE_NATUAL);
        }
    }

    private void refreshEqButtonStateUI() {
        if (eq_bgview == null || eq_bgview.getVisibility() == View.GONE) {
            return;
        }
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (eq_switch_type.getCheckedRadioButtonId() == R.id.eq_switch_open) {
                    eq_test.setEnabled(true);
                    eq_test.setVisibility(View.VISIBLE);
                    if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_K7PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_K6PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9CHATGPT) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9ANC)) {
                        eq_basetype.setVisibility(View.GONE);
                        eq_basetype2.setVisibility(View.VISIBLE);
                    }
                } else {
                    eq_test.setEnabled(false);
                    eq_test.setVisibility(View.INVISIBLE);
                    if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_K7PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_K6PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9PRO) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9CHATGPT) || curModelName.equals(COMMAND_SET_PRODUCT_MODEL_I9ANC)) {
                        eq_basetype.setVisibility(View.GONE);
                        eq_basetype2.setVisibility(View.INVISIBLE);
                    }
                }
            }
        });
    }

    @Override
    public void onMicStateChanged(int state, String msg) {
        Log.i(TAG, "onMicStateChanged: --------" + state + "----" + msg);
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (state == 0) {
                    refreshSpeakerButtonState(true, 2);

                    mic_state_text.setText("mic state normal");
                    mic_state_text.setTextColor(getColor(R.color.green));
                } else if (state == 1) {
                    mic_state_text.setTextColor(getColor(R.color.black));
                    mic_state_text.setText("Detection progress：" + msg + "%");
                } else {
                    ActivityUtils.showToast(msg);

                    refreshSpeakerButtonState(true, 2);

                    mic_state_text.setText("mic state abnormality");
                    mic_state_text.setTextColor(getColor(R.color.fff06e6e));
                }
            }
        });
    }

    @Override
    public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
        Log.i(TAG, "onProgressChanged: ------" + progress);
        if (seekBar == seekbar_mimi_switch_preset) {
            curSeekbarProgress = progress;
            if (curSeekbarProgress == 2) {
                /* 60 year old tech4_v1  */
                text_mimi_switch_preset.setText("preset: " + 60 + "");
            } else if (curSeekbarProgress == 1) {
                /* 45 year old tech4_v1  */
                text_mimi_switch_preset.setText("preset: " + 45 + "");
            } else {
                /* 18 year old tech4_v1  */
                text_mimi_switch_preset.setText("preset: " + 18 + "");
            }
        } else if (seekBar == seekbar_mimi_switch_intensity) {
            curSeekbarProgress = progress;
            float result = (float) (progress * 0.01);
            text_mimi_switch_intensity.setText("intensity: " + result + "");
        } else if (seekBar == seekbar_eq_60) {
            float result = -5 + (float) (progress * 1);
            text_eq_60.setText(result + "");
        } else if (seekBar == seekbar_eq_120) {
            float result = -5 + (float) (progress * 1);
            text_eq_120.setText(result + "");
        } else if (seekBar == seekbar_eq_250) {
            float result = -5 + (float) (progress * 1);
            text_eq_250.setText(result + "");
        } else if (seekBar == seekbar_eq_750) {
            float result = -5 + (float) (progress * 1);
            text_eq_750.setText(result + "");
        } else if (seekBar == seekbar_eq_2k) {
            float result = -5 + (float) (progress * 1);
            text_eq_2k.setText(result + "");
        } else if (seekBar == seekbar_eq_5k) {
            float result = -5 + (float) (progress * 1);
            text_eq_5k.setText(result + "");
        } else if (seekBar == seekbar_eq_15k) {
            float result = -5 + (float) (progress * 1);
            text_eq_15k.setText(result + "");
        } else if (seekBar == seekbar_eq_2_32) {
            float result = -12 + (float) (progress * 1);
            text_eq_2_32.setText(result + "");
        } else if (seekBar == seekbar_eq_2_64) {
            float result = -12 + (float) (progress * 1);
            text_eq_2_64.setText(result + "");
        } else if (seekBar == seekbar_eq_2_125) {
            float result = -12 + (float) (progress * 1);
            text_eq_2_125.setText(result + "");
        } else if (seekBar == seekbar_eq_2_250) {
            float result = -12 + (float) (progress * 1);
            text_eq_2_250.setText(result + "");
        } else if (seekBar == seekbar_eq_2_500) {
            float result = -12 + (float) (progress * 1);
            text_eq_2_500.setText(result + "");
        } else if (seekBar == seekbar_eq_2_1k) {
            float result = -12 + (float) (progress * 1);
            text_eq_2_1k.setText(result + "");
        } else if (seekBar == seekbar_eq_2_2k) {
            float result = -12 + (float) (progress * 1);
            text_eq_2_2k.setText(result + "");
        } else if (seekBar == seekbar_eq_2_4k) {
            float result = -12 + (float) (progress * 1);
            text_eq_2_4k.setText(result + "");
        } else if (seekBar == seekbar_eq_2_8k) {
            float result = -12 + (float) (progress * 1);
            text_eq_2_8k.setText(result + "");
        } else if (seekBar == seekbar_eq_2_16k) {
            float result = -12 + (float) (progress * 1);
            text_eq_2_16k.setText(result + "");
        }
        else if (seekBar == seekbar_volume) {
            curSeekbarProgress = progress;
            tv_volume_cur.setText(curSeekbarProgress + "");
        }
    }

    @Override
    public void onStartTrackingTouch(SeekBar seekBar) {
        Log.i(TAG, "onStartTrackingTouch: ------");

    }

    @Override
    public void onStopTrackingTouch(SeekBar seekBar) {
        Log.i(TAG, "onStopTrackingTouch: ------");
        if (seekBar == seekbar_mimi_switch_preset) {
//            mPresenter.sendTestData(COMMAND_SET_TYPE_MIMI_SWITCH, true, SWITCH_STATUS_SET_PARAM, (byte)(curSeekbarProgress + 1));
            if (curSeekbarProgress == 2) {
                /* 60 year old tech4_v1  */
                //Replace "data" with 196 bytes of the parameter you got from mimiSdk
                byte[] data = ArrayUtil.toBytes(sample_preset_60yo.replace("0x", "").replace(" ", ""));
                mPresenter.sendMimiSetPresentData(data);
            } else if (curSeekbarProgress == 1) {
                /* 45 year old tech4_v1  */
                byte[] data = ArrayUtil.toBytes(sample_preset_45yo.replace("0x", "").replace(" ", ""));
                mPresenter.sendMimiSetPresentData(data);
            } else {
                /* 18 year old tech4_v1  */
                byte[] data = ArrayUtil.toBytes(sample_preset_18yo.replace("0x", "").replace(" ", ""));
                mPresenter.sendMimiSetPresentData(data);
            }

        } else if (seekBar == seekbar_mimi_switch_intensity) {
            mPresenter.sendTestData(COMMAND_SET_TYPE_MIMI_SWITCH, true, SWITCH_STATUS_SET_PARAM_2, (byte)(curSeekbarProgress));
        } else if (seekBar == seekbar_eq_60 || seekBar == seekbar_eq_120 || seekBar == seekbar_eq_250 || seekBar == seekbar_eq_750 || seekBar == seekbar_eq_2k || seekBar == seekbar_eq_5k || seekBar == seekbar_eq_15k) {
            sendSetEqParamData();
        } else if (seekBar == seekbar_eq_2_32 || seekBar == seekbar_eq_2_64 || seekBar == seekbar_eq_2_125 || seekBar == seekbar_eq_2_250 || seekBar == seekbar_eq_2_500 || seekBar == seekbar_eq_2_1k || seekBar == seekbar_eq_2_2k || seekBar == seekbar_eq_2_4k || seekBar == seekbar_eq_2_8k || seekBar == seekbar_eq_2_16k) {
            sendSetEqParamData2();
        }
        else if (seekBar == seekbar_volume) {
            mPresenter.sendTestData(APP_TOTA_VOLUME, true, (byte) curSeekbarProgress);
        }
    }

    private void sendSetEqParamData() {
        mPresenter.sendTestData(COMMAND_SET_TYPE_EQ_SWITCH, true, SWITCH_STATUS_SET_PARAM_2, (byte) seekbar_eq_60.getProgress(), (byte) seekbar_eq_120.getProgress(), (byte) seekbar_eq_250.getProgress(), (byte) seekbar_eq_750.getProgress(), (byte) seekbar_eq_2k.getProgress(), (byte) seekbar_eq_5k.getProgress(), (byte) seekbar_eq_15k.getProgress());
    }

    private void sendSetEqParamData2() {
        mPresenter.sendTestData(COMMAND_SET_TYPE_EQ_SWITCH, true, SWITCH_STATUS_SET_PARAM_2, (byte) seekbar_eq_2_32.getProgress(), (byte) seekbar_eq_2_64.getProgress(), (byte) seekbar_eq_2_125.getProgress(), (byte) seekbar_eq_2_250.getProgress(), (byte) seekbar_eq_2_500.getProgress(), (byte) seekbar_eq_2_1k.getProgress(), (byte) seekbar_eq_2_2k.getProgress(), (byte) seekbar_eq_2_4k.getProgress(), (byte) seekbar_eq_2_8k.getProgress(), (byte) seekbar_eq_2_16k.getProgress());
    }

}



