package com.besall.allbase.bluetooth.service.crashdump;

public class CrashDumpConstants {


    public static final short            OP_TOTA_CRASH_DUMP_REQ = (short)0x6500;           //Android/ios send request to dump crash information
    public static final short      OP_TOTA_CRASH_DUMP_PARAM_REQ = (short)0x6501;	 // Android/ios send crash id and buds check whether the id is ok
    public static final short      OP_TOTA_CRASH_DUMP_START_REQ = (short)0x6502;	 //Android /ios notify buds to send crash info
    public static final short   OP_TOTA_CRASH_DUMP_RECEIVED_ACK = (short)0x6503;   //If Android /ios received the pkt, and send ack to buds
    public static final short            OP_TOTA_CRASH_DUMP_END = (short)0x6505;          //Buds send to android /ios, and finish sending pkt

    //msg id
    public static final int                          CRASH_INFO = 0x00001000;
    public static final int                LOGDUMP_LAST_PACKAGE = 0x00001001;
    public static final int                  CRASHDUMP_PROGRESS = 0x00001002;
    public static final int                      LOGDUMP_FINISH = 0x00001003;


    public static final String                         TOTA_SET = "tota_set";
    public static final String                  TOTA_SET_MASTER = "tota_set_master";
    public static final String                   TOTA_SET_SLAVE = "tota_set_slave";
    // 通讯方式
    public static final String                 EXT_PROFILE_TYPE = "PROFILE_TYPE";
    public static final byte                           TYPE_BLE = 0X02;
    public static final byte                           TYPE_SPP = 0x01;
    public static final byte                           TYPE_HFP = 0X04;

    public static final byte                             ACK_OK = 0x01;
    public static final byte                          ACK_ERROR = 0x00;
    public static final byte                         TYPE_CRASH = 0x00;
    public static final byte                           TYPE_LOG = 0x01;

    public static final String                        USER_NAME = "user_name";
    public static final String                        USER_MAIL = "user_mail";
    public static final String                     USER_COMPANY = "user_company";
    public static final String                       USER_PHONE = "user_phone";
    public static final String                       USER_EXTRA = "user_extra";
    public static final String                   KEY_START_TIME = "key_start_time";
    public static final String                     KEY_ZIP_PATH = "key_zip_path";
    public static final String                     KEY_ZIP_NAME = "key_zip_name";
    public static final String             KEY_FILE_FOLDER_NAME = "Crash Dump";


    public static final int                    CMD_LENGTH_ERROR = 0x00006000;
    public static final int                      CMD_ROLE_ERROR = 0x00006001;
    public static final int                      CMD_6504_ERROR = 0x00006002;

    public static final int                        NO_CRASHDATA = 0x00006100;


    public static final int       CMD_TOTA_CRASH_DUMP_PARAM_REQ = 0x00006500;
    public static final int             CMD_TOTA_CRASH_DUMP_END = 0x00006501;
    public static final int           CMD_TOTA_CRASH_DUMP_START = 0x00006502;
    public static final int    CMD_TOTA_CRASH_DUMP_RECEIVED_ACK = 0x00006503;

}
