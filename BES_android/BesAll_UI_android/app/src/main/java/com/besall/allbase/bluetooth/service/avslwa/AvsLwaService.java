package com.besall.allbase.bluetooth.service.avslwa;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_SUCCESS;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_IS_LWA_LINK_KEY;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_DATA_LOG;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_GET_WHOLE_SETTING_DATA;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_RECEIVE_LWA_STATE_CONNECT;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_RECEIVE_LWA_STATE_OFLINE;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_RECEIVE_PRODUCTID;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_RECEIVE_WIFI_STATE_CONNECT;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_RECEIVE_WIFI_STATE_CONNECT_FAIL;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_RECEIVE_WIFI_STATE_CONNECT_SUCCESS;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_RECEIVE_WIFI_STATE_OFLINE;
import static com.besall.allbase.bluetooth.service.avslwa.AvsLwaConstants.AVS_LWA_SET_ERROR;

import android.content.Context;
import android.util.Log;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.service.base.BesBaseService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.message.BaseMessage;
import com.bes.sdk.utils.DeviceProtocol;

import java.util.Map;


public class AvsLwaService extends BesBaseService {

    public AvsLwaService(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        super(serviceConfig, listener, context);
        startConnect(serviceConfig);
    }

    @Override
    public void onStatusChanged(HmDevice device, int status, DeviceProtocol protocol) {
        super.onStatusChanged(device, status, protocol);
        LOG(TAG, "onStatusChanged---service---" + status);

        if (status == BES_CONNECT_SUCCESS) {
//            callBackStateChangedMessage(BES_CONNECT_SUCCESS, "");
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            boolean hasLwaLink = (boolean) SPHelper.getPreference(mContext, AVS_IS_LWA_LINK_KEY, false);
            if (hasLwaLink) {
//                sendData(new byte[]{0x00, 0x00, 0x00, 0x11, 0x11, 0x11});
                Log.i(TAG, "onStatusChanged:------1111");
//                callBackStateChangedMessage(BES_CONNECT_SUCCESS, "");
            } else {
                sendData(AvsLwaCMD.getAvsLinkStaData());
            }
//            callBackStateChangedMessage(AVS_LWA_RECEIVE_WIFI_STATE_OFLINE, "");
//            callBackStateChangedMessage(AVS_LWA_RECEIVE_PRODUCTID, "");

        } else if (getDeviceConnectState(mConfig) != BesSdkConstants.BesConnectState.BES_CONNECT) {
            callBackErrorMessage(status);
        }
    }

    @Override
    public void onDataReceived(BaseMessage deviceMessage) {
        super.onDataReceived(deviceMessage);
        callBackStateChangedMessage(AVS_LWA_DATA_LOG, ArrayUtil.toHex((byte[]) deviceMessage.getMsgContent()));
        callBackStateChangedMessage(AVS_LWA_DATA_LOG,  "长度" + ((byte[]) deviceMessage.getMsgContent()).length);

        int result = AvsLwaCMD.receiveData((byte[]) deviceMessage.getMsgContent(), mContext);
        if (result == AVS_LWA_RECEIVE_WIFI_STATE_OFLINE) {
            callBackStateChangedMessage(result, "");
//            sendData(AvsLwaCMD.getWifiData("BES-10F-Guest", "OPQrst12300456"));
        } else if (result == AVS_LWA_RECEIVE_WIFI_STATE_CONNECT) {
            sendData(AvsLwaCMD.getAvsLwaStateData());
        } else if (result == AVS_LWA_RECEIVE_WIFI_STATE_CONNECT_FAIL) {
            callBackStateChangedMessage(result, "");
        } else if (result == AVS_LWA_RECEIVE_WIFI_STATE_CONNECT_SUCCESS) {
            sendData(AvsLwaCMD.getProductSidData());
        } else if (result == AVS_LWA_RECEIVE_LWA_STATE_OFLINE) {
            sendData(AvsLwaCMD.getProductSidData());
        } else if (result == AVS_LWA_RECEIVE_LWA_STATE_CONNECT) {
            callBackStateChangedMessage(result, "");
        } else if (result == AVS_LWA_RECEIVE_PRODUCTID) {
            callBackStateChangedMessage(result, "");
        } else if (result == AVS_LWA_SET_ERROR) {
            callBackStateChangedMessage(result, "");
        } else if (result == AVS_LWA_GET_WHOLE_SETTING_DATA) {
            callBackStateChangedMessage(result, AvsLwaCMD.getCurWholeSettingData());
        } else {
            callBackStateChangedMessage(result, "");
        }
    }

    public Map<String, String> getProductSid() {
        return AvsLwaCMD.getProductSid();
    }

    public void sendWifiData(String name, String  psw) {
        sendData(AvsLwaCMD.getWifiData(name, psw));
    }

    public void sendAuthorizeResult(String authorizationCode, String redirectUri, String clientId) {
        sendData(AvsLwaCMD.getAuthorizeResultData(authorizationCode, redirectUri, clientId));
    }

    public void sendLanguageData(String language) {
        sendData(AvsLwaCMD.getSetLanguageData(language));
    }

    public void sendCurSettingData(int type, int value) {
        sendData(AvsLwaCMD.getCurSettingData(type, value));
    }

    public void sendGetWholeSettingData() {
        sendData(AvsLwaCMD.getWholeSettingData());
    }

    public String getCurType() {
        return AvsLwaCMD.getCurType();
    }

    public String getCurValue() {
        return AvsLwaCMD.getCurValue();
    }

}
