package com.besall.allbase.view.activity.chipstoollevel4.health.bloodoxygen;

import android.content.Context;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.besall.allbase.bluetooth.service.health.blood.BloodService;
import com.besall.allbase.view.base.BasePresenter;

public class BloodPresenter extends BasePresenter<IBloodActivity> implements IBloodPresenter{
    private BloodService bloodService;

    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        bloodService = new BloodService(serviceConfig, listener, context);
    }

    @Override
    public void sendBOS() {
        bloodService.sendRequest();
    }
}
