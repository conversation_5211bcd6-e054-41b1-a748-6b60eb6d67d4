package com.besall.allbase.bluetooth.service.commandset;

public class CommandSetConstants {

    public static final String                COMMAND_SET_PRODUCT_MODEL_IE2A = "IE2A";
    public static final String                COMMAND_SET_PRODUCT_MODEL_IE2B = "IE2B";
    public static final String           COMMAND_SET_PRODUCT_MODEL_IM2_DOLBY = "IM2_Dolby";
    public static final String              COMMAND_SET_PRODUCT_MODEL_IM2PRO = "IM2PRO";
    public static final String             COMMAND_SET_PRODUCT_MODEL_IM2LDAC = "IM2LDAC";
    public static final String                 COMMAND_SET_PRODUCT_MODEL_IH5 = "IH5";
    public static final String                 COMMAND_SET_PRODUCT_MODEL_IH6 = "IH6";
    public static final String              COMMAND_SET_PRODUCT_MODEL_IH6PRO = "IH6PRO";
    public static final String                  COMMAND_SET_PRODUCT_MODEL_R1 = "R1";
    public static final String                  COMMAND_SET_PRODUCT_MODEL_R2 = "R2";
    public static final String              COMMAND_SET_PRODUCT_MODEL_255ARC = "255_ARC";
    public static final String          COMMAND_SET_PRODUCT_MODEL_255ARCPLUS = "255_ARC_PLUS";
    public static final String                  COMMAND_SET_PRODUCT_MODEL_B2 = "B2";
    public static final String                COMMAND_SET_PRODUCT_MODEL_E53B = "E53B";
    public static final String             COMMAND_SET_PRODUCT_MODEL_ANAVRIN = "Anavrin";
    public static final String              COMMAND_SET_PRODUCT_MODEL_NEBULA = "Nebula";
    public static final String              COMMAND_SET_PRODUCT_MODEL_ZENITH = "Zenith";
    public static final String           COMMAND_SET_PRODUCT_MODEL_IE_Gaming = "IE_Gaming";
    public static final String                 COMMAND_SET_PRODUCT_MODEL_D49 = "D49";
    public static final String                COMMAND_SET_PRODUCT_MODEL_KB20 = "KB20";
    public static final String     COMMAND_SET_PRODUCT_MODEL_TOUCH_HEADPHONE = "TOUCH_HEADPHONE";
    public static final String               COMMAND_SET_PRODUCT_MODEL_K7PRO = "K7PRO";
    public static final String               COMMAND_SET_PRODUCT_MODEL_K6PRO = "K6PRO";
    public static final String                  COMMAND_SET_PRODUCT_MODEL_I9 = "I9";
    public static final String               COMMAND_SET_PRODUCT_MODEL_I9PRO = "I9PRO";
    public static final String           COMMAND_SET_PRODUCT_MODEL_I9CHATGPT = "I9ChatGpt";
    public static final String               COMMAND_SET_PRODUCT_MODEL_I9ANC = "I9ANC";
    public static final String         COMMAND_SET_PRODUCT_MODEL_NIRVANAZPRO = "NirvanaZPro";
    public static final String      COMMAND_SET_PRODUCT_MODEL_NIRVANA_ULTRON = "Nirvana_Ultron";
    public static final String               COMMAND_SET_PRODUCT_MODEL_AD271 = "AD271";
    public static final String                  COMMAND_SET_PRODUCT_MODEL_J1 = "J1";
    public static final String                COMMAND_SET_PRODUCT_MODEL_1053 = "1053";
    public static final String              COMMAND_SET_PRODUCT_MODEL_ADBLUE = "AD Blue";

    //speaker box
    public static final String                 COMMAND_SET_PRODUCT_MODEL_H50 = "Nirvana Luxe";
    public static final String                 COMMAND_SET_PRODUCT_MODEL_H40 = "Stone Arc Pro Plus";
    public static final String                 COMMAND_SET_PRODUCT_MODEL_H20 = "Stone Arc Pro";
    public static final String                 COMMAND_SET_PRODUCT_MODEL_H10 = "Stone Arc";
    public static final String                  COMMAND_SET_PRODUCT_MODEL_P1 = "P1";
    public static final String           COMMAND_SET_PRODUCT_MODEL_BES_BOARD = "BES-board";


    //cmd
    public static final short                        OP_TOTA_COMMAND_SET_CMD = (short)0x6308;

    public static final byte        COMMAND_SET_TYPE_BUTTON_SETTINGS_CONTROL = 0x01;
    public static final byte                  COMMAND_SET_TYPE_FACTORY_RESET = 0x02;
    public static final byte                COMMAND_SET_TYPE_MUSIC_PLAY_BACK = 0x03;
    public static final byte             COMMAND_SET_TYPE_BATTREY_PERCENTAGE = 0x04;
    public static final byte                COMMAND_SET_TYPE_EARBUD_FIT_TEST = 0x05;
//    public static final byte                             COMMAND_SET_TYPE_EQ = 0x06;
    public static final byte               COMMAND_SET_TYPE_IN_EAR_DETECTION = 0x07;
    public static final byte         COMMAND_SET_TYPE_GET_SPP_CONNECT_STATUS = 0x08;
    public static final byte      COMMAND_SET_TYPE_GET_BT_STATE_AND_ADDREDSS = 0x09;
    public static final byte               COMMAND_SET_TYPE_GET_BUTTON_STATE = 0x10;
    public static final byte      COMMAND_SET_TYPE_GET_CURRENT_PRODUCT_MODEL = 0x11;
    public static final byte                   COMMAND_SET_TYPE_REGULATE_ANC = 0x12;
    public static final byte                      COMMAND_SET_TYPE_EQ_SWITCH = 0x13;
    public static final byte                   COMMAND_SET_TYPE_DOLBY_SWITCH = 0x14;
    public static final byte             COMMAND_SET_TYPE_BES_SPATIAL_SWITCH = 0x15;
    public static final byte                    COMMAND_SET_TYPE_MIMI_SWITCH = 0x16;
    public static final byte                    COMMAND_SET_TYPE_CEAV_SWITCH = 0x17;
    public static final byte                      COMMAND_SET_TYPE_START_OTA = 0x18;
    public static final byte                     COMMAND_SET_TYPE_TWS_STATUS = 0x19;
    public static final byte                     COMMAND_SET_TYPE_MULTIPOINT = 0x1A;
    public static final byte                    COMMAND_SET_TYPE_SWITCH_ROLE = 0x1B;
    public static final byte                      COMMAND_SET_TYPE_LED_ONOFF = 0x1C;
    public static final byte            COMMAND_SET_TYPE_SPATIAL_AUDIO_ONOFF = 0x1D;
    public static final byte               COMMAND_SET_TYPE_SHARE_MODE_ONOFF = 0x1E;


    public static final byte                  APP_TOTA_HEAD_TRACKING_OFF_CMD = 0x20;
    public static final byte                   APP_TOTA_HEAD_TRACKING_ON_CMD = 0x21;
    public static final byte                               APP_TOTA_TARE_CMD = 0x26;
    public static final byte                    APP_TOTA_AUTOCENTER_SLOW_CMD = 0x27;
    public static final byte                    APP_TOTA_AUTOCENTER_FAST_CMD = 0x28;
    public static final byte            APP_TOTA_GET_HEAD_TRACKING_STATE_CMD = 0x63;
    public static final byte               APP_TOTA_GET_AUTO_CENTER_MODE_CMD = 0x64;
    public static final byte                APP_TOTA_GET_IMU_ORIENTATION_CMD = 0x65;

    public static final byte                             APP_TOTA_CODEC_TYPE = (byte) 0x80;
    public static final byte                                   APP_TOTA_GAME = (byte) 0x81;
    public static final byte                                 APP_TOTA_VOLUME = (byte) 0x82;
    public static final byte                            APP_TOTA_TOUCH_ONOFF = (byte) 0x83;
    public static final byte                              APP_TOTA_DAX_PHONE = (byte) 0x84;

    public static final byte                                    APP_TOTA_GET = (byte) 0xFF;

    public static final byte                                  DAX_PHONE_TRUE = 0x01;
    public static final byte                                 DAX_PHONE_FALSE = 0x00;

    //button setting control
    public static final byte              BUTTON_SETTINGS_CONTROL_CLICK_LEFT = 0x01;
    public static final byte             BUTTON_SETTINGS_CONTROL_CLICK_RIGHT = 0x02;

    public static final byte                   BUTTON_SETTINGS_CONTROL_CLICK = 0x01;
    public static final byte            BUTTON_SETTINGS_CONTROL_DOUBLE_CLICK = 0x02;
    public static final byte            BUTTON_SETTINGS_CONTROL_TRIPLE_CLICK = 0x03;
    public static final byte              BUTTON_SETTINGS_CONTROL_LONG_PRESS = 0x04;
    public static final byte                   BUTTON_SETTINGS_CONTROL_SWIPE = 0x05;


    public static final byte                 BUTTON_SETTINGS_CONTROL_DISABLE = 0x00;
    public static final byte              BUTTON_SETTINGS_CONTROL_LAST_MUSIC = 0x01;
    public static final byte              BUTTON_SETTINGS_CONTROL_NEXT_MUSIC = 0x02;
    public static final byte           BUTTON_SETTINGS_CONTROL_AMBIENT_MUSIC = 0x03;
    public static final byte         BUTTON_SETTINGS_CONTROL_PHONE_CALL_BACK = 0x04;
    public static final byte              BUTTON_SETTINGS_CONTROL_VOLUME_ADD = 0x05;
    public static final byte             BUTTON_SETTINGS_CONTROL_VOLUME_LOSE = 0x06;
    public static final byte              BUTTON_SETTINGS_CONTROL_PLAY_MUSIC = 0x07;
    public static final byte              BUTTON_SETTINGS_CONTROL_STOP_MUSIC = 0x08;
    public static final byte               BUTTON_SETTINGS_CONTROL_ASSISTANT = 0x09;
    public static final byte       BUTTON_SETTINGS_CONTROL_PLAY_PAUSE_MUSIC = 0x0a;
    public static final byte               BUTTON_SETTINGS_CONTROL_GAME_MODE = 0x0b;
    public static final byte                    BUTTON_SETTINGS_CONTROL_ALGO = 0x0c;
    public static final byte               BUTTON_SETTINGS_CONTROL_SPEAKTHRU = 0x0d;

    public static final byte            BUTTON_SETTINGS_CONTROL_SWIPE_ENABLE = (byte) 0xFE;
    public static final byte           BUTTON_SETTINGS_CONTROL_SWIPE_DISABLE = (byte) 0xFF;


    public static final byte       BUTTON_SETTINGS_CONTROL_RESULT_NO_CONNECT = 0x00;
    public static final byte          BUTTON_SETTINGS_CONTROL_RESULT_SUCCESS = 0x01;
    public static final byte             BUTTON_SETTINGS_CONTROL_RESULT_FAIL = 0x02;

    //music play back
    public static final byte                            MUSIC_PLAY_BACK_PLAY = 0x01;
    public static final byte                           MUSIC_PLAY_BACK_PAUSE = 0x02;
    public static final byte                            MUSIC_PLAY_BACK_NEXT = 0x03;
    public static final byte                            MUSIC_PLAY_BACK_PREV = 0x04;

    //fit test
    public static final byte                            EARBUD_FIT_TEST_FIT = 0x01;
    public static final byte                        EARBUD_FIT_TEST_NOT_FIT = 0x02;

    //eq
//    public static final byte                                EQ_TYPE_DEFAULT = 0x01;
    public static final byte                                    EQ_TYPE_POP = 0x02;
    public static final byte                                   EQ_TYPE_ROCK = 0x03;
    public static final byte                                   EQ_TYPE_JAZZ = 0x04;
    public static final byte                                EQ_TYPE_CLASSIC = 0x05;
    public static final byte                                EQ_TYPE_COUNTRY = 0x06;

    public static final byte                           EQ_TYPE_2_BASS_BOOST = 0x02;
    public static final byte                              EQ_TYPE_2_CLASSIC = 0x03;
    public static final byte                              EQ_TYPE_2_HIP_HOP = 0x04;
    public static final byte                                 EQ_TYPE_2_JAZZ = 0x05;
    public static final byte                              EQ_TYPE_2_PODCAST = 0x06;
    public static final byte                                 EQ_TYPE_2_ROCK = 0x07;
    public static final byte                                  EQ_TYPE_2_POP = 0x08;

    //in_ear_detection
    public static final byte                           IN_EAR_DETECTION_ALL = 0x00;
    public static final byte                      IN_EAR_DETECTION_LEFT_EAR = 0x01;
    public static final byte                     IN_EAR_DETECTION_RIGHT_EAR = 0x02;
    public static final byte                     IN_EAR_DETECTION_WD_PROMTP = 0x03;

    public static final byte                         IN_EAR_DETECTION_CLOSE = 0x00;
    public static final byte                          IN_EAR_DETECTION_OPEN = 0x01;
    public static final byte                IN_EAR_DETECTION_RESULT_OUT_EAR = 0x00;
    public static final byte                 IN_EAR_DETECTION_RESULT_IN_EAR = 0x01;

    //battery
    public static final byte                        BATTREY_PERCENTAGE_LEFT = 0x01;
    public static final byte                       BATTREY_PERCENTAGE_RIGHT = 0x02;
    public static final byte                         BATTREY_PERCENTAGE_BOX = 0x03;

    //get spp status
    public static final byte                 GET_SPP_CONNECT_STATUS_CONNECT = 0x01;
    public static final byte              GET_SPP_CONNECT_STATUS_DISCONNECT = 0x02;

    //button state
    public static final byte                    GET_BUTTON_STATE_LEFT_CLICK = 0x01;
    public static final byte                   GET_BUTTON_STATE_RIGHT_CLICK = 0x02;
    public static final byte             GET_BUTTON_STATE_LEFT_DOUBLE_CLICK = 0x03;
    public static final byte            GET_BUTTON_STATE_RIGHT_DOUBLE_CLICK = 0x04;
    public static final byte             GET_BUTTON_STATE_LEFT_TRIPLE_CLICK = 0x05;
    public static final byte            GET_BUTTON_STATE_RIGHT_TRIPLE_CLICK = 0x06;
    public static final byte               GET_BUTTON_STATE_LEFT_LONG_PRESS = 0x07;
    public static final byte              GET_BUTTON_STATE_RIGHT_LONG_PRESS = 0x08;
    public static final byte                    GET_BUTTON_STATE_LEFT_SWIPE = 0x09;
    public static final byte                   GET_BUTTON_STATE_RIGHT_SWIPE = 0x10;

    //function test
    public static final byte                        FUNCTION_TEST_TYPE_CEVA = 0x01;
    public static final byte                       FUNCTION_TEST_TYPE_DOLBY = 0x02;

    public static final byte                       FUNCTION_TEST_RESULT_FIT = 0x01;
    public static final byte                   FUNCTION_TEST_RESULT_NOT_FIT = 0x02;

    public static final byte                          REGULATE_ANC_TYPE_ANC = 0x01;
    public static final byte                     REGULATE_ANC_TYPE_AMNBITNE = 0x02;
    public static final byte                    REGULATE_ANC_TYPE_SPEAKTHRU = 0x03;
    public static final byte                      REGULATE_ANC_TYPE_DEFAULT = 0x04;
    public static final byte                          REGULATE_ANC_TYPE_LOW = 0x05;
    public static final byte                          REGULATE_ANC_TYPE_MID = 0x06;
    public static final byte                         REGULATE_ANC_TYPE_HIGH = 0x07;


    //Dolby
    public static final byte                            DOLBY_TYPE_NO_STATE = 0x00;
    public static final byte                              DOLBY_TYPE_NATUAL = 0x01;
    public static final byte                               DOLBY_TYPE_MOVIE = 0x02;


    //switch status
    public static final byte                            SWITCH_STATUS_SET_0 = 0x00;
    public static final byte                              SWITCH_STATUS_GET = 0x01;
    public static final byte                              SWITCH_STATUS_SET = 0x02;
    public static final byte                        SWITCH_STATUS_SET_PARAM = 0x03;
    public static final byte                      SWITCH_STATUS_SET_PARAM_2 = 0x04;
    public static final byte                      SWITCH_STATUS_SET_PARAM_3 = 0x05;
    public static final byte         SWITCH_STATUS_SET_PARAM_GET_TECH_LEVEL = 0x05;


    public static final byte                           SWITCH_STATUS_OPEN_0 = 0x00;
    public static final byte                             SWITCH_STATUS_OPEN = 0x01;
    public static final byte                              SWITCH_STATUS_OFF = 0x02;
    public static final byte                              SWITCH_STATUS_NEW = 0x03;


    public static final byte                             MULTIPOINT_SET_GET = 0x00;
    public static final byte                             MULTIPOINT_RECEIVE = 0x01;
    public static final byte                               MULTIPOINT_CLOSE = 0x00;
    public static final byte                                MULTIPOINT_OPEN = 0x01;

    //mimi
    public static final byte                          MIMI_SET_PRESENT_FAIL = 0x00;
    public static final byte                       MIMI_SET_PRESENT_SUCCESS = 0x01;


    public static final byte                        HEAD_TRACKING_STATE_OFF = 0x00;
    public static final byte                         HEAD_TRACKING_STATE_ON = 0x01;

    public static final byte                          AUTO_CENTER_MODE_SLOW = 0x27;
    public static final byte                          AUTO_CENTER_MODE_FAST = 0x28;
    public static final byte                    AUTO_CENTER_MODE_STATIONARY = 0x62;



    //message
    public static final int                COMMAND_SET_RECEIVE_BATTERY_LEFT = 0x0000200;

    public static final int               COMMAND_SET_RECEIVE_BATTERY_RIGHT = 0x0000201;
    public static final int                      COMMAND_SET_RECEIVE_IS_FIT = 0x0000202;
    public static final int                        COMMAND_SET_RECEIVE_DATA = 0x0000203;
    public static final int     COMMAND_SET_RECEIVE_IN_EAR_DETECTION_RESULT = 0x0000204;

    public static final int          COMMAND_SET_RECEIVE_SPP_CONNECT_STATUS = 0x0000205;
    public static final int               COMMAND_SET_RECEIVE_BUTTON_STATUS = 0x0000206;
    public static final int       COMMAND_SET_RECEIVE_CURRENT_PRODUCT_MODEL = 0x0000207;
    public static final int                COMMAND_SET_RECEIVE_REGULATE_ANC = 0x0000208;
    public static final int                   COMMAND_SET_RECEIVE_EQ_SWITCH = 0x0000209;
    public static final int                COMMAND_SET_RECEIVE_DOLBY_SWITCH = 0x0000210;
    public static final int      COMMAND_SET_RECEIVE_DOLBY_SWITCH_AND_PARAM = 0x0000211;
    public static final int     COMMAND_SET_RECEIVE_BUTTON_SETTINGS_CONTROL = 0x0000212;
    public static final int          COMMAND_SET_RECEIVE_BES_SPATIAL_SWITCH = 0x0000213;
    public static final int                 COMMAND_SET_RECEIVE_MIMI_SWITCH = 0x0000214;
    public static final int     COMMAND_SET_RECEIVE_MIMI_SET_PRESENT_RESULT = 0x0000215;
    public static final int                 COMMAND_SET_RECEIVE_CEVA_SWITCH = 0x0000216;
    public static final int                 COMMAND_SET_RECEIVE_VERSION_CRC = 0x0000217;
    public static final int                   COMMAND_SET_RECEIVE_START_OTA = 0x0000218;
    public static final int        COMMAND_SET_RECEIVE_TWS_AND_MASTER_STATE = 0x0000219;
    public static final int            COMMAND_SET_RECEIVE_MULTIPOINT_STATE = 0x0000220;
    public static final int             COMMAND_SET_RECEIVE_MIMI_TECH_LEVEL = 0x0000221;
    public static final int              COMMAND_SET_RECEIVE_MIMI_INTENSITY = 0x0000222;
    public static final int                 COMMAND_SET_RECEIVE_SWITCH_ROLE = 0x0000223;
    public static final int                 COMMAND_SET_RECEIVE_BATTERY_BOX = 0x0000224;
    public static final int            COMMAND_SET_RECEIVE_WD_PROMPT_RESULT = 0x0000225;
    public static final int            COMMAND_SET_RECEIVE_LED_ONOFF_RESULT = 0x0000226;
    public static final int        COMMAND_SET_RECEIVE_SPATIAL_AUDIO_RESULT = 0x0000227;
    public static final int  COMMAND_SET_RECEIVE_HEAD_TRACKING_STATE_RESULT = 0x0000228;
    public static final int     COMMAND_SET_RECEIVE_AUTO_CENTER_MODE_RESULT = 0x0000229;
    public static final int      COMMAND_SET_RECEIVE_IMU_ORIENTATION_RESULT = 0x0000230;
    public static final int           COMMAND_SET_RECEIVE_SHARE_MODE_RESULT = 0x0000231;
    public static final int           COMMAND_SET_RECEIVE_CODEC_TYPE_RESULT = 0x0000233;
    public static final int            COMMAND_SET_RECEIVE_GAME_MODE_RESULT = 0x0000234;
    public static final int               COMMAND_SET_RECEIVE_VOLUME_RESULT = 0x0000235;
    public static final int          COMMAND_SET_RECEIVE_TOUCH_ONOFF_RESULT = 0x0000236;

    //handle msg
    public static final int       MSG_COMMSND_SET_ROLE_SWITCH_SCAN_TIME_OUT = 0x0000250;



    /* 60 year old tech4_v1  */
    public static final String sample_preset_60yo = "0xD7, 0x49, 0xD6, 0x38, 0xD6, 0x30, 0xD6, 0x3C, 0xD6, 0x4F, 0xD6, 0x7E, 0xD6, 0x6B, 0xD7, 0x4E, 0xD7, 0xA8, 0xD7, 0xD0, 0xD4, 0x0C, 0xD4, 0xAC, 0xD5, 0xEE, 0xD7, 0x68, 0xD6, 0x38, 0xD6, 0x30, 0xD6,0x3C, 0xD6, 0x4F, 0xD6, 0x7E, 0xD6, 0x6B, 0xD7, 0x4E, 0xD7, 0xA8, 0xD7, 0xD0, 0xD4, 0x0C, 0xD4, 0xAC, 0xD5, 0xEE, 0x22, 0x68, 0x2D, 0x40, 0x2D, 0x7E, 0x2C, 0xA0, 0x2F, 0xD4, 0x2E, 0x06, 0x2E, 0x4D,0x21, 0x87, 0x20, 0xD1, 0x23, 0xD2, 0x22, 0xD4, 0x25, 0x39, 0x24, 0x20, 0x22, 0x2B, 0x2D, 0x40, 0x2D, 0x7E, 0x2C, 0xA0, 0x2F, 0xD4, 0x2E, 0x06, 0x2E, 0x4D, 0x21, 0x87, 0x20, 0xD1, 0x23, 0xD2, 0x22,0xD4, 0x25, 0x39, 0x24, 0x20, 0x9B, 0x2B, 0x9B, 0xFB, 0x9B, 0xFB, 0x9B, 0xFB, 0x9B, 0xFB, 0x9B, 0xFB, 0x9B, 0xFB, 0x9B, 0xFB, 0x9B, 0xFB, 0x9B, 0xFB, 0x9B, 0xFB, 0x9B, 0xFB, 0x9B, 0xFB, 0xDC, 0xFB,0xDC, 0xBC, 0xDC, 0xBC, 0xDC, 0xBC, 0xDC, 0xBC, 0xDC, 0xBC, 0xDC, 0xBC, 0x56, 0x45, 0x56, 0x44, 0xD6, 0x46, 0xD6, 0x59, 0x56, 0x58, 0x56, 0x58, 0x56, 0x58, 0x56, 0x58, 0x56, 0x58, 0x56, 0x58, 0x56,0x58, 0x56, 0x58, 0x56, 0x58, 0x0F, 0x6F, 0x0F, 0x6F, 0x0F, 0x6F, 0x0F, 0x6F, 0x0F, 0x6F, 0x0F, 0x6F, 0xE4, 0x6F, 0xE4, 0x84, 0xE4, 0x84, 0xE4, 0x84, 0xE4, 0x84, 0xE4, 0x84, 0xE4, 0x84";
    /* 45 year old tech4_v1 */
    public static final String sample_preset_45yo = "0xD6, 0x49, 0xD6, 0x33, 0xD6, 0xE2, 0xD6, 0x25, 0xD6, 0x01, 0xD6, 0x22, 0xD6, 0x27, 0xD6, 0x36, 0xD6, 0x3D, 0xD6, 0x3F, 0xD6, 0x20, 0xD6, 0x25, 0xD6, 0x2F, 0xD6, 0x4D, 0xD6, 0x33, 0xD6, 0xE2, 0xD6,0x25, 0xD6, 0x01, 0xD6, 0x22, 0xD6, 0x27, 0xD6, 0x36, 0xD6, 0x3D, 0xD6, 0x3F, 0xD6, 0x20, 0xD6, 0x25, 0xD6, 0x2F, 0x2B, 0x4D, 0x29, 0xB6, 0x29, 0xB6, 0x2B, 0xB6, 0x2A, 0xB6, 0x2A, 0x36, 0x2D, 0xB6,0x2D, 0x36, 0x2C, 0xB6, 0x2C, 0x36, 0x2F, 0xB6, 0x2F, 0x36, 0x21, 0xB6, 0x2B, 0x36, 0x29, 0xB6, 0x29, 0xB6, 0x2B, 0xB6, 0x2A, 0xB6, 0x2A, 0x36, 0x2D, 0xB6, 0x2D, 0x36, 0x2C, 0xB6, 0x2C, 0x36, 0x2F,0xB6, 0x2F, 0x36, 0x21, 0xB6, 0xB0, 0x36, 0xB0, 0xD0, 0xB0, 0xD0, 0xB0, 0xD0, 0xB0, 0xD0, 0xB0, 0xD0, 0xB0, 0xD0, 0xB0, 0xD0, 0xB0, 0xD0, 0xB0, 0xD0, 0xB0, 0xD0, 0xB0, 0xD0, 0xB0, 0xD0, 0xDC, 0xD0,0xDC, 0xBC, 0xDC, 0xBC, 0xDC, 0xBC, 0xDC, 0xBC, 0xDC, 0xBC, 0xDC, 0xBC, 0xD6, 0x4A, 0x56, 0x4D, 0x56, 0x4E, 0x56, 0x40, 0xD6, 0x43, 0xD6, 0x43, 0xD6, 0x43, 0xD6, 0x43, 0xD6, 0x43, 0xD6, 0x43, 0xD6,0x43, 0xD6, 0x43, 0xD6, 0x43, 0x0F, 0x6F, 0x0F, 0x6F, 0x0F, 0x6F, 0x0F, 0x6F, 0x0F, 0x6F, 0x0F, 0x6F, 0xE4, 0x6F, 0xE4, 0x84, 0xE4, 0x84, 0xE4, 0x84, 0xE4, 0x84, 0xE4, 0x84, 0xE4, 0x84";
    /* 18 year old tech4_v1 */
    public static final String sample_preset_18yo = "0xD6, 0x49, 0xD6, 0xC0, 0xD6, 0x80, 0xD6, 0x20, 0xD6, 0x01, 0xD6, 0x3C, 0xD6, 0x2E, 0xD6, 0xC0, 0xD6, 0xC4, 0xD6, 0xD9, 0xD6, 0xDB, 0xD6, 0xCE, 0xD6, 0x34, 0xD6, 0x64, 0xD6, 0xC0, 0xD6, 0x80, 0xD6,0x20, 0xD6, 0x01, 0xD6, 0x3C, 0xD6, 0x2E, 0xD6, 0xC0, 0xD6, 0xC4, 0xD6, 0xD9, 0xD6, 0xDB, 0xD6, 0xCE, 0xD6, 0x34, 0x28, 0x64, 0x29, 0xB6, 0x29, 0xB6, 0x2B, 0xB6, 0x2A, 0xB6, 0x2A, 0x36, 0x2D, 0xB6,0x2D, 0x36, 0x2C, 0xB6, 0x2C, 0x36, 0x2C, 0xB6, 0x2F, 0xB6, 0x2E, 0x36, 0x28, 0x36, 0x29, 0xB6, 0x29, 0xB6, 0x2B, 0xB6, 0x2A, 0xB6, 0x2A, 0x36, 0x2D, 0xB6, 0x2D, 0x36, 0x2C, 0xB6, 0x2C, 0x36, 0x2C,0xB6, 0x2F, 0xB6, 0x2E, 0x36, 0x65, 0x36, 0x65, 0x05, 0x65, 0x05, 0x65, 0x05, 0x65, 0x05, 0x65, 0x05, 0x65, 0x05, 0x65, 0x05, 0x65, 0x05, 0x65, 0x05, 0x65, 0x05, 0x65, 0x05, 0x65, 0x05, 0xDC, 0x05,0xDC, 0xBC, 0xDC, 0xBC, 0xDC, 0xBC, 0xDC, 0xBC, 0xDC, 0xBC, 0xDC, 0xBC, 0x56, 0x4B, 0x56, 0x4B, 0xD6, 0x4D, 0xD6, 0x41, 0xD6, 0x40, 0xD6, 0x40, 0xD6, 0x40, 0xD6, 0x40, 0xD6, 0x40, 0xD6, 0x40, 0xD6,0x40, 0xD6, 0x40, 0xD6, 0x40, 0x0F, 0x6F, 0x0F, 0x6F, 0x0F, 0x6F, 0x0F, 0x6F, 0x0F, 0x6F, 0x0F, 0x6F, 0xE4, 0x6F, 0xE4, 0x84, 0xE4, 0x84, 0xE4, 0x84, 0xE4, 0x84, 0xE4, 0x84, 0xE4, 0x84";

}
