package com.besall.allbase.bluetooth.service.EQ;

import android.content.Context;
import android.util.Log;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.service.base.BesBaseService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.message.BaseMessage;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.view.activity.chipstoollevel4.EQ.EQListBeans;

import java.util.ArrayList;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_ERROR;

public class EQService extends BesBaseService {

    public EQService(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        super(serviceConfig, listener, context);

        if (serviceConfig.getDeviceProtocol() == DeviceProtocol.PROTOCOL_SPP) {
            SPHelper.putPreference(mContext, BesSdkConstants.BES_CONNECT_SERVICE, BesSdkConstants.BES_SPP_CONNECT.toString());
        }
        startConnect(serviceConfig);
    }

    @Override
    public void onStatusChanged(HmDevice device, int status, DeviceProtocol protocol) {
        super.onStatusChanged(device, status, protocol);
        if (status == BES_CONNECT_ERROR) {
            callBackStateChangedMessage(BES_CONNECT_ERROR,"error");
        }
    }

    @Override
    public void onDataReceived(BaseMessage deviceMessage) {
        super.onDataReceived(deviceMessage);
        Log.i(TAG, "onDataReceived: -----" + ArrayUtil.toHex((byte[]) deviceMessage.getMsgContent()));
        if (mConfig.getTotaConnect() && !totauccess) {
            return;
        }


    }

    public void eqSet(int prefix, String name, float gain0, float gain1, ArrayList<EQListBeans> eqList) {
        sendData(EQCMD.getEqSetCMD(prefix, name, gain0, gain1, eqList));
    }

}
