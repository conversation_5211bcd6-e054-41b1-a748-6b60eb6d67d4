package com.besall.allbase.view.activity.level2;


import android.content.Intent;
import android.net.Uri;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.utils.SPHelper;
import com.besall.allbase.R;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.activity.level3.FunctionOtaActivity;
import com.besall.allbase.view.activity.level3.FunctionToolsActivity;
import com.besall.allbase.view.activity.level3.FunctionWatchActivity;
import com.besall.allbase.view.base.BaseActivity;

/**
 * <AUTHOR>
 * @time $ $
 */
public class FunctionChosenActivity extends BaseActivity<IFunctionChosenActivity,FunctionChosenPresenter> implements IFunctionChosenActivity, View.OnClickListener {

    private static FunctionChosenActivity instance;
    private Button otaBtn;
    private Button toolsBtn;
    private Button watch_tools;
    private Button go_to_google_assistant;

    @Override
    protected FunctionChosenPresenter createPresenter() {
        return new FunctionChosenPresenter();
    }

    @Override
    protected void initBeforeSetContent() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_functionchoose;
    }

    @Override
    protected void bindView() {
        otaBtn = (Button)findViewById(R.id.Func_ota);
        toolsBtn = (Button)findViewById(R.id.Func_tools);
        watch_tools = (Button)findViewById(R.id.watch_tools);
        go_to_google_assistant = (Button)findViewById(R.id.go_to_google_assistant);
//        watch_tools.setVisibility(View.GONE);
        tv_title = (TextView) findViewById(R.id.tv_title);
        mToolbar = (Toolbar) findViewById(R.id.toolbar);
    }

    @Override
    protected void initView() {
        otaBtn.setOnClickListener(this);
        toolsBtn.setOnClickListener(this);
        go_to_google_assistant.setOnClickListener(instance);
        watch_tools.setOnClickListener(instance);
        tv_title.setText("BES TOOLS");
        mToolbar.setTitle("");
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {

    }

    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_setting, menu);

        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;
            case R.id.menu_setting:
                mPresenter.goToSettingActivity(instance);
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.Func_ota:
                ActivityUtils.gotoAct(new Intent(), instance, FunctionOtaActivity.class);
                break;
            case R.id.Func_tools:
                ActivityUtils.gotoAct(new Intent(), instance, FunctionToolsActivity.class);
                break;
            case R.id.watch_tools:
                ActivityUtils.gotoAct(new Intent(), instance, FunctionWatchActivity.class);
                break;
            case R.id.go_to_google_assistant:
                Intent intent= new Intent();
                intent.setAction("android.intent.action.VIEW");
                Uri content_url = Uri.parse("https://search.app.goo.gl/?link=https%3A%2F%2Fassistant.google.com&apn=com.google.android.googlequicksearchbox&amv=301108870&al=googleapp%3A//deeplink/%3Fdata%3DEjsKBgiPkLSPARIDCL8FGhISEAgJEgzqxZ2_BgbC2JvpBgAiGAoWaHR0cHM6Ly93d3cuZ29vZ2xlLmNvbQ");
                intent.setData(content_url);
                startActivity(intent);
                break;
            default:
                break;
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        //log
        SPHelper.removePreference(this, BesSdkConstants.BES_SAVE_LOG_NAME);
    }


}
