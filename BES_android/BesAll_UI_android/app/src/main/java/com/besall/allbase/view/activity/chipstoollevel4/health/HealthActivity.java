package com.besall.allbase.view.activity.chipstoollevel4.health;

import static com.besall.allbase.bluetooth.BluetoothConstants.Scan.BES_SCAN_RESULT;

import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.connect.BTService;
import com.bes.bessdk.scan.BtHeleper;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.R;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.activity.chipstoollevel4.health.Pulse.PulseActivity;
import com.besall.allbase.view.activity.chipstoollevel4.health.bloodoxygen.BloodActivity;
import com.besall.allbase.view.activity.chipstoollevel4.health.sleep.SleepActivity;
import com.besall.allbase.view.activity.chipstoollevel4.health.sports.SportsActivity;
import com.besall.allbase.view.activity.chipstoollevel4.health.temperature.TemperatureActivity;
import com.besall.allbase.view.base.BaseActivity;

public class HealthActivity extends BaseActivity<IHealthActivity, HealthPresenter> implements IHealthActivity, View.OnClickListener, BesServiceListener {
    private static HealthActivity instance;
    private LinearLayout mLinearLayout;
    public String[] functions = {"Temperature", "Sleep", "Blood Oxygen", "Pulse","Health","Stress","Sports"};


    BluetoothDevice mDevice;
    HmDevice mHmDevice;
    BesServiceConfig mServiceConfig;

    private Button pick_device;
    private Button pick_device_ble;
    private Button connect_device;
    private TextView device_address;
    private TextView device_name;


    @Override
    protected HealthPresenter createPresenter() {
        return new HealthPresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        mServiceConfig = new BesServiceConfig();

    }

    @Override
    protected int getContentViewId() {
        return R.layout.act_connect;
    }

    @Override
    protected void bindView() {
        tv_title = (TextView) findViewById(R.id.tv_title);
        mToolbar = (Toolbar) findViewById(R.id.toolbar);

        pick_device = (Button)findViewById(R.id.pick_device);
        pick_device_ble = (Button)findViewById(R.id.pick_device_ble);
        connect_device =(Button)findViewById(R.id.connect_device);
        device_address = (TextView) findViewById(R.id.device_address);
        device_name = (TextView) findViewById(R.id.device_name);
        loadanimdrawable();

    }

    @Override
    protected void initView() {
        inittoolbar("Health");
        pick_device.setOnClickListener(instance);
        pick_device_ble.setOnClickListener(instance);
        pick_device_ble.setVisibility(View.VISIBLE);
        connect_device.setOnClickListener(instance);

    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == BluetoothConstants.Scan.REQUEST_CODE_SCAN) {
            onPickDevice(resultCode, data);
        }
    }

    private void onPickDevice(int resultCode, Intent data) {
        if (resultCode == RESULT_OK) {
            mHmDevice = (HmDevice) data.getSerializableExtra(BES_SCAN_RESULT);
            mDevice = BtHeleper.getBluetoothAdapter(instance).getRemoteDevice(mHmDevice.getPreferredProtocol() == DeviceProtocol.PROTOCOL_BLE ? mHmDevice.getBleAddress() : mHmDevice.getDeviceMAC());
            Log.i(TAG, "onPickDevice: ---" + mHmDevice);
            Log.i(TAG, "onPickDevice: " + mDevice.getName());
            Log.i(TAG, "onPickDevice: " + mDevice.getAddress());
            device_address.setText(mDevice.getAddress());
            mServiceConfig.setDevice(mHmDevice);
            String name = mDevice.getName();
            SpannableString ss = new SpannableString(name);
            BesSdkConstants.BesConnectState state = BTService.getDeviceConnectState(instance, mServiceConfig);
            Log.i(TAG, "onPickDevice: -------" + state);
            if (state == BesSdkConstants.BesConnectState.BES_CONNECT) {
                ss.setSpan(new ForegroundColorSpan(Color.rgb(103, 200, 77)), 0, name.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
            device_name.setText(ss);
        }
    }

    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_setting, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;
            case R.id.menu_setting:
                mPresenter.goToSettingActivity(instance);
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    public void initHealth(){
        setContentView(R.layout.activity_health);
        inittoolbar("Health");

        mLinearLayout = (LinearLayout) findViewById(R.id.buttonView);

        DisplayMetrics screen = new DisplayMetrics();
        getWindowManager().getDefaultDisplay().getMetrics(screen);
        int maxWidth = screen.widthPixels;
        int maxHeight = screen.heightPixels;

        for (int i = 0; i < functions.length; i++) {
            int id = getResources().getIdentifier("health" + i, "drawable", getPackageName());
            Drawable drawable = getResources().getDrawable(id);
            Button button = new Button(this);
            button.setText(functions[i]);
            button.setId(i);
            button.setOnClickListener(instance);
            button.setTextSize(18);
            button.setBackgroundResource(R.drawable.rectangle_cardbtn);

            Drawable leftD = getResources().getDrawable(R.drawable.home_icon_chipstools);
            drawable.setBounds(10,10,100,100);
            Drawable rightD = getResources().getDrawable(R.drawable.home_icon_arrow);
            rightD.setBounds(1,1,100,100);
            button.setCompoundDrawables( drawable,null, rightD, null);
            button.setLayoutParams(new ViewGroup.LayoutParams((int) (maxWidth * 0.88), 150));

            View view = new View(this);
            view.setId(i);
            view.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 20));
//            view.setBackgroundResource(R.color.ffe1e6eb);
            mLinearLayout.addView(button);
            mLinearLayout.addView(view);
        }

        mToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                backToConnect();
            }
        });
    }

    @Override
    public void onClick(View v) {
//        Log.i(TAG, "onClick: " + functions[v.getId()]);
        Intent intent = new Intent();
        if (mDevice != null){
            intent.putExtra("device", mDevice.getAddress());
            intent.putExtra("name", mDevice.getName());
        }
        switch (v.getId()){
            case 0:
                ActivityUtils.gotoAct(intent, instance, TemperatureActivity.class);
                break;
            case 1:
                ActivityUtils.gotoAct(intent, instance, SleepActivity.class);
                break;
            case 2:
                ActivityUtils.gotoAct(intent, instance, BloodActivity.class);
                break;
            case 3:
                ActivityUtils.gotoAct(intent, instance, PulseActivity.class);
                break;
            case 4:
                break;
            case 5:
                break;
            case 6:
                ActivityUtils.gotoAct(intent, instance, SportsActivity.class);
                break;
            case 7:
                break;
            case R.id.pick_device:
                mPresenter.pickDecice(instance, BluetoothConstants.Scan.SCAN_SPP);
                connect_device.setVisibility(View.VISIBLE);
                mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
                mServiceConfig.setServiceUUID(BesSdkConstants.BES_SPP_CONNECT);
                break;
            case R.id.pick_device_ble:
                mPresenter.pickDecice(instance, BluetoothConstants.Scan.SCAN_BLE);
                connect_device.setVisibility(View.VISIBLE);
                mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_BLE);
                mServiceConfig.setServiceUUID(BesSdkConstants.BES_TOTA_SERVICE_OTA_UUID);
                mServiceConfig.setCharacteristicsUUID(BesSdkConstants.BES_TOTA_CHARACTERISTI_OTA_UUID);
                mServiceConfig.setDescriptorUUID(BesSdkConstants.BES_TOTA_DESCRIPTOR_OTA_UUID);
                break;
            case R.id.connect_device:
//                initHealth();
                loadinganim();
                if (mHmDevice == null) {
                    loadingDialog.dismiss();
                    ActivityUtils.showToast(R.string.connect_failed);
                    return;
                }
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        Log.i(TAG, "run: 1");
                        mServiceConfig.setDevice(mHmDevice);
                        Log.i(TAG, "onPickDevice:1111 " + mDevice.getAddress());
                        mServiceConfig.setTotaConnect(true);
                        boolean useTotaV2 = (boolean) SPHelper.getPreference(instance, BesSdkConstants.BES_TOTA_USE_TOTAV2, BesSdkConstants.BES_TOTA_USE_TOTAV2_VALUE);
                        mServiceConfig.setUseTotaV2(useTotaV2);
                        mPresenter.connectDevice(mServiceConfig, instance, instance);
                    }
                });
                break;
        }
    }

    public void backToConnect() {
        setContentView(R.layout.act_connect);
        bindView();
        initView();
    }

    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (state == true) {
                    initHealth();
                    loadingDialog.dismiss();
                }
            }
        });
    }

    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {

    }

    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {

    }

    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {

    }

    String tempData = "";
    int time = 0;
    boolean state = false;
    byte[] dataLen = new byte[2];
    byte[] totalCount = new byte[2];
    int totalCounts = 0;//总包数
    int totalLen = 0;//数据总长度
    byte[] mpkgData = new byte[178];
    String recevieMsg = "";
    // 接收数据分包
    public boolean checkPubkey(byte[] data){
        Log.i(TAG, "1111111----: " + ArrayUtil.toHex(data));
        if ((data[0] & 0xff) == 0x09 && (data[1] & 0xff) == 0x02){
            Log.i(TAG, "checkPubkey: "+ArrayUtil.toHex(data));
            byte[] pkglen = new byte[2];
            System.arraycopy(data, 2, pkglen,0,2);
            int pkgL = ArrayUtil.byte2int(pkglen);
            Log.i(TAG, "checkPubkey: "+pkgL);

            byte[] pkgData = new byte[pkgL - 4];
            System.arraycopy(data, 4, pkgData, 0, pkgL - 4);
            recevieMsg = ArrayUtil.toHex(pkgData);
            state = true;
            return state;
        } else
        if (data[0] == 0x7f ) {
            //第一包
            if ((data[8] & 0xff) == 0x09 && (data[9] & 0xff) == 0x02) {
                totalCount[0] = data[4];
                totalCount[1] = data[5];
                dataLen[0] = data[10];
                dataLen[1] = data[11];
                totalCounts = ArrayUtil.byte2int(totalCount);//总包数
                totalLen = ArrayUtil.byte2int(dataLen);//数据总长度
                Log.i(TAG, "totalCounts: " + totalCounts);
                Log.i(TAG, "totalLen: " + totalLen);
                mpkgData = new byte[170];
                System.arraycopy(data, 12, mpkgData,0,170);
                tempData = tempData + ArrayUtil.toHex(mpkgData);
                Log.i(TAG, "tempData: 170-------" + tempData);
                time ++;
            } else if (time < (totalCounts - 1) && time > 0) {
                mpkgData = new byte[178];
                System.arraycopy(data, 4, mpkgData,0,178);
                tempData = tempData + ArrayUtil.toHex(mpkgData);
                Log.i(TAG, "tempData: 178----" + tempData);
                time ++;
            } else if (time == totalCounts - 1){
                mpkgData = new byte[totalLen - 170 - 178 * (totalCounts - 2)];
                System.arraycopy(data, 4, mpkgData,0,totalLen - 170 - 178 * (totalCounts - 2));
                tempData = tempData + ArrayUtil.toHex(mpkgData);
                Log.i(TAG, "tempData: last---" + tempData);
                recevieMsg = tempData;
                Log.i(TAG, "checkPubkey: times" + recevieMsg);
                state = true;
                return state;
            }
            Log.i(TAG, "times: " + time);
        }


        Log.i(TAG, "checkPubkeystate: " + state);
        Log.i(TAG, "checkPubkeystate: " + recevieMsg);

        return state;
    }
    //发送数据分包
    public byte[] dataSplit(byte[] bytes){
        byte[] pkgData = new byte[182];
        int len = bytes.length + 8;
        int remainder = len % 178;
        boolean isLastData = (remainder != 0);
        int count = len / 178 + (isLastData ? 1 : 0);
        int lastPkgLen = len - 178 * (count - 1);
        byte[] totalCount = ArrayUtil.intToBytes2(count);
        byte[] multiHead = new byte[8];
//        byte[] CRC = ArrayUtil.intToBytesLittle(bytes.length);
        long CRCL = ArrayUtil.crc32(bytes,0, bytes.length);
        byte[] CRC_L = new byte[4];
        CRC_L[0] =(byte) CRCL;
        CRC_L[1] = (byte) (CRCL >> 8);
        CRC_L[2] = (byte) (CRCL >> 16);
        CRC_L[3] = (byte) (CRCL >> 24);

        multiHead[0] = totalCount[0];
        multiHead[1] = totalCount[1];
        multiHead[2] = CRC_L[0]; //crc_l
        multiHead[3] = CRC_L[1]; //crc_H
        multiHead[4] = (byte) 0x09;
        multiHead[5] = (byte) 0x01;
        byte[] multiData = new byte[len];
        System.arraycopy(ArrayUtil.intToBytes2(len), 0, multiHead, 6, 2);
        System.arraycopy(multiHead, 0, multiData, 0, 8);
        System.arraycopy(bytes, 0 ,multiData, 8, bytes.length); //总数据

        if (count > 1) {
            int curL = 0;
            for (int i = 0; i < count; i++) {
                pkgData[0] = (byte) 0x7f;
                byte[] countNum = ArrayUtil.intToBytes2(i);
                pkgData[1] = CRC_L[0]; //crc_L
                pkgData[2] = countNum[0];
                pkgData[3] = countNum[1];
                Log.i(TAG, "initView: ====" + i);
                Log.i(TAG, "initView: ----" + curL);
                if (curL < 178 && i > 0){
                    pkgData = new byte[curL + 4];
                    pkgData[0] = (byte) 0x7f;
                    pkgData[1] = CRC_L[0]; //crc_L
                    pkgData[2] = countNum[0];
                    pkgData[3] = countNum[1];
                    Log.i(TAG, "dataSplit: " + (len-curL) + pkgData.length);
                    System.arraycopy(multiData, len - curL, pkgData, 4, curL);
                    Log.i(TAG, "lastPkgLen: " + ArrayUtil.toHex(pkgData));
//                    LOG("lastPkgLen = " + ArrayUtil.toHex(pkgData));
//                    mPresenter.sendData(pkgData, 20);
                    return pkgData;
                }
                System.arraycopy(multiData, i * 178, pkgData, 4, 178);
                Log.i(TAG, "curpkg: " + ArrayUtil.toHex(pkgData));
//                LOG("curpkg = " + ArrayUtil.toHex(pkgData));
                curL = len - 178 * (i + 1);
//                mPresenter.sendData(pkgData, 20);
                return pkgData;
//                checklog("sendData----------" + ArrayUtil.toHex(pkgData));
            }
        } else {
            pkgData = new byte[bytes.length + 4];
            pkgData[0] = (byte) 0x09;
            pkgData[1] = (byte) 0x01;
            System.arraycopy(ArrayUtil.intToBytes2(bytes.length), 0, pkgData, 2, 2);
            System.arraycopy(bytes, 0, pkgData, 4, bytes.length);
//            mPresenter.sendData(pkgData, 0);
            return pkgData;
        }

        return pkgData;
    }
}