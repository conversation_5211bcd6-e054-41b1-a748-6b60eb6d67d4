package com.besall.allbase.view.activity.chipstoollevel4.blewifi;

import static com.bes.bessdk.BesSdkConstants.BES_WIFI_CHARACTERISTIC_TX_UUID_6;
import static com.bes.bessdk.BesSdkConstants.BesConnectState;
import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_SUCCESS;
import static com.bes.bessdk.BesSdkConstants.BES_WIFI_CHARACTERISTIC_RX_UUID;
import static com.bes.bessdk.BesSdkConstants.BES_WIFI_CHARACTERISTIC_TX_UUID;
import static com.bes.bessdk.BesSdkConstants.BES_WIFI_DESCRIPTOR_UUID;
import static com.bes.bessdk.BesSdkConstants.BES_WIFI_SERVICE_UUID;
import static com.bes.bessdk.BesSdkConstants.BesConnectState.BES_CONNECT;
import static com.besall.allbase.bluetooth.BluetoothConstants.Scan.BES_SCAN_RESULT;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_RECEIVE_WIFI_CONFIG;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_RECEIVE_WIFI_OPEN_WIFI_FAIL;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_WIFI_OPEN_WIFI_RSP;

import android.bluetooth.BluetoothDevice;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Bundle;
import android.provider.Settings;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.connect.BleConnector;
import com.bes.bessdk.scan.BtHeleper;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.R;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.activity.chipstoollevel4.blewifi.wifilist.WifiListActivity;
import com.besall.allbase.view.activity.tools.confirmdialog.ConfirmDialog;
import com.besall.allbase.view.activity.tools.confirmdialog.ConfirmDialoglistener;
import com.besall.allbase.view.base.BaseActivity;


/**
 * <AUTHOR>
 * @time $ $
 */
public class BleWifiActivity extends BaseActivity<IBleWifiActivity, BleWifiPresenter> implements IBleWifiActivity, BesServiceListener, View.OnClickListener
{
    private static BleWifiActivity instance;

    BluetoothDevice mDevice;
    HmDevice mHmDevice;
    BesServiceConfig mServiceConfig;

    private Button pickDevice;
    private Button connect_device;
    private Button pick_wifi;
    private Button send_data;

    private TextView ble_name;
    private EditText wifi_name_value;
    private EditText wifi_pw_value;
    private Button open_wifi;

    @Override
    protected BleWifiPresenter createPresenter() {
        return new BleWifiPresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        Log.i(TAG, "bindView: ---------" + getCurrentSsid(instance));
        //log
        SPHelper.putPreference(instance, BesSdkConstants.BES_SAVE_LOG_NAME, "BleWifi");
        mServiceConfig = new BesServiceConfig();
        mServiceConfig.setServiceUUID(BES_WIFI_SERVICE_UUID);
        mServiceConfig.setCharacteristicsUUID(BES_WIFI_CHARACTERISTIC_TX_UUID);
        mServiceConfig.setCharacteristicsTX(BES_WIFI_CHARACTERISTIC_TX_UUID_6);
        mServiceConfig.setDescriptorUUID(BES_WIFI_DESCRIPTOR_UUID);
        mServiceConfig.setTotaConnect(false);
        mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_BLE);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_blewifi;
    }

    @Override
    protected void bindView() {
        pickDevice = (Button)findViewById(R.id.pick_device);
        connect_device = (Button)findViewById(R.id.connect_device);
        pick_wifi = (Button)findViewById(R.id.pick_wifi);
        send_data = (Button)findViewById(R.id.send_data);
        ble_name = (TextView)findViewById(R.id.ble_name);
        wifi_name_value = (EditText) findViewById(R.id.wifi_name_value);
        open_wifi = (Button)findViewById(R.id.open_wifi);

        if (getwifiname().length() > 0) {
            wifi_name_value.setText(getwifiname());
        } else {
            Log.i(TAG, "bindView: ---------" + getCurrentSsid(instance));
            wifi_name_value.setText(getCurrentSsid(instance));
        }

        wifi_pw_value = (EditText) findViewById(R.id.wifi_pw_value);
        if (getwifipw().length() > 0 ) {
            wifi_pw_value.setText(getwifipw());
        }
    }

    @Override
    protected void initView() {
        String titleStr = "BLE WIFI";
        tv_title.setText(titleStr);
        mToolbar.setTitle("");
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);

        pickDevice.setOnClickListener(instance);
        connect_device.setOnClickListener(instance);
        pick_wifi.setOnClickListener(instance);
        send_data.setOnClickListener(instance);
        open_wifi.setOnClickListener(instance);
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
//        mPresenter.disconnect();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode != RESULT_OK) {
            return;
        }
        if (requestCode == BluetoothConstants.Scan.REQUEST_CODE_SCAN) {
            onPickDevice(resultCode, data);
        } else if (requestCode == BleWifiConstants.CHOOSE_WIFI_RESULT) {
            String str = data.getStringExtra(BleWifiConstants.CHOOSE_WIFI_RESULT_KEY);
            wifi_name_value.setText(str);
        }
    }

    private void onPickDevice(int resultCode, Intent data) {
        if (resultCode == RESULT_OK) {
            mHmDevice = (HmDevice) data.getSerializableExtra(BES_SCAN_RESULT);
            mDevice = BtHeleper.getBluetoothAdapter(instance).getRemoteDevice(mHmDevice.getPreferredProtocol() == DeviceProtocol.PROTOCOL_BLE ? mHmDevice.getBleAddress() : mHmDevice.getDeviceMAC());

            mServiceConfig.setDevice(mHmDevice);
            BesConnectState state = BleConnector.getsConnector(instance, null, null).getDeviceConnectState(mServiceConfig);
            if (state == BES_CONNECT) {
                ble_name.setTextColor(instance.getResources().getColor(R.color.green));
                mPresenter.connectDevice(mServiceConfig, instance, instance);
            } else {
                ble_name.setTextColor(Color.DKGRAY);
            }
            ble_name.setText(mDevice.getName());
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.pick_device:
                mPresenter.pickDecice(instance, BluetoothConstants.Scan.SCAN_BLE);
                break;
            case R.id.connect_device:
                if (mHmDevice == null) {
                    ActivityUtils.showToast(R.string.pleaseSelectDevice);
                    return;
                }

                mServiceConfig.setDevice(mHmDevice);
                mPresenter.connectDevice(mServiceConfig, instance, instance);
                break;
            case R.id.pick_wifi:
                mPresenter.pickWifi(instance);
                break;
            case R.id.send_data:
                sendWifiData();
                break;
            case R.id.open_wifi:
                mPresenter.openWifi();
                break;
            default:
                break;
        }
    }

    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {

    }

    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                ble_name.setTextColor(Color.RED);
                showToast(R.string.connect_failed);
            }
        });
    }

    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (msg == BES_CONNECT_SUCCESS) {
                    ble_name.setTextColor(instance.getResources().getColor(R.color.green));
                    showToast(R.string.connect_success);
                }  else if (msg == BES_BLE_WIFI_OPEN_WIFI_RSP) {
                    showToast(R.string.open_wifi_wait);
                } else if (msg == BES_BLE_RECEIVE_WIFI_OPEN_WIFI_FAIL) {
                    showToast(R.string.open_wifi_fail);
                } else if (msg == BES_BLE_RECEIVE_WIFI_CONFIG) {
                    String[] wifiInfo = msgStr.split("\n");
                    connectToHotspot(wifiInfo[1], wifiInfo[2]);
                } else {
                    ble_name.setTextColor(Color.RED);
                    showToast(R.string.connect_failed);
                }
            }
        });
    }

    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {

    }

    public void sendWifiData() {
        if (wifi_name_value.getText().length() == 0) {
            showToast("Wifi name cannot be empty");
            return;
        }
        if (ble_name.getCurrentTextColor() != instance.getResources().getColor(R.color.green)) {
            showToast("please check ble connect");
            return;
        }

        String name = wifi_name_value.getText().toString();
        String pw = wifi_pw_value.getText().toString();
        //保存值
        SPHelper.putPreference(this, BleWifiConstants.WIFI_NAME, name);
        SPHelper.putPreference(this, BleWifiConstants.WIFA_PASSWORD, pw);

        mPresenter.sendWifiData(name, pw);
    }

    public static String getCurrentSsid(Context context) {
        String ssid = "";
        ConnectivityManager connManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo networkInfo = connManager.getNetworkInfo(ConnectivityManager.TYPE_WIFI);
        if (networkInfo.isConnected()) {
            final WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
            final WifiInfo connectionInfo = wifiManager.getConnectionInfo();
            Log.i("TAG", "getCurrentSsid: ------" + connectionInfo);
            if (connectionInfo != null && !connectionInfo.getSSID().contains("unknown ssid")) {
                ssid = connectionInfo.getSSID().substring(1, connectionInfo.getSSID().length() - 1);
            }
        }
        return ssid;
    }

    private String getwifiname() {
        return (String) SPHelper.getPreference(this, BleWifiConstants.WIFI_NAME,"");
    }

    private String getwifipw() {
        return (String) SPHelper.getPreference(this, BleWifiConstants.WIFA_PASSWORD,"");
    }

    private void connectToHotspot(String ssid, String psw) {
        ClipboardManager clipboard = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
        clipboard.setPrimaryClip(ClipData.newPlainText("label", psw));

        showConfirmDialog(ssid, psw);
    }

    private void showConfirmDialog(String ssid, String psw) {
        String msg = "Click OK to jump to the WIFI setting screen\nPlease select a WIFI name:" + ssid + "\npassword:" + psw + "(Copied on the paste board)" + "\nmake a connection";
        ConfirmDialog confirmDialog = new ConfirmDialog(this, msg, new ConfirmDialoglistener() {
            @Override
            public void confirmYes() {
                Intent intent = new Intent(Settings.ACTION_WIFI_SETTINGS);
                Bundle extras = new Bundle();
                extras.putString("ssid", ssid);
                extras.putString("password", psw);
                intent.putExtras(extras);
                startActivity(intent);
            }

            @Override
            public void confirmNo() {

            }
        });

        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                confirmDialog.show();
            }
        });
    }

    protected void showToast(int msg) {
        Toast.makeText(this, msg, Toast.LENGTH_SHORT).show();
    }

    protected void showToast(String msg) {
        Toast.makeText(this, msg, Toast.LENGTH_SHORT).show();
    }

}
