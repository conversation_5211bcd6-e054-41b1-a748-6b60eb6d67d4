package com.besall.allbase.view.activity.chipstoollevel4.AvsLwa.resize;

import android.content.Context;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;

public interface IAvsLwaResizePresenter {

    void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context);

    void sendCurSettingData(int type, int value);

    void getWholeSettingData();

    String getCurType();

    String getCurValue();

    void disconnect();

}
