package com.besall.allbase.view.activity.chipstoollevel4.customerdial;

import android.content.Context;
import android.content.Intent;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;
import com.besall.allbase.bluetooth.service.customerdial.CustomerDialService;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.base.BasePresenter;


/**
 * <AUTHOR>
 * @time $ $
 */
class CustomerDialPresenter extends BasePresenter<ICustomerDialActivity> implements ICustomerDialPresenter {
    public final String TAG = getClass().getSimpleName();
    CustomerDialService dialService;

    @Override
    public void pickDecice(CustomerDialActivity context, int scan) {
        Intent intent = new Intent();
        intent.putExtra(BluetoothConstants.Scan.BES_SCAN, scan);
        ActivityUtils.gotoActForResult(intent, BluetoothConstants.Scan.REQUEST_CODE_SCAN, context, ScanActivity.class);
    }

    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        dialService = new CustomerDialService(serviceConfig, listener, context);

    }


    @Override
    public void disconnect() {
        if (dialService != null)
            dialService.disconnected();
    }

    @Override
    public void test() {
        if (dialService != null)
            dialService.test();
    }

    @Override
    public void startTransfer(byte[] oldData, byte[] data, int type, int isIncremental, byte[] param) {
        if (dialService != null)
            dialService.startTransfer(oldData, data, type, isIncremental, param);
    }


}
