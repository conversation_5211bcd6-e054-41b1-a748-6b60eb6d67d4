package com.besall.allbase.view.activity.chipstoollevel4.SmartVoice;

import android.content.Context;
import android.content.Intent;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;
import com.besall.allbase.bluetooth.service.smartvoice.SmartVoiceService;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.base.BasePresenter;

public class SmartVoicePresenter extends BasePresenter<ISmartVoiceActivity> implements ISmartVoicePresenter {

    private SmartVoiceService smartVoiceService;


    @Override
    public void pickDecice(SmartVoiceActivity context, int scan) {
        Intent intent = new Intent();
        intent.putExtra(BluetoothConstants.Scan.BES_SCAN, scan);
        ActivityUtils.gotoActForResult(intent, BluetoothConstants.Scan.REQUEST_CODE_SCAN, context, ScanActivity.class);
    }

    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        if (smartVoiceService == null) {
            smartVoiceService = new SmartVoiceService(serviceConfig, listener, context);
        }

    }
}
