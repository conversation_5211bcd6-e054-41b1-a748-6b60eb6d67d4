package com.besall.allbase.view.activity.tools.actionsheet;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.widget.Button;

import com.besall.allbase.R;
import com.besall.allbase.common.utils.ResourceUtil;

public class ActionSheet extends Dialog implements View.OnClickListener {
    private Context mContext;
    private String[] mItems;
    private ActionSheetListener mListener;
    private Button cancel, action_sheet_0, action_sheet_1;

    public ActionSheet(Context context, String[] items, ActionSheetListener actionSheetListener) {
        super(context);
        this.mContext = context;
        this.mItems = items;
        this.mListener = actionSheetListener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        getWindow().requestFeature(Window.FEATURE_NO_TITLE);
        LayoutInflater inflater = (LayoutInflater) mContext
                .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View layout = inflater.inflate(ResourceUtil.getLayoutId(mContext,
                "action_sheet"), null);
        setContentView(layout);
        initview();
    }

    private void initview() {
        action_sheet_0 = (Button) findViewById(ResourceUtil.getId(mContext, "action_sheet_0"));
        action_sheet_0.setText(mItems[0]);
        action_sheet_0.setOnClickListener(this);
        action_sheet_1 = (Button) findViewById(ResourceUtil.getId(mContext, "action_sheet_1"));
        action_sheet_1.setText(mItems[1]);
        action_sheet_1.setOnClickListener(this);
        cancel = findViewById(ResourceUtil.getId(mContext, "action_sheet_cancel"));
        cancel.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.action_sheet_0:
                mListener.didSelectItem(0);
                break;
            case R.id.action_sheet_1:
                mListener.didSelectItem(1);
                break;
            default:
                break;
        }
        ActionSheet.this.dismiss();
    }
}
