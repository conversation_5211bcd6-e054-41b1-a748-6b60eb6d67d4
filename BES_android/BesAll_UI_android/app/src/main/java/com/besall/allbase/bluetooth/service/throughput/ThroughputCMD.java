package com.besall.allbase.bluetooth.service.throughput;

import android.content.Context;
import android.util.Log;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.CmdInfo;
import com.besall.allbase.common.utils.BlePreferenceUtil;
import com.besall.allbase.common.utils.LogUtils;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;

import static com.besall.allbase.bluetooth.service.throughput.ThroughputConstants.KEY_SPP_DATA_SIZE;



public class ThroughputCMD {

    static String TAG = "ThroughputCMD";
    static byte[] waitMoreDatas ;
    static int throughDataSize = 20 ;


    public static byte[] sendInitConfigToDevice(Context context, int status) {

        byte[] configs = getInitConfig(context, status);
        CmdInfo iniCmd = new CmdInfo(ThroughputConstants.OP_INFORM_THROUGHPUT_TEST_CONFIG ,configs);
        Log.i(TAG, "sendInitConfigToDevice: " + ArrayUtil.toHex(iniCmd.toBytes()));
        return iniCmd.toBytes();
    }


    public static byte[] sendThroughputPackage(Context context, int status) {
        CmdInfo throughputDataCmd = new CmdInfo(ThroughputConstants.OP_THROUGHPUT_TEST_DATA , getThroughPutPackage(context, status));
        return throughputDataCmd.toBytes();
    }


    public static byte[] sendPackageDone() {
        CmdInfo throughputDoneCmd = new CmdInfo(ThroughputConstants.OP_THROUGHPUT_TEST_DONE , null);
        return throughputDoneCmd.toBytes();
    }

    public static byte[] sendack() {
        CmdInfo throughputACKCmd = new CmdInfo(ThroughputConstants.OP_THROUGHPUT_TEST_DATA_ACK , null);
        return throughputACKCmd.toBytes();
    }

    private static byte[] getThroughPutPackage(Context context, int status) {
        Log.i(TAG, "getThroughPutPackage: ");
        int dataSize = 0;
        if (status == 0) {
            dataSize = (int) BlePreferenceUtil.get(context , ThroughputConstants.REAL_SPP_DATA_SIZE , 20);
        } else if (status == 1){
            dataSize = (int) BlePreferenceUtil.get(context , ThroughputConstants.REAL_BLE_DATA_SIZE , 20);
//            dataSize = 300;
        }
//        int dataSize = 20;
        if(dataSize < 20){
            dataSize = 20 ;
        }
        if (status == 0) {
            if(dataSize > (ThroughputConstants.SPP_MAX_DATA_SIZE )) {
                dataSize =ThroughputConstants.SPP_MAX_DATA_SIZE ;
            }
        } else if (status == 1) {
            if(dataSize > (ThroughputConstants.BLE_MAX_DATA_SIZE )) {
                dataSize = ThroughputConstants.BLE_MAX_DATA_SIZE ;
            }
        }

        byte[] arrays = new byte[dataSize - 4];
        int  testDataPattern =  Integer.valueOf((String) BlePreferenceUtil.get(context, ThroughputConstants.KEY_TEST_DATA_PATTERN , 0+""));
        byte sendData = 0x00 ;
        if(testDataPattern < ThroughputConstants.THROUGH_PUT_PACKAGE_PATTERN.length){
            sendData = ThroughputConstants.THROUGH_PUT_PACKAGE_PATTERN[testDataPattern];
        }
        for(int i = 0 ; i < dataSize - 4; i ++) {
            if (testDataPattern == 0) {
                arrays[i] = (byte)(Math.random() * 255) ;
            } else {
                arrays[i] = (byte)(sendData&0xff) ;
            }
        }
        return arrays ;
    }

    public static int receiveData(byte[] data, Context context, int type){
        Log.i(TAG, "receiveData: --" + ArrayUtil.toHex(data));
        byte[] result = new byte[data.length];
        int r = 0;
        if (data == null) return 0;
        if (data[0] == 0x09 && (data[1]&0xff) == 0x80 && data.length == 20) {
            parseInitConfig(data, context, type);
            r = ThroughputConstants.RECEIVED_INI_CONFIG_RET;
            return r;
        } else if (data[0] == 0x0a && (data[1]&0xff) == 0x80 && data.length > 4) {
            if (type == 0 ) {
                BlePreferenceUtil.put(context,ThroughputConstants.SPPGET_THROUGH_PUT_PACKAGE,data.length - CmdInfo.HEAD_LEN);
                r = ThroughputConstants.GET_THROUGH_PUT_PACKAGE;
            } else if (type == 1) {
                Log.i(TAG, "receiveData: spp-----");
                BlePreferenceUtil.put(context,ThroughputConstants.BLEGET_THROUGH_PUT_PACKAGE,data.length - CmdInfo.HEAD_LEN);
                r = ThroughputConstants.GET_THROUGH_PUT_PACKAGE;
            }
            return r;

        } else if (data[0] == 0x0B && (data[1]&0xff) == 0x80) {

            r = ThroughputConstants.SPP_SEND_THROUGH_PUT_NEXT;
            return r;

        } else if (data[0] == 0x0C && (data[1]&0xff) == 0x80) {    //收到结束信息
            r = ThroughputConstants.GET_THROUGH_PUT_DONE_MSG;
            return r;

        } else if (data[0] == 0x00 && (data[1]&0xff) == 0x80) { //配置信息返回
            r = ThroughputConstants.INI_CONFIG_RETURN;
            return r;
        }
        return r;

//
//        if (type == 0 ) {
//            if (data == null) return 0;
//            if (data[0] == 0x09 && (data[1]&0xff) == 0x80 && data.length == 20) {
//                Log.i(TAG, "handleBLEDeviceControlCmd: handleDeviceControlCmd  ini response");
//                parseIniConfig(data, context);
//                r = ThroughputConstants.RECEIVED_INI_CONFIG_RET;
//                return r;
//
//            } else if (data[0] == 0x0a && (data[1]&0xff) == 0x80 && data.length > 4 ) {  //收到吞吐量数据流
//                BlePreferenceUtil.put(context,ThroughputConstants.BLEGET_THROUGH_PUT_PACKAGE,data.length-4);
//                r = ThroughputConstants.GET_THROUGH_PUT_PACKAGE;
//                return r;
//
//            } else if (data[0] == 0x0C && (data[1]&0xff) == 0x80 ) {    //收到结束信息
//                r = ThroughputConstants.BLE_GET_THROUGH_PUT_DONE_MSG;
//                return r;
//
//            } else if (data[0] == 0x00 && (data[1]&0xff) == 0x80 ) { //配置信息返回
//                Log.i(TAG, "RESPONE: ");
//                r = ThroughputConstants.INI_CONFIG_RETURN;
//                return r;
//            }
//            return r;
//        }
//        if (type == 1 ) {
//            Log.i(TAG, "handleDeviceControlCmd: " + ArrayUtil.toHex(data));
//
//            if (data == null) return 0;
//            if (data[0] == 0x09 && (data[1]&0xff) == 0x80 && data.length == 20) {
//                Log.i(TAG, "handleDeviceControlCmd: ini response");
//                parseIniConfig(data, context);
//                r = ThroughputConstants.RECEIVED_INI_CONFIG_RET;
//                return r;
//            } else if (data[0] == 0x0a && (data[1] & 0xff) == 0x80 && data.length > 4) {  //收到吞吐量数据流
//                BlePreferenceUtil.put(context,ThroughputConstants.SPPGET_THROUGH_PUT_PACKAGE,data.length-CmdInfo.HEAD_LEN);
//                r = ThroughputConstants.GET_THROUGH_PUT_PACKAGE;
//                return r;
//            } else if (data[0] == 0x0B && (data[1] & 0xff) == 0x80 ) {//收到结束信息
//                Log.i(TAG, "receiveData: next");
//                r = ThroughputConstants.SPP_SEND_THROUGH_PUT_NEXT;
//                return r;
//            } else if (data[0] == 0x0C && (data[1] & 0xff) == 0x80 ) {    //收到结束信息
//                r = ThroughputConstants.GET_THROUGH_PUT_DONE_MSG;
//                return r;
//            } else if (data[0] == 0x00 && (data[1] & 0xff) == 0x80 ){ //配置信息返回
//                r = ThroughputConstants.INI_CONFIG_RETURN;
//                return r;
//            }
//            return r;
//        }
//        return r;
//            int throughDataSize = Integer.valueOf((String)BlePreferenceUtil.get(context , KEY_SPP_DATA_SIZE , "20"));
//            setThroughDataSize(throughDataSize);
//            byte[][] ret = checkDataEnoughAndRetArray(data);
//            if(ret != null && ret.length > 0) {
//                for (int i = 0 ; i < ret.length ; i++){
//                    Log.i(TAG, ">>>>>>>>>>>>>>> ret index "+i );
//                    if(ret[i] != null && ret[i].length > 0 && (ret[i][0] & 0xff) == 0xff){
//                        Log.i(TAG, "..check is opus  and ret "+ArrayUtil.toHex(ret[i]));
//                    } else {
//                        if (ret[i] != null && ret[i].length > 0) {
//                            Log.i(TAG, "..check is cmd  and ret "+ArrayUtil.toHex(ret[i]));
//                            result = ret[i];
//                            Log.i(TAG, "handleDeviceControlCmd: " + ArrayUtil.toHex(data));
//
//                            if (result == null) return 0;
//                            if (result[0] == 0x09 && (result[1]&0xff) == 0x80 && result.length == 20) {
//                                Log.i(TAG, "handleDeviceControlCmd: ini response");
//                                parseIniConfig(result, context);
//                                r = ThroughputConstants.RECEIVED_INI_CONFIG_RET;
//                                return r;
//                            } else if (result[0] == 0x0a && (result[1] & 0xff) == 0x80) {  //收到吞吐量数据流
//                                BlePreferenceUtil.put(context,ThroughputConstants.SPPGET_THROUGH_PUT_PACKAGE,result.length-CmdInfo.HEAD_LEN);
//                                r = ThroughputConstants.GET_THROUGH_PUT_PACKAGE;
//                                return r;
//                            } else if (result[0] == 0x0B && (result[1] & 0xff) == 0x80 ) {//收到结束信息
//                                Log.i(TAG, "receiveData: next");
//                                r = ThroughputConstants.SPP_SEND_THROUGH_PUT_NEXT;
//                                return r;
//                            } else if (result[0] == 0x0C && (result[1] & 0xff) == 0x80 ) {    //收到结束信息
//                                r = ThroughputConstants.GET_THROUGH_PUT_DONE_MSG;
//                                return r;
//                            } else if (result[0] == 0x00 && (result[1] & 0xff) == 0x80 ){ //配置信息返回
//                                r = ThroughputConstants.INI_CONFIG_RETURN;
//                                return r;
//                            }
//                            return r;
//                        } else {
//                            Log.i(TAG, "cmd onReceive but checkDataEnoughAndRet is null" );
//                        }
//                    }
//                }
//            }else{
//                Log.i(TAG, "..check false ");
//            }
//        }
//        return r;
    }

    private static void parseInitConfig(byte[] data, Context mContext, int type) {
        if(data == null || data.length < 20) {
            return;
        }
        int testDataPattern = data[4]&0xff;
        int lastingTimeSecond = (data[5] & 0xff) + (((data[6]&0xff) << 8) & 0xffff) ;
        int dataSize = (data[7]&0xff) + (((data[8]&0xff) << 8)&0xffff) ;
        int direction =  data[9] & 0xff ;
        int isWithRepsone = data[10] & 0xff ;
        int isUseSpecificConnectionParam = data[11] & 0xff ;
        int minIntervalInMs = (data[12] & 0xff) + (((data[13] & 0xff) << 8) & 0xffff) ;
        int maxIntervalInMs = (data[14] & 0xff) + (((data[15] & 0xff) << 8) & 0xffff) ;
        BlePreferenceUtil.put(mContext , ThroughputConstants.REAL_TEST_DATA_PATTERN , testDataPattern + "");
        BlePreferenceUtil.put(mContext , ThroughputConstants.REAL_LASTING_TIME_SECOND , lastingTimeSecond + "");
        if (type == 0){
            BlePreferenceUtil.put(mContext , ThroughputConstants.REAL_SPP_DATA_SIZE , dataSize);
        } else if (type == 1) {
            Log.i(TAG, "parseInitConfig: ble" + dataSize);
            BlePreferenceUtil.put(mContext , ThroughputConstants.REAL_BLE_DATA_SIZE , dataSize);
        }
        BlePreferenceUtil.put(mContext , ThroughputConstants.REAL_TRAN_DRIECTION , direction + "");
        BlePreferenceUtil.put(mContext , ThroughputConstants.REAL_IS_WITH_RESPONSE , isWithRepsone + "");
        BlePreferenceUtil.put(mContext , ThroughputConstants.REAL_IS_USE_SPECIFIC_BLE_CONNECTION , isUseSpecificConnectionParam + "");
        BlePreferenceUtil.put(mContext , ThroughputConstants.REAL_MIN_CONNECTION_INTERVAL_IN_MS , minIntervalInMs );
        BlePreferenceUtil.put(mContext , ThroughputConstants.REAL_MAX_CONNECTION_INTERVAL_IN_MS , maxIntervalInMs );
    }

    private static byte[] getInitConfig(Context context, int status) {
        int  testDataPattern = Integer.valueOf((String) BlePreferenceUtil.get(context, ThroughputConstants.KEY_TEST_DATA_PATTERN , ThroughputConstants.THROUGH_PUT_PACKAGE_PATTERN[0] + ""));
        int lastingTimeSecond = Integer.valueOf((String) BlePreferenceUtil.get(context , ThroughputConstants.KEY_LASTING_TIME_SECOND , "10" ));
        int  dataSize = 0;
        if (status == 0){
            dataSize =  Integer.valueOf((String)  BlePreferenceUtil.get(context, ThroughputConstants.KEY_SPP_DATA_SIZE ,ThroughputConstants.SPP_MAX_DATA_SIZE + "" ));
        } else if (status == 1) {
            dataSize =  Integer.valueOf((String)  BlePreferenceUtil.get(context, ThroughputConstants.KEY_BLE_DATA_SIZE ,ThroughputConstants.BLE_MAX_DATA_SIZE + "" ));
        }
        int  direction =  Integer.valueOf((String)  BlePreferenceUtil.get(context , ThroughputConstants.KEY_TRAN_DRIECTION , "0"));
        int  isWithRepsone =  Integer.valueOf((String)  BlePreferenceUtil.get(context , ThroughputConstants.KEY_IS_WITH_RESPONSE , "0" ));
        int  isUseSpecificConnectionParam =  Integer.valueOf((String)BlePreferenceUtil.get(context , ThroughputConstants.KEY_IS_USE_SPECIFIC_BLE_CONNECTION , "1" ));
        int  minIntervalInMs = Integer.valueOf((String)BlePreferenceUtil.get(context , ThroughputConstants.KEY_MIN_CONNECTION_INTERVAL_IN_MS , "10" ));
        int  maxIntervalInMs = Integer.valueOf((String) BlePreferenceUtil.get(context , ThroughputConstants.KEY_MAX_CONNECTION_INTERVAL_IN_MS , "20" ));
        ByteBuffer byteBuffer = ByteBuffer.allocate(16);
        byteBuffer.put((byte)(testDataPattern & 0xff));
        byteBuffer.put((byte)(lastingTimeSecond & 0xff));
        byteBuffer.put((byte)((lastingTimeSecond >> 8) & 0xff));
        byteBuffer.put((byte)(dataSize & 0xff));
        byteBuffer.put((byte)((dataSize >> 8) & 0xff));
        byteBuffer.put((byte)(direction & 0xff));
        byteBuffer.put((byte)(isWithRepsone & 0xff));
        byteBuffer.put((byte)(isUseSpecificConnectionParam & 0xff));
        byteBuffer.put((byte)(minIntervalInMs & 0xff));
        byteBuffer.put((byte)((minIntervalInMs >> 8 ) & 0xff));
        byteBuffer.put((byte)(maxIntervalInMs & 0xff));
        byteBuffer.put((byte)((maxIntervalInMs >> 8 ) & 0xff));
        return  byteBuffer.array();
    }

//    public static void setThroughDataSize(int size){
//        throughDataSize = size ;
//    }
//
//    static synchronized public byte[][] checkDataEnoughAndRetArray(byte[] data){
//        List<byte[]> mArrays = new ArrayList<byte[]>();
//        if(data == null || data.length == 0){
//            return  null ;
//        }
//        waitMoreDatas = appendData(data);
//        if(waitMoreDatas != null && waitMoreDatas.length > 0) {
//            LOG("AFTER APPEND DATA = "+ ArrayUtil.toHex(waitMoreDatas));
//        }
//        boolean isOut = false ;    //退出迭代
//        do {
//            byte[] temp = checkDataEnoughAndRet(waitMoreDatas);
//            if(temp != null){
//                LOG("--------- loop temp != null so continue");
//                mArrays.add(temp);
//            } else {
//                LOG("--------- loop temp == null ready go out");
//                isOut = true ;
//            }
//        } while (!isOut);
//        if(mArrays != null && mArrays.size() > 0 ) {
//            LOG("--------- loop check total size is "+ mArrays.size());
//            byte[][] datas = new byte[mArrays.size()][];
//            for (int i = 0 ; i < mArrays.size() ; i++) {
//                datas[i] = mArrays.get(i);
//            }
//            return datas ;
//        } else {
//            LOG("--------- loop check total size is zero");
//        }
//        return null ;
//    }
//
//    static synchronized private byte[] checkDataEnoughAndRet(byte[] data) {
//        if (isEnoughDataToCheck(waitMoreDatas)) {
//            LOG( ">> EnoughDataToCheck");
//            boolean isVoiceData = isVoiceData(waitMoreDatas);
//            if (isVoiceData) {
//                boolean isEnoughPerOpusData = isEnoughPerOpusData(waitMoreDatas);
//                if (isEnoughPerOpusData) {
//                    LOG("isVoiceData AND EnoughPerOpusData");
//                    return  getOpusDatas(waitMoreDatas);
//                } else {
//                    LOG("isVoiceData BUT NEED WAIT MORE DATA");
//                }
//            } else {
//                boolean isEnoughCmdAndRight = isEnoughCmdAndRight(waitMoreDatas);
//                if (isEnoughCmdAndRight) {
//                    LOG("isCmdData AND EnoughCmdAndRight");
//                    return  getCmdData(waitMoreDatas);
//                } else {
//                    LOG("isCmdData BUT NEED WAIT MORE DATA");
//                }
//            }
//        } else {
//            LOG("is not enough data  to check ");
//        }
//        return  null ;
//    }
//
//    private static byte[] appendData(byte[] data) {
//        if (waitMoreDatas != null) {
//            byte[] dstData = new byte[waitMoreDatas.length + data.length];
//            System.arraycopy(waitMoreDatas , 0 , dstData , 0 , waitMoreDatas.length);
//            System.arraycopy(data , 0 , dstData , waitMoreDatas.length , data.length);
//            return dstData ;
//        } else {
//            return data ;
//        }
//    }
//
//    /**
//     * 只有长度大于一个时候才能进行数据有效判断，如果长度少于等于一个，那么则等待更多数据
//     * @param datas
//     * @return
//     */
//    private static boolean isEnoughDataToCheck(byte[] datas) {
//        if (datas != null && datas.length > 1) {
//            return  true ;
//        }
//        return  false ;
//    }
//
//    /**
//     * 判断是否为语音流数据,语音流的协议其实为0xff 0xff
//     * @param data
//     * @return
//     */
//    private static boolean isVoiceData(byte[] data) {
//        if (data != null && data.length > 1 && (data[0]&0xff) == 0XFF && (data[1]&0xff)==0xff ) {
//            return true ;
//        }
//        return  false ;
//    }
//    /**
//     * 判断是否满足语音流
//     * @param datas
//     * @return
//     */
//    private static boolean isEnoughPerOpusData(byte[] datas) {
//        if (datas != null && datas.length > 1 && (datas[0]&0xff) == 0XFF && (datas[1]&0xff)==0xff ) {
//            return true ;
//        }
//        return  false ;
//    }
//
//    private static byte[] getOpusDatas(byte[] datas) {
//        if (datas != null && datas.length > 1 && (datas[0]&0xff) == 0XFF && (datas[1]&0xff)==0xff) {
//            waitMoreDatas = null ;
//            return null ;
//        }
//        LOG("getOpusDatas but ret null ");
//        return  null ;
//    }
//
//    private static boolean isEnoughCmdAndRight(byte[] datas){
//        if (datas == null && datas.length <= 1) {
//            return false;
//        }
//        if ((datas[0]&0xff) == 0x07 && datas.length >= 0) {
//            return true ;
//        } else if ((datas[0]&0xff) == 0x04 && datas.length >= 5) {
//            return true ;
//        } else if ((datas[0]&0xff) == 0x05 && datas.length >= 0) {
//            return true ;
//        } else if ((datas[0]&0xff) == 0x01 && datas.length >= 0) {
//            return  true ;
//        } else if ((datas[0]&0xff) == 0x03 && datas.length >= 0) {
//            return  true ;
//        } else if ((datas[0]&0xff) == 0x00 && datas.length >= 0) {
//            return  true ;
//        } else if ((datas[0]&0xff) == 0x09 && datas.length >= 20) {
//            return  true ;
//        } else if ((datas[0]&0xff) == 0x0a && datas.length >= throughDataSize) {
//            return  true ;
//        } else if ((datas[0]&0xff) == 0x0b && datas.length >= CmdInfo.HEAD_LEN) {
//            return  true ;
//        } else if ((datas[0]&0xff) == 0x0C && datas.length >= CmdInfo.HEAD_LEN) {
//            return  true ;
//        }
//        return false ;
//    }
//
//    private static byte[] getCmdData(byte[] datas) {
//        int len = 0 ;
//        if ((datas[0]&0xff) == 0x07 && datas.length >= 5) { //07,80,01,00,01,
//            LOG("getCmdData start byte is 0x07");
//            len = 5  ;
//        } else if ((datas[0]&0xff) == 0x04 && datas.length >= 5) { //04,80,01,00,01,  准备开始数据流标志
//            LOG("getCmdData start byte is 0x04");
//            len = 5  ;
//        } else if ((datas[0]&0xff) == 0x05 && datas.length >= 4) {//05,80,00,00  准备结束数据流标志
//            LOG("getCmdData  start byte is 0x05");
//            len = 4  ;
//        } else if ((datas[0]&0xff) == 0x01 && datas.length >= 12) {// 01,80,08,00,00,00,00,00,00,00,00,00,  开始数据流标志
//            LOG("getCmdData  start byte is 0x01");
//            len = 12  ;
//        } else if ((datas[0]&0xff) == 0x03 && datas.length >= 16) {//03,80,0c,00,00,00,00,00,00,00,00,00,00,00,00,00,  结束数据流标志
//            LOG("getCmdData  start byte is 0x03");
//            len = 16  ;
//        } else if ((datas[0]&0xff) == 0x00 && datas.length >= 10) {
//            //00,80,07,00,04,80,00,00,01,00,02,
//            //00,80,06,00,05,80,00,00,00,00,
//            LOG("getCmdData  start byte is 0x00");
//            if (datas.length > 3) {
//                if ((datas[2]&0xff) == 0x07) {
//                    len = 11 ;
//                } else if ((datas[2]&0xff) == 0x06) {
//                    len = 10 ;
//                }
//            } else {
//                return  null;
//            }
//        } else if ((datas[0]&0xff) == 0x09 && datas.length >= 20) {
//            len =  20 ;
//        } else if ((datas[0]&0xff) == 0x0a && datas.length >= throughDataSize) {
//            len =   throughDataSize ;
//        } else if ((datas[0]&0xff) == 0x0b && datas.length >= CmdInfo.HEAD_LEN) {
//            len =   CmdInfo.HEAD_LEN ;
//        } else if ((datas[0]&0xff) == 0x0C && datas.length >= CmdInfo.HEAD_LEN) {
//            len =   CmdInfo.HEAD_LEN ;
//        } else {
//            LOG("getCmdData and i donot known why ret null \n "+ ArrayUtil.toHex(datas));
//            return  null ;
//        }
//        return  getDataAndLeave(datas , len) ;
//    }
//
//    private static byte[] getDataAndLeave(byte[] datas, int len) {
//        if (datas == null || datas.length < len ) {
//            LOG("getDataAndLeave datas == null || datas.length < len len = "+ len);
//            waitMoreDatas = null ;
//            return  null ;
//        }
//        byte[] dstDatas = new byte[len];
//        System.arraycopy(datas , 0 , dstDatas , 0 , len);
//        if (datas.length == len) {
//            LOG("getDataAndLeave and waitMoreDatas is clear");
//            waitMoreDatas = null ;
//        } else {
//            int leaveDataLen = datas.length  - len ;
//            byte[] leaveData = new byte[leaveDataLen];
//            System.arraycopy(datas ,len , leaveData , 0 , leaveDataLen  );
//            waitMoreDatas = leaveData ;
//            LOG("getDataAndLeave and waitMoreDatas has leave some datas \n"+ ArrayUtil.toHex(waitMoreDatas));
//        }
//        return  dstDatas ;
//    }
//
//    private static void LOG(String msg) {
//        if (msg != null) {
//            LogUtils.e(TAG , msg);
//        }
//
//    }
}