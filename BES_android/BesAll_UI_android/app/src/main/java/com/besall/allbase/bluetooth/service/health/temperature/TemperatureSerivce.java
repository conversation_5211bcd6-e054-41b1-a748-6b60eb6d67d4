package com.besall.allbase.bluetooth.service.health.temperature;

import android.content.Context;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.service.base.BesBaseService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;

public class TemperatureSerivce extends BesBaseService {


    public TemperatureSerivce(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        super(serviceConfig, listener, context);
        startConnect(serviceConfig);
    }

    public void OPHealth(){
        if (totauccess) {
            sendData(TemperatureCMD.HealthInfo(mContext, 0), 0);
        } else {
            callBackErrorMessage(BesSdkConstants.BES_TOTA_ERROR);
        }
    }
}
