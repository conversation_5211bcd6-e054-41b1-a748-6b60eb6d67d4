package com.besall.allbase.view.activity.chipstoollevel4.customercmd;

import android.content.Context;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.besall.allbase.view.activity.chipstoollevel4.logdump.LogDumpActivity;


/**
 * <AUTHOR>
 * @time $ $
 */
interface ICustomCmdPresenter {
    void pickDecice(CustomCmdActivity context, int scan);

    void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context);

    void sendCustomCmd(String cmd, int i);

    void sendCustomCommand(byte[] data);

    void stopSpp();

    void importfile(CustomCmdActivity context, int json);

    void selectfile(CustomCmdActivity context, int file);
}
