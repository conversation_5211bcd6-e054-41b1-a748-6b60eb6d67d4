package com.besall.allbase.view.activity.tools.confirmdialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.EditText;

import com.besall.allbase.common.utils.ResourceUtil;

public class BisCodeDialog extends Dialog {
    private Context context;
    private Button cancel, confirm;
    private EditText bis_code_psw;
    private BisCodeDialoglistener bisCodeDialoglistener;
    private String content;


    public BisCodeDialog(Context context, String content, BisCodeDialoglistener
            listener) {
        super(context);
        this.context = context;
        this.content = content;
        this.bisCodeDialoglistener = listener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        getWindow().requestFeature(Window.FEATURE_NO_TITLE);
        LayoutInflater inflater = (LayoutInflater) context
                .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View layout = inflater.inflate(ResourceUtil.getLayoutId(context,
                "bis_code_dialog"), null);
        setContentView(layout);
        initview();
    }

    private void initview() {
        cancel = findViewById(ResourceUtil.getId(context, "dialog_cancel"));
        confirm = findViewById(ResourceUtil.getId(context, "dialog_confirm"));;
        bis_code_psw = findViewById(ResourceUtil.getId(context, "bis_code_psw"));
        bis_code_psw.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (start == 0) {
                    return;
                }
                String text = s.toString();
                if (text.length() > 16) {
                    text = text.substring(0, 16);
                    bis_code_psw.setText(text);
                }
//                String text = s.toString().replace(" ", "");
//                if (text.length() > (16 * 2)) {
//                    text = text.substring(0, 16 * 2);
//                }
//                //"11"
//                String lastStr = "";
//                for (int i = 0; i < (text.length() / 2 + (text.length() % 2 == 0 ? 0 : 1)); i ++) {
//                    lastStr = lastStr + (lastStr.length() == 0 ? "" : " ") + text.substring(i * 2, i * 2 + ((i * 2 + 2) > text.length() ? 1 : 2));
//                }
//                bis_code_psw.setText(lastStr);
//                bis_code_psw.setSelection(lastStr.length());
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
        cancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                bisCodeDialoglistener.confirmNo();
                BisCodeDialog.this.dismiss();
            }
        });
        confirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (bis_code_psw.getText().toString().length() > 0) {
                    bisCodeDialoglistener.confirmYesWithText(BisCodeDialog.this, bis_code_psw.getText().toString());
                }
            }
        });
    }


}
