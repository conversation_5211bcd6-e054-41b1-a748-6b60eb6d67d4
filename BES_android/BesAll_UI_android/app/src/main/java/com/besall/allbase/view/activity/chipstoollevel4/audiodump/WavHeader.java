package com.besall.allbase.view.activity.chipstoollevel4.audiodump;

import com.bes.bessdk.utils.ArrayUtil;

public class WavHeader {
    final String riffChunkId = "RIFF";
    int riffChunkSize;
    final String riffType = "WAVE";

    /**
     * FORMAT 数据块
     */
    final String formatChunkId = "fmt ";
    final int formatChunkSize = 16;
    final short audioFormat = 1;
    int channels;
    int sampleRate;
    int byteRate;
    short blockAlign;
    int sampleBits;

    /**
     * FORMAT 数据块
     */
    final String dataChunkId = "data";
    int dataChunkSize;

    WavHeader(int totalAudioLen, int sampleRate, int channels, int sampleBits) {
        this.riffChunkSize = totalAudioLen;
        this.channels = channels;
        this.sampleRate = sampleRate;
        this.byteRate = sampleRate * sampleBits / 8 * channels;
        this.blockAlign = (short) (channels * sampleBits / 8);
        this.sampleBits = sampleBits;
        this.dataChunkSize = totalAudioLen - 44;
    }

    public byte[] getHeader() {
        byte[] result;
        result = ArrayUtil.byteMerger(ArrayUtil.toBytes(ArrayUtil.str2HexStr(riffChunkId)), ArrayUtil.int2byte(riffChunkSize));
        result = ArrayUtil.byteMerger(result, ArrayUtil.toBytes(ArrayUtil.str2HexStr(riffType)));
        result = ArrayUtil.byteMerger(result, ArrayUtil.toBytes(ArrayUtil.str2HexStr(formatChunkId)));
        result = ArrayUtil.byteMerger(result, ArrayUtil.int2byte(formatChunkSize));
        result = ArrayUtil.byteMerger(result, ArrayUtil.intToBytes2(audioFormat));
        result = ArrayUtil.byteMerger(result, ArrayUtil.intToBytes2(channels));
        result = ArrayUtil.byteMerger(result, ArrayUtil.int2byte(sampleRate));
        result = ArrayUtil.byteMerger(result, ArrayUtil.int2byte(byteRate));
        result = ArrayUtil.byteMerger(result, ArrayUtil.intToBytes2(blockAlign));
        result = ArrayUtil.byteMerger(result, ArrayUtil.intToBytes2(sampleBits));
        result = ArrayUtil.byteMerger(result, ArrayUtil.toBytes(ArrayUtil.str2HexStr(dataChunkId)));
        result = ArrayUtil.byteMerger(result, ArrayUtil.int2byte(dataChunkSize));
        return result;
    }
}
