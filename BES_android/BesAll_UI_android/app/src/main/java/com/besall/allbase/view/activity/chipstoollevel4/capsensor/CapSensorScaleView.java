package com.besall.allbase.view.activity.chipstoollevel4.capsensor;

import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_CHART_COEFFICIENTS_DATA;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_CHART_COEFFICIENTS_DATA_KEY;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_CHART_MAXIMUM_DATA;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_CHART_MAXIMUM_DATA_KEY;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_CHART_PIECE_DATA;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_CHART_PIECE_DATA_KEY;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_CHART_USE_CUSTOM_DATA;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_CHART_USE_CUSTOM_DATA_KEY;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import com.bes.bessdk.utils.SPHelper;

public class CapSensorScaleView extends View {
    private Context mContext;

    public CapSensorScaleView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
    }


    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        int width = canvas.getWidth();
        int maximum = CAPSENSOR_CHART_MAXIMUM_DATA;
        int coefficients = CAPSENSOR_CHART_COEFFICIENTS_DATA;
        int piece = CAPSENSOR_CHART_PIECE_DATA;

        boolean useCustomData = (boolean) SPHelper.getPreference(mContext, CAPSENSOR_CHART_USE_CUSTOM_DATA_KEY, CAPSENSOR_CHART_USE_CUSTOM_DATA);
        if (useCustomData) {
            maximum = (int) SPHelper.getPreference(mContext, CAPSENSOR_CHART_MAXIMUM_DATA_KEY, CAPSENSOR_CHART_MAXIMUM_DATA);
            coefficients = (int) SPHelper.getPreference(mContext, CAPSENSOR_CHART_COEFFICIENTS_DATA_KEY, CAPSENSOR_CHART_COEFFICIENTS_DATA);
            piece = (int) SPHelper.getPreference(mContext, CAPSENSOR_CHART_PIECE_DATA_KEY, CAPSENSOR_CHART_PIECE_DATA);
        }
        Paint paint = new Paint();
        paint.setColor(Color.BLACK);
        paint.setStyle(Paint.Style.FILL);
        Path textPath = new Path();
        paint.setTextSize(30);
        paint.setAntiAlias(true);
        textPath.moveTo(width / 2 - 200, 25);
        textPath.lineTo(width / 2 + 200, 25);
        canvas.drawTextOnPath("capsensor data [*" + coefficients + "]", textPath, 0, 0, paint);

        float thresholdValue = maximum - piece * coefficients;
        paint.setTextSize(20);
        for (int i = 0; i < piece + 1; i ++) {
            Path valuePath = new Path();
            valuePath.moveTo(i * (width / piece) - 6, 30);
            valuePath.lineTo(i * (width / piece) - 6, 30 + 50);
            int curValue = (int)(thresholdValue + i * coefficients);
            canvas.drawTextOnPath(curValue + "", valuePath, 0, 0, paint);
        }
    }
}
