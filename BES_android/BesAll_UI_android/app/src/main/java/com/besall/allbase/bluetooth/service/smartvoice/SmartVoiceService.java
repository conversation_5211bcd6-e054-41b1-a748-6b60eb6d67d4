package com.besall.allbase.bluetooth.service.smartvoice;


import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_SUCCESS;

import android.content.Context;
import android.util.Log;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.service.base.BesBaseService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.message.BaseMessage;
import com.bes.sdk.utils.DeviceProtocol;

public class SmartVoiceService extends BesBaseService {

    public SmartVoiceService(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        super(serviceConfig, listener, context);

        startConnect(serviceConfig);
    }

    @Override
    public void onStatusChanged(HmDevice device, int status, DeviceProtocol protocol) {
        super.onStatusChanged(device, status, protocol);
        if (status == BES_CONNECT_SUCCESS) {
            callBackStateChangedMessage(BES_CONNECT_SUCCESS, "");
        } else if (getDeviceConnectState(mConfig) != BesSdkConstants.BesConnectState.BES_CONNECT) {
            callBackErrorMessage(status);
        }
    }

    @Override
    public void onDataReceived(BaseMessage deviceMessage) {
        super.onDataReceived(deviceMessage);
        if (mConfig.getTotaConnect() && !totauccess) {
            return;
        }

    }



    private void addLog(String TAG, String str) {
        Log.i(TAG, str);
    }
}
