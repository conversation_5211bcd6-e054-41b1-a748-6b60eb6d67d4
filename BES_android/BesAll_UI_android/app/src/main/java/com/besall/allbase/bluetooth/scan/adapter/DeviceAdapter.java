package com.besall.allbase.bluetooth.scan.adapter;

import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.UiThread;

import com.bes.bessdk.connect.BTService;
import com.bes.bessdk.connect.BleConnector;
import com.bes.bessdk.scan.BtHeleper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.R;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by zhaowanxing on 2017/4/16.
 */

public class DeviceAdapter extends BaseAdapter implements View.OnClickListener {

    private List<HmDevice> mHmDevices;
    private List<Integer> mRssis;
    private List<Boolean> mSelects;


    private Context mContext;
    private int connectDevNum = 0;
    private boolean isMultipleDevices = false;
    private DeviceProtocol mDeviceProtocol;

    public DeviceAdapter(Context context, DeviceProtocol deviceProtocol) {
        mContext = context;
        mDeviceProtocol = deviceProtocol;
        mHmDevices = new ArrayList<>();
        mRssis = new ArrayList<>();
        mSelects = new ArrayList<>();
    }

    @Override
    public int getCount() {
        return mHmDevices.size();
    }

    @Override
    public HmDevice getItem(int position) {
        return mHmDevices.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        if (convertView == null) {
            convertView = LayoutInflater.from(mContext).inflate(R.layout.device_item, parent, false);
            holder = new ViewHolder();
            holder.mSelect = (ImageView) convertView.findViewById(R.id.select_image);
            holder.mName = (TextView) convertView.findViewById(R.id.name);
            holder.mAddress = (TextView) convertView.findViewById(R.id.address);
            holder.mRssiIcon = (ImageView) convertView.findViewById(R.id.rssi_icon);
            holder.mRssiValue = (TextView) convertView.findViewById(R.id.rssi_value);
            holder.disconnect_btn = (Button) convertView.findViewById(R.id.disconnect_btn);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }
        if (isMultipleDevices) {
            holder.mSelect.setVisibility(View.VISIBLE);
        } else {
            holder.mSelect.setVisibility(View.GONE);
        }
        if (mDeviceProtocol == DeviceProtocol.PROTOCOL_USB) {
            HmDevice hmDevice = mHmDevices.get(position);
            holder.mName.setText(hmDevice.getDeviceName());
            holder.mAddress.setText("ProductId:" + hmDevice.getDevicePid() + "    " + "VendorId:" + hmDevice.getDeviceVid());
            return convertView;
        }
        holder.mSelect.setImageDrawable(mSelects.get(position) == true ? mContext.getDrawable(R.drawable.ota_top_sele) : mContext.getDrawable(R.drawable.ota_top_nor));
        HmDevice hmDevice = mHmDevices.get(position);
        BluetoothDevice device = BtHeleper.getBluetoothAdapter(mContext).getRemoteDevice(hmDevice.getPreferredProtocol() == DeviceProtocol.PROTOCOL_BLE ? hmDevice.getBleAddress() : hmDevice.getDeviceMAC());
        String name = device.getName();
        if (TextUtils.isEmpty(name)) {
            name = device.getAddress();
        }
        holder.mName.setText(name);
        holder.mName.setTextColor(holder.mAddress.getTextColors().getDefaultColor());
        holder.mAddress.setText(device.getAddress());
        int rssi = mRssis.get(position);
        holder.mRssiIcon.setImageResource(getRssiLevel(rssi));
        holder.mRssiValue.setText(String.valueOf(rssi));

        holder.mRssiIcon.setVisibility(View.VISIBLE);
        holder.mRssiValue.setVisibility(View.VISIBLE);
        holder.disconnect_btn.setVisibility(View.GONE);
        if (rssi == 1000) {
            holder.mName.setTextColor(mContext.getResources().getColor(R.color.green));
            holder.mRssiIcon.setVisibility(View.GONE);
            holder.mRssiValue.setVisibility(View.GONE);
            holder.disconnect_btn.setVisibility(View.VISIBLE);
            holder.disconnect_btn.setOnClickListener(this);
            holder.disconnect_btn.setTag(position);
        }

        return convertView;
    }

    @UiThread
    public void addBefore(HmDevice device, int rssi) {
        synchronized (mHmDevices) {
            mHmDevices.add(device);
            mSelects.add(false);
            mRssis.add(rssi);
            notifyDataSetChanged();
        }
        connectDevNum = mHmDevices.size();
    }

    @UiThread
    public void add(HmDevice hmDevice, int rssi) {
        synchronized (mHmDevices) {
            if (mDeviceProtocol == DeviceProtocol.PROTOCOL_USB) {
                mHmDevices.add(hmDevice);
                return;
            }
            BluetoothDevice device = BtHeleper.getBluetoothAdapter(mContext).getRemoteDevice(hmDevice.getPreferredProtocol() == DeviceProtocol.PROTOCOL_BLE ? hmDevice.getBleAddress() : hmDevice.getDeviceMAC());
            int index = - 1;
            for (int i = 0; i < mHmDevices.size(); i ++) {
                if (i < connectDevNum) {
                    if (device.getAddress().equals(mHmDevices.get(i).getPreferredProtocol() == DeviceProtocol.PROTOCOL_BLE ? mHmDevices.get(i).getBleAddress() : mHmDevices.get(i).getDeviceMAC())) {
                        return;
                    }
                } else {
                    if (device.getAddress().equals(mHmDevices.get(i).getPreferredProtocol() == DeviceProtocol.PROTOCOL_BLE ? mHmDevices.get(i).getBleAddress() : mHmDevices.get(i).getDeviceMAC())) {
                        index = i;
                    }
                }
            }
            if (index < 0) {
                mHmDevices.add(hmDevice);
                mSelects.add(false);
                mRssis.add(rssi);
                notifyDataSetChanged();
            } else {
                if (!TextUtils.isEmpty(device.getName())) {
                    mHmDevices.set(index, hmDevice);
                    notifyDataSetChanged();
                }
                mRssis.add(index, rssi);
            }
        }
    }

    @UiThread
    public void addSelectState(int index) {
        mSelects.set(index, !mSelects.get(index));
        notifyDataSetChanged();
    }

    public void clear() {
        synchronized (mHmDevices) {
            mHmDevices.clear();
            mRssis.clear();
            mSelects.clear();
            notifyDataSetChanged();
        }
    }

    public void setIsMultipleDevices(boolean isMultipleDev) {
        isMultipleDevices = isMultipleDev;
    }

    public List<HmDevice> getSelectedDevices() {
        List<HmDevice> curHmDevices = new ArrayList();
        for (int i = 0; i < mHmDevices.size(); i ++) {
            if (mSelects.get(i)) {
                if (curHmDevices.size() < 10) {
                    curHmDevices.add(mHmDevices.get(i));
                }
            }
        }
        return curHmDevices;
    }

    private int getRssiLevel(int rssi) {
        if (rssi < -90) {
            return R.mipmap.signal_level0;
        }
        if (rssi < -80) {
            return R.mipmap.signal_level1;
        }
        if (rssi < -70) {
            return R.mipmap.signal_level2;
        }
        if (rssi < -60) {
            return R.mipmap.signal_level3;
        }
        if (rssi < -50) {
            return R.mipmap.signal_level4;
        }
        return R.mipmap.signal_level5;
    }

    @Override
    public void onClick(View v) {
        int position = (Integer) v.getTag();
        HmDevice hmDevice = mHmDevices.get(position);
        BTService.disconnect(hmDevice);
    }

    private class ViewHolder {
        ImageView mSelect;
        TextView mName;
        TextView mAddress;
        ImageView mRssiIcon;
        TextView mRssiValue;
        Button disconnect_btn;
    }
}
