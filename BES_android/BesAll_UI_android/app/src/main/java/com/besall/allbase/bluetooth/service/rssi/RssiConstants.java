package com.besall.allbase.bluetooth.service.rssi;

public class RssiConstants {

    //cmd
    public static final short         NEW_OP_TOTA_RSSI_READ_CMD = (short) 0x6306;
    public static final short             OP_TOTA_RSSI_READ_CMD = (short) 0x6309;
    public static final short         OLD_OP_TOTA_RSSI_READ_CMD = (short) 0x900b;



    //msg id
    public static final int                          RSSI_START = 0x00001000;



    public static final String                 RSSI_SAVE_FOLDER = "RssiLog";
    public static final String                    RSSI_PROTOCOL = "RSSI PROTOCOL";
    public final static boolean         BES_RSSI_PROTOCOL_VALUE = true;


}
