package com.besall.allbase.view.activity;


import static androidx.test.espresso.Espresso.onView;
import static androidx.test.espresso.action.ViewActions.clearText;
import static androidx.test.espresso.action.ViewActions.click;
import static androidx.test.espresso.action.ViewActions.typeText;
import static androidx.test.espresso.matcher.ViewMatchers.withId;
import static androidx.test.uiautomator.Until.findObject;

import androidx.test.espresso.ViewInteraction;
import androidx.test.ext.junit.rules.ActivityScenarioRule;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.uiautomator.By;
import androidx.test.uiautomator.UiObject2;

import com.besall.allbase.R;

import org.junit.Assert;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(AndroidJUnit4.class)
public class WIFTTEST {

    @Rule
    public ActivityScenarioRule<TestActivity> testRule = new ActivityScenarioRule<>(TestActivity.class);


    @Test
    public void EditTest() throws InterruptedException{
        onView(withId(R.id.wlan_name)).perform(clearText());
        Thread.sleep(1000);
        onView(withId(R.id.wlan_name)).perform(typeText("Android_a4b3"));
        Thread.sleep(1000);
        onView(withId(R.id.test_btn)).perform(click());
        Thread.sleep(5000);
    }


}
