// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 50;
	objects = {

/* Begin PBXBuildFile section */
		7C0583AF2C928E3A002D2AF4 /* oneplusone.txt in Resources */ = {isa = PBXBuildFile; fileRef = 7C0583AE2C928E3A002D2AF4 /* oneplusone.txt */; };
		7C1761E4272BD01500F1AF99 /* FunctionChooseViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C1761E3272BD01500F1AF99 /* FunctionChooseViewController.m */; };
		7C1761E7272BD48E00F1AF99 /* FunctionToolsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C1761E6272BD48E00F1AF99 /* FunctionToolsViewController.m */; };
		7C1761EB272BD71500F1AF99 /* EQViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C1761EA272BD71500F1AF99 /* EQViewController.m */; };
		7C1761EF272BEB7D00F1AF99 /* EQTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C1761EE272BEB7D00F1AF99 /* EQTableViewCell.m */; };
		7C19011029E13E3800B4135C /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7C19010F29E13E3800B4135C /* AVFoundation.framework */; };
		7C1F57FB29E0531A00220EA2 /* speaker_test_video.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 7C1F57FA29E0531A00220EA2 /* speaker_test_video.mp3 */; };
		7C3A6F4D26738FDA0075ED04 /* HomeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C3A6F4C26738FDA0075ED04 /* HomeViewController.m */; };
		7C3AB0BF2A57A91400B1B6C6 /* SmartVoiceViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C3AB0BE2A57A91400B1B6C6 /* SmartVoiceViewController.m */; };
		7C3AB0C32A57AA9100B1B6C6 /* SmartVoiceService.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C3AB0C22A57AA9100B1B6C6 /* SmartVoiceService.m */; };
		7C3AB0C62A57AAA200B1B6C6 /* SmartVoiceCMD.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C3AB0C52A57AAA200B1B6C6 /* SmartVoiceCMD.m */; };
		7C3AB0C92A57AABD00B1B6C6 /* SmartVoiceConstants.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C3AB0C82A57AABD00B1B6C6 /* SmartVoiceConstants.m */; };
		7C3ACC3E2678CDDF001CCDB0 /* DeviceView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C3ACC3D2678CDDF001CCDB0 /* DeviceView.m */; };
		7C3ACC442679D22C001CCDB0 /* BesButton.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C3ACC432679D22C001CCDB0 /* BesButton.m */; };
		7C3ACC4A267AFA91001CCDB0 /* BaseOptionView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C3ACC49267AFA91001CCDB0 /* BaseOptionView.m */; };
		7C3F7405267B771E005F5E65 /* SourceHanSansCN-Normal.otf in Resources */ = {isa = PBXBuildFile; fileRef = 7C3F7404267B771E005F5E65 /* SourceHanSansCN-Normal.otf */; };
		7C3F7415267B7C2C005F5E65 /* BesFont.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C3F7414267B7C2C005F5E65 /* BesFont.m */; };
		7C3F741B267B7F5F005F5E65 /* VersionPathView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C3F741A267B7F5F005F5E65 /* VersionPathView.m */; };
		7C3F7420267C4A12005F5E65 /* SourceHanSansCN-Bold.otf in Resources */ = {isa = PBXBuildFile; fileRef = 7C3F741F267C4A12005F5E65 /* SourceHanSansCN-Bold.otf */; };
		7C3F742C2681B50C005F5E65 /* BesAlert.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C3F742B2681B50C005F5E65 /* BesAlert.m */; };
		7C3F74352681C083005F5E65 /* CircleAnimationView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C3F74342681C083005F5E65 /* CircleAnimationView.m */; };
		7C4039F629A60FB200C24BD8 /* CustomerDialViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C4039F529A60FB200C24BD8 /* CustomerDialViewController.m */; };
		7C4039FA29A7080600C24BD8 /* CustomerDialService.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C4039F929A7080600C24BD8 /* CustomerDialService.m */; };
		7C4039FD29A7086600C24BD8 /* CustomerDialCMD.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C4039FC29A7086600C24BD8 /* CustomerDialCMD.m */; };
		7C403A0129A85B5100C24BD8 /* MakeDialViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C403A0029A85B5100C24BD8 /* MakeDialViewController.m */; };
		7C403A0729A8A00800C24BD8 /* MakeDialShowView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C403A0629A8A00800C24BD8 /* MakeDialShowView.m */; };
		7C4331CE29A8B58700686CF8 /* MakeDialCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C4331CD29A8B58700686CF8 /* MakeDialCell.m */; };
		7C4331D129A8EE8100686CF8 /* CustomerDialBean.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C4331D029A8EE8100686CF8 /* CustomerDialBean.m */; };
		7C4331D429AC7E6D00686CF8 /* CropPhotoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C4331D329AC7E6D00686CF8 /* CropPhotoViewController.m */; };
		7C4331D729ACA10E00686CF8 /* ShadeView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C4331D629ACA10E00686CF8 /* ShadeView.m */; };
		7C4331DA29ADA20800686CF8 /* UIImage+fixOrientation.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C4331D929ADA20800686CF8 /* UIImage+fixOrientation.m */; };
		7C441E9725ECCA7000E5FB4D /* AppSettingViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C441E9625ECCA7000E5FB4D /* AppSettingViewController.m */; };
		7C4CA3A92689B3700071B474 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 7C4CA3AB2689B3700071B474 /* Localizable.strings */; };
		7C4CA6DB2672090700F8BE9F /* BesBaseViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C4CA6DA2672090700F8BE9F /* BesBaseViewController.m */; };
		7C4CA6E9267333EA00F8BE9F /* BesColor.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C4CA6E8267333EA00F8BE9F /* BesColor.m */; };
		7C5EECB82B81D63700342F55 /* libopus.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 7C7C2BBB2AEA59F200C49657 /* libopus.a */; };
		7C632ED528B7A9FC002203BF /* VersionPathViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C632ED428B7A9FC002203BF /* VersionPathViewCell.m */; };
		7C673F0E2996270A00C310A2 /* AvsLwaCMD.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C673F0D2996270A00C310A2 /* AvsLwaCMD.m */; };
		7C673F112996271C00C310A2 /* AvsLwaService.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C673F102996271C00C310A2 /* AvsLwaService.m */; };
		7C673F142996273700C310A2 /* AvsLwaConstants.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C673F132996273700C310A2 /* AvsLwaConstants.m */; };
		7C673F172996682D00C310A2 /* AvsGuideViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C673F162996682D00C310A2 /* AvsGuideViewController.m */; };
		7C673F1A29966A1700C310A2 /* AvsLoginViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C673F1929966A1700C310A2 /* AvsLoginViewController.m */; };
		7C673F1D29966A6200C310A2 /* AvsSelectLanguageController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C673F1C29966A6200C310A2 /* AvsSelectLanguageController.m */; };
		7C673F2029966A9600C310A2 /* AvsResizeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C673F1F29966A9600C310A2 /* AvsResizeViewController.m */; };
		7C673F23299A335D00C310A2 /* LoginWithAmazon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7C673F22299A335D00C310A2 /* LoginWithAmazon.framework */; };
		7C673F26299A33A300C310A2 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7C673F25299A33A300C310A2 /* Security.framework */; };
		7C673F28299A33B000C310A2 /* SafariServices.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7C673F27299A33B000C310A2 /* SafariServices.framework */; };
		7C673F2B299B35BE00C310A2 /* AvsResizeView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C673F2A299B35BE00C310A2 /* AvsResizeView.m */; };
		7C673F2E299B363A00C310A2 /* AvsSettingView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C673F2D299B363A00C310A2 /* AvsSettingView.m */; };
		7C7C2BBF2AEA59F200C49657 /* OpusCodec.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C7C2BB82AEA59F200C49657 /* OpusCodec.m */; };
		7C7FD86C2A063AD9008E65C8 /* pngquant.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FD75D2A063AD8008E65C8 /* pngquant.c */; };
		7C7FD86D2A063AD9008E65C8 /* rwpng.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FD75E2A063AD8008E65C8 /* rwpng.c */; };
		7C7FD86E2A063AD9008E65C8 /* pngquant_opts.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FD75F2A063AD8008E65C8 /* pngquant_opts.c */; };
		7C7FDAB52A063D5E008E65C8 /* libimagequant.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FD9A72A063D5C008E65C8 /* libimagequant.c */; };
		7C7FDAB72A063D5E008E65C8 /* mediancut.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FD9AB2A063D5C008E65C8 /* mediancut.c */; };
		7C7FDAB82A063D5E008E65C8 /* mempool.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FD9AD2A063D5C008E65C8 /* mempool.c */; };
		7C7FDAB92A063D5E008E65C8 /* pam.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FD9AF2A063D5C008E65C8 /* pam.c */; };
		7C7FDABB2A063D5E008E65C8 /* blur.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FD9B32A063D5C008E65C8 /* blur.c */; };
		7C7FDABC2A063D5E008E65C8 /* remap.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FD9B42A063D5C008E65C8 /* remap.c */; };
		7C7FDABD2A063D5E008E65C8 /* kmeans.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FD9B62A063D5C008E65C8 /* kmeans.c */; };
		7C7FDABE2A063D5E008E65C8 /* nearest.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FD9B82A063D5C008E65C8 /* nearest.c */; };
		7C7FDAF72A063E1C008E65C8 /* pngtest.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FDAD82A063E1C008E65C8 /* pngtest.c */; };
		7C7FDAF82A063E1C008E65C8 /* pngrio.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FDAD92A063E1C008E65C8 /* pngrio.c */; };
		7C7FDAF92A063E1C008E65C8 /* pngerror.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FDADA2A063E1C008E65C8 /* pngerror.c */; };
		7C7FDAFA2A063E1C008E65C8 /* pngwio.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FDADB2A063E1C008E65C8 /* pngwio.c */; };
		7C7FDAFB2A063E1C008E65C8 /* pngtrans.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FDADD2A063E1C008E65C8 /* pngtrans.c */; };
		7C7FDAFC2A063E1C008E65C8 /* pngwrite.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FDAE02A063E1C008E65C8 /* pngwrite.c */; };
		7C7FDAFE2A063E1C008E65C8 /* pngwutil.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FDAE52A063E1C008E65C8 /* pngwutil.c */; };
		7C7FDAFF2A063E1C008E65C8 /* pngwtran.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FDAE62A063E1C008E65C8 /* pngwtran.c */; };
		7C7FDB002A063E1C008E65C8 /* pngpread.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FDAE92A063E1C008E65C8 /* pngpread.c */; };
		7C7FDB012A063E1C008E65C8 /* pngread.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FDAEB2A063E1C008E65C8 /* pngread.c */; };
		7C7FDB032A063E1C008E65C8 /* filter_neon_intrinsics.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FDAEE2A063E1C008E65C8 /* filter_neon_intrinsics.c */; };
		7C7FDB042A063E1C008E65C8 /* arm_init.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FDAEF2A063E1C008E65C8 /* arm_init.c */; };
		7C7FDB052A063E1C008E65C8 /* palette_neon_intrinsics.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FDAF02A063E1C008E65C8 /* palette_neon_intrinsics.c */; };
		7C7FDB062A063E1C008E65C8 /* pngmem.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FDAF12A063E1C008E65C8 /* pngmem.c */; };
		7C7FDB072A063E1C008E65C8 /* pngget.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FDAF22A063E1C008E65C8 /* pngget.c */; };
		7C7FDB082A063E1C008E65C8 /* png.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FDAF32A063E1C008E65C8 /* png.c */; };
		7C7FDB092A063E1C008E65C8 /* pngrtran.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FDAF42A063E1C008E65C8 /* pngrtran.c */; };
		7C7FDB0A2A063E1C008E65C8 /* pngrutil.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FDAF52A063E1C008E65C8 /* pngrutil.c */; };
		7C7FDB0B2A063E1C008E65C8 /* pngset.c in Sources */ = {isa = PBXBuildFile; fileRef = 7C7FDAF62A063E1C008E65C8 /* pngset.c */; };
		7C82E324286B2FCA00AA377C /* BesBTConnector.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C82E323286B2FCA00AA377C /* BesBTConnector.m */; };
		7C82E327286B300C00AA377C /* BesBaseService.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C82E326286B300C00AA377C /* BesBaseService.m */; };
		7C82E329286B306300AA377C /* BesOTAService.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C82E328286B306300AA377C /* BesOTAService.m */; };
		7C82E32B286B319500AA377C /* ScanViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C82E32A286B319500AA377C /* ScanViewController.m */; };
		7C8F5F2D29DABA0800C74862 /* CommandSetViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C8F5F2C29DABA0800C74862 /* CommandSetViewController.m */; };
		7C8F5F3029DABA1B00C74862 /* CheckCrcViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C8F5F2F29DABA1B00C74862 /* CheckCrcViewController.m */; };
		7C8F5F3529DEC2CB00C74862 /* CheckCrcService.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C8F5F3429DEC2CB00C74862 /* CheckCrcService.m */; };
		7C8F5F3829DEC2E300C74862 /* CheckCrcCMD.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C8F5F3729DEC2E300C74862 /* CheckCrcCMD.m */; };
		7C8F5F3B29DEC2FC00C74862 /* CheckCrcConstants.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C8F5F3A29DEC2FC00C74862 /* CheckCrcConstants.m */; };
		7C8F5F3E29DEC31C00C74862 /* CommandSetService.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C8F5F3D29DEC31C00C74862 /* CommandSetService.m */; };
		7C8F5F4129DEC33600C74862 /* CommandSetCMD.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C8F5F4029DEC33600C74862 /* CommandSetCMD.m */; };
		7C8F5F4429DEC39000C74862 /* CommandSetConstants.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C8F5F4329DEC39000C74862 /* CommandSetConstants.m */; };
		7C9246682AD7914900DBDBB8 /* CoreLocation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7C9246672AD7914900DBDBB8 /* CoreLocation.framework */; };
		7C94B5F42682072C0057F32A /* OtaingView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C94B5F32682072C0057F32A /* OtaingView.m */; };
		7C94B6002683540E0057F32A /* BesLoadingView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C94B5FF2683540E0057F32A /* BesLoadingView.m */; };
		7C94B606268472660057F32A /* AddFileTipsView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C94B605268472660057F32A /* AddFileTipsView.m */; };
		7C99241225C7FEF80006BD0A /* ArrayUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C99241125C7FEF80006BD0A /* ArrayUtil.m */; };
		7C99246C25CD4BFE0006BD0A /* NSData+XCYCoreTransfor.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C99246B25CD4BFE0006BD0A /* NSData+XCYCoreTransfor.m */; };
		7C99247F25D167290006BD0A /* BesOTASetting.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C99247725D167290006BD0A /* BesOTASetting.m */; };
		7C99248025D167290006BD0A /* BesOTACMD.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C99247825D167290006BD0A /* BesOTACMD.m */; };
		7C99248125D167290006BD0A /* BesOTAConstants.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C99247D25D167290006BD0A /* BesOTAConstants.m */; };
		7C9AFA5225BAC840001BF928 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C9AFA5125BAC840001BF928 /* AppDelegate.m */; };
		7C9AFA5525BAC840001BF928 /* SceneDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C9AFA5425BAC840001BF928 /* SceneDelegate.m */; };
		7C9AFA5B25BAC840001BF928 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 7C9AFA5925BAC840001BF928 /* Main.storyboard */; };
		7C9AFA5D25BAC841001BF928 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 7C9AFA5C25BAC841001BF928 /* Assets.xcassets */; };
		7C9AFA6025BAC841001BF928 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 7C9AFA5E25BAC841001BF928 /* LaunchScreen.storyboard */; };
		7C9AFA6325BAC841001BF928 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C9AFA6225BAC841001BF928 /* main.m */; };
		7C9AFA6D25BAC842001BF928 /* BesAllTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C9AFA6C25BAC842001BF928 /* BesAllTests.m */; };
		7C9AFA7825BAC842001BF928 /* BesAllUITests.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C9AFA7725BAC842001BF928 /* BesAllUITests.m */; };
		7C9AFAB225C2B3B5001BF928 /* UIView+Ext.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C9AFAB025C2B3B5001BF928 /* UIView+Ext.m */; };
		7C9AFBC825C43599001BF928 /* NSString+XCYCoreOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C9AFBC725C43599001BF928 /* NSString+XCYCoreOperation.m */; };
		7C9AFBD125C53778001BF928 /* BesServiceConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C9AFBCD25C53778001BF928 /* BesServiceConfig.m */; };
		7C9AFBDB25C5391F001BF928 /* BesUUID.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C9AFBDA25C5391F001BF928 /* BesUUID.m */; };
		7C9E3CA72AE3BBB7003129C9 /* BleWifiDemoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C9E3CA62AE3BBB7003129C9 /* BleWifiDemoViewController.m */; };
		7CABFD092685829F00A91310 /* CustomAlert.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CABFD082685829F00A91310 /* CustomAlert.m */; };
		7CABFD0E2685A88300A91310 /* SourceHanSansCN-Medium.otf in Resources */ = {isa = PBXBuildFile; fileRef = 7CABFD0D2685A88300A91310 /* SourceHanSansCN-Medium.otf */; };
		7CAC8BC42700979D0050D983 /* TOTAConnectCMD.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CAC8BC32700979D0050D983 /* TOTAConnectCMD.m */; };
		7CAC8BC82702F6660050D983 /* BesFunc.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CAC8BC72702F6660050D983 /* BesFunc.m */; };
		7CAC8BCB270300DF0050D983 /* Sha256.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CAC8BCA270300DF0050D983 /* Sha256.m */; };
		7CAC8BCE27030F180050D983 /* aes.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CAC8BCD27030F180050D983 /* aes.m */; };
		7CACE2FF2888F69000805A86 /* CustomCmdFileController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CACE2F92888F69000805A86 /* CustomCmdFileController.m */; };
		7CACE3002888F69000805A86 /* CustomHistoryCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CACE2FA2888F69000805A86 /* CustomHistoryCell.m */; };
		7CACE3012888F69000805A86 /* CustomHistoryList.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CACE2FB2888F69000805A86 /* CustomHistoryList.m */; };
		7CACE3022888F69000805A86 /* CustomCmdViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CACE2FD2888F69000805A86 /* CustomCmdViewController.m */; };
		7CACE3082888F72100805A86 /* BesCustomCmdConstants.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CACE3062888F72100805A86 /* BesCustomCmdConstants.m */; };
		7CACE3092888F72100805A86 /* BesCustomCmdCMD.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CACE3072888F72100805A86 /* BesCustomCmdCMD.m */; };
		7CC19825272FD3C3008E28C2 /* EQBeans.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CC19824272FD3C3008E28C2 /* EQBeans.m */; };
		7CC1982D27300E18008E28C2 /* BesEQCMD.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CC1982C27300E18008E28C2 /* BesEQCMD.m */; };
		7CC1983027300E3F008E28C2 /* BesEQConstants.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CC1982F27300E3F008E28C2 /* BesEQConstants.m */; };
		7CC1983327300E5D008E28C2 /* BesEQService.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CC1983227300E5D008E28C2 /* BesEQService.m */; };
		7CC1983627301164008E28C2 /* ConnectDeviceView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CC1983527301164008E28C2 /* ConnectDeviceView.m */; };
		7CD5426D25DFE49100310A01 /* OTAViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CD5426C25DFE49100310A01 /* OTAViewController.m */; };
		7CD5427325E0E31700310A01 /* FunctionOTAViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CD5427225E0E31700310A01 /* FunctionOTAViewController.m */; };
		7CD5427C25E37D3400310A01 /* ScanTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CD5427B25E37D3400310A01 /* ScanTableViewCell.m */; };
		7CD5428525E3961400310A01 /* OTAFileController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CD5428425E3961400310A01 /* OTAFileController.m */; };
		7CD5428B25E39B7300310A01 /* OTAFileTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CD5428A25E39B7300310A01 /* OTAFileTableViewCell.m */; };
		7CD5429425E4DA6D00310A01 /* OTASettingView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CD5429325E4DA6D00310A01 /* OTASettingView.m */; };
		7CD5429D25E78E1A00310A01 /* FileUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CD5429C25E78E1A00310A01 /* FileUtils.m */; };
		7CEAB7652995134A00464592 /* AvsLwaViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CEAB7642995134A00464592 /* AvsLwaViewController.m */; };
		7CEAB76B299517B700464592 /* AvsLwaProductCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CEAB76A299517B700464592 /* AvsLwaProductCell.m */; };
		7CECC71F2845F1B100644AAF /* BleWifiViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CECC71E2845F1B100644AAF /* BleWifiViewController.m */; };
		7CECC7222845F59700644AAF /* WifiUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CECC7212845F59700644AAF /* WifiUtils.m */; };
		7CF295382AF379A000744136 /* AlexaHttpManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CF295372AF379A000744136 /* AlexaHttpManager.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		7C9AFA6925BAC842001BF928 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7C9AFA4525BAC840001BF928 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7C9AFA4C25BAC840001BF928;
			remoteInfo = BesAll;
		};
		7C9AFA7425BAC842001BF928 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7C9AFA4525BAC840001BF928 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7C9AFA4C25BAC840001BF928;
			remoteInfo = BesAll;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		7C0583AE2C928E3A002D2AF4 /* oneplusone.txt */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = oneplusone.txt; sourceTree = "<group>"; };
		7C1761E2272BD01500F1AF99 /* FunctionChooseViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FunctionChooseViewController.h; sourceTree = "<group>"; };
		7C1761E3272BD01500F1AF99 /* FunctionChooseViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FunctionChooseViewController.m; sourceTree = "<group>"; };
		7C1761E5272BD48E00F1AF99 /* FunctionToolsViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FunctionToolsViewController.h; sourceTree = "<group>"; };
		7C1761E6272BD48E00F1AF99 /* FunctionToolsViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FunctionToolsViewController.m; sourceTree = "<group>"; };
		7C1761E9272BD71500F1AF99 /* EQViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EQViewController.h; sourceTree = "<group>"; };
		7C1761EA272BD71500F1AF99 /* EQViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EQViewController.m; sourceTree = "<group>"; };
		7C1761ED272BEB7D00F1AF99 /* EQTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EQTableViewCell.h; sourceTree = "<group>"; };
		7C1761EE272BEB7D00F1AF99 /* EQTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EQTableViewCell.m; sourceTree = "<group>"; };
		7C19010F29E13E3800B4135C /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		7C1F57FA29E0531A00220EA2 /* speaker_test_video.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = speaker_test_video.mp3; sourceTree = "<group>"; };
		7C3A6F4B26738FDA0075ED04 /* HomeViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HomeViewController.h; sourceTree = "<group>"; };
		7C3A6F4C26738FDA0075ED04 /* HomeViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HomeViewController.m; sourceTree = "<group>"; };
		7C3AB0BD2A57A91400B1B6C6 /* SmartVoiceViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SmartVoiceViewController.h; sourceTree = "<group>"; };
		7C3AB0BE2A57A91400B1B6C6 /* SmartVoiceViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SmartVoiceViewController.m; sourceTree = "<group>"; };
		7C3AB0C12A57AA9100B1B6C6 /* SmartVoiceService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SmartVoiceService.h; sourceTree = "<group>"; };
		7C3AB0C22A57AA9100B1B6C6 /* SmartVoiceService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SmartVoiceService.m; sourceTree = "<group>"; };
		7C3AB0C42A57AAA200B1B6C6 /* SmartVoiceCMD.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SmartVoiceCMD.h; sourceTree = "<group>"; };
		7C3AB0C52A57AAA200B1B6C6 /* SmartVoiceCMD.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SmartVoiceCMD.m; sourceTree = "<group>"; };
		7C3AB0C72A57AABD00B1B6C6 /* SmartVoiceConstants.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SmartVoiceConstants.h; sourceTree = "<group>"; };
		7C3AB0C82A57AABD00B1B6C6 /* SmartVoiceConstants.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SmartVoiceConstants.m; sourceTree = "<group>"; };
		7C3ACC3C2678CDDF001CCDB0 /* DeviceView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DeviceView.h; sourceTree = "<group>"; };
		7C3ACC3D2678CDDF001CCDB0 /* DeviceView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DeviceView.m; sourceTree = "<group>"; };
		7C3ACC422679D22C001CCDB0 /* BesButton.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BesButton.h; sourceTree = "<group>"; };
		7C3ACC432679D22C001CCDB0 /* BesButton.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BesButton.m; sourceTree = "<group>"; };
		7C3ACC48267AFA91001CCDB0 /* BaseOptionView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BaseOptionView.h; sourceTree = "<group>"; };
		7C3ACC49267AFA91001CCDB0 /* BaseOptionView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BaseOptionView.m; sourceTree = "<group>"; };
		7C3F7404267B771E005F5E65 /* SourceHanSansCN-Normal.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "SourceHanSansCN-Normal.otf"; sourceTree = "<group>"; };
		7C3F7413267B7C2C005F5E65 /* BesFont.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BesFont.h; sourceTree = "<group>"; };
		7C3F7414267B7C2C005F5E65 /* BesFont.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BesFont.m; sourceTree = "<group>"; };
		7C3F7419267B7F5F005F5E65 /* VersionPathView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = VersionPathView.h; sourceTree = "<group>"; };
		7C3F741A267B7F5F005F5E65 /* VersionPathView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = VersionPathView.m; sourceTree = "<group>"; };
		7C3F741F267C4A12005F5E65 /* SourceHanSansCN-Bold.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "SourceHanSansCN-Bold.otf"; sourceTree = "<group>"; };
		7C3F742A2681B50C005F5E65 /* BesAlert.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BesAlert.h; sourceTree = "<group>"; };
		7C3F742B2681B50C005F5E65 /* BesAlert.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BesAlert.m; sourceTree = "<group>"; };
		7C3F74332681C083005F5E65 /* CircleAnimationView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CircleAnimationView.h; sourceTree = "<group>"; };
		7C3F74342681C083005F5E65 /* CircleAnimationView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CircleAnimationView.m; sourceTree = "<group>"; };
		7C4039F429A60FB200C24BD8 /* CustomerDialViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CustomerDialViewController.h; sourceTree = "<group>"; };
		7C4039F529A60FB200C24BD8 /* CustomerDialViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CustomerDialViewController.m; sourceTree = "<group>"; };
		7C4039F829A7080600C24BD8 /* CustomerDialService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CustomerDialService.h; sourceTree = "<group>"; };
		7C4039F929A7080600C24BD8 /* CustomerDialService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CustomerDialService.m; sourceTree = "<group>"; };
		7C4039FB29A7086600C24BD8 /* CustomerDialCMD.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CustomerDialCMD.h; sourceTree = "<group>"; };
		7C4039FC29A7086600C24BD8 /* CustomerDialCMD.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CustomerDialCMD.m; sourceTree = "<group>"; };
		7C4039FF29A85B5100C24BD8 /* MakeDialViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MakeDialViewController.h; sourceTree = "<group>"; };
		7C403A0029A85B5100C24BD8 /* MakeDialViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MakeDialViewController.m; sourceTree = "<group>"; };
		7C403A0229A8924C00C24BD8 /* CustomerDialConstants.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CustomerDialConstants.h; sourceTree = "<group>"; };
		7C403A0529A8A00800C24BD8 /* MakeDialShowView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MakeDialShowView.h; sourceTree = "<group>"; };
		7C403A0629A8A00800C24BD8 /* MakeDialShowView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MakeDialShowView.m; sourceTree = "<group>"; };
		7C4331CC29A8B58700686CF8 /* MakeDialCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MakeDialCell.h; sourceTree = "<group>"; };
		7C4331CD29A8B58700686CF8 /* MakeDialCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MakeDialCell.m; sourceTree = "<group>"; };
		7C4331CF29A8EE8100686CF8 /* CustomerDialBean.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CustomerDialBean.h; sourceTree = "<group>"; };
		7C4331D029A8EE8100686CF8 /* CustomerDialBean.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CustomerDialBean.m; sourceTree = "<group>"; };
		7C4331D229AC7E6D00686CF8 /* CropPhotoViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CropPhotoViewController.h; sourceTree = "<group>"; };
		7C4331D329AC7E6D00686CF8 /* CropPhotoViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CropPhotoViewController.m; sourceTree = "<group>"; };
		7C4331D529ACA10E00686CF8 /* ShadeView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ShadeView.h; sourceTree = "<group>"; };
		7C4331D629ACA10E00686CF8 /* ShadeView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ShadeView.m; sourceTree = "<group>"; };
		7C4331D829ADA20800686CF8 /* UIImage+fixOrientation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+fixOrientation.h"; sourceTree = "<group>"; };
		7C4331D929ADA20800686CF8 /* UIImage+fixOrientation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+fixOrientation.m"; sourceTree = "<group>"; };
		7C441E9525ECCA7000E5FB4D /* AppSettingViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppSettingViewController.h; sourceTree = "<group>"; };
		7C441E9625ECCA7000E5FB4D /* AppSettingViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppSettingViewController.m; sourceTree = "<group>"; };
		7C4CA3862689AEDE0071B474 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Main.strings"; sourceTree = "<group>"; };
		7C4CA3872689AEDE0071B474 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/LaunchScreen.strings"; sourceTree = "<group>"; };
		7C4CA3AA2689B3700071B474 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Localizable.strings"; sourceTree = "<group>"; };
		7C4CA3AF2689B37B0071B474 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		7C4CA6D92672090700F8BE9F /* BesBaseViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BesBaseViewController.h; sourceTree = "<group>"; };
		7C4CA6DA2672090700F8BE9F /* BesBaseViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BesBaseViewController.m; sourceTree = "<group>"; };
		7C4CA6E7267333EA00F8BE9F /* BesColor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BesColor.h; sourceTree = "<group>"; };
		7C4CA6E8267333EA00F8BE9F /* BesColor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BesColor.m; sourceTree = "<group>"; };
		7C632ED328B7A9FC002203BF /* VersionPathViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = VersionPathViewCell.h; sourceTree = "<group>"; };
		7C632ED428B7A9FC002203BF /* VersionPathViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = VersionPathViewCell.m; sourceTree = "<group>"; };
		7C673F0C2996270A00C310A2 /* AvsLwaCMD.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AvsLwaCMD.h; sourceTree = "<group>"; };
		7C673F0D2996270A00C310A2 /* AvsLwaCMD.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AvsLwaCMD.m; sourceTree = "<group>"; };
		7C673F0F2996271C00C310A2 /* AvsLwaService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AvsLwaService.h; sourceTree = "<group>"; };
		7C673F102996271C00C310A2 /* AvsLwaService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AvsLwaService.m; sourceTree = "<group>"; };
		7C673F122996273700C310A2 /* AvsLwaConstants.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AvsLwaConstants.h; sourceTree = "<group>"; };
		7C673F132996273700C310A2 /* AvsLwaConstants.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AvsLwaConstants.m; sourceTree = "<group>"; };
		7C673F152996682D00C310A2 /* AvsGuideViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AvsGuideViewController.h; sourceTree = "<group>"; };
		7C673F162996682D00C310A2 /* AvsGuideViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AvsGuideViewController.m; sourceTree = "<group>"; };
		7C673F1829966A1700C310A2 /* AvsLoginViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AvsLoginViewController.h; sourceTree = "<group>"; };
		7C673F1929966A1700C310A2 /* AvsLoginViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AvsLoginViewController.m; sourceTree = "<group>"; };
		7C673F1B29966A6200C310A2 /* AvsSelectLanguageController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AvsSelectLanguageController.h; sourceTree = "<group>"; };
		7C673F1C29966A6200C310A2 /* AvsSelectLanguageController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AvsSelectLanguageController.m; sourceTree = "<group>"; };
		7C673F1E29966A9600C310A2 /* AvsResizeViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AvsResizeViewController.h; sourceTree = "<group>"; };
		7C673F1F29966A9600C310A2 /* AvsResizeViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AvsResizeViewController.m; sourceTree = "<group>"; };
		7C673F22299A335D00C310A2 /* LoginWithAmazon.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = LoginWithAmazon.framework; sourceTree = "<group>"; };
		7C673F25299A33A300C310A2 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		7C673F27299A33B000C310A2 /* SafariServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SafariServices.framework; path = System/Library/Frameworks/SafariServices.framework; sourceTree = SDKROOT; };
		7C673F29299B35BE00C310A2 /* AvsResizeView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AvsResizeView.h; sourceTree = "<group>"; };
		7C673F2A299B35BE00C310A2 /* AvsResizeView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AvsResizeView.m; sourceTree = "<group>"; };
		7C673F2C299B363A00C310A2 /* AvsSettingView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AvsSettingView.h; sourceTree = "<group>"; };
		7C673F2D299B363A00C310A2 /* AvsSettingView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AvsSettingView.m; sourceTree = "<group>"; };
		7C7C2BB72AEA59F200C49657 /* opus.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = opus.h; sourceTree = "<group>"; };
		7C7C2BB82AEA59F200C49657 /* OpusCodec.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = OpusCodec.m; sourceTree = "<group>"; };
		7C7C2BB92AEA59F200C49657 /* AudioDefine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AudioDefine.h; sourceTree = "<group>"; };
		7C7C2BBA2AEA59F200C49657 /* opus_multistream.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = opus_multistream.h; sourceTree = "<group>"; };
		7C7C2BBB2AEA59F200C49657 /* libopus.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libopus.a; sourceTree = "<group>"; };
		7C7C2BBC2AEA59F200C49657 /* OpusCodec.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OpusCodec.h; sourceTree = "<group>"; };
		7C7C2BBD2AEA59F200C49657 /* opus_types.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = opus_types.h; sourceTree = "<group>"; };
		7C7C2BBE2AEA59F200C49657 /* opus_defines.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = opus_defines.h; sourceTree = "<group>"; };
		7C7FD1812A0632C1008E65C8 /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		7C7FD1832A0632F7008E65C8 /* libc++.1.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.1.tbd"; path = "usr/lib/libc++.1.tbd"; sourceTree = SDKROOT; };
		7C7FD1852A063313008E65C8 /* libc++abi.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++abi.tbd"; path = "usr/lib/libc++abi.tbd"; sourceTree = SDKROOT; };
		7C7FD75B2A063AD8008E65C8 /* rwpng.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = rwpng.h; sourceTree = "<group>"; };
		7C7FD75C2A063AD8008E65C8 /* pngquant_opts.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pngquant_opts.h; sourceTree = "<group>"; };
		7C7FD75D2A063AD8008E65C8 /* pngquant.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = pngquant.c; sourceTree = "<group>"; };
		7C7FD75E2A063AD8008E65C8 /* rwpng.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = rwpng.c; sourceTree = "<group>"; };
		7C7FD75F2A063AD8008E65C8 /* pngquant_opts.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = pngquant_opts.c; sourceTree = "<group>"; };
		7C7FD7602A063AD8008E65C8 /* pngquant.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pngquant.h; sourceTree = "<group>"; };
		7C7FD9A62A063D5C008E65C8 /* pam.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pam.h; sourceTree = "<group>"; };
		7C7FD9A72A063D5C008E65C8 /* libimagequant.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = libimagequant.c; sourceTree = "<group>"; };
		7C7FD9A92A063D5C008E65C8 /* remap.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = remap.h; sourceTree = "<group>"; };
		7C7FD9AA2A063D5C008E65C8 /* blur.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = blur.h; sourceTree = "<group>"; };
		7C7FD9AB2A063D5C008E65C8 /* mediancut.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = mediancut.c; sourceTree = "<group>"; };
		7C7FD9AC2A063D5C008E65C8 /* nearest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = nearest.h; sourceTree = "<group>"; };
		7C7FD9AD2A063D5C008E65C8 /* mempool.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = mempool.c; sourceTree = "<group>"; };
		7C7FD9AE2A063D5C008E65C8 /* kmeans.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = kmeans.h; sourceTree = "<group>"; };
		7C7FD9AF2A063D5C008E65C8 /* pam.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = pam.c; sourceTree = "<group>"; };
		7C7FD9B12A063D5C008E65C8 /* libimagequant_private.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = libimagequant_private.h; sourceTree = "<group>"; };
		7C7FD9B22A063D5C008E65C8 /* mediancut.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = mediancut.h; sourceTree = "<group>"; };
		7C7FD9B32A063D5C008E65C8 /* blur.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = blur.c; sourceTree = "<group>"; };
		7C7FD9B42A063D5C008E65C8 /* remap.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = remap.c; sourceTree = "<group>"; };
		7C7FD9B52A063D5C008E65C8 /* libimagequant.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = libimagequant.h; sourceTree = "<group>"; };
		7C7FD9B62A063D5C008E65C8 /* kmeans.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = kmeans.c; sourceTree = "<group>"; };
		7C7FD9B72A063D5C008E65C8 /* mempool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = mempool.h; sourceTree = "<group>"; };
		7C7FD9B82A063D5C008E65C8 /* nearest.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = nearest.c; sourceTree = "<group>"; };
		7C7FDAD72A063E1C008E65C8 /* pnginfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pnginfo.h; sourceTree = "<group>"; };
		7C7FDAD82A063E1C008E65C8 /* pngtest.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = pngtest.c; sourceTree = "<group>"; };
		7C7FDAD92A063E1C008E65C8 /* pngrio.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = pngrio.c; sourceTree = "<group>"; };
		7C7FDADA2A063E1C008E65C8 /* pngerror.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = pngerror.c; sourceTree = "<group>"; };
		7C7FDADB2A063E1C008E65C8 /* pngwio.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = pngwio.c; sourceTree = "<group>"; };
		7C7FDADC2A063E1C008E65C8 /* pngstruct.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pngstruct.h; sourceTree = "<group>"; };
		7C7FDADD2A063E1C008E65C8 /* pngtrans.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = pngtrans.c; sourceTree = "<group>"; };
		7C7FDADE2A063E1C008E65C8 /* config.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = config.h; sourceTree = "<group>"; };
		7C7FDADF2A063E1C008E65C8 /* pngpriv.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pngpriv.h; sourceTree = "<group>"; };
		7C7FDAE02A063E1C008E65C8 /* pngwrite.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = pngwrite.c; sourceTree = "<group>"; };
		7C7FDAE12A063E1C008E65C8 /* png.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = png.h; sourceTree = "<group>"; };
		7C7FDAE52A063E1C008E65C8 /* pngwutil.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = pngwutil.c; sourceTree = "<group>"; };
		7C7FDAE62A063E1C008E65C8 /* pngwtran.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = pngwtran.c; sourceTree = "<group>"; };
		7C7FDAE72A063E1C008E65C8 /* pnglibconf.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pnglibconf.h; sourceTree = "<group>"; };
		7C7FDAE82A063E1C008E65C8 /* pngconf.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pngconf.h; sourceTree = "<group>"; };
		7C7FDAE92A063E1C008E65C8 /* pngpread.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = pngpread.c; sourceTree = "<group>"; };
		7C7FDAEA2A063E1C008E65C8 /* pngdebug.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pngdebug.h; sourceTree = "<group>"; };
		7C7FDAEB2A063E1C008E65C8 /* pngread.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = pngread.c; sourceTree = "<group>"; };
		7C7FDAEE2A063E1C008E65C8 /* filter_neon_intrinsics.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = filter_neon_intrinsics.c; sourceTree = "<group>"; };
		7C7FDAEF2A063E1C008E65C8 /* arm_init.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = arm_init.c; sourceTree = "<group>"; };
		7C7FDAF02A063E1C008E65C8 /* palette_neon_intrinsics.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = palette_neon_intrinsics.c; sourceTree = "<group>"; };
		7C7FDAF12A063E1C008E65C8 /* pngmem.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = pngmem.c; sourceTree = "<group>"; };
		7C7FDAF22A063E1C008E65C8 /* pngget.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = pngget.c; sourceTree = "<group>"; };
		7C7FDAF32A063E1C008E65C8 /* png.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = png.c; sourceTree = "<group>"; };
		7C7FDAF42A063E1C008E65C8 /* pngrtran.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = pngrtran.c; sourceTree = "<group>"; };
		7C7FDAF52A063E1C008E65C8 /* pngrutil.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = pngrutil.c; sourceTree = "<group>"; };
		7C7FDAF62A063E1C008E65C8 /* pngset.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = pngset.c; sourceTree = "<group>"; };
		7C82E322286B2FC900AA377C /* BesBTConnector.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BesBTConnector.h; sourceTree = "<group>"; };
		7C82E323286B2FCA00AA377C /* BesBTConnector.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BesBTConnector.m; sourceTree = "<group>"; };
		7C82E325286B300C00AA377C /* BesBaseService.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BesBaseService.h; sourceTree = "<group>"; };
		7C82E326286B300C00AA377C /* BesBaseService.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BesBaseService.m; sourceTree = "<group>"; };
		7C82E328286B306300AA377C /* BesOTAService.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BesOTAService.m; sourceTree = "<group>"; };
		7C82E32A286B319500AA377C /* ScanViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ScanViewController.m; sourceTree = "<group>"; };
		7C85008C2AD6CAE0004FBFB7 /* BesAll.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = BesAll.entitlements; sourceTree = "<group>"; };
		7C8F5F2B29DABA0800C74862 /* CommandSetViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CommandSetViewController.h; sourceTree = "<group>"; };
		7C8F5F2C29DABA0800C74862 /* CommandSetViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CommandSetViewController.m; sourceTree = "<group>"; };
		7C8F5F2E29DABA1B00C74862 /* CheckCrcViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CheckCrcViewController.h; sourceTree = "<group>"; };
		7C8F5F2F29DABA1B00C74862 /* CheckCrcViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CheckCrcViewController.m; sourceTree = "<group>"; };
		7C8F5F3329DEC2CB00C74862 /* CheckCrcService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CheckCrcService.h; sourceTree = "<group>"; };
		7C8F5F3429DEC2CB00C74862 /* CheckCrcService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CheckCrcService.m; sourceTree = "<group>"; };
		7C8F5F3629DEC2E300C74862 /* CheckCrcCMD.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CheckCrcCMD.h; sourceTree = "<group>"; };
		7C8F5F3729DEC2E300C74862 /* CheckCrcCMD.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CheckCrcCMD.m; sourceTree = "<group>"; };
		7C8F5F3929DEC2FC00C74862 /* CheckCrcConstants.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CheckCrcConstants.h; sourceTree = "<group>"; };
		7C8F5F3A29DEC2FC00C74862 /* CheckCrcConstants.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CheckCrcConstants.m; sourceTree = "<group>"; };
		7C8F5F3C29DEC31C00C74862 /* CommandSetService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CommandSetService.h; sourceTree = "<group>"; };
		7C8F5F3D29DEC31C00C74862 /* CommandSetService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CommandSetService.m; sourceTree = "<group>"; };
		7C8F5F3F29DEC33600C74862 /* CommandSetCMD.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CommandSetCMD.h; sourceTree = "<group>"; };
		7C8F5F4029DEC33600C74862 /* CommandSetCMD.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CommandSetCMD.m; sourceTree = "<group>"; };
		7C8F5F4229DEC39000C74862 /* CommandSetConstants.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CommandSetConstants.h; sourceTree = "<group>"; };
		7C8F5F4329DEC39000C74862 /* CommandSetConstants.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CommandSetConstants.m; sourceTree = "<group>"; };
		7C9246672AD7914900DBDBB8 /* CoreLocation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreLocation.framework; path = System/Library/Frameworks/CoreLocation.framework; sourceTree = SDKROOT; };
		7C94B5F22682072C0057F32A /* OtaingView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OtaingView.h; sourceTree = "<group>"; };
		7C94B5F32682072C0057F32A /* OtaingView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OtaingView.m; sourceTree = "<group>"; };
		7C94B5FE2683540E0057F32A /* BesLoadingView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BesLoadingView.h; sourceTree = "<group>"; };
		7C94B5FF2683540E0057F32A /* BesLoadingView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BesLoadingView.m; sourceTree = "<group>"; };
		7C94B604268472660057F32A /* AddFileTipsView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AddFileTipsView.h; sourceTree = "<group>"; };
		7C94B605268472660057F32A /* AddFileTipsView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AddFileTipsView.m; sourceTree = "<group>"; };
		7C99241025C7FEF80006BD0A /* ArrayUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ArrayUtil.h; sourceTree = "<group>"; };
		7C99241125C7FEF80006BD0A /* ArrayUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ArrayUtil.m; sourceTree = "<group>"; };
		7C99246A25CD4BFE0006BD0A /* NSData+XCYCoreTransfor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSData+XCYCoreTransfor.h"; sourceTree = "<group>"; };
		7C99246B25CD4BFE0006BD0A /* NSData+XCYCoreTransfor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSData+XCYCoreTransfor.m"; sourceTree = "<group>"; };
		7C99247725D167290006BD0A /* BesOTASetting.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BesOTASetting.m; sourceTree = "<group>"; };
		7C99247825D167290006BD0A /* BesOTACMD.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BesOTACMD.m; sourceTree = "<group>"; };
		7C99247925D167290006BD0A /* BesOTAConstants.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BesOTAConstants.h; sourceTree = "<group>"; };
		7C99247A25D167290006BD0A /* BesOTAService.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BesOTAService.h; sourceTree = "<group>"; };
		7C99247B25D167290006BD0A /* BesOTACMD.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BesOTACMD.h; sourceTree = "<group>"; };
		7C99247C25D167290006BD0A /* BesOTASetting.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BesOTASetting.h; sourceTree = "<group>"; };
		7C99247D25D167290006BD0A /* BesOTAConstants.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BesOTAConstants.m; sourceTree = "<group>"; };
		7C9AFA4D25BAC840001BF928 /* BesAll.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = BesAll.app; sourceTree = BUILT_PRODUCTS_DIR; };
		7C9AFA5025BAC840001BF928 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		7C9AFA5125BAC840001BF928 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		7C9AFA5325BAC840001BF928 /* SceneDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SceneDelegate.h; sourceTree = "<group>"; };
		7C9AFA5425BAC840001BF928 /* SceneDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SceneDelegate.m; sourceTree = "<group>"; };
		7C9AFA5A25BAC840001BF928 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		7C9AFA5C25BAC841001BF928 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		7C9AFA5F25BAC841001BF928 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		7C9AFA6125BAC841001BF928 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		7C9AFA6225BAC841001BF928 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		7C9AFA6825BAC842001BF928 /* BesAllTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = BesAllTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		7C9AFA6C25BAC842001BF928 /* BesAllTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BesAllTests.m; sourceTree = "<group>"; };
		7C9AFA6E25BAC842001BF928 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		7C9AFA7325BAC842001BF928 /* BesAllUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = BesAllUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		7C9AFA7725BAC842001BF928 /* BesAllUITests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BesAllUITests.m; sourceTree = "<group>"; };
		7C9AFA7925BAC842001BF928 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		7C9AFAB025C2B3B5001BF928 /* UIView+Ext.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+Ext.m"; sourceTree = "<group>"; };
		7C9AFAB125C2B3B5001BF928 /* UIView+Ext.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+Ext.h"; sourceTree = "<group>"; };
		7C9AFABD25C2B80D001BF928 /* PrefixHeader.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PrefixHeader.pch; sourceTree = "<group>"; };
		7C9AFAFB25C3B5B9001BF928 /* ScanViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ScanViewController.h; sourceTree = "<group>"; };
		7C9AFBA625C3F2D5001BF928 /* BesSdkConstants.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BesSdkConstants.h; sourceTree = "<group>"; };
		7C9AFBC625C43599001BF928 /* NSString+XCYCoreOperation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString+XCYCoreOperation.h"; sourceTree = "<group>"; };
		7C9AFBC725C43599001BF928 /* NSString+XCYCoreOperation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSString+XCYCoreOperation.m"; sourceTree = "<group>"; };
		7C9AFBCD25C53778001BF928 /* BesServiceConfig.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BesServiceConfig.m; sourceTree = "<group>"; };
		7C9AFBCF25C53778001BF928 /* BesServiceConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BesServiceConfig.h; sourceTree = "<group>"; };
		7C9AFBD925C5391F001BF928 /* BesUUID.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BesUUID.h; sourceTree = "<group>"; };
		7C9AFBDA25C5391F001BF928 /* BesUUID.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BesUUID.m; sourceTree = "<group>"; };
		7C9E3CA52AE3BBB6003129C9 /* BleWifiDemoViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BleWifiDemoViewController.h; sourceTree = "<group>"; };
		7C9E3CA62AE3BBB7003129C9 /* BleWifiDemoViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BleWifiDemoViewController.m; sourceTree = "<group>"; };
		7CABFD072685829F00A91310 /* CustomAlert.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CustomAlert.h; sourceTree = "<group>"; };
		7CABFD082685829F00A91310 /* CustomAlert.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CustomAlert.m; sourceTree = "<group>"; };
		7CABFD0D2685A88300A91310 /* SourceHanSansCN-Medium.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "SourceHanSansCN-Medium.otf"; sourceTree = "<group>"; };
		7CAC8BC22700979D0050D983 /* TOTAConnectCMD.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TOTAConnectCMD.h; sourceTree = "<group>"; };
		7CAC8BC32700979D0050D983 /* TOTAConnectCMD.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TOTAConnectCMD.m; sourceTree = "<group>"; };
		7CAC8BC62702F6660050D983 /* BesFunc.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BesFunc.h; sourceTree = "<group>"; };
		7CAC8BC72702F6660050D983 /* BesFunc.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BesFunc.m; sourceTree = "<group>"; };
		7CAC8BC9270300DF0050D983 /* Sha256.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Sha256.h; sourceTree = "<group>"; };
		7CAC8BCA270300DF0050D983 /* Sha256.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = Sha256.m; sourceTree = "<group>"; };
		7CAC8BCC27030F180050D983 /* aes.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = aes.h; sourceTree = "<group>"; };
		7CAC8BCD27030F180050D983 /* aes.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = aes.m; sourceTree = "<group>"; };
		7CACE2F72888F69000805A86 /* CustomHistoryList.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CustomHistoryList.h; sourceTree = "<group>"; };
		7CACE2F82888F69000805A86 /* CustomCmdViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CustomCmdViewController.h; sourceTree = "<group>"; };
		7CACE2F92888F69000805A86 /* CustomCmdFileController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CustomCmdFileController.m; sourceTree = "<group>"; };
		7CACE2FA2888F69000805A86 /* CustomHistoryCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CustomHistoryCell.m; sourceTree = "<group>"; };
		7CACE2FB2888F69000805A86 /* CustomHistoryList.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CustomHistoryList.m; sourceTree = "<group>"; };
		7CACE2FC2888F69000805A86 /* CustomCmdFileController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CustomCmdFileController.h; sourceTree = "<group>"; };
		7CACE2FD2888F69000805A86 /* CustomCmdViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CustomCmdViewController.m; sourceTree = "<group>"; };
		7CACE2FE2888F69000805A86 /* CustomHistoryCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CustomHistoryCell.h; sourceTree = "<group>"; };
		7CACE3042888F72100805A86 /* BesCustomCmdConstants.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BesCustomCmdConstants.h; sourceTree = "<group>"; };
		7CACE3052888F72100805A86 /* BesCustomCmdCMD.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BesCustomCmdCMD.h; sourceTree = "<group>"; };
		7CACE3062888F72100805A86 /* BesCustomCmdConstants.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BesCustomCmdConstants.m; sourceTree = "<group>"; };
		7CACE3072888F72100805A86 /* BesCustomCmdCMD.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BesCustomCmdCMD.m; sourceTree = "<group>"; };
		7CC19823272FD3C3008E28C2 /* EQBeans.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EQBeans.h; sourceTree = "<group>"; };
		7CC19824272FD3C3008E28C2 /* EQBeans.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EQBeans.m; sourceTree = "<group>"; };
		7CC1982B27300E18008E28C2 /* BesEQCMD.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BesEQCMD.h; sourceTree = "<group>"; };
		7CC1982C27300E18008E28C2 /* BesEQCMD.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BesEQCMD.m; sourceTree = "<group>"; };
		7CC1982E27300E3F008E28C2 /* BesEQConstants.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BesEQConstants.h; sourceTree = "<group>"; };
		7CC1982F27300E3F008E28C2 /* BesEQConstants.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BesEQConstants.m; sourceTree = "<group>"; };
		7CC1983127300E5D008E28C2 /* BesEQService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BesEQService.h; sourceTree = "<group>"; };
		7CC1983227300E5D008E28C2 /* BesEQService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BesEQService.m; sourceTree = "<group>"; };
		7CC1983427301164008E28C2 /* ConnectDeviceView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ConnectDeviceView.h; sourceTree = "<group>"; };
		7CC1983527301164008E28C2 /* ConnectDeviceView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ConnectDeviceView.m; sourceTree = "<group>"; };
		7CD5426B25DFE49100310A01 /* OTAViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OTAViewController.h; sourceTree = "<group>"; };
		7CD5426C25DFE49100310A01 /* OTAViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OTAViewController.m; sourceTree = "<group>"; };
		7CD5427125E0E31700310A01 /* FunctionOTAViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FunctionOTAViewController.h; sourceTree = "<group>"; };
		7CD5427225E0E31700310A01 /* FunctionOTAViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FunctionOTAViewController.m; sourceTree = "<group>"; };
		7CD5427A25E37D3400310A01 /* ScanTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ScanTableViewCell.h; sourceTree = "<group>"; };
		7CD5427B25E37D3400310A01 /* ScanTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ScanTableViewCell.m; sourceTree = "<group>"; };
		7CD5428325E3961400310A01 /* OTAFileController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OTAFileController.h; sourceTree = "<group>"; };
		7CD5428425E3961400310A01 /* OTAFileController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OTAFileController.m; sourceTree = "<group>"; };
		7CD5428925E39B7300310A01 /* OTAFileTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OTAFileTableViewCell.h; sourceTree = "<group>"; };
		7CD5428A25E39B7300310A01 /* OTAFileTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OTAFileTableViewCell.m; sourceTree = "<group>"; };
		7CD5429225E4DA6D00310A01 /* OTASettingView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OTASettingView.h; sourceTree = "<group>"; };
		7CD5429325E4DA6D00310A01 /* OTASettingView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OTASettingView.m; sourceTree = "<group>"; };
		7CD5429B25E78E1A00310A01 /* FileUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FileUtils.h; sourceTree = "<group>"; };
		7CD5429C25E78E1A00310A01 /* FileUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FileUtils.m; sourceTree = "<group>"; };
		7CEAB7632995134A00464592 /* AvsLwaViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AvsLwaViewController.h; sourceTree = "<group>"; };
		7CEAB7642995134A00464592 /* AvsLwaViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AvsLwaViewController.m; sourceTree = "<group>"; };
		7CEAB769299517B700464592 /* AvsLwaProductCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AvsLwaProductCell.h; sourceTree = "<group>"; };
		7CEAB76A299517B700464592 /* AvsLwaProductCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AvsLwaProductCell.m; sourceTree = "<group>"; };
		7CECC71D2845F1B100644AAF /* BleWifiViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BleWifiViewController.h; sourceTree = "<group>"; };
		7CECC71E2845F1B100644AAF /* BleWifiViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BleWifiViewController.m; sourceTree = "<group>"; };
		7CECC7202845F59700644AAF /* WifiUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WifiUtils.h; sourceTree = "<group>"; };
		7CECC7212845F59700644AAF /* WifiUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WifiUtils.m; sourceTree = "<group>"; };
		7CF295362AF379A000744136 /* AlexaHttpManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AlexaHttpManager.h; sourceTree = "<group>"; };
		7CF295372AF379A000744136 /* AlexaHttpManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AlexaHttpManager.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		7C9AFA4A25BAC840001BF928 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7C9246682AD7914900DBDBB8 /* CoreLocation.framework in Frameworks */,
				7C19011029E13E3800B4135C /* AVFoundation.framework in Frameworks */,
				7C5EECB82B81D63700342F55 /* libopus.a in Frameworks */,
				7C673F28299A33B000C310A2 /* SafariServices.framework in Frameworks */,
				7C673F26299A33A300C310A2 /* Security.framework in Frameworks */,
				7C673F23299A335D00C310A2 /* LoginWithAmazon.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7C9AFA6525BAC842001BF928 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7C9AFA7025BAC842001BF928 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		7C1761E8272BD6F000F1AF99 /* Tools */ = {
			isa = PBXGroup;
			children = (
				7C1761E5272BD48E00F1AF99 /* FunctionToolsViewController.h */,
				7C1761E6272BD48E00F1AF99 /* FunctionToolsViewController.m */,
				7CC1983427301164008E28C2 /* ConnectDeviceView.h */,
				7CC1983527301164008E28C2 /* ConnectDeviceView.m */,
				7C3AB0BC2A57A8EE00B1B6C6 /* SmartVoice */,
				7C8F5F2929DAB97E00C74862 /* CheckCrc */,
				7C8F5F2A29DAB97E00C74862 /* CommandSet */,
				7C4039F329A60F8C00C24BD8 /* CustomerDial */,
				7CEAB7622995131300464592 /* AVSLwa */,
				7CACE2F62888F69000805A86 /* CustomCmd */,
				7CECC71C2845F1B100644AAF /* BleWifi */,
				7C1761EC272BD95700F1AF99 /* EQ */,
			);
			path = Tools;
			sourceTree = "<group>";
		};
		7C1761EC272BD95700F1AF99 /* EQ */ = {
			isa = PBXGroup;
			children = (
				7C1761E9272BD71500F1AF99 /* EQViewController.h */,
				7C1761EA272BD71500F1AF99 /* EQViewController.m */,
				7C1761ED272BEB7D00F1AF99 /* EQTableViewCell.h */,
				7C1761EE272BEB7D00F1AF99 /* EQTableViewCell.m */,
				7CC19823272FD3C3008E28C2 /* EQBeans.h */,
				7CC19824272FD3C3008E28C2 /* EQBeans.m */,
			);
			path = EQ;
			sourceTree = "<group>";
		};
		7C3AB0BC2A57A8EE00B1B6C6 /* SmartVoice */ = {
			isa = PBXGroup;
			children = (
				7C3AB0BD2A57A91400B1B6C6 /* SmartVoiceViewController.h */,
				7C3AB0BE2A57A91400B1B6C6 /* SmartVoiceViewController.m */,
			);
			path = SmartVoice;
			sourceTree = "<group>";
		};
		7C3AB0C02A57AA5900B1B6C6 /* SmartVoice */ = {
			isa = PBXGroup;
			children = (
				7C3AB0C12A57AA9100B1B6C6 /* SmartVoiceService.h */,
				7C3AB0C22A57AA9100B1B6C6 /* SmartVoiceService.m */,
				7C3AB0C42A57AAA200B1B6C6 /* SmartVoiceCMD.h */,
				7C3AB0C52A57AAA200B1B6C6 /* SmartVoiceCMD.m */,
				7C3AB0C72A57AABD00B1B6C6 /* SmartVoiceConstants.h */,
				7C3AB0C82A57AABD00B1B6C6 /* SmartVoiceConstants.m */,
			);
			path = SmartVoice;
			sourceTree = "<group>";
		};
		7C3ACC382678A5EC001CCDB0 /* Component */ = {
			isa = PBXGroup;
			children = (
				7C3ACC3C2678CDDF001CCDB0 /* DeviceView.h */,
				7C3ACC3D2678CDDF001CCDB0 /* DeviceView.m */,
				7C3ACC422679D22C001CCDB0 /* BesButton.h */,
				7C3ACC432679D22C001CCDB0 /* BesButton.m */,
				7C3ACC48267AFA91001CCDB0 /* BaseOptionView.h */,
				7C3ACC49267AFA91001CCDB0 /* BaseOptionView.m */,
				7C3F7419267B7F5F005F5E65 /* VersionPathView.h */,
				7C3F741A267B7F5F005F5E65 /* VersionPathView.m */,
				7C632ED328B7A9FC002203BF /* VersionPathViewCell.h */,
				7C632ED428B7A9FC002203BF /* VersionPathViewCell.m */,
				7C94B5F22682072C0057F32A /* OtaingView.h */,
				7C94B5F32682072C0057F32A /* OtaingView.m */,
				7C3F74332681C083005F5E65 /* CircleAnimationView.h */,
				7C3F74342681C083005F5E65 /* CircleAnimationView.m */,
				7CD5429225E4DA6D00310A01 /* OTASettingView.h */,
				7CD5429325E4DA6D00310A01 /* OTASettingView.m */,
				7C3F742A2681B50C005F5E65 /* BesAlert.h */,
				7C3F742B2681B50C005F5E65 /* BesAlert.m */,
				7CABFD072685829F00A91310 /* CustomAlert.h */,
				7CABFD082685829F00A91310 /* CustomAlert.m */,
				7C94B5FE2683540E0057F32A /* BesLoadingView.h */,
				7C94B5FF2683540E0057F32A /* BesLoadingView.m */,
				7C94B604268472660057F32A /* AddFileTipsView.h */,
				7C94B605268472660057F32A /* AddFileTipsView.m */,
			);
			path = Component;
			sourceTree = "<group>";
		};
		7C4039F329A60F8C00C24BD8 /* CustomerDial */ = {
			isa = PBXGroup;
			children = (
				7C7FD3712A06380C008E65C8 /* png256Sdk */,
				7C4039FE29A85B1300C24BD8 /* makedial */,
				7C4039F429A60FB200C24BD8 /* CustomerDialViewController.h */,
				7C4039F529A60FB200C24BD8 /* CustomerDialViewController.m */,
			);
			path = CustomerDial;
			sourceTree = "<group>";
		};
		7C4039F729A707D600C24BD8 /* CustomerDial */ = {
			isa = PBXGroup;
			children = (
				7C4039F829A7080600C24BD8 /* CustomerDialService.h */,
				7C4039F929A7080600C24BD8 /* CustomerDialService.m */,
				7C4039FB29A7086600C24BD8 /* CustomerDialCMD.h */,
				7C4039FC29A7086600C24BD8 /* CustomerDialCMD.m */,
				7C403A0229A8924C00C24BD8 /* CustomerDialConstants.h */,
			);
			path = CustomerDial;
			sourceTree = "<group>";
		};
		7C4039FE29A85B1300C24BD8 /* makedial */ = {
			isa = PBXGroup;
			children = (
				7C4039FF29A85B5100C24BD8 /* MakeDialViewController.h */,
				7C403A0029A85B5100C24BD8 /* MakeDialViewController.m */,
				7C403A0529A8A00800C24BD8 /* MakeDialShowView.h */,
				7C403A0629A8A00800C24BD8 /* MakeDialShowView.m */,
				7C4331CC29A8B58700686CF8 /* MakeDialCell.h */,
				7C4331CD29A8B58700686CF8 /* MakeDialCell.m */,
				7C4331CF29A8EE8100686CF8 /* CustomerDialBean.h */,
				7C4331D029A8EE8100686CF8 /* CustomerDialBean.m */,
				7C4331D229AC7E6D00686CF8 /* CropPhotoViewController.h */,
				7C4331D329AC7E6D00686CF8 /* CropPhotoViewController.m */,
				7C4331D829ADA20800686CF8 /* UIImage+fixOrientation.h */,
				7C4331D929ADA20800686CF8 /* UIImage+fixOrientation.m */,
				7C4331D529ACA10E00686CF8 /* ShadeView.h */,
				7C4331D629ACA10E00686CF8 /* ShadeView.m */,
			);
			path = makedial;
			sourceTree = "<group>";
		};
		7C4CA6D8267208ED00F8BE9F /* base */ = {
			isa = PBXGroup;
			children = (
				7C4CA6D92672090700F8BE9F /* BesBaseViewController.h */,
				7C4CA6DA2672090700F8BE9F /* BesBaseViewController.m */,
			);
			path = base;
			sourceTree = "<group>";
		};
		7C4CA6E226730D7A00F8BE9F /* Tools */ = {
			isa = PBXGroup;
			children = (
				7C5EECB72B81D55D00342F55 /* Opus */,
				7CF295352AF3798400744136 /* manager */,
				7C4CA6E7267333EA00F8BE9F /* BesColor.h */,
				7C4CA6E8267333EA00F8BE9F /* BesColor.m */,
				7C3F7413267B7C2C005F5E65 /* BesFont.h */,
				7C3F7414267B7C2C005F5E65 /* BesFont.m */,
			);
			path = Tools;
			sourceTree = "<group>";
		};
		7C4CA6E326730DA800F8BE9F /* VC */ = {
			isa = PBXGroup;
			children = (
				7C3A6F4B26738FDA0075ED04 /* HomeViewController.h */,
				7C3A6F4C26738FDA0075ED04 /* HomeViewController.m */,
				7C1761E2272BD01500F1AF99 /* FunctionChooseViewController.h */,
				7C1761E3272BD01500F1AF99 /* FunctionChooseViewController.m */,
				7C1761E8272BD6F000F1AF99 /* Tools */,
				7CC1982927300BB2008E28C2 /* OTA */,
			);
			path = VC;
			sourceTree = "<group>";
		};
		7C5EECB72B81D55D00342F55 /* Opus */ = {
			isa = PBXGroup;
			children = (
				7C7C2BB62AEA59F200C49657 /* lib */,
			);
			path = Opus;
			sourceTree = "<group>";
		};
		7C673F0B299626CC00C310A2 /* AVSLwa */ = {
			isa = PBXGroup;
			children = (
				7C673F0C2996270A00C310A2 /* AvsLwaCMD.h */,
				7C673F0D2996270A00C310A2 /* AvsLwaCMD.m */,
				7C673F0F2996271C00C310A2 /* AvsLwaService.h */,
				7C673F102996271C00C310A2 /* AvsLwaService.m */,
				7C673F122996273700C310A2 /* AvsLwaConstants.h */,
				7C673F132996273700C310A2 /* AvsLwaConstants.m */,
			);
			path = AVSLwa;
			sourceTree = "<group>";
		};
		7C673F21299A332200C310A2 /* Library */ = {
			isa = PBXGroup;
			children = (
				7C673F22299A335D00C310A2 /* LoginWithAmazon.framework */,
			);
			path = Library;
			sourceTree = "<group>";
		};
		7C673F24299A33A300C310A2 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				7C9246672AD7914900DBDBB8 /* CoreLocation.framework */,
				7C7FD1852A063313008E65C8 /* libc++abi.tbd */,
				7C7FD1832A0632F7008E65C8 /* libc++.1.tbd */,
				7C7FD1812A0632C1008E65C8 /* libc++.tbd */,
				7C19010F29E13E3800B4135C /* AVFoundation.framework */,
				7C673F27299A33B000C310A2 /* SafariServices.framework */,
				7C673F25299A33A300C310A2 /* Security.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		7C7C2BB62AEA59F200C49657 /* lib */ = {
			isa = PBXGroup;
			children = (
				7C7C2BB72AEA59F200C49657 /* opus.h */,
				7C7C2BB82AEA59F200C49657 /* OpusCodec.m */,
				7C7C2BB92AEA59F200C49657 /* AudioDefine.h */,
				7C7C2BBA2AEA59F200C49657 /* opus_multistream.h */,
				7C7C2BBB2AEA59F200C49657 /* libopus.a */,
				7C7C2BBC2AEA59F200C49657 /* OpusCodec.h */,
				7C7C2BBD2AEA59F200C49657 /* opus_types.h */,
				7C7C2BBE2AEA59F200C49657 /* opus_defines.h */,
			);
			path = lib;
			sourceTree = "<group>";
		};
		7C7FD3712A06380C008E65C8 /* png256Sdk */ = {
			isa = PBXGroup;
			children = (
				7C7FD5E92A063AD7008E65C8 /* bes_pngquant */,
			);
			path = png256Sdk;
			sourceTree = "<group>";
		};
		7C7FD5E92A063AD7008E65C8 /* bes_pngquant */ = {
			isa = PBXGroup;
			children = (
				7C7FDAD62A063E1C008E65C8 /* libpng-1.6.37 */,
				7C7FD9A52A063D5C008E65C8 /* libimagequant */,
				7C7FD75A2A063AD8008E65C8 /* libquantcmd */,
			);
			path = bes_pngquant;
			sourceTree = "<group>";
		};
		7C7FD75A2A063AD8008E65C8 /* libquantcmd */ = {
			isa = PBXGroup;
			children = (
				7C7FD75B2A063AD8008E65C8 /* rwpng.h */,
				7C7FD75C2A063AD8008E65C8 /* pngquant_opts.h */,
				7C7FD75D2A063AD8008E65C8 /* pngquant.c */,
				7C7FD75E2A063AD8008E65C8 /* rwpng.c */,
				7C7FD75F2A063AD8008E65C8 /* pngquant_opts.c */,
				7C7FD7602A063AD8008E65C8 /* pngquant.h */,
			);
			path = libquantcmd;
			sourceTree = "<group>";
		};
		7C7FD9A52A063D5C008E65C8 /* libimagequant */ = {
			isa = PBXGroup;
			children = (
				7C7FD9A62A063D5C008E65C8 /* pam.h */,
				7C7FD9A72A063D5C008E65C8 /* libimagequant.c */,
				7C7FD9A92A063D5C008E65C8 /* remap.h */,
				7C7FD9AA2A063D5C008E65C8 /* blur.h */,
				7C7FD9AB2A063D5C008E65C8 /* mediancut.c */,
				7C7FD9AC2A063D5C008E65C8 /* nearest.h */,
				7C7FD9AD2A063D5C008E65C8 /* mempool.c */,
				7C7FD9AE2A063D5C008E65C8 /* kmeans.h */,
				7C7FD9AF2A063D5C008E65C8 /* pam.c */,
				7C7FD9B12A063D5C008E65C8 /* libimagequant_private.h */,
				7C7FD9B22A063D5C008E65C8 /* mediancut.h */,
				7C7FD9B32A063D5C008E65C8 /* blur.c */,
				7C7FD9B42A063D5C008E65C8 /* remap.c */,
				7C7FD9B52A063D5C008E65C8 /* libimagequant.h */,
				7C7FD9B62A063D5C008E65C8 /* kmeans.c */,
				7C7FD9B72A063D5C008E65C8 /* mempool.h */,
				7C7FD9B82A063D5C008E65C8 /* nearest.c */,
			);
			path = libimagequant;
			sourceTree = "<group>";
		};
		7C7FDAD62A063E1C008E65C8 /* libpng-1.6.37 */ = {
			isa = PBXGroup;
			children = (
				7C7FDAD72A063E1C008E65C8 /* pnginfo.h */,
				7C7FDAD82A063E1C008E65C8 /* pngtest.c */,
				7C7FDAD92A063E1C008E65C8 /* pngrio.c */,
				7C7FDADA2A063E1C008E65C8 /* pngerror.c */,
				7C7FDADB2A063E1C008E65C8 /* pngwio.c */,
				7C7FDADC2A063E1C008E65C8 /* pngstruct.h */,
				7C7FDADD2A063E1C008E65C8 /* pngtrans.c */,
				7C7FDADE2A063E1C008E65C8 /* config.h */,
				7C7FDADF2A063E1C008E65C8 /* pngpriv.h */,
				7C7FDAE02A063E1C008E65C8 /* pngwrite.c */,
				7C7FDAE12A063E1C008E65C8 /* png.h */,
				7C7FDAE52A063E1C008E65C8 /* pngwutil.c */,
				7C7FDAE62A063E1C008E65C8 /* pngwtran.c */,
				7C7FDAE72A063E1C008E65C8 /* pnglibconf.h */,
				7C7FDAE82A063E1C008E65C8 /* pngconf.h */,
				7C7FDAE92A063E1C008E65C8 /* pngpread.c */,
				7C7FDAEA2A063E1C008E65C8 /* pngdebug.h */,
				7C7FDAEB2A063E1C008E65C8 /* pngread.c */,
				7C7FDAEC2A063E1C008E65C8 /* arm */,
				7C7FDAF12A063E1C008E65C8 /* pngmem.c */,
				7C7FDAF22A063E1C008E65C8 /* pngget.c */,
				7C7FDAF32A063E1C008E65C8 /* png.c */,
				7C7FDAF42A063E1C008E65C8 /* pngrtran.c */,
				7C7FDAF52A063E1C008E65C8 /* pngrutil.c */,
				7C7FDAF62A063E1C008E65C8 /* pngset.c */,
			);
			path = "libpng-1.6.37";
			sourceTree = "<group>";
		};
		7C7FDAEC2A063E1C008E65C8 /* arm */ = {
			isa = PBXGroup;
			children = (
				7C7FDAEE2A063E1C008E65C8 /* filter_neon_intrinsics.c */,
				7C7FDAEF2A063E1C008E65C8 /* arm_init.c */,
				7C7FDAF02A063E1C008E65C8 /* palette_neon_intrinsics.c */,
			);
			path = arm;
			sourceTree = "<group>";
		};
		7C8F5F2929DAB97E00C74862 /* CheckCrc */ = {
			isa = PBXGroup;
			children = (
				7C8F5F2E29DABA1B00C74862 /* CheckCrcViewController.h */,
				7C8F5F2F29DABA1B00C74862 /* CheckCrcViewController.m */,
			);
			path = CheckCrc;
			sourceTree = "<group>";
		};
		7C8F5F2A29DAB97E00C74862 /* CommandSet */ = {
			isa = PBXGroup;
			children = (
				7C8F5F2B29DABA0800C74862 /* CommandSetViewController.h */,
				7C8F5F2C29DABA0800C74862 /* CommandSetViewController.m */,
			);
			path = CommandSet;
			sourceTree = "<group>";
		};
		7C8F5F3129DEB8F400C74862 /* CheckCrc */ = {
			isa = PBXGroup;
			children = (
				7C8F5F3329DEC2CB00C74862 /* CheckCrcService.h */,
				7C8F5F3429DEC2CB00C74862 /* CheckCrcService.m */,
				7C8F5F3629DEC2E300C74862 /* CheckCrcCMD.h */,
				7C8F5F3729DEC2E300C74862 /* CheckCrcCMD.m */,
				7C8F5F3929DEC2FC00C74862 /* CheckCrcConstants.h */,
				7C8F5F3A29DEC2FC00C74862 /* CheckCrcConstants.m */,
			);
			path = CheckCrc;
			sourceTree = "<group>";
		};
		7C8F5F3229DEB8F400C74862 /* CommandSet */ = {
			isa = PBXGroup;
			children = (
				7C8F5F3C29DEC31C00C74862 /* CommandSetService.h */,
				7C8F5F3D29DEC31C00C74862 /* CommandSetService.m */,
				7C8F5F3F29DEC33600C74862 /* CommandSetCMD.h */,
				7C8F5F4029DEC33600C74862 /* CommandSetCMD.m */,
				7C8F5F4229DEC39000C74862 /* CommandSetConstants.h */,
				7C8F5F4329DEC39000C74862 /* CommandSetConstants.m */,
			);
			path = CommandSet;
			sourceTree = "<group>";
		};
		7C99247625D167290006BD0A /* ota */ = {
			isa = PBXGroup;
			children = (
				7C99247925D167290006BD0A /* BesOTAConstants.h */,
				7C99247D25D167290006BD0A /* BesOTAConstants.m */,
				7C99247B25D167290006BD0A /* BesOTACMD.h */,
				7C99247825D167290006BD0A /* BesOTACMD.m */,
				7C99247A25D167290006BD0A /* BesOTAService.h */,
				7C82E328286B306300AA377C /* BesOTAService.m */,
				7C99247C25D167290006BD0A /* BesOTASetting.h */,
				7C99247725D167290006BD0A /* BesOTASetting.m */,
			);
			path = ota;
			sourceTree = "<group>";
		};
		7C9AFA4425BAC840001BF928 = {
			isa = PBXGroup;
			children = (
				7C9AFA4F25BAC840001BF928 /* BesAll */,
				7C9AFA6B25BAC842001BF928 /* BesAllTests */,
				7C9AFA7625BAC842001BF928 /* BesAllUITests */,
				7C9AFA4E25BAC840001BF928 /* Products */,
				7C673F24299A33A300C310A2 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		7C9AFA4E25BAC840001BF928 /* Products */ = {
			isa = PBXGroup;
			children = (
				7C9AFA4D25BAC840001BF928 /* BesAll.app */,
				7C9AFA6825BAC842001BF928 /* BesAllTests.xctest */,
				7C9AFA7325BAC842001BF928 /* BesAllUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		7C9AFA4F25BAC840001BF928 /* BesAll */ = {
			isa = PBXGroup;
			children = (
				7C0583AE2C928E3A002D2AF4 /* oneplusone.txt */,
				7C85008C2AD6CAE0004FBFB7 /* BesAll.entitlements */,
				7C1F57FA29E0531A00220EA2 /* speaker_test_video.mp3 */,
				7CABFD0D2685A88300A91310 /* SourceHanSansCN-Medium.otf */,
				7C3F741F267C4A12005F5E65 /* SourceHanSansCN-Bold.otf */,
				7C3F7404267B771E005F5E65 /* SourceHanSansCN-Normal.otf */,
				7C4CA6E226730D7A00F8BE9F /* Tools */,
				7C9AFAAF25C2B3B5001BF928 /* UIView+Ext */,
				7C4CA6D8267208ED00F8BE9F /* base */,
				7C4CA6E326730DA800F8BE9F /* VC */,
				7C3ACC382678A5EC001CCDB0 /* Component */,
				7CC1982627300ACC008E28C2 /* Bluetooth */,
				7C9AFA5025BAC840001BF928 /* AppDelegate.h */,
				7C9AFA5125BAC840001BF928 /* AppDelegate.m */,
				7C9AFA5325BAC840001BF928 /* SceneDelegate.h */,
				7C9AFA5425BAC840001BF928 /* SceneDelegate.m */,
				7C9AFBA425C3F2D5001BF928 /* bessdk */,
				7C9AFABD25C2B80D001BF928 /* PrefixHeader.pch */,
				7C9AFA5925BAC840001BF928 /* Main.storyboard */,
				7C9AFA5C25BAC841001BF928 /* Assets.xcassets */,
				7C9AFA5E25BAC841001BF928 /* LaunchScreen.storyboard */,
				7C9AFA6125BAC841001BF928 /* Info.plist */,
				7C9AFA6225BAC841001BF928 /* main.m */,
				7C4CA3AB2689B3700071B474 /* Localizable.strings */,
			);
			path = BesAll;
			sourceTree = "<group>";
		};
		7C9AFA6B25BAC842001BF928 /* BesAllTests */ = {
			isa = PBXGroup;
			children = (
				7C9AFA6C25BAC842001BF928 /* BesAllTests.m */,
				7C9AFA6E25BAC842001BF928 /* Info.plist */,
			);
			path = BesAllTests;
			sourceTree = "<group>";
		};
		7C9AFA7625BAC842001BF928 /* BesAllUITests */ = {
			isa = PBXGroup;
			children = (
				7C9AFA7725BAC842001BF928 /* BesAllUITests.m */,
				7C9AFA7925BAC842001BF928 /* Info.plist */,
			);
			path = BesAllUITests;
			sourceTree = "<group>";
		};
		7C9AFAAF25C2B3B5001BF928 /* UIView+Ext */ = {
			isa = PBXGroup;
			children = (
				7C9AFAB025C2B3B5001BF928 /* UIView+Ext.m */,
				7C9AFAB125C2B3B5001BF928 /* UIView+Ext.h */,
			);
			path = "UIView+Ext";
			sourceTree = "<group>";
		};
		7C9AFBA425C3F2D5001BF928 /* bessdk */ = {
			isa = PBXGroup;
			children = (
				7C9AFBA625C3F2D5001BF928 /* BesSdkConstants.h */,
				7C9AFBC425C43581001BF928 /* tools */,
				7C9AFBA525C3F2D5001BF928 /* utils */,
				7C9AFBA725C3F2D5001BF928 /* service */,
				7C9AFBAC25C3F2D5001BF928 /* connect */,
			);
			path = bessdk;
			sourceTree = "<group>";
		};
		7C9AFBA525C3F2D5001BF928 /* utils */ = {
			isa = PBXGroup;
			children = (
				7C99241025C7FEF80006BD0A /* ArrayUtil.h */,
				7C99241125C7FEF80006BD0A /* ArrayUtil.m */,
				7CD5429B25E78E1A00310A01 /* FileUtils.h */,
				7CD5429C25E78E1A00310A01 /* FileUtils.m */,
			);
			path = utils;
			sourceTree = "<group>";
		};
		7C9AFBA725C3F2D5001BF928 /* service */ = {
			isa = PBXGroup;
			children = (
				7C99247625D167290006BD0A /* ota */,
				7C9AFBCC25C53778001BF928 /* Base */,
			);
			path = service;
			sourceTree = "<group>";
		};
		7C9AFBAC25C3F2D5001BF928 /* connect */ = {
			isa = PBXGroup;
			children = (
				7C82E322286B2FC900AA377C /* BesBTConnector.h */,
				7C82E323286B2FCA00AA377C /* BesBTConnector.m */,
			);
			path = connect;
			sourceTree = "<group>";
		};
		7C9AFBC425C43581001BF928 /* tools */ = {
			isa = PBXGroup;
			children = (
				7C9AFBC525C43581001BF928 /* category */,
			);
			path = tools;
			sourceTree = "<group>";
		};
		7C9AFBC525C43581001BF928 /* category */ = {
			isa = PBXGroup;
			children = (
				7C99246A25CD4BFE0006BD0A /* NSData+XCYCoreTransfor.h */,
				7C99246B25CD4BFE0006BD0A /* NSData+XCYCoreTransfor.m */,
				7C9AFBC625C43599001BF928 /* NSString+XCYCoreOperation.h */,
				7C9AFBC725C43599001BF928 /* NSString+XCYCoreOperation.m */,
			);
			path = category;
			sourceTree = "<group>";
		};
		7C9AFBCC25C53778001BF928 /* Base */ = {
			isa = PBXGroup;
			children = (
				7C9AFBCF25C53778001BF928 /* BesServiceConfig.h */,
				7C9AFBCD25C53778001BF928 /* BesServiceConfig.m */,
				7C82E325286B300C00AA377C /* BesBaseService.h */,
				7C82E326286B300C00AA377C /* BesBaseService.m */,
				7CAC8BC22700979D0050D983 /* TOTAConnectCMD.h */,
				7CAC8BC32700979D0050D983 /* TOTAConnectCMD.m */,
				7C9AFBD925C5391F001BF928 /* BesUUID.h */,
				7C9AFBDA25C5391F001BF928 /* BesUUID.m */,
				7CAC8BC52702F6580050D983 /* sha */,
			);
			path = Base;
			sourceTree = "<group>";
		};
		7CAC8BC52702F6580050D983 /* sha */ = {
			isa = PBXGroup;
			children = (
				7CAC8BC62702F6660050D983 /* BesFunc.h */,
				7CAC8BC72702F6660050D983 /* BesFunc.m */,
				7CAC8BC9270300DF0050D983 /* Sha256.h */,
				7CAC8BCA270300DF0050D983 /* Sha256.m */,
				7CAC8BCC27030F180050D983 /* aes.h */,
				7CAC8BCD27030F180050D983 /* aes.m */,
			);
			path = sha;
			sourceTree = "<group>";
		};
		7CACE2F62888F69000805A86 /* CustomCmd */ = {
			isa = PBXGroup;
			children = (
				7CACE2F82888F69000805A86 /* CustomCmdViewController.h */,
				7CACE2FD2888F69000805A86 /* CustomCmdViewController.m */,
				7CACE2F72888F69000805A86 /* CustomHistoryList.h */,
				7CACE2FB2888F69000805A86 /* CustomHistoryList.m */,
				7CACE2FC2888F69000805A86 /* CustomCmdFileController.h */,
				7CACE2F92888F69000805A86 /* CustomCmdFileController.m */,
				7CACE2FE2888F69000805A86 /* CustomHistoryCell.h */,
				7CACE2FA2888F69000805A86 /* CustomHistoryCell.m */,
			);
			path = CustomCmd;
			sourceTree = "<group>";
		};
		7CACE3032888F72100805A86 /* CustomCmd */ = {
			isa = PBXGroup;
			children = (
				7CACE3052888F72100805A86 /* BesCustomCmdCMD.h */,
				7CACE3072888F72100805A86 /* BesCustomCmdCMD.m */,
				7CACE3042888F72100805A86 /* BesCustomCmdConstants.h */,
				7CACE3062888F72100805A86 /* BesCustomCmdConstants.m */,
			);
			path = CustomCmd;
			sourceTree = "<group>";
		};
		7CC1982627300ACC008E28C2 /* Bluetooth */ = {
			isa = PBXGroup;
			children = (
				7CC1982827300AFE008E28C2 /* Scan */,
				7CC1982727300AFE008E28C2 /* Service */,
			);
			path = Bluetooth;
			sourceTree = "<group>";
		};
		7CC1982727300AFE008E28C2 /* Service */ = {
			isa = PBXGroup;
			children = (
				7C3AB0C02A57AA5900B1B6C6 /* SmartVoice */,
				7C8F5F3129DEB8F400C74862 /* CheckCrc */,
				7C8F5F3229DEB8F400C74862 /* CommandSet */,
				7C4039F729A707D600C24BD8 /* CustomerDial */,
				7C673F0B299626CC00C310A2 /* AVSLwa */,
				7CACE3032888F72100805A86 /* CustomCmd */,
				7CC1982A27300E00008E28C2 /* EQ */,
			);
			path = Service;
			sourceTree = "<group>";
		};
		7CC1982827300AFE008E28C2 /* Scan */ = {
			isa = PBXGroup;
			children = (
				7C9AFAFB25C3B5B9001BF928 /* ScanViewController.h */,
				7C82E32A286B319500AA377C /* ScanViewController.m */,
				7CD5427A25E37D3400310A01 /* ScanTableViewCell.h */,
				7CD5427B25E37D3400310A01 /* ScanTableViewCell.m */,
			);
			path = Scan;
			sourceTree = "<group>";
		};
		7CC1982927300BB2008E28C2 /* OTA */ = {
			isa = PBXGroup;
			children = (
				7CD5427125E0E31700310A01 /* FunctionOTAViewController.h */,
				7CD5427225E0E31700310A01 /* FunctionOTAViewController.m */,
				7CD5426B25DFE49100310A01 /* OTAViewController.h */,
				7CD5426C25DFE49100310A01 /* OTAViewController.m */,
				7C441E9525ECCA7000E5FB4D /* AppSettingViewController.h */,
				7C441E9625ECCA7000E5FB4D /* AppSettingViewController.m */,
				7CD5428325E3961400310A01 /* OTAFileController.h */,
				7CD5428425E3961400310A01 /* OTAFileController.m */,
				7CD5428925E39B7300310A01 /* OTAFileTableViewCell.h */,
				7CD5428A25E39B7300310A01 /* OTAFileTableViewCell.m */,
			);
			path = OTA;
			sourceTree = "<group>";
		};
		7CC1982A27300E00008E28C2 /* EQ */ = {
			isa = PBXGroup;
			children = (
				7CC1982E27300E3F008E28C2 /* BesEQConstants.h */,
				7CC1982F27300E3F008E28C2 /* BesEQConstants.m */,
				7CC1982B27300E18008E28C2 /* BesEQCMD.h */,
				7CC1982C27300E18008E28C2 /* BesEQCMD.m */,
				7CC1983127300E5D008E28C2 /* BesEQService.h */,
				7CC1983227300E5D008E28C2 /* BesEQService.m */,
			);
			path = EQ;
			sourceTree = "<group>";
		};
		7CEAB7622995131300464592 /* AVSLwa */ = {
			isa = PBXGroup;
			children = (
				7C673F21299A332200C310A2 /* Library */,
				7CEAB7632995134A00464592 /* AvsLwaViewController.h */,
				7CEAB7642995134A00464592 /* AvsLwaViewController.m */,
				7CEAB769299517B700464592 /* AvsLwaProductCell.h */,
				7CEAB76A299517B700464592 /* AvsLwaProductCell.m */,
				7C673F1829966A1700C310A2 /* AvsLoginViewController.h */,
				7C673F1929966A1700C310A2 /* AvsLoginViewController.m */,
				7C673F152996682D00C310A2 /* AvsGuideViewController.h */,
				7C673F162996682D00C310A2 /* AvsGuideViewController.m */,
				7C673F1B29966A6200C310A2 /* AvsSelectLanguageController.h */,
				7C673F1C29966A6200C310A2 /* AvsSelectLanguageController.m */,
				7C673F1E29966A9600C310A2 /* AvsResizeViewController.h */,
				7C673F1F29966A9600C310A2 /* AvsResizeViewController.m */,
				7C673F29299B35BE00C310A2 /* AvsResizeView.h */,
				7C673F2A299B35BE00C310A2 /* AvsResizeView.m */,
				7C673F2C299B363A00C310A2 /* AvsSettingView.h */,
				7C673F2D299B363A00C310A2 /* AvsSettingView.m */,
			);
			path = AVSLwa;
			sourceTree = "<group>";
		};
		7CECC71C2845F1B100644AAF /* BleWifi */ = {
			isa = PBXGroup;
			children = (
				7C9E3CA52AE3BBB6003129C9 /* BleWifiDemoViewController.h */,
				7C9E3CA62AE3BBB7003129C9 /* BleWifiDemoViewController.m */,
				7CECC71D2845F1B100644AAF /* BleWifiViewController.h */,
				7CECC71E2845F1B100644AAF /* BleWifiViewController.m */,
				7CECC7202845F59700644AAF /* WifiUtils.h */,
				7CECC7212845F59700644AAF /* WifiUtils.m */,
			);
			path = BleWifi;
			sourceTree = "<group>";
		};
		7CF295352AF3798400744136 /* manager */ = {
			isa = PBXGroup;
			children = (
				7CF295362AF379A000744136 /* AlexaHttpManager.h */,
				7CF295372AF379A000744136 /* AlexaHttpManager.m */,
			);
			path = manager;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		7C9AFA4C25BAC840001BF928 /* BesAll */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7C9AFA7C25BAC842001BF928 /* Build configuration list for PBXNativeTarget "BesAll" */;
			buildPhases = (
				7C9AFA4925BAC840001BF928 /* Sources */,
				7C9AFA4A25BAC840001BF928 /* Frameworks */,
				7C9AFA4B25BAC840001BF928 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = BesAll;
			productName = BesAll;
			productReference = 7C9AFA4D25BAC840001BF928 /* BesAll.app */;
			productType = "com.apple.product-type.application";
		};
		7C9AFA6725BAC842001BF928 /* BesAllTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7C9AFA7F25BAC842001BF928 /* Build configuration list for PBXNativeTarget "BesAllTests" */;
			buildPhases = (
				7C9AFA6425BAC842001BF928 /* Sources */,
				7C9AFA6525BAC842001BF928 /* Frameworks */,
				7C9AFA6625BAC842001BF928 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7C9AFA6A25BAC842001BF928 /* PBXTargetDependency */,
			);
			name = BesAllTests;
			productName = BesAllTests;
			productReference = 7C9AFA6825BAC842001BF928 /* BesAllTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		7C9AFA7225BAC842001BF928 /* BesAllUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7C9AFA8225BAC842001BF928 /* Build configuration list for PBXNativeTarget "BesAllUITests" */;
			buildPhases = (
				7C9AFA6F25BAC842001BF928 /* Sources */,
				7C9AFA7025BAC842001BF928 /* Frameworks */,
				7C9AFA7125BAC842001BF928 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7C9AFA7525BAC842001BF928 /* PBXTargetDependency */,
			);
			name = BesAllUITests;
			productName = BesAllUITests;
			productReference = 7C9AFA7325BAC842001BF928 /* BesAllUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		7C9AFA4525BAC840001BF928 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				KnownAssetTags = (
					New,
				);
				LastUpgradeCheck = 1230;
				TargetAttributes = {
					7C9AFA4C25BAC840001BF928 = {
						CreatedOnToolsVersion = 12.3;
					};
					7C9AFA6725BAC842001BF928 = {
						CreatedOnToolsVersion = 12.3;
						TestTargetID = 7C9AFA4C25BAC840001BF928;
					};
					7C9AFA7225BAC842001BF928 = {
						CreatedOnToolsVersion = 12.3;
						TestTargetID = 7C9AFA4C25BAC840001BF928;
					};
				};
			};
			buildConfigurationList = 7C9AFA4825BAC840001BF928 /* Build configuration list for PBXProject "BesAll" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
				English,
			);
			mainGroup = 7C9AFA4425BAC840001BF928;
			productRefGroup = 7C9AFA4E25BAC840001BF928 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				7C9AFA4C25BAC840001BF928 /* BesAll */,
				7C9AFA6725BAC842001BF928 /* BesAllTests */,
				7C9AFA7225BAC842001BF928 /* BesAllUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		7C9AFA4B25BAC840001BF928 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7C0583AF2C928E3A002D2AF4 /* oneplusone.txt in Resources */,
				7C9AFA6025BAC841001BF928 /* LaunchScreen.storyboard in Resources */,
				7C3F7405267B771E005F5E65 /* SourceHanSansCN-Normal.otf in Resources */,
				7C9AFA5D25BAC841001BF928 /* Assets.xcassets in Resources */,
				7CABFD0E2685A88300A91310 /* SourceHanSansCN-Medium.otf in Resources */,
				7C1F57FB29E0531A00220EA2 /* speaker_test_video.mp3 in Resources */,
				7C4CA3A92689B3700071B474 /* Localizable.strings in Resources */,
				7C9AFA5B25BAC840001BF928 /* Main.storyboard in Resources */,
				7C3F7420267C4A12005F5E65 /* SourceHanSansCN-Bold.otf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7C9AFA6625BAC842001BF928 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7C9AFA7125BAC842001BF928 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		7C9AFA4925BAC840001BF928 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7CD5429425E4DA6D00310A01 /* OTASettingView.m in Sources */,
				7C7FDB042A063E1C008E65C8 /* arm_init.c in Sources */,
				7C8F5F4129DEC33600C74862 /* CommandSetCMD.m in Sources */,
				7C7FD86D2A063AD9008E65C8 /* rwpng.c in Sources */,
				7C82E329286B306300AA377C /* BesOTAService.m in Sources */,
				7C7FDB052A063E1C008E65C8 /* palette_neon_intrinsics.c in Sources */,
				7C4039F629A60FB200C24BD8 /* CustomerDialViewController.m in Sources */,
				7CECC71F2845F1B100644AAF /* BleWifiViewController.m in Sources */,
				7C7FDABD2A063D5E008E65C8 /* kmeans.c in Sources */,
				7C673F142996273700C310A2 /* AvsLwaConstants.m in Sources */,
				7C7FDAFA2A063E1C008E65C8 /* pngwio.c in Sources */,
				7C403A0729A8A00800C24BD8 /* MakeDialShowView.m in Sources */,
				7C1761E7272BD48E00F1AF99 /* FunctionToolsViewController.m in Sources */,
				7C403A0129A85B5100C24BD8 /* MakeDialViewController.m in Sources */,
				7C1761E4272BD01500F1AF99 /* FunctionChooseViewController.m in Sources */,
				7C94B606268472660057F32A /* AddFileTipsView.m in Sources */,
				7CACE3002888F69000805A86 /* CustomHistoryCell.m in Sources */,
				7C8F5F3829DEC2E300C74862 /* CheckCrcCMD.m in Sources */,
				7CEAB7652995134A00464592 /* AvsLwaViewController.m in Sources */,
				7C673F0E2996270A00C310A2 /* AvsLwaCMD.m in Sources */,
				7CAC8BCE27030F180050D983 /* aes.m in Sources */,
				7C3ACC3E2678CDDF001CCDB0 /* DeviceView.m in Sources */,
				7C7FDB0B2A063E1C008E65C8 /* pngset.c in Sources */,
				7CACE3022888F69000805A86 /* CustomCmdViewController.m in Sources */,
				7C4039FD29A7086600C24BD8 /* CustomerDialCMD.m in Sources */,
				7CEAB76B299517B700464592 /* AvsLwaProductCell.m in Sources */,
				7CD5426D25DFE49100310A01 /* OTAViewController.m in Sources */,
				7C673F2E299B363A00C310A2 /* AvsSettingView.m in Sources */,
				7C7FDAFC2A063E1C008E65C8 /* pngwrite.c in Sources */,
				7C7FDB012A063E1C008E65C8 /* pngread.c in Sources */,
				7C94B6002683540E0057F32A /* BesLoadingView.m in Sources */,
				7CF295382AF379A000744136 /* AlexaHttpManager.m in Sources */,
				7C9AFBD125C53778001BF928 /* BesServiceConfig.m in Sources */,
				7C673F1A29966A1700C310A2 /* AvsLoginViewController.m in Sources */,
				7CACE2FF2888F69000805A86 /* CustomCmdFileController.m in Sources */,
				7C673F2B299B35BE00C310A2 /* AvsResizeView.m in Sources */,
				7C99246C25CD4BFE0006BD0A /* NSData+XCYCoreTransfor.m in Sources */,
				7C7FDB062A063E1C008E65C8 /* pngmem.c in Sources */,
				7CD5428525E3961400310A01 /* OTAFileController.m in Sources */,
				7C7C2BBF2AEA59F200C49657 /* OpusCodec.m in Sources */,
				7C3F7415267B7C2C005F5E65 /* BesFont.m in Sources */,
				7CD5429D25E78E1A00310A01 /* FileUtils.m in Sources */,
				7C7FDB032A063E1C008E65C8 /* filter_neon_intrinsics.c in Sources */,
				7C7FDAF92A063E1C008E65C8 /* pngerror.c in Sources */,
				7C7FD86E2A063AD9008E65C8 /* pngquant_opts.c in Sources */,
				7C4CA6DB2672090700F8BE9F /* BesBaseViewController.m in Sources */,
				7C99247F25D167290006BD0A /* BesOTASetting.m in Sources */,
				7C8F5F3529DEC2CB00C74862 /* CheckCrcService.m in Sources */,
				7CACE3092888F72100805A86 /* BesCustomCmdCMD.m in Sources */,
				7C99241225C7FEF80006BD0A /* ArrayUtil.m in Sources */,
				7C673F2029966A9600C310A2 /* AvsResizeViewController.m in Sources */,
				7C3AB0BF2A57A91400B1B6C6 /* SmartVoiceViewController.m in Sources */,
				7CC19825272FD3C3008E28C2 /* EQBeans.m in Sources */,
				7C7FDB072A063E1C008E65C8 /* pngget.c in Sources */,
				7C99248025D167290006BD0A /* BesOTACMD.m in Sources */,
				7C94B5F42682072C0057F32A /* OtaingView.m in Sources */,
				7C7FDAB52A063D5E008E65C8 /* libimagequant.c in Sources */,
				7C1761EB272BD71500F1AF99 /* EQViewController.m in Sources */,
				7C9AFBDB25C5391F001BF928 /* BesUUID.m in Sources */,
				7C7FDAB92A063D5E008E65C8 /* pam.c in Sources */,
				7C99248125D167290006BD0A /* BesOTAConstants.m in Sources */,
				7C9AFBC825C43599001BF928 /* NSString+XCYCoreOperation.m in Sources */,
				7C7FD86C2A063AD9008E65C8 /* pngquant.c in Sources */,
				7C4039FA29A7080600C24BD8 /* CustomerDialService.m in Sources */,
				7C7FDAFE2A063E1C008E65C8 /* pngwutil.c in Sources */,
				7C7FDAFB2A063E1C008E65C8 /* pngtrans.c in Sources */,
				7C7FDAB72A063D5E008E65C8 /* mediancut.c in Sources */,
				7C7FDAF72A063E1C008E65C8 /* pngtest.c in Sources */,
				7CC1983327300E5D008E28C2 /* BesEQService.m in Sources */,
				7CC1983627301164008E28C2 /* ConnectDeviceView.m in Sources */,
				7C4331CE29A8B58700686CF8 /* MakeDialCell.m in Sources */,
				7C7FDAF82A063E1C008E65C8 /* pngrio.c in Sources */,
				7C673F1D29966A6200C310A2 /* AvsSelectLanguageController.m in Sources */,
				7C7FDB002A063E1C008E65C8 /* pngpread.c in Sources */,
				7C3ACC4A267AFA91001CCDB0 /* BaseOptionView.m in Sources */,
				7C8F5F3E29DEC31C00C74862 /* CommandSetService.m in Sources */,
				7C8F5F3B29DEC2FC00C74862 /* CheckCrcConstants.m in Sources */,
				7C3F74352681C083005F5E65 /* CircleAnimationView.m in Sources */,
				7C673F112996271C00C310A2 /* AvsLwaService.m in Sources */,
				7C7FDB082A063E1C008E65C8 /* png.c in Sources */,
				7CACE3012888F69000805A86 /* CustomHistoryList.m in Sources */,
				7CAC8BC42700979D0050D983 /* TOTAConnectCMD.m in Sources */,
				7CD5427C25E37D3400310A01 /* ScanTableViewCell.m in Sources */,
				7CAC8BC82702F6660050D983 /* BesFunc.m in Sources */,
				7CABFD092685829F00A91310 /* CustomAlert.m in Sources */,
				7C4331D729ACA10E00686CF8 /* ShadeView.m in Sources */,
				7C9AFA5225BAC840001BF928 /* AppDelegate.m in Sources */,
				7C7FDABE2A063D5E008E65C8 /* nearest.c in Sources */,
				7C9AFAB225C2B3B5001BF928 /* UIView+Ext.m in Sources */,
				7C7FDAFF2A063E1C008E65C8 /* pngwtran.c in Sources */,
				7C8F5F2D29DABA0800C74862 /* CommandSetViewController.m in Sources */,
				7C3AB0C32A57AA9100B1B6C6 /* SmartVoiceService.m in Sources */,
				7C8F5F3029DABA1B00C74862 /* CheckCrcViewController.m in Sources */,
				7C7FDAB82A063D5E008E65C8 /* mempool.c in Sources */,
				7C3AB0C92A57AABD00B1B6C6 /* SmartVoiceConstants.m in Sources */,
				7C4CA6E9267333EA00F8BE9F /* BesColor.m in Sources */,
				7C441E9725ECCA7000E5FB4D /* AppSettingViewController.m in Sources */,
				7C8F5F4429DEC39000C74862 /* CommandSetConstants.m in Sources */,
				7C9AFA6325BAC841001BF928 /* main.m in Sources */,
				7C7FDABB2A063D5E008E65C8 /* blur.c in Sources */,
				7C4331D129A8EE8100686CF8 /* CustomerDialBean.m in Sources */,
				7C3F741B267B7F5F005F5E65 /* VersionPathView.m in Sources */,
				7C9E3CA72AE3BBB7003129C9 /* BleWifiDemoViewController.m in Sources */,
				7C3A6F4D26738FDA0075ED04 /* HomeViewController.m in Sources */,
				7C7FDABC2A063D5E008E65C8 /* remap.c in Sources */,
				7C1761EF272BEB7D00F1AF99 /* EQTableViewCell.m in Sources */,
				7C7FDB0A2A063E1C008E65C8 /* pngrutil.c in Sources */,
				7C632ED528B7A9FC002203BF /* VersionPathViewCell.m in Sources */,
				7C82E327286B300C00AA377C /* BesBaseService.m in Sources */,
				7C7FDB092A063E1C008E65C8 /* pngrtran.c in Sources */,
				7C82E32B286B319500AA377C /* ScanViewController.m in Sources */,
				7CC1982D27300E18008E28C2 /* BesEQCMD.m in Sources */,
				7C4331D429AC7E6D00686CF8 /* CropPhotoViewController.m in Sources */,
				7C3ACC442679D22C001CCDB0 /* BesButton.m in Sources */,
				7C673F172996682D00C310A2 /* AvsGuideViewController.m in Sources */,
				7CD5428B25E39B7300310A01 /* OTAFileTableViewCell.m in Sources */,
				7CECC7222845F59700644AAF /* WifiUtils.m in Sources */,
				7C82E324286B2FCA00AA377C /* BesBTConnector.m in Sources */,
				7CC1983027300E3F008E28C2 /* BesEQConstants.m in Sources */,
				7C3AB0C62A57AAA200B1B6C6 /* SmartVoiceCMD.m in Sources */,
				7CD5427325E0E31700310A01 /* FunctionOTAViewController.m in Sources */,
				7CACE3082888F72100805A86 /* BesCustomCmdConstants.m in Sources */,
				7C9AFA5525BAC840001BF928 /* SceneDelegate.m in Sources */,
				7C4331DA29ADA20800686CF8 /* UIImage+fixOrientation.m in Sources */,
				7C3F742C2681B50C005F5E65 /* BesAlert.m in Sources */,
				7CAC8BCB270300DF0050D983 /* Sha256.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7C9AFA6425BAC842001BF928 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7C9AFA6D25BAC842001BF928 /* BesAllTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7C9AFA6F25BAC842001BF928 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7C9AFA7825BAC842001BF928 /* BesAllUITests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		7C9AFA6A25BAC842001BF928 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7C9AFA4C25BAC840001BF928 /* BesAll */;
			targetProxy = 7C9AFA6925BAC842001BF928 /* PBXContainerItemProxy */;
		};
		7C9AFA7525BAC842001BF928 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7C9AFA4C25BAC840001BF928 /* BesAll */;
			targetProxy = 7C9AFA7425BAC842001BF928 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		7C4CA3AB2689B3700071B474 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				7C4CA3AA2689B3700071B474 /* zh-Hans */,
				7C4CA3AF2689B37B0071B474 /* en */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
		7C9AFA5925BAC840001BF928 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				7C9AFA5A25BAC840001BF928 /* Base */,
				7C4CA3862689AEDE0071B474 /* zh-Hans */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		7C9AFA5E25BAC841001BF928 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				7C9AFA5F25BAC841001BF928 /* Base */,
				7C4CA3872689AEDE0071B474 /* zh-Hans */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		7C9AFA7A25BAC842001BF928 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		7C9AFA7B25BAC842001BF928 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		7C9AFA7D25BAC842001BF928 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CODE_SIGN_ENTITLEMENTS = BesAll/BesAll.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = T7XKZVJT62;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/BesAll/VC/Tools/AVSLwa/Library",
				);
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "$(SRCROOT)/BesAll/PrefixHeader.pch";
				INFOPLIST_FILE = BesAll/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/BesAll/VC/Tools/CustomerDial/Index8Sdk",
					"$(PROJECT_DIR)/BesAll/Bluetooth/Service/SmartVoice/lib",
					"$(PROJECT_DIR)/BesAll/Tools/Opus/lib",
				);
				MARKETING_VERSION = 2025061201;
				OTHER_LDFLAGS = "";
				PRODUCT_BUNDLE_IDENTIFIER = com.bes.BesAll.HB;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		7C9AFA7E25BAC842001BF928 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CODE_SIGN_ENTITLEMENTS = BesAll/BesAll.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = T7XKZVJT62;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/BesAll/VC/Tools/AVSLwa/Library",
				);
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "$(SRCROOT)/BesAll/PrefixHeader.pch";
				INFOPLIST_FILE = BesAll/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/BesAll/VC/Tools/CustomerDial/Index8Sdk",
					"$(PROJECT_DIR)/BesAll/Bluetooth/Service/SmartVoice/lib",
					"$(PROJECT_DIR)/BesAll/Tools/Opus/lib",
				);
				MARKETING_VERSION = 2025061201;
				OTHER_LDFLAGS = "";
				PRODUCT_BUNDLE_IDENTIFIER = com.bes.BesAll.HB;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		7C9AFA8025BAC842001BF928 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 9TM42DC5A7;
				INFOPLIST_FILE = BesAllTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.3;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.bes.BesAllTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/BesAll.app/BesAll";
			};
			name = Debug;
		};
		7C9AFA8125BAC842001BF928 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 9TM42DC5A7;
				INFOPLIST_FILE = BesAllTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.3;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.bes.BesAllTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/BesAll.app/BesAll";
			};
			name = Release;
		};
		7C9AFA8325BAC842001BF928 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 9TM42DC5A7;
				INFOPLIST_FILE = BesAllUITests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.bes.BesAllUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = BesAll;
			};
			name = Debug;
		};
		7C9AFA8425BAC842001BF928 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 9TM42DC5A7;
				INFOPLIST_FILE = BesAllUITests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.bes.BesAllUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = BesAll;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7C9AFA4825BAC840001BF928 /* Build configuration list for PBXProject "BesAll" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7C9AFA7A25BAC842001BF928 /* Debug */,
				7C9AFA7B25BAC842001BF928 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7C9AFA7C25BAC842001BF928 /* Build configuration list for PBXNativeTarget "BesAll" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7C9AFA7D25BAC842001BF928 /* Debug */,
				7C9AFA7E25BAC842001BF928 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7C9AFA7F25BAC842001BF928 /* Build configuration list for PBXNativeTarget "BesAllTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7C9AFA8025BAC842001BF928 /* Debug */,
				7C9AFA8125BAC842001BF928 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7C9AFA8225BAC842001BF928 /* Build configuration list for PBXNativeTarget "BesAllUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7C9AFA8325BAC842001BF928 /* Debug */,
				7C9AFA8425BAC842001BF928 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 7C9AFA4525BAC840001BF928 /* Project object */;
}
