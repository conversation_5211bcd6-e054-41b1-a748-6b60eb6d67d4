<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>APIKey</key>
	<string>eyJhbGciOiJSU0EtU0hBMjU2IiwidmVyIjoiMSJ9.eyJ2ZXIiOiIzIiwiZW5kcG9pbnRzIjp7ImF1dGh6IjoiaHR0cHM6Ly93d3cuYW1hem9uLmNvbS9hcC9vYSIsInRva2VuRXhjaGFuZ2UiOiJodHRwczovL2FwaS5hbWF6b24uY29tL2F1dGgvbzIvdG9rZW4ifSwiY2xpZW50SWQiOiJhbXpuMS5hcHBsaWNhdGlvbi1vYTItY2xpZW50LjQ1ZWIyYWNhYjdlMTQzZTc5ZjBhZmJhNjIzM2IxODUwIiwiYXBwRmFtaWx5SWQiOiJhbXpuMS5hcHBsaWNhdGlvbi40MjEyM2I1NjE1OTU0MWYwOTYxZDEwMGE5YmY5NGNiYSIsImJ1bmRsZVNlZWRJZCI6ImNvbS5iZXMuQmVzQWxsIiwiYnVuZGxlSWQiOiJjb20uYmVzLkJlc0FsbCIsImlzcyI6IkFtYXpvbiIsInR5cGUiOiJBUElLZXkiLCJhcHBWYXJpYW50SWQiOiJhbXpuMS5hcHBsaWNhdGlvbi1jbGllbnQuYTZlZDI2ZTdhZjNlNDk3OGFlMzViZmU5OTI5Njk4MzciLCJ0cnVzdFBvb2wiOm51bGwsImFwcElkIjoiYW16bjEuYXBwbGljYXRpb24tY2xpZW50LmE2ZWQyNmU3YWYzZTQ5NzhhZTM1YmZlOTkyOTY5ODM3IiwiaWQiOiIyOGZhMzAxNy1iNTQ5LTRjM2ItYjdkZC0zNjkyYTM5NGExZmQiLCJpYXQiOiIxNjc2MjgxODk0ODY5In0=.pZ5+1kDgpw9AMw9JzJzi8VmB1q5zvipAFtehAk2xxUCfJOT3qlqg/2UFx1KpYqv5y/0sDktIayznBsFalUmp8RWMXBo8GRXCWzkuyFdsh0QwiAxXPU/tsnMKJFEa4UmhUCK0/X2hykvdxi1vML8mNkiJM4aVdqS5qhg8DyriaWuJJKgPX1AA5/2HYFrWyQl9YAp/b3sWikK8bs0BNKbPL17i6fW0+sMVTgevWjBORgyPtAql/vyi/LgtzBKKv2/iB7cfqJit6H2GW4yhKv3SE6NPhQOc9cxWzSvFkctlEWCP8gBavWK06nSRw/EhxP9tYVPKQy8I9PY094BRVtog8Q==</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Bestechnic</string>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeName</key>
			<string>bin</string>
			<key>LSHandlerRank</key>
			<string>Default</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.data</string>
				<string>public.archive</string>
			</array>
		</dict>
	</array>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>$(PRODUCT_BUNDLE_PACKAGE_TYPE)</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.bes.BesAll</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>amzn-com.bes.BesAll</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSHasLocalizedDisplayName</key>
	<true/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>This app uses Bluetooth to connect with your device</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>This app uses Bluetooth to connect with your device</string>
	<key>NSCameraUsageDescription</key>
	<string>Whether to allow the use of camera to get the required images</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>To connect your device to the network to get wifi, you must enable location access</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>To connect your device to the network to get wifi, you must enable location access</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>You must enable the location permission to obtain wifi information</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app uses Microphone to test your mic</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Whether to allow the use of albums to get the required images</string>
	<key>UIAppFonts</key>
	<array>
		<string>SourceHanSansCN-Bold.otf</string>
		<string>SourceHanSansCN-Medium.otf</string>
		<string>SourceHanSansCN-Normal.otf</string>
	</array>
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<false/>
		<key>UISceneConfigurations</key>
		<dict>
			<key>UIWindowSceneSessionRoleApplication</key>
			<array>
				<dict>
					<key>UISceneConfigurationName</key>
					<string>Default Configuration</string>
					<key>UISceneDelegateClassName</key>
					<string>SceneDelegate</string>
					<key>UISceneStoryboardFile</key>
					<string>Main</string>
				</dict>
			</array>
		</dict>
	</dict>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportsDocumentBrowser</key>
	<true/>
</dict>
</plist>
