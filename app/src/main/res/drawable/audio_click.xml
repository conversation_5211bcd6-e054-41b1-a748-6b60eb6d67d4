<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 获得焦点但未按下时的背景图片 -->
    <item android:drawable="@drawable/chips_tools_icon_stop" android:state_enabled="true" android:state_focused="true" android:state_pressed="false"/>
    <!-- 按下时的背景图片 -->
    <item android:drawable="@drawable/chips_tools_icon_stop" android:state_enabled="true" android:state_pressed="true"/>
    <!-- 按下时的背景图片 -->
    <item android:drawable="@drawable/chips_tools_icon_stop" android:state_checked="true" android:state_enabled="true"/>
    <!-- 默认时的背景图片 -->
    <item android:drawable="@drawable/chips_tools_icon_sound_play"/>

</selector>

