<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- most important is order of layers -->

    <!-- Bottom right side 2dp Shadow -->
    <item
        android:width="66dp" android:height="34dp"
        android:left="2dp"
        android:top="2dp"
        android:right="2dp"
        android:bottom="2dp">

        <shape android:shape="rectangle">
            <corners android:radius="4dp"/>
            <solid android:color="#3D0A2463" />
        </shape>
    </item>

<!--     Bottom 2dp Shadow-->
    <item
        android:width="66dp" android:height="34dp">
        <shape android:shape="rectangle">
            <corners android:radius="4dp"/>
            <solid android:color="#3D0A2463" />
        </shape>
    </item>

    <!-- White Top color -->
    <item android:bottom="2dp" android:right="2dp" android:width="66dp" android:height="34dp">
        <shape android:shape="rectangle">
            <corners android:radius="4dp"/>
            <solid android:color="#FFFFFF" />
            <stroke android:width="0.5dip" android:color="#3D0A2463"/>
        </shape>
    </item>
</layer-list>
