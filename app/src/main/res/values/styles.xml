<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:fontFamily">@font/sanshan_normal</item>
        <item name="android:includeFontPadding">false</item>
        <!--        <item name="android:typeface">fonts/SourceHanSansCN-Normal.otf</item>-->
<!--        <item name="android:windowTranslucentNavigation">true</item>-->
<!--        <item name="android:windowLightNavigationBar">true</item>-->
<!--        <item name="android:background">@drawable/base_bg</item>-->
        <item name="android:windowTranslucentStatus">false</item>
    </style>

<!--    <style name="BaseAppTheme" parent="AppTheme">-->

<!--        <item name="android:statusBarColor">@color/white</item>-->
        //系统status

<!--        <item name="android:windowLightStatusBar">true</item>-->


<!--        <item name="android:actionBarStyle">@style/ActionBarStyle</item>-->
<!--        <item name="actionBarItemBackground">@drawable/nav_bar_bg</item>-->
<!--        <item name="statusBarBackground">@drawable/nav_bar_bg</item>-->
<!--        <item name="android:textColor">@color/black</item>-->
<!--        <item name="android:textColorPrimary">#fff</item>-->
<!--        <item name="android:textColorSecondary">#ccc</item>-->
<!--        <item name="android:background">@color/white</item>-->
<!--        <item name="windowNoTitle">true</item>-->

<!--    </style>-->

    <style name="titlebartheme" parent="@style/AppTheme">
        <item name="android:textColorPrimary">@color/black</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/color_transparent</item>
        <item name="colorAccent">@color/colorPrimary</item>
<!--        <item name="android:windowTranslucentStatus">false</item>-->
<!--        <item name="android:fitsSystemWindows">true</item>-->
<!--        <item name="android:navigationBarColor">@color/color_transparent</item>-->
<!--        <item name="background">@drawable/rectangle</item>-->

    </style>

    <style name="CustomRadioButton" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:button">@drawable/radiobutton_click</item>
    </style>
    <style name="titlebartheme.ToolBar" parent="titlebartheme">
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.PopupMenu.Overflow</item>>
        <!-- <item name="actionOverflowMenuStyle">-->
    </style>

    <style name="Widget.AppCompat.PopupMenu.Overflow" parem="Widget.AppCompat.PopupMenu.Overflow">

        <item name="overlapAnchor">false</item>
        <!-- 上面那一行代码的意思是指的是其位于toolbar下方-->
    </style>
<!--    <style name="ActionBarStyle"   parent="android:Widget.Holo.Light.ActionBar.Solid.Inverse">-->
<!--        <item name="android:background">@drawable/nav_bar_bg</item>-->
<!--    </style>-->
<!--    <style name="Theme.Transparent" parent="ThemeOverlay.AppCompat.ActionBar">-->
<!--        <item name="android:windowIsTranslucent">true</item>-->
<!--        <item name="android:windowBackground">@android:color/transparent</item>-->
<!--        <item name="android:windowContentOverlay">@null</item>-->
<!--        <item name="android:windowNoTitle">true</item>-->
<!--        <item name="android:windowIsFloating">true</item>-->
<!--        <item name="android:backgroundDimEnabled">true</item>-->
<!--    </style>-->


<!--    <style name="AppTheme.NoActionBar">-->
<!--        <item name="windowActionBar">false</item>-->
<!--        <item name="windowNoTitle">true</item>-->
<!--    </style>-->

<!--    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />-->

<!--    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />-->

</resources>
