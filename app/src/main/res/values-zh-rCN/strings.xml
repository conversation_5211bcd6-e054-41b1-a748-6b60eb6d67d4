<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Bestechnic</string>
    <string name="activity_ble_ota">BLE OTA</string>
    <string name="activity_spp_ota">SPP OTA</string>
    <string name="activity_ble_ota_v1">BLE OTA 1.0</string>
    <string name="activity_spp_ota_v1">SPP OTA 1.0</string>
    <string name="activity_ble_ota_v2">BLE OTA 2.0</string>
    <string name="activity_spp_ota_v2">SPP OTA 2.0</string>
    <string name="activity_main">BES Toolbox</string>
    <string name="activity_bluetooth_scan">BLE SCAN</string>
    <string name="activity_classics_devices_scan">Classics BT SCAN</string>
    <string name="activity_USB_scan">USB SCAN</string>
    <string name="privacy_policy"> 《服务协议|隐私政策》</string>
    <string name="agree">同意</string>
    <string name="disagree">不同意</string>
    <string name="privacy_guide"> 隐私政策: </string>
    <string name="agreement">
        Bestechnic隐私政策和使用条款
        \n发布日期：2022/10/10
        \n\n“Bestechnic”是由恒玄科技（上海）股份有限公司（以下简称“我们”）提供的蓝牙芯片测试工具。您的信任对我们非常重要，我们深知个人信息对您的重要性，我们将按法律法规要求，采取相应安全保护措施，尽力保护您的个人信息安全可控。有鉴于此，我们制定本隐私权政策（以下简称“本政策”）并提醒您：
        \n本应用尊重并保护所有使用服务用户的个人隐私权。为了给您提供更准确、更有个性化的服务，本应用会按照本隐私权政策的规定使用和披露您的个人信息。但本应用将以高度的勤勉、审慎义务对待这些信息，不会将信息发送给第三方，信息只可以在本地查看，APP内部不设置网络请求，如后续有需要设置将在协议中注明。请你务必审慎阅读、充分理解"服务协议"和隐私政策"各条款。包括但不限于:为了向你提供蓝牙链接、内容分享等服务，我们需要收集你的设备信息、操作日志等个人信息。你可以随时在"设置"中查看、变更、删除个人信息并管理你的授权。
        \n\n第一部分 定义
        \n\n个人信息是指以电子或者其他方式记录的能够单独或者与其他信息结合识别特定自然人身份或者反映特定自然人活动情况的各种信息。
        \n\n个人信息删除：指在实现日常业务功能所涉及的系统中去除个人信息的行为，使其保持不可被检索、访问的状态。
        \n\n我们收集数据是根据您与我们的互动和您所做出的选择，包括您的隐私设置以及您使用的产品和功能。
        \n\n第二部分 隐私权限
        \n\n本部分将帮助您了解以下内容：
        \n1.	我们如何收集和使用您的信息\n2.	我们如何共享、转让、公开披露您的信息\n3.	我们如何保护您的个人信息\n4.	您如何管理您的个人信息\n5.	本政策如何更新\n6.	如何联系我们
        \n\n一、 我们如何收集和使用您的信息
        \n\n在您使用我们的产品和/或服务时，我们需要/可能需要收集和使用的您的个人信息：\n为实现向您提供我们产品和/或服务的基本功能，您须授权我们收集、使用的必要的信息。如您拒绝提供相应信息，您将无法正常使用我们的产品和/或服务；
        \n您理解并同意：\n1、我们致力于打造多样的产品和服务以满足您的需求。因我们向您提供的产品和服务种类众多，且不同用户选择使用的具体产品/服务范围存在差异，相应的，基本/附加功能及收集使用的个人信息类型、范围等会有所区别，请以具体的产品/服务功能为准；
        \n2、为给您带来更好的产品和服务体验，我们在持续努力改进我们的技术，随之我们可能会不时推出新的或优化后的功能，可能需要收集、使用新的个人信息或变更个人信息使用目的或方式。对此，我们将通过更新本政策、弹窗、页面提示等方式另行向您说明对应信息的收集目的、范围及使用方式，并为您提供自主选择同意的方式，且在征得您明示同意后收集、使用。在此过程中，如果您有任何疑问、意见或建议的，您可通过我们提供的联系方式与我们联系，我们会尽快为您做出解答。
        \n\n二、我们如何共享，转让，公开披露您的个人信息
        \n\n我们不会与恒选科技服务提供者以外的公司、组织和个人共享您的个人信息，以下情形中，共享、转让、公开披露您的个人信息无需事先征得您的授权同意：
        \n1、与国家安全、国防安全有关的。\n2、与公共安全、公共卫生、重大公共利益有关的。\n3、与犯罪侦查、起诉、审判和判决执行等有关的。\n4、出于维护您或其他个人的生命、财产等重大合法权益但又很难得到本人同意的。\n5、您自行向社会公众公开的个人信息。\n6、从合法公开披露的信息中收集个人信息的，如合法的新闻报道、政府信息公开等渠道。
        \n\n三、我们如何保护您的个人信息
        \n\n为保障您的信息安全，我们已采取符合业界标准、合理可行的安全防护措施保护您提供的个人信息安全，防止个人信息遭到未经授权访问、公开披露、使用、修改、损坏或丢失。
        \n\n四、您如何管理您的个人信息
        \n\n您有权查询、更正或补充您的信息。您可以通过以下方式自行进行：您可以在app页面中通过设置界面查看和编辑，这些数据仅存在本地文件内，不会通过网络散播，用户可以自由操作数据的删除和导出。
        \n\n五、本政策如何更新\n\n我们的隐私权政策可能变更。未经您明确同意我们不会限制您按照本政策所应享有的权利。对于重大变更，我们还会提供更为显著的通知（包括通过网站或客户端首页公示的方式进行通知甚至向您提供弹窗提示）。
        \n\n六、如何联系我们\n\n您可以通过以下方式与我们联系，我们将在15个工作日内回复您的请求：\n我们还设立了个人信息保护专职部门，办公地址：上海市浦东金科路2889弄长泰广场B座201室（201203）
        \n\n第三部分 应用权限
        \n\n1.	在使用本app时，我们可能会申请系统设备权限收集设备信息、日志信息、用于数据分析，并申请存储权限，用于下载以及缓存相关文件。
        \n\n2.	电话权限：电话权限仅用作读取手机状态，作为蓝牙链接状态下使用。不收集（包括但不限于）通话内容，电话号码等个人信息
        \n\n3.	位置权限：位置权限将仅用于对于蓝牙设备的发现和连接以及相关服务，不收集（包括但不限于）个人定位的获取。
        \n\n4.	存储权限：存储权限将用于存储必要的应用产出数据，这些数据仅存在本地文件内，不会通过网络散播，用户可以自由操作数据的删除和导出。
        \n\n5.	使用：本应用基于恒玄科技生产系列芯片使用，不适用于所有蓝牙设备，使用时请注意相关功能和芯片的配合。
        \n\n6.	上述权限以及摄像头、相册、存储空间、GPS等敏感权限不会默认或强制开启收取信息。
        \n\n本隐私权政策如何更新 我们可能适时会对本隐私权政策进行调整或变更，本隐私权政策的任何更新将以标注更新时间的方式公布在我们网站上，除法律法规或监管规定另有强制性规定外，经调整或变更的内容一经通知或公布后的7日后生效。如您在隐私权政策调整或变更后继续使用我们提供的任一服务或访问我们相关网站的，我们相信这代表您已充分阅读、理解并接受修改后的隐私权政策并受其约束。
    </string>
    <string name="permissions_guide">为了记录在使用功能时的输出日志，以便后续分析问题，我们需要申请您设备的文件存储权限。</string>

    <!--  ota -->
    <string name="ota_error_config_before_start">升级前配置错误</string>
    <string name="ota_error_choose_side">选择升级左右耳错误</string>
    <string name="ota_error_config_begin_start">升级配置错误</string>
    <string name="ota_error_percent_crc">segment crc 校验错误</string>
    <string name="ota_error_whole_crc">整包 crc 校验错误</string>
    <string name="ota_error_whole_image">整包确认校验错误</string>
    <string name="ota_error_set_user">设置ota user错误</string>
    <string name="ota_error_get_user_timeout">获取ota user版本超时错误</string>
    <string name="ota_error_get_version_timeout">获取固件版本号超时错误</string>
    <string name="current_device">当前选择设备</string>
    <string name="change_device">选择Spp设备</string>
    <string name="change_device_ble">选择Ble设备</string>
    <string name="change_device_tota_spp">选择 TOTA Spp 设备</string>
    <string name="current_ota_file">当前升级文件</string>
    <string name="pick_ota_file_path">请选择正确bin文件路径</string>
    <string name="pick_ota_file">选择升级文件</string>
    <string name="pick_ota_file_left">选择左耳升级文件</string>
    <string name="pick_ota_file_right">选择右耳升级文件</string>
    <string name="current_ota_progress">当前升级进度:</string>
    <string name="start_ota">开始升级</string>
    <string name="ota_file_tips">由于android存储权限变更，部分手机app只能访问内部文件，点击确定切换到内部访问， 点击取消切换到外部访问</string>
    <string name="ota">OTA</string>
    <string name="ota_exit_tips">OTA升级中, 是否退出?</string>
    <string name="cancel">取消</string>
    <string name="connecting_device">正在连接设备...</string>
    <string name="ota_successfully">OTA 成功</string>
    <string name="ota_get_version_success">获取OTA协议版本号成功</string>
    <string name="ota_set_user_success">设置uesr成功</string>
    <string name="ota_set_upgrade_type_slow">普通模式</string>
    <string name="ota_set_upgrade_type_fast">快速模式</string>
    <string name="ota_no_breakpoint_resume">非断点续传</string>
    <string name="ota_breakpoint_resume">断点检查成功</string>
    <string name="ota_start">ota 配置成功, 开始ota</string>
    <string name="ota_disconnect_success">收到 95</string>
    <string name="ota_whole_crc_success">整包crc校验成功</string>
    <string name="ota_percent_crc_error_resend">segment crc校验失败，重新发送</string>
    <string name="ota_upgrade_type">升级方式</string>
    <string name="ota_is_bth">是否是BTH的bin</string>

    <string name="hex_prefix">0x</string>
    <string name="ota_config_yes">Y</string>
    <string name="ota_config_no">N</string>
    <string name="ota_config_clear_user_data_tip">清除用户数据?</string>
    <string name="ota_config_update_bt_addr_tip">更改 BT 地址?</string>
    <string name="ota_config_update_ble_name_tip">更改 BLE 名字?</string>
    <string name="ota_config_update_ble_addr_tip">更改 BLE 地址?</string>
    <string name="ota_config_update_bt_name_tip">更改 BT 名字?</string>
    <string name="ok">OK</string>
    <string name="invalid_bt_address">无效 BT 地址!</string>
    <string name="invalid_ble_address">无效 BLE 地址!</string>
    <string name="invalid_bt_name">无效 BT 名字!</string>
    <string name="invalid_ble_name">无效 BLE 名字!</string>
    <string name="connect_device">连接设备</string>
    <string name="disconnect_device">断开设备</string>
    <string name="current_version">当前版本号</string>
    <string name="old_ota_ways_version_tips">正在获取当前固件版本号...</string>

    <string name="left_earbud_only">只升级左耳</string>
    <string name="right_earbud_only">只升级右耳</string>
    <string name="both_earbuds_in_one_bin">使用同一文件同时升级左右耳</string>
    <string name="both_earbuds_in_two_bins">使用不同文件同时升级左右耳</string>
    <string name="steps_btn">点我查看文件存储步骤</string>
    <string name="steps1">步骤 1</string>
    <string name="steps2">步骤 2</string>
    <string name="steps3">步骤 3</string>
    <string name="steps1_text">将手机连接电脑(由于Android访问权限变化，文件夹在手机里不可见)，并选择传输文件</string>
    <string name="steps2_text">在电脑上找到手机内部存储，找到 Android -> data -> com.bes.besall -> files -> bin 文件夹</string>
    <string name="steps3_text">将要传输的文件复制到bin文件夹下, 再次进入本页面即可使用文件</string>

    <!--  bluetooth -->
    <string name="yes">确定</string>
    <string name="no">取消</string>
    <string name="connect_failed">连接断开</string>
    <string name="connect_success">连接成功</string>
    <string name="exit_hint">再次点击退出应用</string>

    <!--  audio dump -->
    <string name="audio_paused">停止&#8230;</string>
    <string name="audio_playing">播放&#8230;</string>
    <string name="audio_dump_start">Start Audio Dump</string>
    <string name="audio_dump_stop">Stop Audio Dump</string>
    <string name="audio_file_select">选择文件</string>
    <string name="audio_insert_data_form">插入数据格式</string>
    <string name="audio_file_type">文件类型</string>

    <!--  scan -->
    <string name="scan">scan</string>
    <string name="account_input_tips">please enter command</string>
    <string name="scanTips">名字为绿色的设备是当前app已连接设备, 可能是ble或者是SPP; 如果已连接设备中的服务特征和当前功能服务特征相同, 可以跳过连接过程, 直接使用。</string>

    <!--    chipstool-->
    <string name="rssi">RSSI</string>
    <string name="rssi_extend">RSSI EXTEND</string>
    <string name="audio_dump">AUDIO DUMP</string>
    <string name="log_dump">LOG DUMP</string>
    <string name="crash_dump">CRASH DUMP</string>
    <string name="customer_cmd">CUSTOM COMMAND</string>

    <!--crashdump-->
    <string name="ftp_upload">FTP UPLOAD</string>
    <string name="crash_dump_start">Start Crash Dump</string>
    <string name="crash_dump_stop">Stop Crash Dump</string>

    <!--    RSSI-->
    <string name="RSSI_STOP">RSSI STOP</string>
    <string name="NRssi_Protocol">使用 Protocol (0x6306)</string>
    <string name="ORssi_Protocol">使用 Protocol (0x6309)</string>

    <!--    CUSTOMER CMD-->
    <string name="EDIT">编辑</string>
    <string name="filepath">文件路径</string>
    <string name="customcmd">自定义命令</string>
    <string name="importfile">导入文件</string>
    <string name="exportfile">导出文件</string>

    <!--    Log Dump-->
    <string name="startdownload">开始下载</string>
    <string name="downloading">下载中...</string>
    <string name="downloadcomplete">下载完成</string>
    <string name="totalprogress">总进度:</string>

    <string name="last_device">上次选择设备</string>
    <string name="ack_exist_no">无</string>
    <string name="ack_exist_yes">有</string>
    <string name="is_bth_yes">是</string>
    <string name="is_bth_no">否</string>
    <string name="pleaseSelectDevice">请选择设备</string>
    <string name="pleaseSelectOTAFile">请选择文件</string>
    <string name="pleaseCheckTheConnection">请检查连接</string>
    <string name="choose">选择</string>
    <string name="findOtaFileTips">找不到bin文件请点我查看提示</string>
    <string name="success">完成</string>
    <string name="back">返回</string>
    <string name="upgradeUnderWay">正在升级中...</string>
    <string name="OTASuccess">升级成功</string>
    <string name="OTAFail">升级失败</string>
    <string name="settings">设置</string>
    <string name="saveLog">保存Log</string>
    <string name="theLocalLog">本地Log</string>
    <string name="appVersion">版本号</string>
    <string name="delete">删除</string>
    <string name="loading">正在连接...</string>
    <string name="pleaseWait">请等待</string>
    <string name="send_interval">发送间隔</string>
    <string name="default_interval">默认间隔</string>
    <string name="path">路径</string>

    <!--    throughput-->
    <string name="title_real_value">Real Test Value</string>
    <string name="title_config_value">Config Value</string>

    <string name="totaDataEncryption">TOTA数据加密</string>
    <string name="use_totav2">使用 TOTA V2</string>
    <string name="use_phy2m">使用 PHY 2M</string>
    <string name="use_normal_connect">使用普通连接</string>
    <string name="DataTransmissionInterval">发送数据间隔</string>
    <string name="UseTheDefaultInterval">使用默认间隔</string>

    <!--    wifi-->
    <string name="chooseWifi">请选择WIFI</string>
    <string name="wifiName">WIFI 名字</string>
    <string name="wifiPassword">WIFI 密码</string>
    <string name="wifiSendData">发送数据</string>
    <string name="wifiSendDataOK">发送数据成功</string>
    <string name="wifiSendDataFail">发送数据失败</string>

    <string name="stop_vibrate">停止振动</string>


    <!--    AVS LWA-->
    <string name="login_with_amazon">Login with Amazon</string>
    <string name="logout">Logout</string>
    <string name="default_message">Welcome to Login with Amazon!\nIf this is your first time logging in, you will be asked to give permission for this application to access your profile data.</string>
    <string name="login_button_content_description">"Button for authorization through Login with Amazon"</string>
    <string name="return_to_app">Return to App</string>
    <string name="wifiConnectFail">WIFI Connect Fail, Please Retry</string>
    <string name="amazon_guide">Alexa,what\'s the weather todav?\n\nAlexa,play my flsah Briefing.\n\nAlexa,set a timer for 20 minutes.\n\nAlexa,add "Dinner with Mon" to my calendar.\n\nAlexa,set an alarm for 6 a.m.</string>
    <string name="save">保存</string>
    <string name="language_select">语言选择</string>
    <string name="connecting_wifi">正在连接WIFI...</string>
    <string name="setting_language">正在设置设备语言...</string>
    <string name="setting_language_fail">设置失败，请重试</string>
    <string name="setting_success">设置成功</string>
    <string name="setting_tips">获取当前设置...</string>

    <!--    customer dial-->
    <string name="choose_dial">选择表盘</string>
    <string name="choose_customer_dial">选择自定义表盘</string>
    <string name="customer_dial">自定义表盘</string>
    <string name="choose_online_dial">在线表盘</string>
    <string name="choose_picture">图片资源</string>
    <string name="choose_font_library">字库资源</string>
    <string name="choose_tp_firmware">TP 固件</string>
    <string name="choose_heart_rate_firmware">心率固件</string>
    <string name="choose_language_packet">语言数据包</string>

    <string name="current_dial">当前表盘</string>
    <string name="choose_dfu_file">选择dfu文件</string>
    <string name="choose_old_version_file">老版本文件</string>
    <string name="start_transfer_tips">选择表盘或文件</string>
    <string name="start_transfer_dial">开始传输表盘</string>
    <string name="start_transfer_dfu_file">开始传输dfu文件</string>
    <string name="dail_background">背景</string>
    <string name="dail_style">样式</string>
    <string name="dail_color">颜色</string>
    <string name="action_sheet_tips">请选择</string>
    <string name="action_sheet_item0">相机</string>
    <string name="action_sheet_item1">相册</string>
    <string name="form_square">方形</string>
    <string name="form_circle">圆形</string>
    <string name="crop_photo_data_name_length">高</string>
    <string name="crop_photo_data_name_diameter">直径</string>
    <string name="crop_photo_data_name_width">宽</string>
    <string name="crop_photo_save">保存设置</string>
    <string name="flash_address_error">flash address error</string>
    <string name="use_incremental_upgrade">使用增量升级</string>
    <string name="current_dial_size">当前表盘尺寸:</string>

</resources>