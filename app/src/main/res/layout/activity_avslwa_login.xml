<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:tc="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6"
    android:weightSum="1"
    >
    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <include
        android:id="@+id/loginfo"
        layout="@layout/logview"
        android:visibility="gone"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="0.6">

    </View>

    <TextView
        android:id="@+id/profile_info"
        android:layout_width="310dp"
        android:layout_height="300dp"
        android:text="@string/default_message"
        android:textSize="15sp"
        android:gravity="center"
        android:layout_gravity="center"
        android:lineSpacingExtra="10dp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="0.4">

    </View>

    <Button
        android:id="@+id/login_with_amazon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="30dp"
        android:gravity="center"
        android:layout_gravity="center"
        android:background="@drawable/btnlwa_gold_loginwithamazon"
        android:contentDescription="@string/login_button_content_description"
        android:padding="0dp" />

</LinearLayout>


