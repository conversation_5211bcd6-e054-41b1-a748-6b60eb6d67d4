<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android" android:layout_width="match_parent"
    android:layout_height="match_parent">
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/rectangle">
        <ScrollView
            android:id="@+id/ota_log_scrollview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="40dp"
            >
            <TextView
                android:id="@+id/ota_log_text"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:textSize="14sp"
                android:fontFamily="@font/sanshan_normal"
                android:textColor="@color/title_color_light"
                >

            </TextView>
        </ScrollView>

        <ScrollView
            android:id="@+id/ota_log_scrollview_short"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="150dp"
            android:visibility="gone"
            >
            <TextView
                android:id="@+id/ota_log_text_short"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:textSize="14sp"
                android:fontFamily="@font/sanshan_normal"
                android:textColor="@color/title_color_light"
                >

            </TextView>
        </ScrollView>

        <Button
            android:id="@+id/ota_over_btn"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|center"
            android:layout_marginBottom="70dp"
            android:background="@drawable/ota_click"
            android:text="@string/OTASuccess"
            android:textColor="@color/white"
            android:textAllCaps="false"
            >
        </Button>

    </FrameLayout>


</androidx.constraintlayout.widget.ConstraintLayout>