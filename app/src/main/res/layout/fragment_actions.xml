<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/type_choose"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dip"
        android:background="#FFE1E6EB"
        />
    <Button
        android:id="@+id/type_spp"
        android:layout_width="match_parent"
        android:layout_height="58dp"
        android:gravity="left|center_vertical"
        android:textSize="20dp"
        android:textColor="@color/ff2c4662"
        android:padding="20dp"
        android:background="@drawable/rectangle_longbtn"
        android:drawableLeft="@drawable/home_icon_ota"
        android:drawableRight="@drawable/home_icon_arrow"
        android:text="  Type spp"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dip"
        android:layout_marginLeft="20dp"
        android:background="#FFE1E6EB"
        />

    <Button
        android:id="@+id/type_ble"
        android:layout_width="match_parent"
        android:layout_height="58dp"
        android:layout_gravity="center"
        android:padding="20dp"
        android:gravity="left|center_vertical"
        android:background="@drawable/rectangle_longbtn"
        android:drawableLeft="@drawable/home_icon_ota"
        android:drawableRight="@drawable/home_icon_arrow"
        android:textSize="20dp"
        android:textColor="@color/ff2c4662"
        android:text="  Type ble"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dip"
        android:background="#FFE1E6EB"
        />
    </LinearLayout>

</FrameLayout>