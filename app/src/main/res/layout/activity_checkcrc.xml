<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6">

    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <include
        android:id="@+id/loginfo"
        layout="@layout/logview"
        android:visibility="gone"/>

    <Button
        android:id="@+id/get_crc"
        android:layout_width="214dp"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="40dp"
        android:layout_height="68dp"
        android:layout_marginTop="60dp"
        android:layout_gravity="center"
        android:background="@drawable/ota_button_bg"
        android:text="GET CRC"
        android:textColor="@color/white"
        android:textAllCaps="false"
        android:textSize="14sp"
        >
    </Button>

    <TextView
        android:id="@+id/show_crc"
        android:layout_marginTop="20dp"
        android:hint="click GET CRC button to get result"
        android:layout_width="match_parent"
        android:layout_height="90dp"
        android:textSize="18dp"
        android:textColor="@color/black"
        android:gravity="center"
        >

    </TextView>

    <Button
        android:id="@+id/factory_reset"
        android:layout_width="214dp"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="40dp"
        android:layout_height="68dp"
        android:layout_marginTop="60dp"
        android:layout_gravity="center"
        android:background="@drawable/ota_button_bg"
        android:text="Factory Reset"
        android:textColor="@color/white"
        android:textAllCaps="false"
        android:textSize="14sp"
        >
    </Button>

</LinearLayout>



