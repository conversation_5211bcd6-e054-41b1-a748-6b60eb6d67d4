<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="80dp"
    android:orientation="horizontal"
    android:weightSum="20">

    <RadioButton
        android:id="@+id/radioBtn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="2"
        android:paddingTop="15dp"
        android:paddingBottom="15dp"
        android:layout_gravity="center"
        />

    <Spinner
        android:id="@+id/enableFilterType"
        android:layout_width="0dp"
        android:layout_marginLeft="-10dp"
        android:layout_height="40dp"
        android:layout_weight="7"
        android:layout_gravity="center"
        />

    <EditText
        android:id="@+id/gain_et"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_weight="3"
        android:background="@color/activityText"
        android:layout_gravity="center"
        android:textSize="10sp"
        android:inputType="numberDecimal|numberSigned"
        android:textAlignment="center"
        >

    </EditText>

    <EditText
        android:id="@+id/freq"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_weight="5"
        android:background="@color/white"
        android:layout_gravity="center"
        android:textSize="13sp"
        android:textAlignment="center"
        android:inputType="numberDecimal|numberSigned"
        >

    </EditText>

    <EditText
        android:id="@+id/q"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_gravity="center"
        android:layout_weight="2.9"
        android:background="@color/activityText"
        android:textAlignment="center"
        android:inputType="numberDecimal|numberSigned"
        android:textSize="10sp">

    </EditText>

    <View
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="0.1">

    </View>


</LinearLayout>
