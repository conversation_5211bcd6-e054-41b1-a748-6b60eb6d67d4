<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/ffe1e6eb"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:orientation="horizontal"
        android:weightSum="1"
        android:background="@color/white">
        <Button
            android:id="@+id/language_select_cancel"
            android:layout_weight="0.2"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:text="@string/cancel"
            android:background="@null"
            android:textColor="@color/ff068acd"
            android:textAllCaps="false"/>

        <TextView
            android:layout_weight="0.6"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:text="@string/language_select"
            android:gravity="center"
            android:textSize="16dp"
            android:textColor="@color/black"
            >
        </TextView>

        <Button
            android:id="@+id/language_select_save"
            android:layout_weight="0.2"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:text="@string/save"
            android:background="@null"
            android:textColor="@color/ff068acd"
            android:textAllCaps="false"/>


    </LinearLayout>

    <ListView
        android:id="@+id/language_listview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/shape_background"
        />




</LinearLayout>
