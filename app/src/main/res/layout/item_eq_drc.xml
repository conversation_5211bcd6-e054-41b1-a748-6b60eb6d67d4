<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="10dp"
    android:layout_marginBottom="10dp"
    android:weightSum="20"
    android:orientation="vertical"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:layout_marginTop="5dp"
        android:orientation="horizontal">
        <View
            android:id="@+id/seekbar_radioview"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:layout_marginRight="-15dp"
            android:visibility="gone"
            />

        <View
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="5" />

        <TextView
            android:id="@+id/seekbar_min"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="5"
            android:text="1ms"
            android:textSize="10sp" />

        <TextView
            android:id="@+id/seekbar_real"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="5"
            android:text="500"
            android:textSize="10sp" />

        <TextView
            android:id="@+id/seekbar_max"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:text="1000ms"
            android:textSize="10sp"
            />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:orientation="horizontal"
        android:weightSum="20">

        <RadioButton
            android:id="@+id/seekbar_radio"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:layout_marginRight="-15dp"
            android:visibility="gone"
            />

        <TextView
            android:id="@+id/seekbar_title"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="5"
            android:gravity="center"
            android:text="Freq"
            android:textSize="14sp"
            android:textColor="@color/black"
            android:layout_gravity="center"
            />

        <SeekBar
            android:id="@+id/seekbar"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="13"
            android:max="100"
            android:progress="50"
             />



    </LinearLayout>




</LinearLayout>
