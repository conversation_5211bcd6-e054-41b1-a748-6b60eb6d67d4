<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:windowSoftInputMode="stateAlwaysHidden|adjustResize"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView
        android:id="@+id/act_otaui_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bes_bg_white"

        >
    </ImageView>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <include
            android:id="@+id/tool"
            layout="@layout/toolbar"
            />
        <View
            android:layout_width="match_parent"
            android:layout_height="29dp"
            >
        </View>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/ffe1e6eb"
            android:alpha="0.8">
        </View>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="58dp"
            android:orientation="horizontal">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="19dp"
                android:layout_marginLeft="20dp"
                android:layout_gravity="center_vertical"
                android:textSize="16sp"
                android:fontFamily="@font/sanshan_normal"
                android:textColor="@color/title_color_dark"
                android:text="@string/appVersion">
            </TextView>

            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginRight="100dp">
            </View>

            <TextView
                android:id="@+id/version_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="-90dp"
                android:layout_gravity="center_vertical"
                android:textSize="12sp"
                android:fontFamily="@font/sanshan_normal"
                android:textColor="@color/title_color_light"
                >
            </TextView>

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dip"
            android:background="#FFE1E6EB"
            />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="58dp"
            android:orientation="horizontal">
            <Button
                android:id="@+id/privacy_policy"
                android:layout_marginLeft="20dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/privacy_policy"
                android:textColor="@color/ff087ec2"
                android:background="@null"
                android:textAllCaps="false">

            </Button>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/agree_view"
            android:layout_width="match_parent"
            android:layout_height="400dp"
            android:layout_marginLeft="50dp"
            android:layout_marginRight="50dp"
            android:layout_marginTop="-500dp"
            android:background="@color/white"
            android:orientation="vertical"
            android:visibility="gone"
            >

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingBottom="60dp"
                android:background="@color/lineview"
                >

                <TextView
                    android:id="@+id/agreeTV"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:padding="10dp">

                </TextView>

            </ScrollView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="-60dp"
                android:orientation="horizontal"
                android:gravity="center">
                <Button
                    android:id="@+id/disagree"
                    android:layout_width="80dp"
                    android:layout_height="40dp"
                    android:padding="10dp"
                    android:text="@string/disagree"
                    android:background="@color/white"
                    android:textAllCaps="false">

                </Button>

                <View
                    android:layout_width="20dp"
                    android:layout_height="40dp">

                </View>

                <Button
                    android:id="@+id/agree"
                    android:layout_width="80dp"
                    android:layout_height="40dp"
                    android:padding="10dp"
                    android:text="@string/agree"
                    android:textColor="@color/ff087ec2"
                    android:background="@color/white"
                    android:textAllCaps="false">

                </Button>

            </LinearLayout>

        </LinearLayout>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>