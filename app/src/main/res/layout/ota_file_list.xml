<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg_white">

    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <Button
        android:id="@+id/ota_file_steps_btn"
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:text="@string/steps_btn"
        android:textColor="@color/activityText"
        android:background="@null"
        android:layout_marginTop="30dp"
        android:textAllCaps="false"
        android:visibility="gone"/>

    <ListView
        android:id="@+id/ota_file_list"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="30dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"/>

    <LinearLayout
        android:id="@+id/ota_file_steps"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="30dp"
        android:orientation="vertical"
        >
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:text="@string/steps1"
            android:textColor="@color/activityText"
            android:textSize="16sp"/>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="20dp"
            android:text="@string/steps1_text"
            android:textColor="@color/activityText"
            android:textSize="14sp"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:text="@string/steps2"
            android:textColor="@color/activityText"
            android:textSize="16sp"/>

        <TextView
            android:id="@+id/ota_file_steps_2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="20dp"
            android:text="@string/steps2_text"
            android:textColor="@color/activityText"
            android:textSize="14sp"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:text="@string/steps3"
            android:textColor="@color/activityText"
            android:textSize="16sp"/>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="20dp"
            android:text="@string/steps3_text"
            android:textColor="@color/activityText"
            android:textSize="14sp"/>

    </LinearLayout>





</LinearLayout>