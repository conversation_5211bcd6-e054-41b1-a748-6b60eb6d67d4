<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:tc="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6"
    >
    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <include
        android:id="@+id/loginfo"
        layout="@layout/logview"
        android:visibility="gone"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="110dp"
        android:layout_marginTop="25dp"
        android:orientation="vertical"
        android:background="@color/white"
        >
        <Button
            android:id="@+id/log_read_start"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:layout_marginTop="15dp"
            android:layout_marginLeft="20dp"
            android:text="@string/startdownload"
            android:textAlignment="center"
            android:textSize="14sp"
            android:textColor="@color/white"
            android:padding="8dp"
            android:textAllCaps="false"
            android:drawableLeft="@drawable/chips_tools_icon_download"
            android:background="@drawable/rectangle_button2"/>

        <ProgressBar
            android:id="@+id/log_progress"
            style="@android:style/Widget.ProgressBar.Horizontal"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:layout_marginTop="15dp"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:max="100"
            android:progress="0"
            android:progressBackgroundTint="@color/fff6efeb"
            />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            >

            <TextView
                android:id="@+id/total_log_status"
                android:layout_width="wrap_content"
                android:layout_height="18dp"
                android:layout_weight="1"
                android:layout_marginTop="6dp"
                android:layout_marginLeft="20dp"
                android:textSize="11sp"
                android:text="@string/downloading"
                android:visibility="invisible"
                />

            <TextView
                android:layout_width="24dp"
                android:layout_height="16dp"
                android:layout_weight="1"
                android:layout_marginTop="8dp"
                android:text="@string/totalprogress"
                android:textSize="11sp"
                android:textAlignment="textEnd"/>

            <TextView
                android:id="@+id/total_log_progress"
                android:layout_width="wrap_content"
                android:layout_height="16dp"

                android:layout_marginTop="8dp"
                android:layout_marginRight="20dp"
                android:text=""
                android:textSize="11sp"
                android:textAlignment="textEnd"/>

        </LinearLayout>

    </LinearLayout>

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="42dp">
        <ImageView
            android:layout_width="204dp"
            android:layout_height="204dp"
            android:layout_gravity="center"
            android:background="@drawable/chips_tools_icon_file_download" />

        <com.besall.allbase.common.utils.CircleProgressView
            android:layout_width="223dp"
            android:layout_height="223dp"
            android:id="@+id/tasks_view"
            tc:circleColor="@color/color_transparent"
            tc:radius="100dp"
            tc:ringBgColor="@color/color_transparent"
            tc:ringColor="@color/circlering"
            tc:strokeWidth="5dip"
            android:layout_centerInParent="true"/>
    </FrameLayout>


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="11dp"
        android:orientation="horizontal"
        android:layout_gravity="center">
        <TextView
            android:id="@+id/current_log_pro"
            android:layout_width="wrap_content"
            android:layout_height="47dp"

            android:text="0"
            android:fontFamily="@font/sanshan_bold"
            android:textAlignment="center"
            android:textColor="#ff087ec2"
            android:textSize="50sp"/>

        <ImageView
            android:id="@+id/percent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:background="@drawable/percent" />
    </LinearLayout>


    <TextView
        android:id="@+id/current_log_status"
        android:layout_width="wrap_content"
        android:layout_height="22dp"
        android:layout_marginTop="3dp"
        android:layout_gravity="center"
        android:text="@string/downloading"
        android:textColor="#ff087dc0"
        android:textSize="16sp"
        android:visibility="invisible"
        />

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent">
    </View>
    <Button
        android:id="@+id/spp_stop"
        android:layout_width="wrap_content"
        android:layout_height="70dp"
        android:layout_gravity="center"
        android:layout_marginTop="-100dp"
        android:paddingBottom="8dp"
        android:background="@drawable/rectangle_otabutton"
        android:text="@string/disconnect_device"
        android:textColor="@color/white"
        android:textAllCaps="false"/>


</LinearLayout>



