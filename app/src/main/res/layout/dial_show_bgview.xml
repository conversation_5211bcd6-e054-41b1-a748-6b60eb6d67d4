<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="150dp"
    android:layout_height="150dp"
    android:layout_gravity="center_vertical"
    >

    <ImageView
        android:id="@+id/image_item"
        android:layout_width="150dp"
        android:layout_height="150dp"
        android:scaleType="center"
        android:rotation="90"
        android:layout_gravity="center"
        />
    <LinearLayout
        android:id="@+id/function_item_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:rotation="90"
        android:layout_gravity="left|center_vertical"
        android:layout_marginLeft="25dp"
        android:visibility="gone"
        >
        <TextView
            android:id="@+id/date_textview_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="FRI 18"
            android:textAlignment="center"
            android:textSize="8sp"
            android:textColor="@color/white"/>
        <TextView
            android:id="@+id/time_textview_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="10 : 08"
            android:textAlignment="center"
            android:textSize="13sp"
            android:textColor="@color/white" />

<!--        <TextView-->
<!--            android:id="@+id/function_textview_bottom"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:text="温度 9"-->
<!--            android:textAlignment="center"-->
<!--            android:textColor="@color/white"-->
<!--            android:textSize="5sp" />-->
    </LinearLayout>

    <LinearLayout
        android:id="@+id/function_item_icon"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:rotation="90"
        android:gravity="bottom|center"
        android:layout_marginLeft="25dp"
        android:visibility="visible"
        >

        <ImageView
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:src="@drawable/xitongshijian"/>

    </LinearLayout>

<!--    <LinearLayout-->
<!--        android:id="@+id/function_item_top"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:orientation="vertical"-->
<!--        android:rotation="90"-->
<!--        android:layout_gravity="right|center_vertical"-->
<!--        android:layout_marginRight="25dp"-->
<!--        android:visibility="visible"-->
<!--        >-->
<!--        <TextView-->
<!--            android:id="@+id/date_textview_top"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:text="FRI 18"-->
<!--            android:textAlignment="center"-->
<!--            android:textSize="8sp"-->
<!--            android:textColor="@color/white"/>-->
<!--        <TextView-->
<!--            android:id="@+id/time_textview_top"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:text="10 : 08"-->
<!--            android:textAlignment="center"-->
<!--            android:textSize="13sp"-->
<!--            android:textColor="@color/white" />-->

<!--        <TextView-->
<!--            android:id="@+id/function_textview_top"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:text="温度 9"-->
<!--            android:textAlignment="center"-->
<!--            android:textColor="@color/white"-->
<!--            android:textSize="5sp" />-->
<!--    </LinearLayout>-->

</FrameLayout>