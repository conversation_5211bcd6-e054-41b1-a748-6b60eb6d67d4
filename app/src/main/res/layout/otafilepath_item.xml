<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:background="@color/white"
    android:orientation="horizontal"
    android:gravity="center"
    >

    <TextView
        android:id="@+id/name"
        android:layout_width="50dp"
        android:layout_height="match_parent"
        android:gravity="center"
        android:text="namenamename"
        android:textColor="@color/title_color_light"
        android:textSize="13sp"
        />

    <TextView
        android:id="@+id/path"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="5dp"
        android:layout_marginRight="66dp"
        android:gravity="center"
        android:text="pathpathpathpathpathpathpathpath/pathpathpathpathpathpathpathpath/"
        android:textColor="@color/title_color_light"
        android:textSize="10sp"
        />

    <Button
        android:id="@+id/choose_file"
        android:layout_width="61dp"
        android:layout_height="32dp"
        android:layout_marginLeft="-61dp"
        android:gravity="center"
        android:background="@drawable/btn_choose"
        android:text="@string/choose"
        android:textAllCaps="false"
        android:textColor="@color/white">

    </Button>

</LinearLayout>