<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"

    >

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="300dp"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:layout_marginLeft="50dp"
        android:layout_marginRight="50dp"
        android:layout_marginTop="-300dp"
        android:orientation="vertical"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:orientation="vertical"
            android:background="@drawable/action_sheet_corner"
            >
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="35dp"
                android:text="@string/action_sheet_tips"
                android:textAlignment="center"
                android:textColor="@color/btnDisableColor"
                android:textSize="18sp"/>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="30dp"
                android:background="@color/lineview"/>

            <Button
                android:id="@+id/action_sheet_0"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:background="@null"
                android:textColor="@color/color_ff1247c2"
                android:textSize="16sp"
                android:text="@string/action_sheet_tips"
                android:textAllCaps="false"/>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/lineview"/>

            <Button
                android:id="@+id/action_sheet_1"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:background="@null"
                android:textColor="@color/color_ff1247c2"
                android:textSize="16sp"
                android:text="@string/action_sheet_tips"
                android:textAllCaps="false"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="20dp"
            android:orientation="vertical"
            android:background="@drawable/action_sheet_corner">

            <Button
                android:id="@+id/action_sheet_cancel"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:background="@null"
                android:textColor="@color/color_ff1247c2"
                android:textSize="16sp"
                android:text="@string/cancel"
                android:textAllCaps="false"/>

        </LinearLayout>





    </LinearLayout>

</LinearLayout>