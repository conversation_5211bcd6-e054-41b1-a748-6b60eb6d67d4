<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ImageView
            android:id="@+id/crop_photo_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerInside"
            />

    </LinearLayout>

    <com.besall.allbase.view.activity.chipstoollevel4.customerdial.makedial.CropPhotoBgView
        android:id="@+id/crop_photo_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@color/ff666666"
        android:gravity="center"
        android:alpha="0.8"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:orientation="horizontal"
        tools:ignore="MissingConstraints">
        <Button
            android:id="@+id/button_cancel"
            android:layout_width="100dp"
            android:layout_height="match_parent"
            tools:ignore="MissingConstraints"
            android:text="@string/cancel"
            android:textColor="@color/black"
            android:textAllCaps="false"
            android:gravity="center"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginRight="100dp"/>
        <Button
            android:id="@+id/button_sure"
            android:layout_width="100dp"
            android:layout_height="match_parent"
            android:layout_marginLeft="-100dp"
            tools:ignore="MissingConstraints"
            android:text="@string/ok"
            android:textColor="@color/black"
            android:textAllCaps="false"
            android:gravity="center"/>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="500dp"
        android:layout_marginTop="60dp"
        android:orientation="vertical"
        tools:ignore="MissingConstraints">

        <RadioGroup
            android:id="@+id/form_radio_group"
            android:layout_gravity="center"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:text="@string/form_square"
                android:textSize="18sp"
                android:textColor="@color/ffff5d5d"/>
            <RadioButton
                android:id="@+id/form_square"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/form_circle"
                android:layout_gravity="bottom"
                android:textSize="18sp"
                android:textColor="@color/ffff5d5d"
                />
            <RadioButton
                android:id="@+id/form_circle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                />
        </RadioGroup>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:orientation="horizontal"
            android:gravity="center">
            <TextView
                android:id="@+id/cur_data_name_0"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/crop_photo_data_name_length"
                android:textSize="16sp"
                android:textColor="@color/ffff5d5d"/>
            <EditText
                android:id="@+id/cur_data_0"
                android:layout_width="50dp"
                android:layout_height="match_parent"
                android:textAlignment="center"
                android:textColor="@color/ffff5d5d"
                android:textSize="13sp"
                android:inputType="number"/>

            <TextView
                android:id="@+id/crop_photo_symbol"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="X"
                android:textSize="18sp"
                android:textColor="@color/ffff5d5d"/>

            <EditText
                android:id="@+id/cur_data_1"
                android:layout_width="50dp"
                android:layout_height="match_parent"
                android:textAlignment="center"
                android:textColor="@color/ffff5d5d"
                android:textSize="13sp"
                android:inputType="number"/>
            <TextView
                android:id="@+id/cur_data_name_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/crop_photo_data_name_width"
                android:textSize="16sp"
                android:textColor="@color/ffff5d5d"/>

            <Button
                android:id="@+id/crop_photo_save_setting"
                android:text="@string/crop_photo_save"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginLeft="5dp"
                android:textSize="16sp"
                android:textAllCaps="false"
                android:visibility="gone"
                />
        </LinearLayout>

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>