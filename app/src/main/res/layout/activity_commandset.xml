<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6">

    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <include
        android:id="@+id/loginfo"
        layout="@layout/logview"
        android:visibility="gone"/>

    <ScrollView
        android:layout_marginTop="15dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="50dp"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <EditText
                android:id="@+id/command_set_receive_data"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:textSize="14sp"
                android:textColor="@color/black"
                android:background="@null"
                />

            <TextView
                android:id="@+id/command_set_current_version"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:layout_marginLeft="20dp"
                android:textSize="14sp"
                android:textColor="@color/black"
                android:text="CRC:  \nVersion:  \nBuild Data:  "
                />
            <TextView
                android:id="@+id/command_set_current_product_model"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:textSize="18sp"
                android:textColor="@color/black"
                android:text="Current Product Model"
                />

            <Button
                android:id="@+id/start_ota_btn"
                android:layout_width="214dp"
                android:layout_height="68dp"
                android:layout_gravity="center"
                android:background="@drawable/ota_click"
                android:text="Start OTA"
                android:textColor="@color/white"
                android:textAllCaps="false"
                android:textSize="14sp"
                />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <Button
                    android:id="@+id/get_bt_state"
                    android:layout_width="214dp"
                    android:layout_height="68dp"
                    android:layout_gravity="left"
                    android:background="@drawable/ota_click"
                    android:text="Get Bt State"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="14sp"
                    />

                <TextView
                    android:id="@+id/bt_state_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:textAlignment="center"
                    android:hint="bt state"
                    android:textColor="@color/black"
                    />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <Button
                    android:id="@+id/get_spp_state"
                    android:layout_width="214dp"
                    android:layout_height="68dp"
                    android:layout_gravity="left"
                    android:background="@drawable/ota_click"
                    android:text="Get SPP State"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="14sp"
                    />

                <TextView
                    android:id="@+id/spp_state_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:textAlignment="center"
                    android:hint="spp state"
                    android:textColor="@color/black"
                    />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <Button
                    android:id="@+id/check_mic_state"
                    android:layout_width="214dp"
                    android:layout_height="68dp"
                    android:layout_gravity="left"
                    android:background="@drawable/ota_click"
                    android:text="Check MIC State"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="14sp"
                    />

                <TextView
                    android:id="@+id/mic_state_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:textAlignment="center"
                    android:hint="mic state"
                    android:textColor="@color/black"
                    />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <Button
                    android:id="@+id/check_left_speaker"
                    android:layout_width="200dp"
                    android:layout_height="68dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="Check Left Speaker"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="14sp"
                    />
                <Button
                    android:id="@+id/check_right_speaker"
                    android:layout_width="200dp"
                    android:layout_height="68dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="Check Right Speaker"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="14sp"
                    />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:background="@color/lineview"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:textColor="@color/ff666666"
                android:text="Wear Detect"
                android:textSize="16sp"/>

            <LinearLayout
                android:layout_marginTop="5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="1">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.5"
                        android:text="Left Earphone"
                        android:textAlignment="center"/>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.5"
                        android:text="Right Earphone"
                        android:textAlignment="center"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_weight="1"
                    >
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_weight="0.5"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        >
                        <com.suke.widget.SwitchButton
                            android:id="@+id/switchButton_in_ear_detection_left"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="15dp"
                            android:layout_gravity="center_vertical"
                            app:sb_show_indicator="false"/>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_weight="0.5"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        >
                        <com.suke.widget.SwitchButton
                            android:id="@+id/switchButton_in_ear_detection_right"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="15dp"
                            android:layout_gravity="center_vertical"
                            app:sb_show_indicator="false"/>
                    </LinearLayout>
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="1">
                <TextView
                    android:id="@+id/text_in_ear_detection_left"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5"
                    android:text="In-ear testing is turned off"
                    android:layout_gravity="center"
                    android:textAlignment="center"/>

                <TextView
                    android:id="@+id/text_in_ear_detection_right"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5"
                    android:text="In-ear testing is turned off"
                    android:layout_gravity="center"
                    android:textAlignment="center"/>
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="5dp"
                android:background="@color/lineview"/>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal"
                    android:weightSum="1">
                    <Button
                        android:id="@+id/earbuds_click_left"
                        android:layout_width="0dp"
                        android:layout_weight="0.5"
                        android:layout_height="68dp"
                        android:layout_gravity="center"
                        android:background="@drawable/ota_click"
                        android:text="Click Left Earbud"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="13sp"
                        >
                    </Button>

                    <Button
                        android:id="@+id/earbuds_click_right"
                        android:layout_weight="0.5"
                        android:layout_width="0dp"
                        android:layout_height="68dp"
                        android:layout_gravity="center"
                        android:background="@drawable/ota_click"
                        android:text="Click Right Earbud"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="13sp"
                        >
                    </Button>


                </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="1">
                <TextView
                    android:id="@+id/button_state_1"
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:layout_height="wrap_content"
                    android:textAlignment="center"
                    android:textColor="@color/black"
                    android:textSize="13sp"
                    android:text="current functon"
                    />

                <TextView
                    android:id="@+id/button_state_2"
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:layout_height="wrap_content"
                    android:textAlignment="center"
                    android:textColor="@color/black"
                    android:textSize="13sp"
                    android:text="current functon"
                    />
            </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="1">
                    <Button
                        android:id="@+id/earbuds_double_click_left"
                        android:layout_width="0dp"
                        android:layout_weight="0.5"
                        android:layout_height="68dp"
                        android:layout_gravity="center"
                        android:background="@drawable/ota_click"
                        android:text="Double Click Left Earbud"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="13sp"
                        >
                    </Button>

                    <Button
                        android:id="@+id/earbuds_double_click_right"
                        android:layout_weight="0.5"
                        android:layout_width="0dp"
                        android:layout_height="68dp"
                        android:layout_gravity="center"
                        android:background="@drawable/ota_click"
                        android:text="Double Click Right Earbud"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="13sp"
                        >
                    </Button>
                </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="1">
                <TextView
                    android:id="@+id/button_state_3"
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:layout_height="wrap_content"
                    android:textAlignment="center"
                    android:textColor="@color/black"
                    android:textSize="13sp"
                    android:text="current functon"
                    />

                <TextView
                    android:id="@+id/button_state_4"
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:layout_height="wrap_content"
                    android:textAlignment="center"
                    android:textColor="@color/black"
                    android:textSize="13sp"
                    android:text="current functon"
                    />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="1">
                <Button
                    android:id="@+id/earbuds_triple_click_left"
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:layout_height="68dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="Triple Click Left Earbud"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="13sp"
                    >
                </Button>

                <Button
                    android:id="@+id/earbuds_triple_click_right"
                    android:layout_weight="0.5"
                    android:layout_width="0dp"
                    android:layout_height="68dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="Triple Click Right Earbud"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="13sp"
                    >
                </Button>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="1">
                <TextView
                    android:id="@+id/button_state_5"
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:layout_height="wrap_content"
                    android:textAlignment="center"
                    android:textColor="@color/black"
                    android:textSize="13sp"
                    android:text="current functon"
                    />

                <TextView
                    android:id="@+id/button_state_6"
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:layout_height="wrap_content"
                    android:textAlignment="center"
                    android:textColor="@color/black"
                    android:textSize="13sp"
                    android:text="current functon"
                    />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="1">
                <Button
                    android:id="@+id/earbuds_long_press_left"
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:layout_height="68dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="Long Press Left Earbud"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="13sp"
                    >
                </Button>

                <Button
                    android:id="@+id/earbuds_long_press_right"
                    android:layout_weight="0.5"
                    android:layout_width="0dp"
                    android:layout_height="68dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="Long Press Right Earbud"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="13sp"
                    >
                </Button>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="1">
                <TextView
                    android:id="@+id/button_state_7"
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:layout_height="wrap_content"
                    android:textAlignment="center"
                    android:textColor="@color/black"
                    android:textSize="13sp"
                    android:text="current functon"
                    />

                <TextView
                    android:id="@+id/button_state_8"
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:layout_height="wrap_content"
                    android:textAlignment="center"
                    android:textColor="@color/black"
                    android:textSize="13sp"
                    android:text="current functon"
                    />
            </LinearLayout>

                <RadioGroup
                    android:id="@+id/earbuds_click_func_0"
                    android:layout_marginLeft="20dp"
                    android:layout_marginTop="5dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:text="Last Music"
                        android:textColor="@color/title_color"
                        android:textSize="14sp" />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_last_music"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="14sp"
                        android:text="Next Music"
                        />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_next_music"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="12sp"
                        android:text="ANC"
                        />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_ambient_music"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:fontFamily="@font/sanshan_normal"
                        android:gravity="right"
                        android:text="Call back"
                        android:textColor="@color/title_color"
                        android:textSize="14sp" />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_call_back"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />
                </RadioGroup>

                <RadioGroup
                    android:id="@+id/earbuds_click_func_1"
                    android:layout_marginLeft="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:text="Volume+"
                        android:textColor="@color/title_color"
                        android:textSize="14sp" />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_volume_add"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:fontFamily="@font/sanshan_normal"
                        android:gravity="right"
                        android:text="Volume-"
                        android:textColor="@color/title_color"
                        android:textSize="14sp" />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_volume_lose"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="12sp"
                        android:text="Play music"
                        />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_play_music"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="14sp"
                        android:text="Stop music"
                        />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_stop_music"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />
                </RadioGroup>

                <RadioGroup
                    android:id="@+id/earbuds_click_func_2"
                    android:layout_marginLeft="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="left"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="14sp"
                        android:text="Wake up voice assistant"
                        />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_assistant"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="14sp"
                        android:text="play pause music"
                        />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_play_pause_music"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />
                </RadioGroup>

            <RadioGroup
                android:id="@+id/earbuds_click_func_3"
                android:layout_marginLeft="20dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="left"
                android:orientation="horizontal">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/sanshan_normal"
                    android:textColor="@color/title_color"
                    android:textSize="14sp"
                    android:text="Game mode"
                    />

                <RadioButton
                    android:id="@+id/earbuds_click_func_game_mode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/sanshan_normal"
                    android:textColor="@color/title_color"
                    android:textSize="14sp"
                    android:text="ALGO"
                    />

                <RadioButton
                    android:id="@+id/earbuds_click_func_algo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    />
            </RadioGroup>

            <Button
                android:id="@+id/factory_reset_cmd_set"
                android:layout_width="214dp"
                android:layout_height="68dp"
                android:layout_gravity="center"
                android:background="@drawable/ota_click"
                android:text="Factory Reset"
                android:textColor="@color/white"
                android:textAllCaps="false"
                android:textSize="14sp"
                >
            </Button>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="1">

                <Button
                    android:id="@+id/play"
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:layout_height="68dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="PLAY"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="13sp"
                    >
                </Button>

                <Button
                    android:id="@+id/pause"
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:layout_height="68dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="PAUSE"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="13sp"
                    >
                </Button>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="1">

                <Button
                    android:id="@+id/next"
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:layout_height="68dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="NEXT"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="13sp"
                    >
                </Button>

                <Button
                    android:id="@+id/prev"
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:layout_height="68dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="PREV"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="13sp"
                    >
                </Button>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/button_get_left_battery"
                    android:layout_width="214dp"
                    android:layout_height="68dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="GET Left Battery"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="14sp"
                    >
                </Button>

                <TextView
                    android:id="@+id/text_left_battery"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:textAlignment="center"
                    android:hint="current left battery"
                    android:textColor="@color/black"
                    >

                </TextView>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/button_get_right_battery"
                    android:layout_width="214dp"
                    android:layout_height="68dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="GET Right Battery"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="14sp"
                    >
                </Button>

                <TextView
                    android:id="@+id/text_right_battery"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:textAlignment="center"
                    android:hint="current right battery"
                    android:textColor="@color/black"
                    >

                </TextView>

            </LinearLayout>

<!--            <LinearLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:orientation="horizontal">-->

<!--                <com.suke.widget.SwitchButton-->
<!--                    android:id="@+id/switchButton_fit_test"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginLeft="15dp"-->
<!--                    android:layout_gravity="center_vertical"-->
<!--                    app:sb_show_indicator="false"/>-->

<!--                <Button-->
<!--                    android:id="@+id/fit_test"-->
<!--                    android:layout_width="214dp"-->
<!--                    android:layout_height="68dp"-->
<!--                    android:layout_gravity="center"-->
<!--                    android:background="@drawable/ota_click"-->
<!--                    android:text="Earbud Fit Test"-->
<!--                    android:textColor="@color/white"-->
<!--                    android:textAllCaps="false"-->
<!--                    android:textSize="14sp"-->
<!--                    >-->
<!--                </Button>-->

<!--                <TextView-->
<!--                    android:id="@+id/fit_text"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_gravity="center"-->
<!--                    android:textAlignment="center"-->
<!--                    android:hint="fit test result"-->
<!--                    android:textColor="@color/black"-->
<!--                    >-->

<!--                </TextView>-->

<!--            </LinearLayout>-->

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/eq_test"
                    android:layout_width="214dp"
                    android:layout_height="68dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="Change EQ"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="14sp"
                    >
                </Button>

                <EditText
                    android:visibility="gone"
                    android:id="@+id/eq_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:textAlignment="center"
                    android:hint="eq data (Hex)"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    >

                </EditText>

            </LinearLayout>
/////////////////////////////////////////

<!--            <LinearLayout-->
<!--                android:layout_gravity="center"-->
<!--                android:layout_width="330dp"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:orientation="horizontal"-->
<!--                android:visibility="gone"-->
<!--                >-->
<!--                <com.besall.allbase.view.activity.chipstoollevel4.commandset.MyScrollView-->
<!--                    android:id="@+id/scrollview_0"-->
<!--                    android:layout_width="33dp"-->
<!--                    android:layout_height="200dp"-->
<!--                    android:scrollbarThumbVertical="@null"-->
<!--                    >-->
<!--                    <FrameLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        >-->
<!--                        <View-->
<!--                            android:layout_width="2dp"-->
<!--                            android:layout_height="400dp"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:background="@color/green"/>-->

<!--                        <ImageView-->
<!--                            android:layout_width="15dp"-->
<!--                            android:layout_height="15dp"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:background="@drawable/plus"/>-->

<!--                    </FrameLayout>-->
<!--                </com.besall.allbase.view.activity.chipstoollevel4.commandset.MyScrollView>-->

<!--                <com.besall.allbase.view.activity.chipstoollevel4.commandset.MyScrollView-->
<!--                    android:id="@+id/scrollview_1"-->
<!--                    android:layout_width="33dp"-->
<!--                    android:layout_height="200dp"-->
<!--                    android:scrollbarThumbVertical="@null"-->
<!--                    >-->
<!--                    <FrameLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        >-->
<!--                        <View-->
<!--                            android:layout_width="2dp"-->
<!--                            android:layout_height="400dp"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:background="@color/green"/>-->

<!--                        <ImageView-->
<!--                            android:layout_width="15dp"-->
<!--                            android:layout_height="15dp"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:background="@drawable/plus"/>-->

<!--                    </FrameLayout>-->
<!--                </com.besall.allbase.view.activity.chipstoollevel4.commandset.MyScrollView>-->
<!--                <com.besall.allbase.view.activity.chipstoollevel4.commandset.MyScrollView-->
<!--                    android:id="@+id/scrollview_2"-->
<!--                    android:layout_width="33dp"-->
<!--                    android:layout_height="200dp"-->
<!--                    android:scrollbarThumbVertical="@null"-->
<!--                    >-->
<!--                    <FrameLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        >-->
<!--                        <View-->
<!--                            android:layout_width="2dp"-->
<!--                            android:layout_height="400dp"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:background="@color/green"/>-->

<!--                        <ImageView-->
<!--                            android:layout_width="15dp"-->
<!--                            android:layout_height="15dp"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:background="@drawable/plus"/>-->

<!--                    </FrameLayout>-->
<!--                </com.besall.allbase.view.activity.chipstoollevel4.commandset.MyScrollView>-->
<!--                <com.besall.allbase.view.activity.chipstoollevel4.commandset.MyScrollView-->
<!--                    android:id="@+id/scrollview_3"-->
<!--                    android:layout_width="33dp"-->
<!--                    android:layout_height="200dp"-->
<!--                    android:scrollbarThumbVertical="@null"-->
<!--                    >-->
<!--                    <FrameLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        >-->
<!--                        <View-->
<!--                            android:layout_width="2dp"-->
<!--                            android:layout_height="400dp"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:background="@color/green"/>-->

<!--                        <ImageView-->
<!--                            android:layout_width="15dp"-->
<!--                            android:layout_height="15dp"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:background="@drawable/plus"/>-->

<!--                    </FrameLayout>-->
<!--                </com.besall.allbase.view.activity.chipstoollevel4.commandset.MyScrollView>-->
<!--                <com.besall.allbase.view.activity.chipstoollevel4.commandset.MyScrollView-->
<!--                    android:id="@+id/scrollview_4"-->
<!--                    android:layout_width="33dp"-->
<!--                    android:layout_height="200dp"-->
<!--                    android:scrollbarThumbVertical="@null"-->
<!--                    >-->
<!--                    <FrameLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        >-->
<!--                        <View-->
<!--                            android:layout_width="2dp"-->
<!--                            android:layout_height="400dp"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:background="@color/green"/>-->

<!--                        <ImageView-->
<!--                            android:layout_width="15dp"-->
<!--                            android:layout_height="15dp"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:background="@drawable/plus"/>-->

<!--                    </FrameLayout>-->
<!--                </com.besall.allbase.view.activity.chipstoollevel4.commandset.MyScrollView>-->
<!--                <com.besall.allbase.view.activity.chipstoollevel4.commandset.MyScrollView-->
<!--                    android:id="@+id/scrollview_5"-->
<!--                    android:layout_width="33dp"-->
<!--                    android:layout_height="200dp"-->
<!--                    android:scrollbarThumbVertical="@null"-->
<!--                    >-->
<!--                    <FrameLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        >-->
<!--                        <View-->
<!--                            android:layout_width="2dp"-->
<!--                            android:layout_height="400dp"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:background="@color/green"/>-->

<!--                        <ImageView-->
<!--                            android:layout_width="15dp"-->
<!--                            android:layout_height="15dp"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:background="@drawable/plus"/>-->

<!--                    </FrameLayout>-->
<!--                </com.besall.allbase.view.activity.chipstoollevel4.commandset.MyScrollView>-->
<!--                <com.besall.allbase.view.activity.chipstoollevel4.commandset.MyScrollView-->
<!--                    android:id="@+id/scrollview_6"-->
<!--                    android:layout_width="33dp"-->
<!--                    android:layout_height="200dp"-->
<!--                    android:scrollbarThumbVertical="@null"-->
<!--                    >-->
<!--                    <FrameLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        >-->
<!--                        <View-->
<!--                            android:layout_width="2dp"-->
<!--                            android:layout_height="400dp"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:background="@color/green"/>-->

<!--                        <ImageView-->
<!--                            android:layout_width="15dp"-->
<!--                            android:layout_height="15dp"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:background="@drawable/plus"/>-->

<!--                    </FrameLayout>-->
<!--                </com.besall.allbase.view.activity.chipstoollevel4.commandset.MyScrollView>-->
<!--                <com.besall.allbase.view.activity.chipstoollevel4.commandset.MyScrollView-->
<!--                    android:id="@+id/scrollview_7"-->
<!--                    android:layout_width="33dp"-->
<!--                    android:layout_height="200dp"-->
<!--                    android:scrollbarThumbVertical="@null"-->
<!--                    >-->
<!--                    <FrameLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        >-->
<!--                        <View-->
<!--                            android:layout_width="2dp"-->
<!--                            android:layout_height="400dp"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:background="@color/green"/>-->

<!--                        <ImageView-->
<!--                            android:layout_width="15dp"-->
<!--                            android:layout_height="15dp"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:background="@drawable/plus"/>-->

<!--                    </FrameLayout>-->
<!--                </com.besall.allbase.view.activity.chipstoollevel4.commandset.MyScrollView>-->
<!--                <com.besall.allbase.view.activity.chipstoollevel4.commandset.MyScrollView-->
<!--                    android:id="@+id/scrollview_8"-->
<!--                    android:layout_width="33dp"-->
<!--                    android:layout_height="200dp"-->
<!--                    android:scrollbarThumbVertical="@null"-->
<!--                    >-->
<!--                    <FrameLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        >-->
<!--                        <View-->
<!--                            android:layout_width="2dp"-->
<!--                            android:layout_height="400dp"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:background="@color/green"/>-->

<!--                        <ImageView-->
<!--                            android:layout_width="15dp"-->
<!--                            android:layout_height="15dp"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:background="@drawable/plus"/>-->

<!--                    </FrameLayout>-->
<!--                </com.besall.allbase.view.activity.chipstoollevel4.commandset.MyScrollView>-->
<!--                <com.besall.allbase.view.activity.chipstoollevel4.commandset.MyScrollView-->
<!--                    android:id="@+id/scrollview_9"-->
<!--                    android:layout_width="33dp"-->
<!--                    android:layout_height="200dp"-->
<!--                    android:scrollbarThumbVertical="@null"-->
<!--                    >-->
<!--                    <FrameLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        >-->
<!--                        <View-->
<!--                            android:layout_width="2dp"-->
<!--                            android:layout_height="400dp"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:background="@color/green"/>-->

<!--                        <ImageView-->
<!--                            android:layout_width="15dp"-->
<!--                            android:layout_height="15dp"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:background="@drawable/plus"/>-->

<!--                    </FrameLayout>-->
<!--                </com.besall.allbase.view.activity.chipstoollevel4.commandset.MyScrollView>-->

<!--            </LinearLayout>-->

<!--            <LinearLayout-->
<!--                android:layout_gravity="center"-->
<!--                android:layout_width="330dp"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:orientation="horizontal"-->
<!--                android:visibility="gone">-->
<!--                <TextView-->
<!--                    android:gravity="center"-->
<!--                    android:layout_width="33dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="8"/>-->
<!--                <TextView-->
<!--                    android:gravity="center"-->
<!--                    android:layout_width="33dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="31"/>-->
<!--                <TextView-->
<!--                    android:gravity="center"-->
<!--                    android:layout_width="33dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="62"/>-->
<!--                <TextView-->
<!--                    android:gravity="center"-->
<!--                    android:layout_width="33dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="125"/>-->
<!--                <TextView-->
<!--                    android:gravity="center"-->
<!--                    android:layout_width="33dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="250"/>-->
<!--                <TextView-->
<!--                    android:gravity="center"-->
<!--                    android:layout_width="33dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="500"/>-->
<!--                <TextView-->
<!--                    android:gravity="center"-->
<!--                    android:layout_width="33dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="1k"/>-->
<!--                <TextView-->
<!--                    android:gravity="center"-->
<!--                    android:layout_width="33dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="2k"/>-->
<!--                <TextView-->
<!--                    android:gravity="center"-->
<!--                    android:layout_width="33dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="4k"/>-->
<!--                <TextView-->
<!--                    android:gravity="center"-->
<!--                    android:layout_width="33dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="16k"/>-->

<!--            </LinearLayout>-->

            <RadioGroup
                android:layout_marginLeft="20dp"
                android:id="@+id/eq_basetype"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:gravity="center"
                android:orientation="horizontal">

<!--                <TextView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:fontFamily="@font/sanshan_normal"-->
<!--                    android:text="Default"-->
<!--                    android:textColor="@color/title_color"-->
<!--                    android:textSize="14sp" />-->

<!--                <RadioButton-->
<!--                    android:id="@+id/eq_basetype_default"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:checked="true"-->
<!--                    />-->

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:fontFamily="@font/sanshan_normal"
                    android:textColor="@color/title_color"
                    android:textSize="14sp"
                    android:text="Pop"
                    />

                <RadioButton
                    android:id="@+id/eq_basetype_pop"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:fontFamily="@font/sanshan_normal"
                    android:textColor="@color/title_color"
                    android:textSize="12sp"
                    android:text="Rock"
                    />

                <RadioButton
                    android:id="@+id/eq_basetype_rock"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:fontFamily="@font/sanshan_normal"
                    android:textColor="@color/title_color"
                    android:textSize="14sp"
                    android:text="Jazz"
                    />

                <RadioButton
                    android:id="@+id/eq_basetype_jazz"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:fontFamily="@font/sanshan_normal"
                    android:textColor="@color/title_color"
                    android:textSize="8sp"
                    android:text="Classic"
                    />

                <RadioButton
                    android:id="@+id/eq_basetype_classic"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:fontFamily="@font/sanshan_normal"
                    android:textColor="@color/title_color"
                    android:textSize="8sp"
                    android:text="Country"
                    />

                <RadioButton
                    android:id="@+id/eq_basetype_country"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    />

            </RadioGroup>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:background="@color/lineview"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:textColor="@color/ff666666"
                android:text="EQ State"
                android:textSize="16sp"/>

            <RadioGroup
                android:layout_marginLeft="20dp"
                android:id="@+id/eq_switch_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/sanshan_normal"
                    android:text="On"
                    android:textColor="@color/title_color"
                    android:textSize="16sp" />

                <RadioButton
                    android:id="@+id/eq_switch_open"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:fontFamily="@font/sanshan_normal"
                    android:textColor="@color/title_color"
                    android:textSize="16sp"
                    android:text="Off"
                    />

                <RadioButton
                    android:id="@+id/eq_switch_off"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    />

            </RadioGroup>

        <LinearLayout
            android:id="@+id/linear_anc_state"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:background="@color/lineview"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:textColor="@color/ff666666"
                android:text="ANC State"
                android:textSize="16sp"/>

            <RadioGroup
                android:layout_marginLeft="20dp"
                android:id="@+id/regulate_anc_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/sanshan_normal"
                    android:text="ANC"
                    android:textColor="@color/title_color"
                    android:textSize="16sp" />

                <RadioButton
                    android:id="@+id/regulate_anc_anc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:fontFamily="@font/sanshan_normal"
                    android:textColor="@color/title_color"
                    android:textSize="16sp"
                    android:text="Ambient"
                    />

                <RadioButton
                    android:id="@+id/regulate_anc_ambient"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:fontFamily="@font/sanshan_normal"
                    android:textColor="@color/title_color"
                    android:textSize="16sp"
                    android:text="Default"
                    />

                <RadioButton
                    android:id="@+id/regulate_anc_default"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    />
            </RadioGroup>


        </LinearLayout>

            <LinearLayout
                android:id="@+id/linear_dolby_state"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                >
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:background="@color/lineview"/>

                <TextView
                    android:id="@+id/dolby_switch_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:textColor="@color/ff666666"
                    android:text="Dolby State"
                    android:textSize="16sp"/>

                <RadioGroup
                    android:layout_marginLeft="20dp"
                    android:id="@+id/dolby_switch_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:text="On"
                        android:textColor="@color/title_color"
                        android:textSize="16sp" />

                    <RadioButton
                        android:id="@+id/dolby_switch_open"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="30dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="16sp"
                        android:text="Off"
                        />

                    <RadioButton
                        android:id="@+id/dolby_switch_off"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                </RadioGroup>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <Button
                        android:id="@+id/dolby_type_natual"
                        android:layout_width="200dp"
                        android:layout_height="68dp"
                        android:layout_gravity="center"
                        android:background="@drawable/ota_button_bg_press"
                        android:text="Natual"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="14sp"
                        />

                    <Button
                        android:id="@+id/dolby_type_movie"
                        android:layout_width="200dp"
                        android:layout_height="68dp"
                        android:layout_gravity="center"
                        android:background="@drawable/ota_button_bg_press"
                        android:text="Movie"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="14sp"
                        />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/linear_bes_spatial_state"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:background="@color/lineview"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:textColor="@color/ff666666"
                    android:text="BES spatial State"
                    android:textSize="16sp"/>

                <RadioGroup
                    android:layout_marginLeft="20dp"
                    android:id="@+id/bes_spatial_switch_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:text="On"
                        android:textColor="@color/title_color"
                        android:textSize="16sp" />

                    <RadioButton
                        android:id="@+id/bes_spatial_switch_open"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="30dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="16sp"
                        android:text="Off"
                        />

                    <RadioButton
                        android:id="@+id/bes_spatial_switch_off"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                </RadioGroup>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/linear_mimi_state"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                >
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:background="@color/lineview"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:textColor="@color/ff666666"
                    android:text="mimi State"
                    android:textSize="16sp"/>

                <RadioGroup
                    android:layout_marginLeft="20dp"
                    android:id="@+id/mimi_switch_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:text="On"
                        android:textColor="@color/title_color"
                        android:textSize="16sp" />

                    <RadioButton
                        android:id="@+id/mimi_switch_open"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="30dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="16sp"
                        android:text="Off"
                        />

                    <RadioButton
                        android:id="@+id/mimi_switch_off"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                </RadioGroup>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:textColor="@color/ff666666"
                    android:text="preset"
                    android:textSize="16sp"/>

                <RadioGroup
                    android:layout_marginLeft="20dp"
                    android:id="@+id/mimi_switch_preset"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:text="18"
                        android:textColor="@color/title_color"
                        android:textSize="16sp" />

                    <RadioButton
                        android:id="@+id/mimi_switch_preset_18"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="30dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="16sp"
                        android:text="45"
                        />

                    <RadioButton
                        android:id="@+id/mimi_switch_preset_45"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="30dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="16sp"
                        android:text="60"
                        />

                    <RadioButton
                        android:id="@+id/mimi_switch_preset_60"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                </RadioGroup>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:textColor="@color/ff666666"
                    android:text="intensity"
                    android:textSize="16sp"/>

                <RadioGroup
                    android:layout_marginLeft="20dp"
                    android:id="@+id/mimi_switch_intensity"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:text="0.0"
                        android:textColor="@color/title_color"
                        android:textSize="16sp" />

                    <RadioButton
                        android:id="@+id/mimi_switch_intensity_0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="30dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="16sp"
                        android:text="0.5"
                        />

                    <RadioButton
                        android:id="@+id/mimi_switch_intensity_0_5"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="30dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="16sp"
                        android:text="1.0"
                        />

                    <RadioButton
                        android:id="@+id/mimi_switch_intensity_1_0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                </RadioGroup>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linear_ceva_state"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:background="@color/lineview"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:textColor="@color/ff666666"
                    android:text="ceva State"
                    android:textSize="16sp"/>

                <RadioGroup
                    android:layout_marginLeft="20dp"
                    android:id="@+id/ceva_switch_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:text="On"
                        android:textColor="@color/title_color"
                        android:textSize="16sp" />

                    <RadioButton
                        android:id="@+id/ceva_switch_open"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="30dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="16sp"
                        android:text="Off"
                        />

                    <RadioButton
                        android:id="@+id/ceva_switch_off"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                </RadioGroup>
            </LinearLayout>



            <!--////////////////////////////////////////-->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:background="@color/lineview"/>
            <Button
                android:id="@+id/disconnect"
                android:layout_width="214dp"
                android:layout_height="68dp"
                android:layout_marginTop="20dp"
                android:layout_gravity="center"
                android:background="@drawable/ota_click"
                android:text="Disconnect"
                android:textColor="@color/white"
                android:textAllCaps="false"
                android:textSize="14sp"
                >
            </Button>
        </LinearLayout>

    </ScrollView>

</LinearLayout>



