<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="163dp"
    android:layout_gravity="center"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <Button
            android:id="@+id/choose_device"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_gravity="center"
            android:background="@drawable/ota_click"
            android:text="@string/pleaseSelectDevice"
            android:textAllCaps="false"
            android:textColor="@color/white"
            >
        </Button>

        <Button
            android:id="@+id/connect_device"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/ota_click"
            android:text="@string/connect_device"
            android:textColor="@color/white"
            android:textAllCaps="false"
            >
        </Button>

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>