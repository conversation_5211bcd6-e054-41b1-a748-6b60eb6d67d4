<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android" android:layout_width="match_parent"
    android:layout_height="wrap_content">


    <LinearLayout
        android:layout_width="266dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/rectangle_dialog"
        android:orientation="vertical"
        android:radius="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="15dp"
                android:background="@drawable/tip_icon_prompt" />

            <TextView
                android:layout_width="35dp"
                android:layout_height="22dp"
                android:layout_gravity="center"
                android:text="alert"
                android:textAllCaps="false"
                android:textColor="#ff1d9fdb"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/dialog_text"
                android:layout_width="wrap_content"
                android:layout_height="100dp"
                android:layout_gravity="center"
                android:padding="20dp"
                android:text=""
                android:textAllCaps="false"
                android:textColor="#ff485057"
                android:background="@null"
                android:textSize="16sp"
                android:focusable="false"/>


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:gravity="bottom"
            android:orientation="horizontal">

            <Button
                android:id="@+id/dialog_cancel"
                android:layout_width="105dp"
                android:layout_height="40dp"
                android:layout_marginLeft="20dp"
                android:background="@drawable/rectangle_cancelbutton"
                android:text="@string/cancel"
                android:textAllCaps="false"
                android:textColor="#ff255d95"
                android:textSize="14sp">

            </Button>


            <Button
                android:id="@+id/dialog_confirm"
                android:layout_width="105dp"
                android:layout_height="40dp"
                android:layout_marginLeft="16dp"
                android:background="@drawable/rectangle_cenfirmbutton"
                android:text="@string/yes"
                android:textAllCaps="false"
                android:textColor="#ffffffff"
                android:textSize="14sp"></Button>
        </LinearLayout>
    </LinearLayout>

</RelativeLayout>