<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:tc="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6"
    >
    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <include
        android:id="@+id/loginfo"
        layout="@layout/logview"
        android:visibility="gone"/>

    <!--    <LinearLayout-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="match_parent"-->
    <!--        android:layout_marginTop="20dp"-->
    <!--        android:gravity="center"-->
    <!--        android:orientation="vertical">-->

    <!--        <Button-->
    <!--            android:layout_width="100dp"-->
    <!--            android:layout_height="100dp"-->
    <!--            android:id="@+id/speed_test_start"-->
    <!--            android:text="start">-->

    <!--        </Button>-->

    <!--        <Button-->
    <!--            android:layout_marginTop="10dp"-->
    <!--            android:layout_width="100dp"-->
    <!--            android:layout_height="100dp"-->
    <!--            android:id="@+id/speed_test_stop"-->
    <!--            android:text="stop">-->

    <!--        </Button>-->

    <!--        <TextView-->
    <!--            android:id="@+id/speed_test_view"-->
    <!--            android:layout_marginTop="10dp"-->
    <!--            android:layout_width="100dp"-->
    <!--            android:layout_height="250dp"-->
    <!--            android:scrollbars="vertical"-->

    <!--            >-->

    <!--        </TextView>-->

    <!--    </LinearLayout>-->

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="50dp"
        android:layout_marginTop="20dp"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:orientation="horizontal">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:textColor="@color/black"
                    android:gravity="center"
                    android:text="capensor work in BTH？"
                    android:textSize="13dp"/>

                <com.suke.widget.SwitchButton
                    android:id="@+id/switchButton_isBth"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="10dp"
                    android:textColor="@color/black"
                    android:gravity="center"
                    android:text="extra parameter?"
                    android:textSize="13dp"/>

                <com.suke.widget.SwitchButton
                    android:id="@+id/switchButton_extra_parameter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:orientation="horizontal">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="34dp"
                    android:text="Coefficients :"
                    android:textAlignment="center"
                    android:textSize="13sp"/>

                <EditText
                    android:id="@+id/coefficients"
                    android:layout_width="35dp"
                    android:layout_height="34dp"
                    android:background="@drawable/edittext2"
                    android:inputType="number"
                    android:textAlignment="center"
                    android:textColor="@color/ffff5d5d"
                    android:textSize="12sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="34dp"
                    android:layout_marginLeft="10dp"
                    android:text="Maximum:"
                    android:textAlignment="center"
                    android:textSize="13sp"/>

                <EditText
                    android:id="@+id/maximum"
                    android:layout_width="45dp"
                    android:layout_height="34dp"
                    android:textAlignment="center"
                    android:inputType="number"
                    android:textSize="12sp"
                    android:textColor="@color/ffff5d5d"
                    android:background="@drawable/edittext2"
                    />

                <TextView
                    android:layout_marginLeft="10dp"
                    android:layout_width="wrap_content"
                    android:layout_height="34dp"
                    android:text="Piece :"
                    android:textAlignment="center"
                    android:textSize="13sp"/>

                <EditText
                    android:id="@+id/piece"
                    android:layout_width="35dp"
                    android:layout_height="34dp"
                    android:background="@drawable/edittext2"
                    android:inputType="number"
                    android:textAlignment="center"
                    android:textColor="@color/ffff5d5d"
                    android:textSize="12sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:orientation="horizontal"
                android:layout_marginTop="5dp">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="34dp"
                    android:text="LineSpacing:"
                    android:textAlignment="center"
                    android:textSize="13sp"/>
                <EditText
                    android:id="@+id/line_number"
                    android:layout_width="35dp"
                    android:layout_height="34dp"
                    android:textAlignment="center"
                    android:inputType="number"
                    android:textSize="12sp"
                    android:textColor="@color/ffff5d5d"
                    android:background="@drawable/edittext2"
                    />

                <CheckBox
                    android:id="@+id/use_custom_data"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="15dp"
                    android:text="UseCustomData"
                    android:textSize="12sp"></CheckBox>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:orientation="horizontal"
                android:weightSum="10"
                >

                <Button
                    android:id="@+id/start"
                    android:layout_weight="2.5"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:text="start">

                </Button>

                <Button
                    android:id="@+id/stop"
                    android:layout_weight="2.5"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:text="stop">

                </Button>

                <Button
                    android:id="@+id/continues"
                    android:layout_weight="2.5"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:text="continue">

                </Button>

                <EditText
                    android:id="@+id/saveName"
                    android:layout_weight="2.5"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:hint="log name">

                </EditText>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:orientation="horizontal">
                <CheckBox
                    android:id="@+id/cn1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="true"
                    android:text="1">
                </CheckBox>

                <CheckBox
                    android:id="@+id/cn2"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="true"
                    android:text="2">
                </CheckBox>

                <CheckBox
                    android:id="@+id/cn3"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="true"
                    android:text="3">
                </CheckBox>

                <CheckBox
                    android:id="@+id/cn4"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="true"
                    android:text="4">
                </CheckBox>

                <CheckBox
                    android:id="@+id/cn5"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="true"
                    android:text="5">
                </CheckBox>

                <CheckBox
                    android:id="@+id/cn6"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="true"
                    android:text="6"
                    >
                </CheckBox>

                <CheckBox
                    android:id="@+id/cn7"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="true"
                    android:text="7"
                    >
                </CheckBox>

                <CheckBox
                    android:id="@+id/cn8"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="true"
                    android:text="8"
                    >
                </CheckBox>

            </LinearLayout>

            <TextView
                android:id="@+id/capsensor_data"
                android:layout_marginLeft="6dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>


            <com.besall.allbase.view.activity.chipstoollevel4.capsensor.CapSensorScaleView
                android:id="@+id/capsensor_scale_view"
                android:layout_width="match_parent"
                android:layout_height="30dp"/>

            <com.besall.allbase.view.activity.chipstoollevel4.capsensor.CapsensorListView
                android:id="@+id/capsensor_list_view"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="550dp"
                android:focusable="true"
                />

        </LinearLayout>

    </ScrollView>

</LinearLayout>



