<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6">

    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <include
        android:id="@+id/loginfo"
        layout="@layout/logview"
        android:visibility="gone"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="20dp"
        android:orientation="vertical"
        android:padding="20dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginRight="100dp"
            android:gravity="center"
            android:orientation="vertical"
            >
            <RadioGroup
                android:id="@+id/cmd_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:gravity="left"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/sanshan_normal"
                    android:text="Use header"
                    android:textColor="@color/title_color"
                    android:textSize="14sp" />

                <RadioButton
                    android:id="@+id/use_cmd_header"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:fontFamily="@font/sanshan_normal"
                    android:textColor="@color/title_color"
                    android:textSize="14sp"
                    android:text="No header"
                    />

                <RadioButton
                    android:id="@+id/nouse_cmd_header"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    />

            </RadioGroup>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginRight="0dp"
            android:gravity="center"
            android:orientation="vertical"
            >
            <RadioGroup
                android:id="@+id/cmd_need_efffective_length"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:gravity="left"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/sanshan_normal"
                    android:text="NO Effective Length"
                    android:textColor="@color/title_color"
                    android:textSize="14sp" />

                <RadioButton
                    android:id="@+id/cmd_need_efffective_length_no"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="left"
                    android:fontFamily="@font/sanshan_normal"
                    android:textColor="@color/title_color"
                    android:textSize="14sp"
                    android:text="Has Effective Length"
                    />

                <RadioButton
                    android:id="@+id/cmd_need_efffective_length_yes"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    />

            </RadioGroup>
        </LinearLayout>


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="22dp"
            android:text="@string/filepath"
            android:textColor="@color/title_color"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/file_path"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:text="--"
            android:textColor="@color/title_color"
            android:textSize="15sp" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:text="@string/customcmd"
                android:textColor="@color/title_color"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/cur_cmd_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:text="-"
                android:textColor="@color/title_color"
                android:textSize="15sp" />


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="54dp"
            android:layout_marginTop="7dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:orientation="vertical">

                <RelativeLayout
                    android:id="@+id/cmd_typing"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginRight="65dp"
                    android:focusable="true"
                    android:focusableInTouchMode="true">

<!--                    <RelativeLayout-->
<!--                        android:id="@+id/select_cmd_rl"-->
<!--                        android:layout_width="44.45dp"-->
<!--                        android:layout_height="match_parent"-->
<!--                        android:layout_alignParentRight="true"-->
<!--                        android:background="@color/white">-->

<!--&lt;!&ndash;                        <ImageView&ndash;&gt;-->
<!--&lt;!&ndash;                            android:id="@+id/select_loginaccount_image"&ndash;&gt;-->
<!--&lt;!&ndash;                            android:layout_width="20dp"&ndash;&gt;-->
<!--&lt;!&ndash;                            android:layout_height="20dp"&ndash;&gt;-->

<!--&lt;!&ndash;                            android:layout_alignParentRight="true"&ndash;&gt;-->
<!--&lt;!&ndash;                            android:layout_centerVertical="true"&ndash;&gt;-->
<!--&lt;!&ndash;                            android:layout_marginRight="16.89dp"&ndash;&gt;-->
<!--&lt;!&ndash;                            android:src="@drawable/chips_tools_icon_down" />&ndash;&gt;-->
<!--                    </RelativeLayout>-->

                    <ImageView
                        android:id="@+id/account_login_detete"
                        android:layout_width="23.56dp"
                        android:layout_height="23.11dp"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="4.45dp"
                        android:src="@drawable/delete_image"
                        android:visibility="gone" />


                    <EditText
                        android:id="@+id/cmd_ed"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_alignParentBottom="true"
                        android:layout_toLeftOf="@id/account_login_detete"
                        android:background="@drawable/rectangle_cmded"
                        android:digits="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_"
                        android:drawableRight="@drawable/chips_tools_icon_down"
                        android:gravity="center_vertical"
                        android:hint="@string/account_input_tips"
                        android:imeOptions="actionDone"
                        android:padding="12dp"
                        android:textColorHint="@color/activityText"
                        android:textSize="15sp" />


                </RelativeLayout>

                <TextView
                    android:id="@+id/login_account_textview"
                    android:layout_width="match_parent"
                    android:layout_height="1.78dp"
                     />

            </LinearLayout>


            <ImageButton
                android:id="@+id/data_send"
                android:layout_width="50dp"
                android:layout_height="match_parent"
                android:layout_marginLeft="-50dp"
                android:background="@drawable/chips_tools_icon_send" />

        </LinearLayout>

        <TextView
            android:id="@+id/receive_info"
            android:layout_width="match_parent"
            android:layout_height="95dp"
            android:layout_marginTop="15dp"
            android:background="@drawable/rectangle_cmd_receive"
            android:text="Receive info"
            android:textSize="15sp"
            android:paddingLeft="12dp"
            android:paddingTop="17dp"
            android:paddingRight="12dp"
            android:paddingBottom="17dp"
            />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginTop="15dp"
            android:orientation="horizontal"
            android:weightSum="1"
            >

            <Button
                android:id="@+id/ImportCmd"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:background="@drawable/rectangle_button3"
                android:drawableLeft="@drawable/chips_tools_icon_file_import"
                android:drawablePadding="5dp"
                android:paddingLeft="18dp"
                android:gravity="left|center"
                android:text="@string/importfile"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:layout_weight="0.47"
                android:textAllCaps="false"
                />
             <View
                 android:layout_width="0dp"
                 android:layout_height="match_parent"
                 android:layout_weight="0.06"
                 >
             </View>

            <Button
                android:id="@+id/cmd_save"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:background="@drawable/rectangle_button3"
                android:drawableLeft="@drawable/chips_tools_icon_file_export"
                android:drawablePadding="5dp"
                android:paddingLeft="18dp"
                android:gravity="left|center"
                android:text="@string/exportfile"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:layout_weight="0.47"
                android:textAllCaps="false"/>

        </LinearLayout>

        <Spinner
            android:id="@+id/datamode"
            android:layout_width="200dp"
            android:layout_height="54dp"
            android:layout_gravity="center"
            android:layout_marginTop="20dp"
            android:background="@drawable/rectangle_spinner"
            />

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="-80dp">

        </View>



        <Button
            android:id="@+id/spp_stop"
            android:layout_width="214dp"
            android:layout_marginLeft="40dp"
            android:layout_marginRight="40dp"
            android:layout_height="68dp"
            android:layout_marginTop="-80dp"
            android:layout_gravity="center"
            android:background="@drawable/ota_button_bg"
            android:text="@string/disconnect_device"
            android:textColor="@color/white"
            android:textAllCaps="false"
            android:textSize="14sp"
            >
        </Button>
    </LinearLayout>
</LinearLayout>



