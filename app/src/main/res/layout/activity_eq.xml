<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:tc="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6"
    >
    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <TextView
        android:layout_marginTop="30dp"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:layout_marginLeft="20dp"
        android:text="Status: TOTA Connect Successful"
        android:textSize="15sp"
        android:layout_gravity="center"
        />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="30dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:orientation="horizontal">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:textSize="18sp"
                android:text="EQ Index"
                android:textColor="@color/activityText"
                android:layout_gravity="center"/>

            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginRight="200dp"/>

            <Spinner
                android:id="@+id/eq_all_index"
                android:layout_marginLeft="-100dp"
                android:layout_width="80dp"
                android:layout_height="50dp"
                android:layout_gravity="center"
                android:textAllCaps="false"
                >

            </Spinner>


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="-150dp"
                android:layout_gravity="center"
                android:textSize="15sp"
                android:text="EQ 101"
                />

        </LinearLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:background="@color/color_aa1247c2"
            android:text="   EQ OPERATION"
            android:textSize="25sp"
            android:textAlignment="textStart"
            android:gravity="left|center"
            tools:ignore="RtlCompat">

        </TextView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:orientation="vertical"
            >
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:orientation="horizontal"
                android:weightSum="1">
                <Button
                    android:layout_weight="0.45"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="get running EQ index"
                    >

                </Button>

                <View
                    android:layout_width="0dp"
                    android:layout_weight="0.1"
                    android:layout_height="match_parent"/>

                <Button
                    android:layout_weight="0.45"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="set index"
                    android:textAlignment="center"
                    >

                </Button>

            </LinearLayout>

            <LinearLayout
                android:layout_marginTop="10dp"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:orientation="horizontal">
                <Button
                    android:layout_weight="0.45"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="realtime update"
                    >

                </Button>

                <View
                    android:layout_width="0dp"
                    android:layout_weight="0.1"
                    android:layout_height="match_parent"/>

                <Button
                    android:layout_weight="0.45"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="save data"
                    android:textAlignment="center"
                    >

                </Button>
            </LinearLayout>

        </LinearLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:background="@color/color_aa1247c2"
            android:text="   EQ PARAMETER"
            android:textSize="25sp"
            android:textAlignment="textStart"
            android:gravity="left|center"
            tools:ignore="RtlCompat"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:textSize="15sp"
            android:text="Number of Bands"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <Button
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="1"
                android:textSize="10sp"
                android:id="@+id/btnBandTotal1"/>
            <Button
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="2"
                android:textSize="10sp"
                android:id="@+id/btnBandTotal2"/>
            <Button
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="3"
                android:textSize="10sp"
                android:id="@+id/btnBandTotal3"/>
            <Button
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="4"
                android:textSize="10sp"
                android:id="@+id/btnBandTotal4"/>
            <Button
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="5"
                android:textSize="10sp"
                android:id="@+id/btnBandTotal5"/>
            <Button
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="6"
                android:textSize="10sp"
                android:id="@+id/btnBandTotal6"/>
            <Button
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="7"
                android:textSize="10sp"
                android:id="@+id/btnBandTotal7"/>
            <Button
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="8"
                android:textSize="10sp"
                android:id="@+id/btnBandTotal8"/>
            <Button
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="9"
                android:textSize="10sp"
                android:id="@+id/btnBandTotal9"/>
            <Button
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="10"
                android:textSize="10sp"
                android:id="@+id/btnBandTotal10"/>

        </LinearLayout>

                                <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="1.2"
                                android:text="Freq. (Hz)" />

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="3"
                                android:gravity="center"
                                android:text="Gain" />

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="1"
                                android:text="(dB)" />

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="1"
                                android:text="Q" />

                        </LinearLayout>
                        >

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:orientation="horizontal">

                            <EditText
                                android:id="@+id/editTextFreq0"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1.2"
                                android:inputType="numberDecimal"
                                android:text="200" />
                            <SeekBar
                                android:id="@+id/seekBar0"
                                android:layout_gravity="center_vertical"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="3" />
                            <EditText
                                android:id="@+id/editTextGain0"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:enabled="false"
                                android:text="" />
                            <EditText
                                android:id="@+id/editTextQ0"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:text="2" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:orientation="horizontal">

                            <EditText
                                android:id="@+id/editTextFreq1"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1.2"
                                android:inputType="numberDecimal"
                                android:text="500" />

                            <SeekBar
                                android:id="@+id/seekBar1"
                                android:layout_gravity="center_vertical"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="3" />

                            <EditText
                                android:id="@+id/editTextGain1"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:enabled="false"
                                android:text="" />
                            <EditText
                                android:id="@+id/editTextQ1"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:text="2" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:orientation="horizontal">

                            <EditText
                                android:id="@+id/editTextFreq2"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1.2"
                                android:inputType="numberDecimal"
                                android:text="400" />

                            <SeekBar
                                android:id="@+id/seekBar2"
                                android:layout_gravity="center_vertical"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="3" />
                            <EditText
                                android:id="@+id/editTextGain2"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:enabled="false"
                                android:text="" />
                            <EditText
                                android:id="@+id/editTextQ2"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:text="2" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:orientation="horizontal">

                            <EditText
                                android:id="@+id/editTextFreq3"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1.2"
                                android:inputType="numberDecimal"
                                android:text="550" />

                            <SeekBar
                                android:id="@+id/seekBar3"
                                android:layout_gravity="center_vertical"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="3" />
                            <EditText
                                android:id="@+id/editTextGain3"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:enabled="false"
                                android:text="" />
                            <EditText
                                android:id="@+id/editTextQ3"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:text="2" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:orientation="horizontal">

                            <EditText
                                android:id="@+id/editTextFreq4"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1.2"
                                android:inputType="numberDecimal"
                                android:text="770" />

                            <SeekBar
                                android:id="@+id/seekBar4"
                                android:layout_gravity="center_vertical"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="3" />
                            <EditText
                                android:id="@+id/editTextGain4"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:enabled="false"
                                android:text="" />
                            <EditText
                                android:id="@+id/editTextQ4"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:text="2" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:orientation="horizontal">

                            <EditText
                                android:id="@+id/editTextFreq5"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1.2"
                                android:inputType="numberDecimal"
                                android:text="1000" />

                            <SeekBar
                                android:id="@+id/seekBar5"
                                android:layout_gravity="center_vertical"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="3" />
                            <EditText
                                android:id="@+id/editTextGain5"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:enabled="false"
                                android:text="" />
                            <EditText
                                android:id="@+id/editTextQ5"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:text="2" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:orientation="horizontal">

                            <EditText
                                android:id="@+id/editTextFreq6"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1.2"
                                android:inputType="numberDecimal"
                                android:text="2000" />
                            <SeekBar
                                android:id="@+id/seekBar6"
                                android:layout_gravity="center_vertical"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="3" />
                            <EditText
                                android:id="@+id/editTextGain6"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:enabled="false"
                                android:text="" />
                            <EditText
                                android:id="@+id/editTextQ6"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:text="2" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:orientation="horizontal"
                            android:visibility="visible">

                            <EditText
                                android:id="@+id/editTextFreq7"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1.2"
                                android:inputType="numberDecimal"
                                android:text="4000" />

                            <SeekBar
                                android:id="@+id/seekBar7"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_weight="3" />
                            <EditText
                                android:id="@+id/editTextGain7"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:enabled="false"
                                android:text="" />
                            <EditText
                                android:id="@+id/editTextQ7"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:text="2" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:orientation="horizontal"
                            android:visibility="visible">

                            <EditText
                                android:id="@+id/editTextFreq8"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1.2"
                                android:inputType="numberDecimal"
                                android:text="8000" />
                            <SeekBar
                                android:id="@+id/seekBar8"
                                android:layout_gravity="center_vertical"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="3" />
                            <EditText
                                android:id="@+id/editTextGain8"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:enabled="false"
                                android:text="" />
                            <EditText
                                android:id="@+id/editTextQ8"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:text="2" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:orientation="horizontal"
                            android:visibility="visible">

                            <EditText
                                android:id="@+id/editTextFreq9"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1.2"
                                android:inputType="numberDecimal"
                                android:text="16000" />
                            <SeekBar
                                android:id="@+id/seekBar9"
                                android:layout_gravity="center_vertical"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="3" />
                            <EditText
                                android:id="@+id/editTextGain9"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:enabled="false"
                                android:text="" />
                            <EditText
                                android:id="@+id/editTextQ9"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:text="2" />

                        </LinearLayout>

        </LinearLayout>
    </ScrollView>

</LinearLayout>
