<?xml version="1.0" encoding="utf-8"?>
<com.besall.allbase.common.utils.SlideLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="62dp"
    android:clickable="true">

    <LinearLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="62dp"
        android:gravity="center">

        <ImageView
            android:id="@+id/item_imageview"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginLeft="10dp"
/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="62dp"
            android:orientation="vertical"
            android:layout_marginLeft="15dp">
            <View
                android:layout_width="match_parent"
                android:layout_height="5dp"
                android:layout_weight="1"/>
            <TextView
                android:id="@+id/file_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:layout_weight="1"
                android:text="-"
                android:textSize="14sp"
                android:textColor="@color/ff2c4662"
                android:textAlignment="viewStart"/>

            <TextView
                android:id="@+id/file_size"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:layout_weight="1"
                android:text="-"
                android:textSize="12sp"
                android:textColor="@color/title_color_light"
                android:textAlignment="viewStart"/>
            <View
                android:layout_width="match_parent"
                android:layout_height="5dp"
                android:layout_weight="1"/>
        </LinearLayout>

    </LinearLayout>

                <TextView
                    android:id="@+id/delete_button"
                    android:layout_width="88dp"
                    android:layout_height="62dp"
                    android:gravity="center"
                    android:textColor="#ffffffff"
                    android:background="#ffff5735"
                    android:text="Delete"
                    android:textSize="16sp"/>

</com.besall.allbase.common.utils.SlideLayout>

