package com.besall.allbase.view.activity.tools.SettingActivity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.os.Handler;
import android.os.Message;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.SPHelper;
import com.besall.allbase.R;
import com.besall.allbase.common.Constants;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.activity.tools.confirmdialog.ConfirmDialog;
import com.besall.allbase.view.activity.tools.confirmdialog.ConfirmDialoglistener;
import com.besall.allbase.view.base.BaseActivity;
import com.suke.widget.SwitchButton;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.lang.reflect.Array;
import java.nio.channels.FileChannel;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static com.bes.bessdk.BesSdkConstants.MSG_TIME_OUT;
import static com.besall.allbase.common.utils.FileUtils.getFolderPath;

public class SettingActivity extends BaseActivity<ISettingActivity, SettingPresenter> implements ISettingActivity, View.OnClickListener, SwitchButton.OnCheckedChangeListener {

    private static SettingActivity instance;
    private String curTitle;
    private String logPath = getFolderPath() + "BES/LogData/OTA/";

    private SwitchButton switchButton_tota;
    private SwitchButton switchButton_totav2;
    private SwitchButton switchButton;
    private SwitchButton switchButton_phy2m;
    private SwitchButton switchButton_use_normal_connect;

    private List<SettingBean> filePathList = new ArrayList<>();

    private ListView locolLog_list;
//    private View moreuuid;
//    private View hideshow;
//    private ImageView more;
//    public boolean hidestatus = false;
    private TextView version_text;
    private Button version_button;
    private int clickTimes = 0;

    private EditText ble_interval;
    private EditText spp_interval;
//    private EditText gatt_interval;
//
//    private EditText service_uuid;
//    private EditText characteristicTx_uuid;
//    private EditText characteristicRx_uuid;
//    private EditText descriptor_uuid;
//
    private Button ack_btn_0;
//    private Button ack_btn_1;
//    private Button ack_btn_2;
//    private Button ack_btn_3;
//    private Button ack_btn_4;

    public boolean defaultintervals = false;
//    public boolean defaultserviceuuids = false;
//    public boolean defaultTxuuids = false;
//    public boolean defaultRxuuids = false;
//    public boolean defaultdescriptoruuids = false;

    private ImageView ack_image_0;
//    private ImageView ack_image_1;
//    private ImageView ack_image_2;
//    private ImageView ack_image_3;
//    private ImageView ack_image_4;

    private Button privacy_policy;
    private View agree_view;
    private TextView agreeTV;
    private Button agree;
    private Button disagree;
    private SharedPreferences preferences;
    private SharedPreferences.Editor editor;
    private ScrollView whole_scr;
    private ScrollView scr_policy;
    private final String AGREE_KEY = "Bes_Agree_Key";


    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected SettingPresenter createPresenter() {
        return new SettingPresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        initFilePathResource();
        preferences = getSharedPreferences("AGREE_KEY", 0);
        editor = preferences.edit();
    }

    @Override
    protected int getContentViewId() {
        return R.layout.act_setting;
    }

    @Override
    protected void bindView() {
        //输入法置于输入框下方
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
        switchButton_tota = (SwitchButton)findViewById(R.id.switchButton_tota);
        switchButton_totav2 = (SwitchButton)findViewById(R.id.switchButton_totav2);
        switchButton = (SwitchButton)findViewById(R.id.switchButton);
        locolLog_list = (ListView)findViewById(R.id.locolLog_list);
        version_text = (TextView)findViewById(R.id.version_text);
        version_button = (Button)findViewById(R.id.version_button);
        switchButton_phy2m = (SwitchButton) findViewById(R.id.switchButton_phy2m);
        switchButton_use_normal_connect = (SwitchButton) findViewById(R.id.switchButton_use_normal_connect);
        privacy_policy = (Button)findViewById(R.id.privacy_policy);
        agree_view = (View)findViewById(R.id.agree_view);
        agree = (Button)findViewById(R.id.agree);
        disagree = (Button)findViewById(R.id.disagree);
        agreeTV = (TextView)findViewById(R.id.agreeTV);
        whole_scr = (ScrollView)findViewById(R.id.whole_scr);
        scr_policy = (ScrollView)findViewById(R.id.scr_policy);
//        moreuuid = (View)findViewById(R.id.more_uuids);
//        hideshow = (View)findViewById(R.id.showemore);
//        more = (ImageView)findViewById(R.id.more);
//
        ble_interval =(EditText)findViewById(R.id.ble_interval);
        spp_interval =(EditText)findViewById(R.id.spp_interval);
//        gatt_interval =(EditText)findViewById(R.id.gatt_interval);
//
//        service_uuid =(EditText)findViewById(R.id.service_uuid);
//        characteristicTx_uuid =(EditText)findViewById(R.id.characteristicTx_uuid);
//        characteristicRx_uuid =(EditText)findViewById(R.id.characteristicRx_uuid);
//        descriptor_uuid =(EditText)findViewById(R.id.descriptor_uuid);
//
        ack_btn_0 = (Button)findViewById(R.id.ack_btn_0);
        ack_image_0 = (ImageView)findViewById(R.id.ack_image_0);
//        ack_btn_1 = (Button)findViewById(R.id.ack_btn_1);
//        ack_image_1 = (ImageView)findViewById(R.id.ack_image_1);
//        ack_btn_2 = (Button)findViewById(R.id.ack_btn_2);
//        ack_image_2 = (ImageView)findViewById(R.id.ack_image_2);
//        ack_btn_3 = (Button)findViewById(R.id.ack_btn_3);
//        ack_image_3 = (ImageView)findViewById(R.id.ack_image_3);
//        ack_btn_4 = (Button)findViewById(R.id.ack_btn_4);
//        ack_image_4 = (ImageView)findViewById(R.id.ack_image_4);

    }

    @SuppressLint("ResourceAsColor")
    @Override
    protected void initView() {
        inittoolbar(getString(R.string.settings));

        boolean totaEncryption = (boolean) SPHelper.getPreference(instance, BesSdkConstants.BES_TOTA_ENCRYPTION_KEY, BesSdkConstants.BES_TOTA_ENCRYPTION_VALUE);
        switchButton_tota.setChecked(totaEncryption);
        switchButton_tota.setOnCheckedChangeListener(instance);
        boolean useTotaV2 = (boolean) SPHelper.getPreference(instance, BesSdkConstants.BES_TOTA_USE_TOTAV2, BesSdkConstants.BES_TOTA_USE_TOTAV2_VALUE);
        switchButton_totav2.setChecked(useTotaV2);
        switchButton_totav2.setOnCheckedChangeListener(instance);
        boolean saveLog = (boolean) SPHelper.getPreference(instance, BesSdkConstants.BES_SAVE_LOG_KEY, BesSdkConstants.BES_SAVE_LOG_VALUE);
        switchButton.setChecked(saveLog);
        switchButton.setOnCheckedChangeListener(instance);
//        hideshow.setOnClickListener(instance);
        boolean usePHY2M = (boolean) SPHelper. getPreference(instance,BesSdkConstants.BES_USE_PHY_2M, BesSdkConstants.BES_USE_PHY_2MVALUE);
        switchButton_phy2m.setChecked(usePHY2M);
        switchButton_phy2m.setOnCheckedChangeListener(instance);

        boolean useNormalConnect = (boolean) SPHelper. getPreference(instance,BesSdkConstants.BES_USE_NORMAL_CONNECT, BesSdkConstants.BES_USE_NORMAL_CONNECT_VALUE);
        switchButton_use_normal_connect.setChecked(useNormalConnect);
        switchButton_use_normal_connect.setOnCheckedChangeListener(instance);

        initListView();
        version_text.setText(getVersionName(instance));
        version_button.setOnClickListener(instance);

        privacy_policy.setOnClickListener(instance);
        agree.setOnClickListener(instance);
        disagree.setOnClickListener(instance);

        agreeTV.setText(getString(R.string.agreement));

        version_text.setText(getString(R.string.appVersion) + ": " + getVersionName(instance));

        boolean show = preferences.getBoolean(AGREE_KEY, true);
        if (show) {
            agree_view.setVisibility(View.VISIBLE);
        } else {
            agree_view.setVisibility(View.GONE);
        }

        whole_scr.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                scr_policy.getParent().requestDisallowInterceptTouchEvent(false);
                return false;
            }
        });

        scr_policy.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                v.getParent().requestDisallowInterceptTouchEvent(true);
                return false;
            }
        });
        ack_btn_0.setOnClickListener(instance);
//        ack_btn_1.setOnClickListener(instance);
//        ack_btn_2.setOnClickListener(instance);
//        ack_btn_3.setOnClickListener(instance);
//        ack_btn_4.setOnClickListener(instance);
//
        boolean defaultinterval = (boolean) SPHelper.getPreference(instance, BesSdkConstants.BES_default_INTERVAL, BesSdkConstants.BES_default_INTERVAL_VALUE);
        String bleinterval = (String) SPHelper.getPreference(instance,BesSdkConstants.BES_BLE_INTERVAL,"50");
        String sppinterval = (String) SPHelper.getPreference(instance,BesSdkConstants.BES_SPP_INTERVAL,"30");
//        String gattinterval = (String) SPHelper.getPreference(instance,BesSdkConstants.BES_GATT_INTERVAL,"50");
        if (defaultinterval == true) {
            ack_image_0.setImageResource(R.drawable.ota_top_sele);
            defaultintervals = true;
            ble_interval.setEnabled(false);
            ble_interval.setText("50");
            spp_interval.setEnabled(false);
            spp_interval.setText("30");
//            gatt_interval.setEnabled(false);
//            gatt_interval.setText("30");
        } else {
            ack_image_0.setImageResource(R.drawable.ota_top_nor);
            ble_interval.setEnabled(true);
            spp_interval.setEnabled(true);
//            gatt_interval.setEnabled(true);
            ble_interval.setText(bleinterval);
            spp_interval.setText(sppinterval);
//            gatt_interval.setText(gattinterval);
            defaultintervals = false;
        }

        ble_interval.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                set_bleintervals(ble_interval.getText().toString());
                Log.i(TAG, "afterTextChanged: +++" + ble_interval.getText().toString());
            }
        });

        spp_interval.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                set_sppintervals(spp_interval.getText().toString());
            }
        });

//        ack_image_1.setImageResource(R.drawable.ota_top_sele);
//        ack_image_2.setImageResource(R.drawable.ota_top_sele);
//        ack_image_3.setImageResource(R.drawable.ota_top_sele);
//        ack_image_4.setImageResource(R.drawable.ota_top_sele);

//        service_uuid.setEnabled(false);
//        characteristicTx_uuid.setEnabled(false);
//        characteristicRx_uuid.setEnabled(false);
//        descriptor_uuid.setEnabled(false);

//        service_uuid.addTextChangedListener(new TextWatcher() {
//            @Override
//            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
//
//            }
//
//            @Override
//            public void onTextChanged(CharSequence s, int start, int before, int count) {
//
//            }
//
//            @Override
//            public void afterTextChanged(Editable s) {
//                if(service_uuid.getText().toString().length() == 32){
//                    UUIDcheck(service_uuid.getText().toString());
//                    showToast("UUID SET");
//                } else {
//                    showToast("UUID uncurrect");
//                    return;
//                }
//            }
//        });
//        characteristicTx_uuid.addTextChangedListener(new TextWatcher() {
//            @Override
//            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
//
//            }
//
//            @Override
//            public void onTextChanged(CharSequence s, int start, int before, int count) {
//
//            }
//
//            @Override
//            public void afterTextChanged(Editable s) {
//                if(characteristicTx_uuid.getText().toString().length() == 32){
//                    UUIDcheck(characteristicTx_uuid.getText().toString());
//                    showToast("UUID SET");
//                } else {
//                    showToast("UUID uncurrect");
//                    return;
//                }
//            }
//        });
//        characteristicRx_uuid.addTextChangedListener(new TextWatcher() {
//            @Override
//            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
//
//            }
//
//            @Override
//            public void onTextChanged(CharSequence s, int start, int before, int count) {
//
//            }
//
//            @Override
//            public void afterTextChanged(Editable s) {
//                if(characteristicRx_uuid.getText().toString().length() == 32){
//                    UUIDcheck(characteristicRx_uuid.getText().toString());
//                    showToast("UUID SET");
//                } else {
//                    showToast("UUID uncurrect");
//                    return;
//                }
//            }
//        });
//        descriptor_uuid.addTextChangedListener(new TextWatcher() {
//            @Override
//            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
//
//            }
//
//            @Override
//            public void onTextChanged(CharSequence s, int start, int before, int count) {
//
//            }
//
//            @Override
//            public void afterTextChanged(Editable s) {
//                if(descriptor_uuid.getText().toString().length() == 32){
//                    UUIDcheck(descriptor_uuid.getText().toString());
//                    showToast("UUID SET");
//                } else {
//                    showToast("UUID uncurrect");
//                    return;
//                }
//            }
//        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        initFilePathResource();
        initListView();
    }

    private void initFilePathResource() {
        filePathList = new ArrayList<>();
        File logFile = getExternalFilesDir("OTA");
        if (logFile == null) return;
        File[] logs = logFile.listFiles();
        if (logs == null) return;
        for (int i = 0; i < logs.length; i ++) {
            String filePath = logs[i].getPath();
            Log.i(TAG, "initFilePathResource: 1"+ filePath);
            if (filePath.contains(" OTA")) {
                String fileName = logs[i].getName();
                Log.i(TAG, "initFilePathResource: 1111"+ fileName);
                long size = getFileSize(logs[i].getAbsolutePath());
                String fileSize = "";
                String unitString = "";
                if (size < 1024) {
                    fileSize = size + "";
                    unitString = " B";
                } else if (size < 1024 * 1024) {
                    fileSize = ArrayUtil.div(size, 1024, 2) + "";
                    unitString = "kB";
                } else {
                    fileSize = ArrayUtil.div(size, 1024 * 1024, 2) + "";
                    unitString = "M";
                }
                SettingBean settingBean = new SettingBean(fileName, fileSize + unitString, i + 100,filePath);
                Log.i(TAG, "initFilePathResource: path" + filePath);
                Log.i(TAG, "initFilePathResource: name" + fileName);
                filePathList.add(settingBean);
            }
        }
    }

    public static long getFileSize(String filePath) {
        FileChannel fc = null;
        long fileSize = 0;
        try {
            File f = new File(filePath);
            if (f.exists() && f.isFile()) {
                FileInputStream fis = new FileInputStream(f);
                fc = fis.getChannel();
                fileSize = fc.size();
            } else {
                Log.e("getFileSize","file doesn't exist or is not a file");
            }
        } catch (FileNotFoundException e) {
            Log.e("getFileSize",e.getMessage());
        } catch (IOException e) {
            Log.e("getFileSize",e.getMessage());
        } finally {
            if (null != fc) {
                try {
                    fc.close();
                } catch(IOException e) {
                    Log.e("getFileSize",e.getMessage());
                }
            }
        }
        return fileSize;
    }

    public static String getVersionName(Context mContext) {
        String versionName = "";
        try {
            //获取软件版本号，对应AndroidManifest.xml下android:versionCode
            versionName = mContext.getPackageManager().
                    getPackageInfo(mContext.getPackageName(), 0).versionName;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return versionName;
    }

    private void initListView() {
        SettingAdapter adapter = new SettingAdapter(SettingActivity.this, R.layout.act_setting_lsv, filePathList);
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams)locolLog_list.getLayoutParams();
        layoutParams.height = filePathList.size() * 58 * 3;
        locolLog_list.setLayoutParams(layoutParams);
        locolLog_list.setAdapter(adapter);
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    public void onClick(View v) {
        if (v.getTag() != null && v.getTag().getClass().equals(Integer.class) && (int)v.getTag() > 99 && (int)v.getTag() < 150) {
            int position = (Integer) v.getTag() - 100;
            SettingBean settingBean = filePathList.get(position);
            showConfirmDialog(getString(R.string.delete) + " " + settingBean.getPath(), position);
            Log.i(TAG, "onClick: 11111" + settingBean.getPath());
            return;
        }
        if (v.getTag() != null && v.getTag().getClass().equals(Integer.class) && (int)v.getTag() > 99 + 100 && (int)v.getTag() < 150 + 100) {
            int position = (Integer) v.getTag() - 200;
            SettingBean settingBean = filePathList.get(position);
            Log.i(TAG, "onClick: ---------" + logPath);
            Log.i(TAG, "onClick: ---------" + settingBean.getPath());

            ActivityUtils.showToast( settingBean.getFileName());
            return;
        }

        switch (v.getId()) {
            case R.id.version_button:
                mMsgHandler.removeMessages(MSG_TIME_OUT);
                clickTimes++;
                if (clickTimes > 8) {
                    clickTimes = 0;
                    ActivityUtils.showToast("特别感谢:\nShawn\nBrian\n李绍堃\n朱帅龙\n余文艳\n张涛\n沈凯舟\n范羽");
                } else {
                    Message message = mMsgHandler.obtainMessage(MSG_TIME_OUT);
                    mMsgHandler.sendMessageDelayed(message, 1000);
                }
                break;
            case R.id.privacy_policy:
                agree_view.setVisibility(View.VISIBLE);
                break;
            case R.id.agree:
                agree_view.setVisibility(View.GONE);
                editor.putBoolean(AGREE_KEY, false);
                editor.commit();
                break;
            case R.id.disagree:
                editor.putBoolean(AGREE_KEY, true);
                editor.commit();
                finish();
                break;
//            case R.id.showemore:
//                getuuid();
//                if (hidestatus == false) {
//                    more.setBackgroundResource(R.drawable.icon_show);
//                    moreuuid.setVisibility(View.VISIBLE);
//                    hidestatus = true;
//                } else if (hidestatus = true) {
//                    more.setBackgroundResource(R.drawable.icon_hide);
//                    moreuuid.setVisibility(View.GONE);
//                    hidestatus = false;
//                }
//                break;
            case R.id.ack_btn_0:
                Log.i(TAG, "onClick: 11111");
                if (defaultintervals == false) {
                    ack_image_0.setImageResource(R.drawable.ota_top_sele);
                    ble_interval.setEnabled(false);
                    spp_interval.setEnabled(false);
                    ble_interval.setText("50");
                    spp_interval.setText("30");
//                    gatt_interval.setEnabled(false);
                    defaultintervals = true;
                    SPHelper.putPreference(instance, BesSdkConstants.BES_default_INTERVAL,defaultintervals);
                } else {
                    ack_image_0.setImageResource(R.drawable.ota_top_nor);
                    ble_interval.setEnabled(true);
                    spp_interval.setEnabled(true);
//                    gatt_interval.setEnabled(true);
                    defaultintervals = false;
                    SPHelper.putPreference(instance, BesSdkConstants.BES_default_INTERVAL, defaultintervals);
                }
                break;
//            case R.id.ack_btn_1:
//                if (defaultserviceuuids == false) {
//                    ack_image_1.setImageResource(R.drawable.ota_top_sele);
//                    service_uuid.setEnabled(false);
//                    defaultserviceuuids = true;
//                } else {
//                    ack_image_1.setImageResource(R.drawable.ota_top_nor);
//                    service_uuid.setEnabled(true);
//
//
//
//                    defaultserviceuuids = false;
//                }
//                break;
//            case R.id.ack_btn_2:
//                if (defaultTxuuids == false) {
//                    ack_image_2.setImageResource(R.drawable.ota_top_sele);
//                    characteristicTx_uuid.setEnabled(false);
//                    defaultTxuuids = true;
//                } else {
//                    ack_image_2.setImageResource(R.drawable.ota_top_nor);
//                    characteristicTx_uuid.setEnabled(true);
//                    defaultTxuuids = false;
//                }
//                break;
//            case R.id.ack_btn_3:
//                if (defaultRxuuids == false) {
//                    ack_image_3.setImageResource(R.drawable.ota_top_sele);
//                    characteristicRx_uuid.setEnabled(false);
//                    defaultRxuuids = true;
//                } else {
//                    ack_image_3.setImageResource(R.drawable.ota_top_nor);
//                    characteristicRx_uuid.setEnabled(true);
//                    defaultRxuuids = false;
//                }
//                break;
//            case R.id.ack_btn_4:
//                if (defaultdescriptoruuids == false) {
//                    ack_image_4.setImageResource(R.drawable.ota_top_sele);
//                    descriptor_uuid.setEnabled(false);
//                    defaultdescriptoruuids = true;
//                } else {
//                    ack_image_4.setImageResource(R.drawable.ota_top_nor);
//                    descriptor_uuid.setEnabled(true);
//                    defaultdescriptoruuids = false;
//                }
//                break;
            default:
                break;
        }

    }

    public Handler mMsgHandler = new Handler() {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case MSG_TIME_OUT:
                    clickTimes = 0;
                    break;

                default:
                    break;
            }
        }
    };


    @Override
    public void onCheckedChanged(SwitchButton view, boolean isChecked) {
        if (view == switchButton_tota) {
            SPHelper.putPreference(instance, BesSdkConstants.BES_TOTA_ENCRYPTION_KEY, isChecked);
        } else if (view == switchButton_totav2) {
            SPHelper.putPreference(instance, BesSdkConstants.BES_TOTA_USE_TOTAV2, isChecked);
        } else if (view == switchButton) {
            SPHelper.putPreference(instance, BesSdkConstants.BES_SAVE_LOG_KEY, isChecked);
        } else if (view == switchButton_phy2m) {
            SPHelper.putPreference(instance, BesSdkConstants.BES_USE_PHY_2M, isChecked);
        } else if (view == switchButton_use_normal_connect) {
            SPHelper.putPreference(instance, BesSdkConstants.BES_USE_NORMAL_CONNECT, isChecked);
        }
    }

    private void showConfirmDialog(String msg, int position) {
        ConfirmDialog confirmDialog = new ConfirmDialog(instance, msg, new ConfirmDialoglistener() {
            @Override
            public void confirmYes() {
                SettingBean settingBean = filePathList.get(position);
                File file = new File(settingBean.getFileName());
                Log.i(TAG, "confirmYes: " + file);
                if (file.delete()) {
                    initFilePathResource();
                    initListView();
                }
            }

            @Override
            public void confirmNo() {

            }
        });

        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                confirmDialog.show();
            }
        });

    }


    //    public void UUIDcheck(String uid){
//        Log.i(TAG, "UUIDcheck: " + uid);
//        String tempUUID = uid.replace("-","");
//        String uuid1 = tempUUID.substring(0,8);
//        String uuid2 = tempUUID.substring(8,12);
//        String uuid3 = tempUUID.substring(12,16);
//        String uuid4 = tempUUID.substring(16,20);
//        String uuid5 = tempUUID.substring(20,32);
//        String uuid = uuid1+"-"+uuid2+ "-"+uuid3+ "-"+uuid4+ "-"+uuid5;
//
////        String[] uuid11 = tempUUID.split("\\w{8}(-\\w{4}){3}-\\w{12}");
////        Log.i(TAG, "UUIDcheck: " + Arrays.toString(uuid11));
////        Log.i(TAG, "UUIDcheck: " + uuid11.length);
//        if (defaultserviceuuids == false){
//            SPHelper.putPreference(instance,BesSdkConstants.BES_CONNECT_SERVICE,uuid);
//            Log.i(TAG, "UUIDcheck: " + uuid);
//        }
//        if (defaultTxuuids == false){
//            SPHelper.putPreference(instance,BesSdkConstants.BES_CONNECT_CHARACTERISTIC,uuid);
//        }
//        if (defaultRxuuids == false){
//            SPHelper.putPreference(instance,BesSdkConstants.CUSTOMER_CHARACTERISTIC_RX_UUID,uuid);
//        }
//        if (defaultdescriptoruuids == false) {
//            SPHelper.putPreference(instance, BesSdkConstants.BES_CONNECT_DESCRIPTOR, uuid);
//        }
//    }

//    public void getuuid(){
//        String service_u = (String) SPHelper.getPreference(instance, BesSdkConstants.BES_CONNECT_SERVICE, BES_SERVICE_UUID.toString());
//        Log.i(TAG, "getuuid: " + service_u);
//
//    }

    public void set_bleintervals(String intervals){

        if (ble_interval.length() != 0 ) {
            SPHelper.putPreference(instance, BesSdkConstants.BES_BLE_INTERVAL, intervals);
            Log.i(TAG, "set_intervals: + " + intervals);
        }

    }

    public void set_sppintervals(String intervals){

        if (spp_interval.length() != 0) {
            SPHelper.putPreference(instance, BesSdkConstants.BES_SPP_INTERVAL, intervals);
        }
    }
}
