plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
}

android {
    compileSdkVersion 33
    buildToolsVersion "30.0.1"

    defaultConfig {
        applicationId "com.bes.besall"
        minSdkVersion 23
        targetSdkVersion 29
        versionCode 27
        versionName "20230510.01"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        // 设置ndk编译的cpu架构
        ndk {
            abiFilters 'arm64-v8a'
        }


    }
//
//    //设置CMakeLists文件的位置
//    externalNativeBuild {
//        cmake {
//            path "CMakeLists.txt"
//        }
//    }


    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
////    ndkVersion '21.4.7075529'
//    externalNativeBuild {
//        cmake {
//            path file('CMakeLists.txt')
//        }
//    }
    repositories {
        flatDir {
            dir 'libs'
        }
    }


}

configurations.all {
    resolutionStrategy.eachDependency { DependencyResolveDetails details ->
        def requested = details.requested
        if (requested.group == 'com.android.support') {
            if (!requested.name.startsWith("multidex")) {
                details.useVersion '25.3.0'
            }
        }
    }

    repositories {
        jcenter()
        flatDir {
            dirs 'libs'
        }
        mavenCentral()
        maven { url 'https://jitpack.io' }


    }

}



dependencies {
    implementation project(path: ':besota')
    api 'com.blankj:utilcodex:1.30.6'

    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'com.google.android.material:material:1.2.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.2'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.0.0'
    implementation 'com.google.code.gson:gson:2.8.6'
    implementation files('../login-with-amazon-sdk.jar')
    implementation 'androidx.core:core-ktx:1.7.0'
    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'androidx.test.ext:junit:1.1.2'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'

    debugImplementation 'com.squareup.leakcanary:leakcanary-android:1.6.3'
    releaseImplementation 'com.squareup.leakcanary:leakcanary-android-no-op:1.6.3'
    implementation 'io.reactivex.rxjava2:rxjava:2.0.5'
    implementation 'com.github.tbruyelle:rxpermissions:0.10.2'

    implementation 'com.nbsp:materialfilepicker:1.9.1'
    implementation group: 'net.sourceforge.jexcelapi', name:'jxl', version:'2.6.12'
    //noinspection GradleCompatible
    implementation 'com.android.support:appcompat-v7:25.2.0'
    compile 'com.zhy:autolayout:1.4.5'
    compile 'com.readystatesoftware.systembartint:systembartint:1.0.3'
    compile 'com.github.zcweng:switch-button:0.0.3@aar'

    //UI
    implementation 'com.android.support:recyclerview-v7:27.1.1'
}